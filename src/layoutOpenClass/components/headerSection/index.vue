<template>
  <div class="head-nav-con" :class="[{ 'blue-bg': showBackground }, 'theme-dark']">
    <div class="head-nav">
      <div class="head-nav-con-left">
        <img
          class="logo"
          referrerpolicy="referrer"
          :src="siteInfo?.logoUrl"
          alt=""
          v-if="!isLoginPage"
        />
        <img
          class="logo"
          referrerpolicy="referrer"
          :src="siteInfo?.blueLogoUrl"
          alt=""
          v-else
        />
      </div>
      <div class="head-nav-con-right">
        <div class="nav-right-menu">
          <template v-for="(item, index) in menus" :key="index">
            <div
              :class="[
                { 'menu-item': !isLoginPage },
                { 'menu-item-ln': isLoginPage },
                { active: index === activeMenuIndex },
              ]"
              :style="{ visibility: isShowItem(item) ? 'visible' : 'hidden' }"
              @click="handleSelect(item, index)"
            >
              <el-badge :is-dot="true" :hidden="index !== 6">
                <span class="label"> {{ item.menuName }} </span>
              </el-badge>
            </div>
          </template>
          <el-input
            style="margin: 0px 50px;"
            placeholder="请输入搜索关键词"
            v-model="searchKey"
            >
            <template #suffix>
              <img class="floating-img" @click="selOpenCourse(searchKey)" :src="openClassListSelect"/>
            </template>
          </el-input>
        </div>
        <div class="nav-right-name">
          <template v-if="getToken()">
            <div class="avatar-container">
              <el-dropdown @command="handleCommand" trigger="click">
                <div style="display: flex; align-items: center">
                  <img
                    class="avg"
                    :src="userStore.avatar ? userStore.avatar : profile"
                    alt="Logo"
                    referrerpolicy="referrer"
                  />
                  <span class="name">{{ userName }}</span>
                  <el-icon
                    :style="{ color: '#000' }"
                  >
                    <caret-bottom />
                  </el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="routerLink"
                      >个人中心</el-dropdown-item
                    >
                    <el-dropdown-item divided command="logout">
                      <span>退出登录</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
          <template v-else>
            <span
              class="span_login"
              :class="{ 'ln-sty': isLoginPage }"
              @click="toLogin"
            >
              <span>登录/注册</span>
            </span>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="headNavComp">
import useUserStore from "@/store/modules/user.js";
import useSettingsStore from "@/store/modules/settings.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { onMounted, onUpdated, ref, watch } from "vue";
import openClassListSelect from "@/assets/images/openCourse/open_class_list_select.png";
import { getToken } from "@/utils/auth";
import { useRouter } from "vue-router";
import profile from "@/assets/images/profile.png";
import { getSiteInfoById } from "@/api/basic/home.js";
import { SiteId } from "@/utils/constant";
const router = useRouter();
const userStore = useUserStore();
const menus = ref([
  {
    menuName: "公开课",
    menuRoute: "/digital-platforms",
    routeType: 1,
    showFlg: '1',
    sort: 1,
  },
  {
    menuName: "互动课堂",
    menuRoute: "/search",
    routeType: 1,
    showFlg: '1',
    sort: 2,
  },
  {
    menuName: "教师空间",
    menuRoute: "/teacher-space/teacher-resource",
    routeType: 1,
    showFlg: '1',
    sort: 3,
  },
  {
    menuName: "测试活动",
    menuRoute: "/search",
    routeType: 1,
    showFlg: '1',
    sort: 4,
  },
  {
    menuName: "课程设计",
    menuRoute: "/courseware-design/mycourseware_design",
    routeType: 1,
    showFlg: '1',
    sort: 5,
  },
]);
const siteInfo = ref(null);
let token = computed(() => getToken());
const userName = ref("");
const userInfo = ref({});
const searchKey = ref("");
if (getToken()) {
  userInfo.value = userStore.getInfo();
}
const settingsStore = useSettingsStore();
const { proxy } = getCurrentInstance();
const sideTheme = computed(() => settingsStore.sideTheme);
const activeMenuIndex = ref(-1);
const props = defineProps({
  showBackground: {
    type: Boolean,
    default: false,
  },
  isLoginPage: {
    type: Boolean,
    default: false,
  },
  showInteractiveClass: {
    type: Boolean,
    default: true
  }
});
const themeClass = computed(() => {
  return `theme-${props.theme}`;
});
const isShowItem = (item) => {
  let visibleByRole = item.showFlg === userInfo?.value.userType;

  if (item.menuName === "互动课堂") {
    return visibleByRole && props.showInteractiveClass;
  }
  return visibleByRole;
};
const handleSelect = (item, index) => {
  activeMenuIndex.value = index;
  // 内部链接
  if (item.routeType == 1) {
    if (item.menuName === "教学平台") {
      window.open(item.menuRoute, "_blank");
    }
    if (item.menuRoute || item.menuRoute === 0) {
      proxy.$router.push({ path: item.menuRoute });
    } else {
      proxy.$router.push({ path: "/user" });
    }
    // 外部链接
  } else {
    let url = item.menuRoute;
    if (url.indexOf("http") == -1) {
      url = "http://" + item.menuRoute;
    }
    window.open(url, "_blank");
  }
};
const toLogin = () => {
  proxy.$emit("toLogin");
};

const routerLink = () => {
  if (getToken()) {
    router.push({ path: `/basic-information` });
  }
};

function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      userStore.logOut().then(() => {
        location.href = "/index";
      });
    })
    .catch(() => {});
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

const emits = defineEmits(["setLayout"]);

function setLayout() {
  emits("setLayout");
}

function checkTokenNotExpired() {
  try {
    userStore
      .getInfo()
      .then((response) => {
        if (response.code !== 200) {
          ElMessage.error(response.msg);
          setTimeout(() => {
            userStore.logOut().then(() => {
              location.href = "/index";
            });
          }, 500);
        }
      })
      .catch(() => {
        // return false;
      });
  } catch (e) {
    // return false;
  }
}
function initUserInfo() {
  if (useUserStore().name) {
    userName.value = useUserStore().nickName;
  } else {
    userStore.getInfo().then((response) => {
      if (response.code === 200) {
        userName.value = response?.user?.nickName;
        userInfo.value = response?.user;
      } else {
        userName.value = "";
      }
    });
  }
}

function checkLoginTimeOut() {
  if (token.value) {
    checkTokenNotExpired();
  }
}
function selOpenCourse (searchKey) {
}
onMounted(async () => {
  initUserInfo();
  checkLoginTimeOut();
  await getSiteInfoById(SiteId).then((res) => {
    if (res.code === 200) {
    }
    siteInfo.value = res.data;
  });
  console.log("siteInfo.value ", siteInfo.value);
});
</script>

<style lang="scss" scoped>
@import "@/assets/styles/index";

.head-nav-con {
  width: 100%;
  height: 68px;
  padding-top: 10px;
  padding-bottom: 10px;
  margin-top: 10px;
  margin-bottom: 10px;
  @extend .base-flex;
  justify-content: center;
  align-items: center;

  box-shadow: 0 4px 8px rgba(68, 68, 68, 0.3);

  .head-nav {
    width: 1400px;
    height: 48px;
    padding: 0 10px;
    @extend .base-flex;
    justify-content: space-between;
    align-items: center;

    .head-nav-con-left {
      .logo {
        width: 175px;
        height: 54px;
      }
    }

    .head-nav-con-right {
      @extend .base-flex;
      justify-content: space-between;
      align-items: center;

      .nav-right-menu {
        height: 48px;
        @extend .base-flex;
        justify-content: space-between;
        align-items: center;

        .menu-item {
          min-width: 120px;
          height: 30px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #ffffff;
          text-align: justify;
          font-style: normal;
          cursor: pointer;
          @extend .base-flex;
          justify-content: center;
          align-items: center;
        }

        .menu-item:hover {
          opacity: 0.7;
        }

        .menu-item-ln {
          height: 30px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #333333;
          text-align: justify;
          font-style: normal;
          padding: 0 12px;
          cursor: pointer;
          @extend .base-flex;
          justify-content: center;
          align-items: center;
        }

        .menu-item-ln:hover {
          opacity: 0.7;
        }
        .active {
          position: relative;
          color: #00DDFF;
          &::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 40%;
            width: 20%;
            height: 2px;
            background: linear-gradient(135deg, #00DDFF 0%, #BDF6FF 31%, #00DDFF 71%, #BDF6FF 100%);
          }
        }
      }

      .nav-right-name {
        @extend .base-flex;
        justify-content: space-between;
        align-items: center;

        .avg {
          width: 25px;
          height: 25px;
          border-radius: 50%;
          margin-right: 8px;
        }

        .name {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #ffffff;
          font-style: normal;
        }
      }
    }
  }
}

.blue-bg {
  background: #0966b4;
}

.span_login {
  height: 17px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
  font-style: normal;
  line-height: 17px;
  cursor: pointer;
  @extend .base-flex;
  justify-content: center;
  align-items: center;
}

.span_login:hover {
  opacity: 0.7;
}

.ln-sty {
  color: #0966b4;
}
:deep(.el-input__wrapper) {
    width: 238px;
    height: 40px;
    background: rgba(255,255,255,0.28);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #FFFFFF;
}
:deep(.el-input__suffix) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
// 修改 placeholder 颜色
:deep(.el-input__inner::placeholder) {
  color: #ffffff;
}
.floating-img {
  cursor: pointer;
}

.theme-dark {
  .menu-item,
  .menu-item .label,
  .menu-item-ln,
  .menu-item-ln .label,
  .nav-right-name .name,
  .span_login,
  .span_login span {
    color: #000000 !important;
  }

  .el-icon {
    color: #000000 !important;
  }

  .floating-img {
    filter: brightness(0) !important;
  }

  .active {
    position: relative;
    color: #00A4C6 !important;
    &::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 40%;
      width: 20%;
      height: 2px;
      background: linear-gradient(135deg, #00A4C6 0%, #AEEAF3 31%, #00A4C6 71%, #AEEAF3 100%) !important;
    }
  }

  :deep(.el-input__inner::placeholder) {
    color: #555555 !important;
  }
   :deep(.el-input__wrapper) {
    border: 1px solid #555555 !important;
  }
}
</style>
