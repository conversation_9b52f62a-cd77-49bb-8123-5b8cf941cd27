export function isHtmlTag(questionContent) {
  const match = questionContent?.match(/###(.*?)###/)
  if (match) {
    let contentBetweenHashes = match[1] // 获取 ### 和 ### 之间的内容
    console.log('内容:', contentBetweenHashes)
    
    // 判断内容是否为 HTML 标签
    const isHtmlTag = 
      /<([a-zA-Z][a-zA-Z0-9]*)\b[^>]*>.*?<\/\1>/.test(contentBetweenHashes) || // 完整标签
      /<([a-zA-Z][a-zA-Z0-9]*)\b[^>]*\/?>/.test(contentBetweenHashes) // 自闭合标签
    
    console.log('是否为 HTML 标签:', isHtmlTag)
    
    return isHtmlTag;
  } else {
    return true
  }
}




