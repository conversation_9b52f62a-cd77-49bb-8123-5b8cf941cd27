import { Client } from '@stomp/stompjs'
import { getToken } from '@/utils/auth';

let stompClient = null
let reconnectTimer = null

export function useStompWebSocket(onMessageCallback) {
  function connect() {
    if (stompClient && stompClient.active) return
    const token = getToken();
    if (!token) {
      console.warn('🔴 Token缺失，终止WebSocket连接')
      disconnect()
      return
    }
    const wsUrl = new URL('/ws', import.meta.env.VITE_APP_WS_EDU_BASE_API);
    stompClient = new Client({
      brokerURL: wsUrl.toString().replace(/^http/, 'ws'),
      connectHeaders: {
          Authorization: `${token}`
      },
      reconnectDelay: 5000, // 自动重连时间（仅在某些断线生效）
      debug: (str) => console.log('📡 Debug:', str),
      onConnect: () => {
        console.log('✅ STOMP 连接成功')

        // 清除任何旧的 reconnect 计时器
        if (reconnectTimer) {
          clearTimeout(reconnectTimer)
          reconnectTimer = null
        }

        stompClient.subscribe('/topic/activity', (msg) => {
          console.log('📩 收到消息:', msg.body)
          if (onMessageCallback) {
            onMessageCallback(JSON.parse(msg.body))
          }
        })
      },
      onStompError: (frame) => {
        console.error('❌ STOMP 协议错误', frame)
        tryReconnect()
      },
      onWebSocketError: (event) => {
        console.error('❌ WebSocket 连接失败', event)
        tryReconnect()
      },
      onDisconnect: () => {
        console.log('🔌 STOMP 主动断开')
      }
    })

    stompClient.activate()
  }

  function tryReconnect() {
    if (reconnectTimer) return // 避免重复设置

    console.warn('⏳ 尝试5秒后重新连接 WebSocket...')
    reconnectTimer = setTimeout(() => {
      console.log('🔁 正在重连 WebSocket...')
      connect()
    }, 5000)
  }

  function disconnect() {
    if (stompClient && stompClient.active) {
      stompClient.deactivate()
      console.log('🔌 WebSocket 已断开')
    }
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
  }

  return {
    connect,
    disconnect
  }
}
