import request from '@/utils/request'

// 查询互动课堂的问卷调查问题列表
export function listSurveyQuestion(query) {
  return request({
    url: '/edu/surveyQuestion/list',
    method: 'get',
    params: query
  })
}

// 导出互动课堂的问卷调查问题列表
export function exportSurveyQuestion(query) {
  return request({
    url: '/edu/surveyQuestion/export',
    method: 'post',
    params: query
  })
}

// 获取互动课堂的问卷调查问题详细信息
export function getSurveyQuestion(questionId) {
  return request({
    url: '/edu/surveyQuestion/' + questionId,
    method: 'get'
  })
}

// 新增互动课堂的问卷调查问题
export function addSurveyQuestion(data) {
  return request({
    url: '/edu/surveyQuestion',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的问卷调查问题
export function updateSurveyQuestion(data) {
  return request({
    url: '/edu/surveyQuestion',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的问卷调查问题
export function delSurveyQuestion(questionIds) {
  return request({
    url: '/edu/surveyQuestion/' + questionIds,
    method: 'delete'
  })
}
