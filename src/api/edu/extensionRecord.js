import request from '@/utils/request'

// 查询互动课堂的拓展内容学生记录列表
export function listRecord(query) {
  return request({
    url: '/edu/extensionRecord/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂的拓展内容学生记录详细信息
export function getRecord(recordId) {
  return request({
    url: '/edu/extensionRecord/' + recordId,
    method: 'get'
  })
}

// 查询当前用户指定拓展的提交记录
export function getMyRecord(extensionId) {
  return request({
    url: `/edu/extensionRecord/record/${extensionId}`,
    method: 'get'
  })
}

// 新增互动课堂的拓展内容学生记录
export function addRecord(data) {
  return request({
    url: '/edu/extensionRecord',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的拓展内容学生记录
export function updateRecord(data) {
  return request({
    url: '/edu/extensionRecord',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的拓展内容学生记录
export function delRecord(recordIds) {
  return request({
    url: '/edu/extensionRecord/' + recordIds,
    method: 'delete'
  })
}

// 导出互动课堂的拓展内容学生记录
export function exportRecord(query) {
  return request({
    url: '/edu/extensionRecord/export',
    method: 'post',
    data: query
  })
}
