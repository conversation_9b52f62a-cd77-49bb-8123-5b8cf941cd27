import request from '@/utils/request'

// 查询互动课堂作业/考试管理列表
export function listHomeworkOrTest(query) {
  return request({
    url: '/edu/test/list',
    method: 'get',
    params: query
  })
}

// 导出互动课堂作业/考试管理列表
export function exportHomeworkOrTest(query) {
  return request({
    url: '/edu/test/export',
    method: 'post',
    params: query
  })
}

// 获取互动课堂作业/考试管理详细信息
export function getHomeworkOrTest(assignmentId) {
  return request({
    url: '/edu/test/' + assignmentId,
    method: 'get'
  })
}

// 获取互动课堂作业/考试管理及试卷详细信息
export function getHomeworkOrTestWithPaper(assignmentId) {
  return request({
    url: '/edu/test/paper/' + assignmentId,
    method: 'get'
  })
}

// 新增互动课堂作业/考试管理
export function addHomeworkOrTest(data) {
  return request({
    url: '/edu/test',
    method: 'post',
    data: data
  })
}

// 修改互动课堂作业/考试管理
export function updateHomeworkOrTest(data) {
  return request({
    url: '/edu/test',
    method: 'put',
    data: data
  })
}

// 删除互动课堂作业/考试管理
export function delHomeworkOrTest(assignmentIds) {
  return request({
    url: '/edu/test/' + assignmentIds,
    method: 'delete'
  })
}

// 查询当前用户的互动课堂作业/考试管理列表
export function getHomeworkOrTestListByUserId(query) {
  return request({
    url: '/edu/test/getHomeworkOrTestListByUserId',
    method: 'get',
    params: query
  })
}

// 开始/结束互动课堂作业/考试
export function startOrStopTest(data) {
  return request({
    url: '/edu/test/startOrStopTest',
    method: 'post',
    data: data
  })
}
