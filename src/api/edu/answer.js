import request from '@/utils/request'

// 查询互动课堂学生考试/作业结果列表
export function listAnswer(query) {
  return request({
    url: '/edu/answer/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂学生考试/作业结果详细
export function getAnswer(answerId) {
  return request({
    url: '/edu/answer/' + answerId,
    method: 'get'
  })
}

// 新增互动课堂学生考试/作业结果
export function addAnswer(data) {
  return request({
    url: '/edu/answer',
    method: 'post',
    data: data
  })
}

// 修改互动课堂学生考试/作业结果
export function updateAnswer(data) {
  return request({
    url: '/edu/answer',
    method: 'put',
    data: data
  })
}

// 删除互动课堂学生考试/作业结果
export function delAnswer(answerId) {
  return request({
    url: '/edu/answer/' + answerId,
    method: 'delete'
  })
}
// 作品秀备选
export function getUserAnswerSpare(assignmentIds) {
  return request({
    url: '/edu/answer/getUserAnswerSpare/' + assignmentIds,
    method: 'get'
  })
}