import request from '@/utils/request.js'
// 加入课程
export function addOpenCourseStudent(data) {
  return request({
    url: '/edu/openCourseStudent',
    method: 'post',
    data: data
  })
}
export function delOpenCourseStudent(studentIds) {
  return request({
    url: '/edu/openCourseStudent/' + studentIds,
    method: 'delete',
  });
}

// 查询课程学生信息列表
export function listOpenCourseStudent(query) {
  return request({
    url: '/edu/openCourseStudent/list',
    method: 'get',
    params: query,
  });
}
// 退出课程
export function exitTheCourse(data) {
  return request({
    url: '/edu/openCourseStudent/exitTheCourse',
    method: 'put',
    data: data
  })
}

// 导出公开课加入的学员列表
export function exportOpenCourseStudent(data) {
  return request({
    url: '/edu/openCourseStudent/export',
    method: 'post',
    data: data // 通常导出是POST请求，参数放在data中，如果后端要求特定格式（如form-data），可能需要调整
  })
}
// 获取公开课加入的学员详细信息
export function getOpenCourseStudentInfo(studentId) {
  return request({
    url: '/edu/openCourseStudent/' + studentId,
    method: 'get'
  })
}
// 修改公开课加入的学员
export function updateOpenCourseStudent(data) {
  return request({
    url: '/edu/openCourseStudent',
    method: 'put',
    data: data
  })
}
// 删除公开课加入的学员
export function removeOpenCourseStudent(studentIds) {
  return request({
    url: '/edu/openCourseStudent/' + studentIds,
    method: 'delete'
  })
}
// 查询我加入的公开课列表
export function getMyOpenCourseList(query) {
  return request({
    url: '/edu/openCourseStudent/myList',
    method: 'get',
    params: query
  })
}