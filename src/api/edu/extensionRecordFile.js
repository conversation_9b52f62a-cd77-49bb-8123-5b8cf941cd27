import request from '@/utils/request'

// 查询拓展内容提交文件列表
export function listExtensionRecordFile(query) {
  return request({
    url: '/edu/extensionRecordFile/list',
    method: 'get',
    params: query
  })
}

// 查询拓展内容提交文件详细
export function getExtensionRecordFile(fileId) {
  return request({
    url: '/edu/extensionRecordFile/' + fileId,
    method: 'get'
  })
}

// 新增拓展内容提交文件
export function addExtensionRecordFile(data) {
  return request({
    url: '/edu/extensionRecordFile',
    method: 'post',
    data: data
  })
}

// 修改拓展内容提交文件
export function updateExtensionRecordFile(data) {
  return request({
    url: '/edu/extensionRecordFile',
    method: 'put',
    data: data
  })
}

// 删除拓展内容提交文件
export function delExtensionRecordFile(fileId) {
  return request({
    url: '/edu/extensionRecordFile/' + fileId,
    method: 'delete'
  })
}
