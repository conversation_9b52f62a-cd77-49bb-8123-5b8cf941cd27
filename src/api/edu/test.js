import request from '@/utils/request'

// 查询互动课堂作业/考试管理列表
export function listTest(query) {
  return request({
    url: '/edu/test/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂作业/考试管理详细
export function getTest(assignmentId) {
  return request({
    url: '/edu/test/' + assignmentId,
    method: 'get'
  })
}

// 新增互动课堂作业/考试管理
export function addTest(data) {
  return request({
    url: '/edu/test',
    method: 'post',
    data: data
  })
}

// 修改互动课堂作业/考试管理
export function updateTest(data) {
  return request({
    url: '/edu/test',
    method: 'put',
    data: data
  })
}

// 删除互动课堂作业/考试管理
export function delTest(assignmentId) {
  return request({
    url: '/edu/test/' + assignmentId,
    method: 'delete'
  })
}
// 查询互动课堂作业/考试管理列表
export function getHomeworkOrTestList(query) {
  return request({
    url: '/edu/test/getHomeworkOrTestList',
    method: 'get',
    params: query
  })
}
// 查询互动课堂作业/考试管理列表
export function getHomeworkOrTestActivity(data) {
  return request({
    url: '/edu/test/getHomeworkOrTestActivity',
    method: 'post',
    data: data
  })
}