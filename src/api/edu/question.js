import request from '@/utils/request'

// 查询互动课堂班级提问列表
export function listQuestion(query) {
  return request({
    url: '/edu/question/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂班级提问详细
export function getQuestion(questionId) {
  return request({
    url: '/edu/question/' + questionId,
    method: 'get'
  })
}

// 新增互动课堂班级提问
export function addQuestion(data) {
  return request({
    url: '/edu/question',
    method: 'post',
    data: data
  })
}

// 修改互动课堂班级提问
export function updateQuestion(data) {
  return request({
    url: '/edu/question',
    method: 'put',
    data: data
  })
}

// 删除互动课堂班级提问
export function delQuestion(questionId) {
  return request({
    url: '/edu/question/' + questionId,
    method: 'delete'
  })
}
