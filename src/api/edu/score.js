import request from '@/utils/request'

// 查询互动课堂班级成员各项汇总成绩列表
export function listScore(query) {
  return request({
    url: '/edu/score/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂班级成员各项汇总成绩详细
export function getScore(memberScoreId) {
  return request({
    url: '/edu/score/' + memberScoreId,
    method: 'get'
  })
}

// 新增互动课堂班级成员各项汇总成绩
export function addScore(data) {
  return request({
    url: '/edu/score',
    method: 'post',
    data: data
  })
}

// 修改互动课堂班级成员各项汇总成绩
export function updateScore(data) {
  return request({
    url: '/edu/score',
    method: 'put',
    data: data
  })
}

// 删除互动课堂班级成员各项汇总成绩
export function delScore(memberScoreId) {
  return request({
    url: '/edu/score/' + memberScoreId,
    method: 'delete'
  })
}
