import request from '@/utils/request'

// 查询互动课堂的头脑风暴讨论成员列表
export function listReply(query) {
  return request({
    url: '/edu/reply/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂的头脑风暴讨论成员详细
export function getReply(replyId) {
  return request({
    url: '/edu/reply/' + replyId,
    method: 'get'
  })
}

// 新增互动课堂的头脑风暴讨论成员
export function addReply(data) {
  return request({
    url: '/edu/reply',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的头脑风暴讨论成员
export function updateReply(data) {
  return request({
    url: '/edu/reply',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的头脑风暴讨论成员
export function delReply(replyId) {
  return request({
    url: '/edu/reply/' + replyId,
    method: 'delete'
  })
}
