import request from '@/utils/request'

// 查询学生作业提交记录列表
export function listSubmission(query) {
  return request({
    url: '/edu/submission/list',
    method: 'get',
    params: query
  })
}

// 查询学生作业提交记录详细
export function getSubmission(submissionId) {
  return request({
    url: '/edu/submission/' + submissionId,
    method: 'get'
  })
}

// 新增学生作业提交记录
export function addSubmission(data) {
  return request({
    url: '/edu/submission',
    method: 'post',
    data: data
  })
}

// 修改学生作业提交记录
export function updateSubmission(data) {
  return request({
    url: '/edu/submission',
    method: 'put',
    data: data
  })
}

// 删除学生作业提交记录
export function delSubmission(submissionId) {
  return request({
    url: '/edu/submission/' + submissionId,
    method: 'delete'
  })
}

// 根据作业ID获取学生作业提交记录详细
export function getSubmissionByAssignmentId(assignmentId) {
  return request({
    url: '/edu/submission/getByAssignment/' + assignmentId,
    method: 'get'
  })
}
