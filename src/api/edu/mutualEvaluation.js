import request from '@/utils/request'

// 查询互动课堂互评列表
export function listMutualEvaluation(query) {
  return request({
    url: '/edu/mutualEvaluation/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂互评详细
export function getMutualEvaluation(mutualEvaluationId) {
  return request({
    url: '/edu/mutualEvaluation/' + mutualEvaluationId,
    method: 'get'
  })
}

// 新增互动课堂互评
export function addMutualEvaluation(data) {
  return request({
    url: '/edu/mutualEvaluation',
    method: 'post',
    data: data
  })
}

// 修改互动课堂互评
export function updateMutualEvaluation(data) {
  return request({
    url: '/edu/mutualEvaluation',
    method: 'put',
    data: data
  })
}

// 删除互动课堂互评
export function delMutualEvaluation(mutualEvaluationId) {
  return request({
    url: '/edu/mutualEvaluation/' + mutualEvaluationId,
    method: 'delete'
  })
}
