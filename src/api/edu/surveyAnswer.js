import request from '@/utils/request'

// 查询互动课堂的问卷调查回答列表
export function listAnswer(query) {
  return request({
    url: '/edu/surveyAnswer/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂的问卷调查回答详细
export function getAnswer(answerId) {
  return request({
    url: '/edu/surveyAnswer/' + answerId,
    method: 'get'
  })
}

// 新增互动课堂的问卷调查回答
export function addAnswer(data) {
  return request({
    url: '/edu/surveyAnswer',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的问卷调查回答
export function updateAnswer(data) {
  return request({
    url: '/edu/surveyAnswer',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的问卷调查回答
export function delAnswer(answerId) {
  return request({
    url: '/edu/surveyAnswer/' + answerId,
    method: 'delete'
  })
}
