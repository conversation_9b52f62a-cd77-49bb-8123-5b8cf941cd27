import request from '@/utils/request'

// 查询DUTP-DTB_010数字教材选择题选项列表
export function listOption(query) {
  return request({
    url: '/edu/moocQuestionOption/list',
    method: 'get',
    params: query
  })
}

// 查询DUTP-DTB_010数字教材选择题选项详细
export function getOption(optionId) {
  return request({
    url: '/edu/moocQuestionOption/' + optionId,
    method: 'get'
  })
}

// 新增DUTP-DTB_010数字教材选择题选项
export function addOption(data) {
  return request({
    url: '/edu/moocQuestionOption',
    method: 'post',
    data: data
  })
}

// 修改DUTP-DTB_010数字教材选择题选项
export function updateOption(data) {
  return request({
    url: '/edu/moocQuestionOption',
    method: 'put',
    data: data
  })
}

// 删除DUTP-DTB_010数字教材选择题选项
export function delOption(optionId) {
  return request({
    url: '/edu/moocQuestionOption/' + optionId,
    method: 'delete'
  })
}
