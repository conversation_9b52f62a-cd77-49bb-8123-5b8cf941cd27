import request from '@/utils/request'

// 查询互动课堂公告列表
export function listNotice(query) {
  return request({
    url: '/edu/notice/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂公告详细
export function getNotice(noticeId) {
  return request({
    url: '/edu/notice/' + noticeId,
    method: 'get'
  })
}

// 新增互动课堂公告
export function addNotice(data) {
  return request({
    url: '/edu/notice',
    method: 'post',
    data: data
  })
}

// 修改互动课堂公告
export function updateNotice(data) {
  return request({
    url: '/edu/notice',
    method: 'put',
    data: data
  })
}

// 删除互动课堂公告
export function delNotice(noticeId) {
  return request({
    url: '/edu/notice/' + noticeId,
    method: 'delete'
  })
}
