import request from '@/utils/request'

// 查询互动课堂的头脑风暴列表
export function listBrainstorm(query) {
  return request({
    url: '/edu/brainstorm/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂的头脑风暴详细
export function getBrainstorm(brainstormId) {
  return request({
    url: '/edu/brainstorm/' + brainstormId,
    method: 'get'
  })
}

// 新增互动课堂的头脑风暴
export function addBrainstorm(data) {
  return request({
    url: '/edu/brainstorm',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的头脑风暴
export function updateBrainstorm(data) {
  return request({
    url: '/edu/brainstorm',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的头脑风暴
export function delBrainstorm(brainstormId) {
  return request({
    url: '/edu/brainstorm/' + brainstormId,
    method: 'delete'
  })
}
