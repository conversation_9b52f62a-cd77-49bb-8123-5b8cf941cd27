import request from '@/utils/request'

// 查询互动课堂的课上互动讨论收藏/解答/点赞记录列表
export function listInteraction(query) {
  return request({
    url: '/edu/interaction/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂的课上互动讨论收藏/解答/点赞记录详细
export function getInteraction(interactionId) {
  return request({
    url: '/edu/interaction/' + interactionId,
    method: 'get'
  })
}

// 新增互动课堂的课上互动讨论收藏/解答/点赞记录
export function addInteraction(data) {
  return request({
    url: '/edu/interaction',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的课上互动讨论收藏/解答/点赞记录
export function updateInteraction(data) {
  return request({
    url: '/edu/interaction',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的课上互动讨论收藏/解答/点赞记录
export function delInteraction(interactionId) {
  return request({
    url: '/edu/interaction/' + interactionId,
    method: 'delete'
  })
}
