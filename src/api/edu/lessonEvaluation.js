import request from '@/utils/request'

// 查询课程评价列表
export function listLessonEvaluation(query) {
  return request({
    url: '/edu/lessonEvaluation/list',
    method: 'get',
    params: query
  })
}

// 查询课程评价详细
export function getLessonEvaluation(evaluationId) {
  return request({
    url: '/edu/lessonEvaluation/' + evaluationId,
    method: 'get'
  })
}

// 新增课程评价
export function addLessonEvaluation(data) {
  return request({
    url: '/edu/lessonEvaluation',
    method: 'post',
    data: data
  })
}

// 修改课程评价
export function updateLessonEvaluation(data) {
  return request({
    url: '/edu/lessonEvaluation',
    method: 'put',
    data: data
  })
}

// 删除课程评价
export function delLessonEvaluation(evaluationId) {
  return request({
    url: '/edu/lessonEvaluation/' + evaluationId,
    method: 'delete'
  })
}

// 查询我的课程评价
export function getMyLessonEvaluation(query) {
  return request({
    url: '/edu/lessonEvaluation/getMine',
    method: 'get',
    params: query
  })
}
