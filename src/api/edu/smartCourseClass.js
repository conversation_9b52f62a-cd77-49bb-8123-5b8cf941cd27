import request from '@/utils/request'

// 查询班级列表（无分页）- 根据教师ID和课程ID
export function getListByTeacherIdAndCourseIdNoPage(query) {
    return request({
        url: '/edu/class/getListByTeacherIdAndCourseIdNoPage',
        method: 'get',
        params: query
    })
}

// 查询班级列表
export function listClass(query) {
    return request({
        url: '/edu/class/list',
        method: 'get',
        params: query
    })
}

// 查询班级详细
export function getClass(classId) {
    return request({
        url: '/edu/class/' + classId,
        method: 'get'
    })
}

// 新增班级
export function addClass(data) {
    return request({
        url: '/edu/class',
        method: 'post',
        data: data
    })
}

// 修改班级
export function updateClass(data) {
    return request({
        url: '/edu/class',
        method: 'put',
        data: data
    })
}

// 删除班级
export function delClass(classId) {
    return request({
        url: '/edu/class/' + classId,
        method: 'delete'
    })
}

// 创建互动课堂班级并批量添加教务班级成员
export function addClassWithMembers(data) {
    return request({
        url: '/edu/class/addWithMembers',
        method: 'post',
        data: data
    })
} 