import request from '@/utils/request'

// 查询互动课堂统计数据快照列表
export function listStatic(query) {
  return request({
    url: '/edu/static/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂统计数据快照详细
export function getStatic(staticId) {
  return request({
    url: '/edu/static/' + staticId,
    method: 'get'
  })
}

// 新增互动课堂统计数据快照
export function addStatic(data) {
  return request({
    url: '/edu/static',
    method: 'post',
    data: data
  })
}

// 修改互动课堂统计数据快照
export function updateStatic(data) {
  return request({
    url: '/edu/static',
    method: 'put',
    data: data
  })
}

// 删除互动课堂统计数据快照
export function delStatic(staticId) {
  return request({
    url: '/edu/static/' + staticId,
    method: 'delete'
  })
}
