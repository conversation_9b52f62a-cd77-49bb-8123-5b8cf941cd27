import request from '@/utils/request'

// 查询互动课堂班级回答人员列表
export function listList(query) {
  return request({
    url: '/edu/list/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂班级回答人员详细
export function getList(studentId) {
  return request({
    url: '/edu/list/' + studentId,
    method: 'get'
  })
}

// 新增互动课堂班级回答人员
export function addList(data) {
  return request({
    url: '/edu/list',
    method: 'post',
    data: data
  })
}

// 修改互动课堂班级回答人员
export function updateList(data) {
  return request({
    url: '/edu/list',
    method: 'put',
    data: data
  })
}

// 删除互动课堂班级回答人员
export function delList(studentId) {
  return request({
    url: '/edu/list/' + studentId,
    method: 'delete'
  })
}
