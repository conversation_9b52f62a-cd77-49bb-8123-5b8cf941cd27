import request from '@/utils/request'

// 查询互动课堂班级成员列表
export function listMember(query) {
  return request({
    url: '/edu/smartCourseMember/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂班级成员详细
export function getMember(classMemberId) {
  return request({
    url: '/edu/smartCourseMember/' + classMemberId,
    method: 'get'
  })
}

// 新增互动课堂班级成员
export function addMember(data) {
  return request({
    url: '/edu/smartCourseMember',
    method: 'post',
    data: data
  })
}

// 修改互动课堂班级成员
export function updateMember(data) {
  return request({
    url: '/edu/smartCourseMember',
    method: 'put',
    data: data
  })
}

// 删除互动课堂班级成员
export function delMember(classMemberId) {
  return request({
    url: '/edu/smartCourseMember/' + classMemberId,
    method: 'delete'
  })
}

// 查询互动课堂班级成员详细
export function getLearnInformationStatistics() {
  return request({
    url: '/edu/smartCourseMember/getLearnInformationStatistics',
    method: 'get'
  })
}

// 新增互动课堂班级成员
export function getLearnClass(query) {
  return request({
    url: '/edu/smartCourseMember/getLearnClass',
    method: 'get',
    params: query
  })
}

// 查询互动课堂班级成员列表
export function listForClass(query) {
  return request({
    url: '/edu/smartCourseMember/listForClass',
    method: 'get',
    params: query
  })
}


// 批量新增互动课堂班级成员
export function batchAddMember(data) {
  return request({
    url: '/edu/smartCourseMember/batchAdd',
    method: 'post',
    data: data
  })
}

// 查询互动课堂班级成员助教列表
export function listTeachingAssistant(query) {
  return request({
    url: '/edu/smartCourseMember/listTeachingAssistant/' ,
    method: 'get',
    params: query
  })
}