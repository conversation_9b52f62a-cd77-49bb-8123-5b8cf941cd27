import request from '@/utils/request'


// 查询教师个人资源文件夹列表
export function listMoocResourceFolder(query) {
  return request({
    url: '/edu/moocResourceFolder/list',
    method: 'get',
    params: query
  })
}

// 查询教师个人资源文件夹列表
export function resourceList(query) {
  return request({
    url: '/edu/moocResourceFolder/resourceList',
    method: 'get',
    params: query
  })
}

// 查询所有教师个人资源文件夹列表
export function listMoocResourceFolderAll(query) {
    return request({
      url: '/edu/moocResourceFolder/listAll',
      method: 'get',
      params: query
    })
  }

// 查询教师个人资源文件夹详细
export function getMoocResourceFolder(userFolderId) {
  return request({
    url: '/edu/moocResourceFolder/' + userFolderId,
    method: 'get'
  })
}

// 新增教师个人资源文件夹
export function addMoocResourceFolder(data) {
  return request({
    url: '/edu/moocResourceFolder',
    method: 'post',
    data: data
  })
}

// 修改教师个人资源文件夹
export function updateMoocResourceFolder(data) {
  return request({
    url: '/edu/moocResourceFolder',
    method: 'put',
    data: data
  })
}

// 删除教师个人资源文件夹
export function delMoocResourceFolder(userFolderId) {
  return request({
    url: '/edu/moocResourceFolder/' + userFolderId,
    method: 'delete'
  })
}
// 查询用户资源库列表
export function listMoocResource(query) {
  return request({
    url: '/edu/moocResourceFolder/resourceList',
    method: 'get',
    params: query
  })
}
