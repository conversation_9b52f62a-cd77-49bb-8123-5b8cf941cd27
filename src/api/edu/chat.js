import request from '@/utils/request'

// 查询互动课堂的问卷调查列表
export function listChat(query) {
  return request({
    url: '/edu/chat/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂的问卷调查详细
export function getChat(chatId) {
  return request({
    url: '/edu/chat/' + chatId,
    method: 'get'
  })
}

// 新增互动课堂的问卷调查
export function addChat(data) {
  return request({
    url: '/edu/chat',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的问卷调查
export function updateChat(data) {
  return request({
    url: '/edu/chat',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的问卷调查
export function delChat(chatId) {
  return request({
    url: '/edu/chat/' + chatId,
    method: 'delete'
  })
}
