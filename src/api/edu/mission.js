import request from '@/utils/request'

// 查询互动课堂的教材学习任务列表
export function listMission(query) {
  return request({
    url: '/edu/mission/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂的教材学习任务详细
export function getMission(missionId) {
  return request({
    url: '/edu/mission/' + missionId,
    method: 'get'
  })
}

// 新增互动课堂的教材学习任务
export function addMission(data) {
  return request({
    url: '/edu/mission',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的教材学习任务
export function updateMission(data) {
  return request({
    url: '/edu/mission',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的教材学习任务
export function delMission(missionId) {
  return request({
    url: '/edu/mission/' + missionId,
    method: 'delete'
  })
}
