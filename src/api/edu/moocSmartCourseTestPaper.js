import request from '@/utils/request'

// 查询互动课堂试卷列表
export function listPaper(query) {
  return request({
    url: '/edu/paper/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂试卷详细
export function getPaper(paperId) {
  return request({
    url: '/edu/paper/' + paperId,
    method: 'get'
  })
}

// 新增互动课堂试卷
export function addPaper(data) {
  return request({
    url: '/edu/paper',
    method: 'post',
    data: data
  })
}

// 修改互动课堂试卷
export function updatePaper(data) {
  return request({
    url: '/edu/paper',
    method: 'put',
    data: data
  })
}

// 删除互动课堂试卷
export function delPaper(paperIds) {
  return request({
    url: '/edu/paper/' + paperIds,
    method: 'delete'
  })
}

// 导出互动课堂试卷
export function exportPaper(query) {
  return request({
    url: '/edu/paper/export',
    method: 'post',
    params: query
  })
}

export function getPaperDetails(paperId) {
    return request({
      url: '/edu/paper/details/' + paperId,
      method: 'get'
    })
  }

// 查询当前用户下的试卷列表
export function getTestListNoPage(query) {
  return request({
    url: '/edu/paper/getListByUserIdNoPage',
    method: 'get',
    params: query
  })
}
