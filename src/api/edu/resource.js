import request from '@/utils/request'

// 查询互动课堂课程资源发送记录列表
export function listResource(query) {
  return request({
    url: '/edu/resource/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂课程资源发送记录详细
export function getResource(sentId) {
  return request({
    url: '/edu/resource/' + sentId,
    method: 'get'
  })
}

// 新增互动课堂课程资源发送记录
export function addResource(data) {
  return request({
    url: '/edu/resource',
    method: 'post',
    data: data
  })
}

// 修改互动课堂课程资源发送记录
export function updateResource(data) {
  return request({
    url: '/edu/resource',
    method: 'put',
    data: data
  })
}

// 删除互动课堂课程资源发送记录
export function delResource(sentId) {
  return request({
    url: '/edu/resource/' + sentId,
    method: 'delete'
  })
}
