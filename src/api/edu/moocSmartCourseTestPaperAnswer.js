import request from '@/utils/request'

// 查询互动课堂学生考试/作业结果列表
export function listAnswer(query) {
  return request({
    url: '/edu/answer/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂学生考试/作业结果详细信息
export function getAnswer(answerId) {
  return request({
    url: '/edu/answer/' + answerId,
    method: 'get'
  })
}

// 新增互动课堂学生考试/作业结果
export function addAnswer(data) {
  return request({
    url: '/edu/answer',
    method: 'post',
    data: data
  })
}

// 修改互动课堂学生考试/作业结果
export function updateAnswer(data) {
  return request({
    url: '/edu/answer',
    method: 'put',
    data: data
  })
}

// 删除互动课堂学生考试/作业结果
export function delAnswer(answerIds) {
  return request({
    url: '/edu/answer/' + answerIds,
    method: 'delete'
  })
}

// 导出互动课堂学生考试/作业结果
export function exportAnswer(query) {
  return request({
    url: '/edu/answer/export',
    method: 'post',
    params: query
  })
}

/**
 * 根据试卷ID查询当前用户的答题结果
 */
export function getUserAnswer(testPaperId) {
  return request({
    url: '/edu/answer/getUserAnswer/' + testPaperId,
    method: 'get'
  })
}
