import request from '@/utils/request'

// 查询问卷调查答题明细（按题目存储）列表
export function listUrveyAnswerItem(query) {
  return request({
    url: '/edu/urveyAnswerItem/list',
    method: 'get',
    params: query
  })
}

// 查询问卷调查答题明细（按题目存储）详细
export function getUrveyAnswerItem(answerItemId) {
  return request({
    url: '/edu/urveyAnswerItem/' + answerItemId,
    method: 'get'
  })
}

// 新增问卷调查答题明细（按题目存储）
export function addUrveyAnswerItem(data) {
  return request({
    url: '/edu/urveyAnswerItem',
    method: 'post',
    data: data
  })
}

// 修改问卷调查答题明细（按题目存储）
export function updateUrveyAnswerItem(data) {
  return request({
    url: '/edu/urveyAnswerItem',
    method: 'put',
    data: data
  })
}

// 删除问卷调查答题明细（按题目存储）
export function delUrveyAnswerItem(answerItemId) {
  return request({
    url: '/edu/urveyAnswerItem/' + answerItemId,
    method: 'delete'
  })
}
