import request from '@/utils/request'

// 查询互动课堂班级学生签到记录列表
export function listRecord(query) {
  return request({
    url: '/edu/record/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂班级学生签到记录详细
export function getRecord(attendanceRecordId) {
  return request({
    url: '/edu/record/' + attendanceRecordId,
    method: 'get'
  })
}

// 新增互动课堂班级学生签到记录
export function addRecord(data) {
  return request({
    url: '/edu/record',
    method: 'post',
    data: data
  })
}

// 修改互动课堂班级学生签到记录
export function updateRecord(data) {
  return request({
    url: '/edu/record',
    method: 'put',
    data: data
  })
}

// 删除互动课堂班级学生签到记录
export function delRecord(attendanceRecordId) {
  return request({
    url: '/edu/record/' + attendanceRecordId,
    method: 'delete'
  })
}
