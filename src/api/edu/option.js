import request from '@/utils/request'

// 查询互动课堂的问卷调查选项列表
export function listOption(query) {
  return request({
    url: '/edu/option/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂的问卷调查选项详细
export function getOption(optionId) {
  return request({
    url: '/edu/option/' + optionId,
    method: 'get'
  })
}

// 新增互动课堂的问卷调查选项
export function addOption(data) {
  return request({
    url: '/edu/option',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的问卷调查选项
export function updateOption(data) {
  return request({
    url: '/edu/option',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的问卷调查选项
export function delOption(optionId) {
  return request({
    url: '/edu/option/' + optionId,
    method: 'delete'
  })
}
