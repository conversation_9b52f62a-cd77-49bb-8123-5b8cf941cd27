import request from '@/utils/request'

// 查询用户书籍思维导图数据列表
export function listMindmap(query) {
  return request({
    url: '/edu/mindmap/list',
    method: 'get',
    params: query
  })
}

// 查询用户书籍思维导图数据详细
export function getMindmap(mindmapId) {
  return request({
    url: '/edu/mindmap/' + mindmapId,
    method: 'get'
  })
}

// 根据书籍ID获取用户思维导图数据
export function getMindmapByBookId(bookId) {
  return request({
    url: '/edu/mindmap/book/' + bookId,
    method: 'get'
  })
}

// 新增用户书籍思维导图数据
export function addMindmap(data) {
  return request({
    url: '/edu/mindmap',
    method: 'post',
    data: data
  })
}

// 修改用户书籍思维导图数据
export function updateMindmap(data) {
  return request({
    url: '/edu/mindmap',
    method: 'put',
    data: data
  })
}

// 删除用户书籍思维导图数据
export function delMindmap(mindmapId) {
  return request({
    url: '/edu/mindmap/' + mindmapId,
    method: 'delete'
  })
}
