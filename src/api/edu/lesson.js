import request from '@/utils/request'

// 查询互动课堂的单次课堂列表
export function listLesson(query) {
  return request({
    url: '/edu/lesson/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂的单次课堂详细
export function getLesson(lessonId) {
  return request({
    url: '/edu/lesson/' + lessonId,
    method: 'get'
  })
}

// 新增互动课堂的单次课堂
export function addLesson(data) {
  return request({
    url: '/edu/lesson',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的单次课堂
export function updateLesson(data) {
  return request({
    url: '/edu/lesson',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的单次课堂
export function delLesson(lessonId) {
  return request({
    url: '/edu/lesson/' + lessonId,
    method: 'delete'
  })
}
