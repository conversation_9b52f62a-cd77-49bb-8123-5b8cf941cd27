import request from '@/utils/request'

// 查询学生出勤日志列表
export function listLog(query) {
  return request({
    url: '/edu/log/list',
    method: 'get',
    params: query
  })
}

// 查询学生出勤日志详细
export function getLog(logId) {
  return request({
    url: '/edu/log/' + logId,
    method: 'get'
  })
}

// 新增学生出勤日志
export function addLog(data) {
  return request({
    url: '/edu/log',
    method: 'post',
    data: data
  })
}

// 修改学生出勤日志
export function updateLog(data) {
  return request({
    url: '/edu/log',
    method: 'put',
    data: data
  })
}

// 删除学生出勤日志
export function delLog(logId) {
  return request({
    url: '/edu/log/' + logId,
    method: 'delete'
  })
}
