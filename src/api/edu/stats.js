import request from '@/utils/request'

// 查询班级月度综合统计 (出勤与学习进度)列表
export function listStats(query) {
  return request({
    url: '/edu/stats/list',
    method: 'get',
    params: query
  })
}

// 查询班级月度综合统计 (出勤与学习进度)详细
export function getStats(statId) {
  return request({
    url: '/edu/stats/' + statId,
    method: 'get'
  })
}

// 新增班级月度综合统计 (出勤与学习进度)
export function addStats(data) {
  return request({
    url: '/edu/stats',
    method: 'post',
    data: data
  })
}

// 修改班级月度综合统计 (出勤与学习进度)
export function updateStats(data) {
  return request({
    url: '/edu/stats',
    method: 'put',
    data: data
  })
}

// 删除班级月度综合统计 (出勤与学习进度)
export function delStats(statId) {
  return request({
    url: '/edu/stats/' + statId,
    method: 'delete'
  })
}
