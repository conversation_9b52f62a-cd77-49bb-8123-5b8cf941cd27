import request from '@/utils/request'

// 查询教务班级成员列表
export function listEduMember(query) {
  return request({
    url: '/edu/eduMember/list',
    method: 'get',
    params: query
  })
}

// 查询教务班级成员详细
export function getEduMember(eduClassMemberId) {
  return request({
    url: '/edu/eduMember/' + eduClassMemberId,
    method: 'get'
  })
}

// 新增教务班级成员
export function addEduMember(data) {
  return request({
    url: '/edu/eduMember',
    method: 'post',
    data: data
  })
}

// 修改教务班级成员
export function updateEduMember(data) {
  return request({
    url: '/edu/eduMember',
    method: 'put',
    data: data
  })
}

// 删除教务班级成员
export function delEduMember(eduClassMemberId) {
  return request({
    url: '/edu/eduMember/' + eduClassMemberId,
    method: 'delete'
  })
}
