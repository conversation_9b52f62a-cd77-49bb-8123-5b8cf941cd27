import request from '@/utils/request'

// 查询互动课堂的课上学习小组列表
export function listGroup(query) {
  return request({
    url: '/edu/group/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂的课上学习小组详细
export function getGroup(groupId) {
  return request({
    url: '/edu/group/' + groupId,
    method: 'get'
  })
}

// 新增互动课堂的课上学习小组
export function addGroup(data) {
  return request({
    url: '/edu/group',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的课上学习小组
export function updateGroup(data) {
  return request({
    url: '/edu/group',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的课上学习小组
export function delGroup(groupId) {
  return request({
    url: '/edu/group/' + groupId,
    method: 'delete'
  })
}
