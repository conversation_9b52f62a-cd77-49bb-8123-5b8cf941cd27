import request from '@/utils/request'

// 查询互动课堂班级签到活动列表
export function listAttendance(query) {
  return request({
    url: '/edu/attendance/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂班级签到活动详细
export function getAttendance(attendanceActivityId) {
  return request({
    url: '/edu/attendance/' + attendanceActivityId,
    method: 'get'
  })
}

// 新增互动课堂班级签到活动
export function addAttendance(data) {
  return request({
    url: '/edu/attendance',
    method: 'post',
    data: data
  })
}

// 修改互动课堂班级签到活动
export function updateAttendance(data) {
  return request({
    url: '/edu/attendance',
    method: 'put',
    data: data
  })
}

// 删除互动课堂班级签到活动
export function delAttendance(attendanceActivityId) {
  return request({
    url: '/edu/attendance/' + attendanceActivityId,
    method: 'delete'
  })
}
