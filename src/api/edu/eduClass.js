import request from '@/utils/request'

// 查询教务班级列表
export function listEduClass(query) {
  return request({
    url: '/edu/eduClass/list',
    method: 'get',
    params: query
  })
}

// 查询教务班级详细
export function getEduClass(eduClassId) {
  return request({
    url: '/edu/eduClass/' + eduClassId,
    method: 'get'
  })
}

// 新增教务班级
export function addEduClass(data) {
  return request({
    url: '/edu/eduClass',
    method: 'post',
    data: data
  })
}

// 修改教务班级
export function updateEduClass(data) {
  return request({
    url: '/edu/eduClass',
    method: 'put',
    data: data
  })
}

// 删除教务班级
export function delEduClass(eduClassId) {
  return request({
    url: '/edu/eduClass/' + eduClassId,
    method: 'delete'
  })
}
