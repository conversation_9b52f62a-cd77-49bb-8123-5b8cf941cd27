import request from '@/utils/request.js'
// 新增课程团队成员
export function addCoursewareDesign(data) {
  return request({
    url: '/edu/coursewareDesign',
    method: 'post',
    data: data
  })
}
// 删除课程团队成员
export function delCoursewareDesign(studentIds) {
  return request({
    url: '/edu/coursewareDesign/' + studentIds,
    method: 'delete',
  });
}

// 查询课程团队成员
export function listCoursewareDesign(query) {
  return request({
    url: '/edu/coursewareDesign/listNoPage',
    method: 'get',
    params: query,
  });
}
// 修改课程团队成员
export function exitCoursewareDesign(data) {
  return request({
    url: '/edu/coursewareDesign',
    method: 'put',
    data: data
  })
}
// 查询课课件资源
export function getCoursewareDesignResources(coursewareDesignId) {
  return request({
    url: '/edu/coursewareDesign/getcoursewareDesign/' + coursewareDesignId,
    method: 'get'
  })
}