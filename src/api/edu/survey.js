import request from '@/utils/request'

// 查询互动课堂的问卷调查列表
export function listSurvey(query) {
  return request({
    url: '/edu/survey/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂的问卷调查详细
export function getSurvey(surveyId) {
  return request({
    url: '/edu/survey/' + surveyId,
    method: 'get'
  })
}

// 新增互动课堂的问卷调查
export function addSurvey(data) {
  return request({
    url: '/edu/survey',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的问卷调查
export function updateSurvey(data) {
  return request({
    url: '/edu/survey',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的问卷调查
export function delSurvey(surveyId) {
  return request({
    url: '/edu/survey/' + surveyId,
    method: 'delete'
  })
}

// 获取问卷详情，包括问题、选项和用户答案
export function getSurveyDetails(surveyId) {
  return request({
    url: '/edu/survey/details/' + surveyId,
    method: 'get'
  })
}

export function getSurveyTeachDetails(surveyId,userId) {
  return request({
    url: '/edu/survey/teachDetails/' + surveyId + '/' + userId,
    method: 'get'
  })
}

// 查询互动课堂的问卷调查列表
export function getCourseSurvey(query) {
  return request({
    url: '/edu/survey/getCourseSurvey',
    method: 'get',
    params: query
  })
}

export function getStudentAnswerList(surveyId) {
  return request({
    url: '/edu/survey/getStudentAnswerList/' + surveyId,
    method: 'get'
  })
}

export function getTeachDetailsStatistics(surveyId) {
  return request({
    url: '/edu/survey/teachDetailsStatistics/' + surveyId,
    method: 'get',
  })
}

export function getStudentAnswerInfo(surveyId,answerId) {
  return request({
    url: '/edu/survey/getStudentAnswerInfo/' + surveyId + '/' + answerId,
    method: 'get'
  })
}

// 获取问卷详情，包括问题、选项和用户答案
export function getCourseSurveyInfo(surveyId) {
  return request({
    url: '/edu/survey/getCourseSurveyInfo/' + surveyId,
    method: 'get'
  })
}