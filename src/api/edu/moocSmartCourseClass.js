import request from '@/utils/request'

// 查询互动课堂班级列表（无分页）
export function getListByTeacherIdAndCourseIdNoPage(query) {
    return request({
        url: '/edu/class/getListByTeacherIdAndCourseIdNoPage',
        method: 'get',
        params: query
    })
}

    // 查询互动课堂班级列表
    export function listClass(query) {
        return request({
            url: '/edu/class/list',
            method: 'get',
            params: query
        })
    }

// 查询互动课堂班级详细
    export function getClass(classId) {
        return request({
            url: '/edu/class/' + classId,
            method: 'get'
        })
    }

// 新增互动课堂班级
    export function addClass(data) {
        return request({
            url: '/edu/class',
            method: 'post',
            data: data
        
        })
    }

// 修改互动课堂班级
    export function updateClass(data) {
        return request({
            url: '/edu/class',
            method: 'put',
            data: data
        })
    }

// 删除互动课堂班级
    export function delClass(classId) {
        return request({
            url: '/edu/class/' + classId,
            method: 'delete'
        })

    }

// 复制班级
export function copyClass(classId) {
    return request({
        url: '/edu/class/copy/' + classId,
        method: 'post'
    })
}

// 加入班级
export function joinClass(data) {
    return request({
        url: '/edu/class/join',
        method: 'post',
        data: data
    })
}

