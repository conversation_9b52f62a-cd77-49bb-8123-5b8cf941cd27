import request from '@/utils/request'

// 查询题库目录列表
export function listFolder(query) {
  return request({
    url: '/edu/moocQuestionFolder/list',
    method: 'get',
    params: query
  })
}

// 查询题库目录列表
export function tree(query) {
  return request({
    url: '/edu/moocQuestionFolder/tree',
    method: 'get',
    params: query
  })
}


// 查询题库目录详细
export function getFolder(folderId) {
  return request({
    url: '/edu/moocQuestionFolder/' + folderId,
    method: 'get'
  })
}

// 新增题库目录
export function addFolder(data) {
  return request({
    url: '/edu/moocQuestionFolder',
    method: 'post',
    data: data
  })
}

// 修改题库目录
export function updateFolder(data) {
  return request({
    url: '/edu/moocQuestionFolder',
    method: 'put',
    data: data
  })
}

// 删除题库目录
export function delFolder(folderId) {
  return request({
    url: '/edu/moocQuestionFolder/' + folderId,
    method: 'delete'
  })
}
