import request from '@/utils/request'

// 查询互动课堂试卷题型集合列表
export function listCollection(query) {
  return request({
    url: '/edu/collection/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂试卷题型集合详细
export function getCollection(collectionId) {
  return request({
    url: '/edu/collection/' + collectionId,
    method: 'get'
  })
}

// 新增互动课堂试卷题型集合
export function addCollection(data) {
  return request({
    url: '/edu/collection',
    method: 'post',
    data: data
  })
}

// 修改互动课堂试卷题型集合
export function updateCollection(data) {
  return request({
    url: '/edu/collection',
    method: 'put',
    data: data
  })
}

// 删除互动课堂试卷题型集合
export function delCollection(collectionId) {
  return request({
    url: '/edu/collection/' + collectionId,
    method: 'delete'
  })
}
