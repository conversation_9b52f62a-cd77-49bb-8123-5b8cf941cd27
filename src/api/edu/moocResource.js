import request from '@/utils/request'

// 查询用户资源库列表
export function listMoocResource(query) {
  return request({
    url: '/edu/moocResource/list',
    method: 'get',
    params: query
  })
}

// 查询用户资源库列表（不分页）
export function listMoocResourceNoPage(query) {
  return request({
    url: '/edu/moocResource/listNoPage',
    method: 'get',
    params: query
  })
}

// 查询用户资源库详细
export function getMoocResource(resourceId) {
  return request({
    url: '/edu/moocResource/' + resourceId,
    method: 'get'
  })
}

// 新增用户资源库
export function addMoocResource(data) {
  return request({
    url: '/edu/moocResource',
    method: 'post',
    data: data
  })
}

// 修改用户资源库
export function updateMoocResource(data) {
  return request({
    url: '/edu/moocResource',
    method: 'put',
    data: data
  })
}

// 删除用户资源库
export function delMoocResource(resourceIds) {
  return request({
    url: '/edu/moocResource/' + resourceIds,
    method: 'delete'
  })
}

// 导出用户资源库
export function exportMoocResource(query) {
  return request({
    url: '/edu/moocResource/export',
    method: 'post',
    params: query
  })
}

// 查询回收站列表
export function recycleList(query) {
  return request({
    url: '/edu/moocResource/recycle/list',
    method: 'get',
    params: query
  })
}

// 放入回收站
export function moveToRecycle(resourceIds) {
  return request({
    url: '/edu/moocResource/recycle/' + resourceIds,
    method: 'put'
  })
}

// 从回收站恢复
export function restore(resourceIds) {
  return request({
    url: '/edu/moocResource/restore/' + resourceIds,
    method: 'put'
  })
}

// 彻底删除用户资源
export function permanentDelete(resourceIds) {
  return request({
    url: '/edu/moocResource/permanent/' + resourceIds,
    method: 'delete'
  })
}

// 查询用户资源库列表
export function listResource(query) {
  return request({
    url: '/edu/moocResource/listResource',
    method: 'get',
    params: query
  })
}


// 获取用户存储空间信息
export function getUserSpace() {
  return request({
    url: '/edu/moocResource/space',
    method: 'get'
  });
}
