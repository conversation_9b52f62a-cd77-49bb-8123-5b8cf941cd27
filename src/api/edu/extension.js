import request from '@/utils/request'

// 查询互动课堂的拓展内容列表
export function listExtension(query) {
  return request({
    url: '/edu/extension/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂的拓展内容详细
export function getExtension(extensionId) {
  return request({
    url: '/edu/extension/' + extensionId,
    method: 'get'
  })
}

// 新增互动课堂的拓展内容
export function addExtension(data) {
  return request({
    url: '/edu/extension',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的拓展内容
export function updateExtension(data) {
  return request({
    url: '/edu/extension',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的拓展内容
export function delExtension(extensionId) {
  return request({
    url: '/edu/extension/' + extensionId,
    method: 'delete'
  })
}
