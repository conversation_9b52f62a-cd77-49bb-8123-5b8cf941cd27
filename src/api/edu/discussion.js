import request from '@/utils/request'

// 查询互动课堂的课上主题讨论列表
export function listDiscussion(query) {
  return request({
    url: '/edu/discussion/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂的课上主题讨论详细
export function getDiscussion(discussionId) {
  return request({
    url: '/edu/discussion/' + discussionId,
    method: 'get'
  })
}

// 新增互动课堂的课上主题讨论
export function addDiscussion(data) {
  return request({
    url: '/edu/discussion',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的课上主题讨论
export function updateDiscussion(data) {
  return request({
    url: '/edu/discussion',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的课上主题讨论
export function delDiscussion(discussionId) {
  return request({
    url: '/edu/discussion/' + discussionId,
    method: 'delete'
  })
}
