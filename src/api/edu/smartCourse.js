import request from '@/utils/request'

// 保存课程信息
export function saveSmartCourse(data) {
    return request({
        url: '/edu/course',
        method: 'post',
        data
    })
}

// 查询数字教材章节目录列表
export function listSmartCourse(query) {
    return request({
        url: '/edu/course/list',
        method: 'get',
        params: query
    })
}

// 查询本人的智能课程列表
export function myListSmartCourse(query) {
    return request({
        url: '/edu/course/myList',
        method: 'get',
        params: query
    })
}

// 获取课程详情
export function getCourse(courseId) {
    return request({
        url: '/edu/course/' + courseId,
        method: 'get'
    })
}

// 更新课程信息
export function updateSmartCourse(data) {
    return request({
        url: '/edu/course',
        method: 'put',
        data
    })
}

// 查询课程列表（无分页）- 根据教师ID
export function getListByTeacherIdNoPage(query) {
    return request({
        url: '/edu/course/getListByTeacherIdNoPage',
        method: 'get',
        params: query
    })
}

// 查询数字教材章节目录列表
export function removeSmartCourse(courseId) {
    return request({
        url: '/edu/course/' + courseId,
        method: 'delete',
    })
}

// 获取互动课堂首页统计数据
export function getDashboardStatistics() {
    return request({
        url: '/edu/course/dashboard/statistics',
        method: 'get'
    })
}
