import request from '@/utils/request'

// 查询互动课堂的课上互动讨论消息列表
export function listMessage(query) {
  return request({
    url: '/edu/message/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂的课上互动讨论消息详细
export function getMessage(messageId) {
  return request({
    url: '/edu/message/' + messageId,
    method: 'get'
  })
}

// 新增互动课堂的课上互动讨论消息
export function addMessage(data) {
  return request({
    url: '/edu/message',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的课上互动讨论消息
export function updateMessage(data) {
  return request({
    url: '/edu/message',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的课上互动讨论消息
export function delMessage(messageId) {
  return request({
    url: '/edu/message/' + messageId,
    method: 'delete'
  })
}
