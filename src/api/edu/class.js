import request from '@/utils/request'

// 查询互动课堂列表
export function listClass(query) {
  return request({
    url: '/edu/class/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂详细
export function getClass(chatId) {
  return request({
    url: '/edu/class/' + chatId,
    method: 'get'
  })
}

// 新增互动课堂
export function addClass(data) {
  return request({
    url: '/edu/class',
    method: 'post',
    data: data
  })
}

// 修改互动课堂
export function updateClass(data) {
  return request({
    url: '/edu/class',
    method: 'put',
    data: data
  })
}

// 删除互动课堂
export function delClass(chatId) {
  return request({
    url: '/edu/class/' + chatId,
    method: 'delete'
  })
}

// 查询互动课堂列表
export function getInfo(query) {
  return request({
    url: '/edu/class/getInfo',
    method: 'get',
    params: query
  })
}

// 查询互动课堂班级活动
export function getActivityList(query) {
  return request({
    url: '/edu/class/getActivityList',
    method: 'get',
    params: query,
    withCredentials: true
  })
}

// 追加互动课堂班级活动
export function addActivityList(data) {
  return request({
    url: '/edu/class/addActivityList',
    method: 'put',
    data: data
  })
}

// 修改互动课堂班级活动
export function updateActivityList(data) {
  return request({
    url: '/edu/class/upActivityList',
    method: 'put',
    data: data
  })
}

// 删除互动课堂班级活动
export function delActivityList(data) {
  return request({
    url: '/edu/class/delActivityList',
    method: 'put',
    data: data
  })
}