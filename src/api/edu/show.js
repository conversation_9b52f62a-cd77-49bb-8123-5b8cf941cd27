import request from '@/utils/request'

// 查询互动课堂的作业列表
export function listShow(query) {
  return request({
    url: '/edu/show/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂的作业详细
export function getShow(homeworkShowId) {
  return request({
    url: '/edu/show/' + homeworkShowId,
    method: 'get'
  })
}

// 新增互动课堂的作业
export function addShow(data) {
  return request({
    url: '/edu/show',
    method: 'post',
    data: data
  })
}

// 修改互动课堂的作业
export function updateShow(data) {
  return request({
    url: '/edu/show',
    method: 'put',
    data: data
  })
}

// 删除互动课堂的作业
export function delShow(homeworkShowId) {
  return request({
    url: '/edu/show/' + homeworkShowId,
    method: 'delete'
  })
}
