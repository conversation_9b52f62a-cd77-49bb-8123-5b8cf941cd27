import request from '@/utils/request'

// 查询智慧课堂习题列表
export function listMoocQuestion(query) {
  return request({
    url: '/edu/moocQuestion/list',
    method: 'get',
    params: query
  })
}

// 查询智慧课堂习题列表(包含选项)
export function listMoocQuestionWithOptions(query) {
  return request({
    url: '/edu/moocQuestion/listWithOptions',
    method: 'get',
    params: query
  })
}

// 查询回收站中的智慧课堂习题列表
export function recycleBinList(query) {
  return request({
    url: '/edu/moocQuestion/recycleBin/list',
    method: 'get',
    params: query
  })
}

// 查询智慧课堂习题详细
export function getMoocQuestion(questionId) {
  return request({
    url: '/edu/moocQuestion/' + questionId,
    method: 'get'
  })
}

// 获取题目信息及用户答案
export function getUserAnswerInfo(query) {
  return request({
    url: '/edu/moocQuestion/getUserAnswerInfo',
    method: 'get',
    params: query
  })
}

// 新增智慧课堂习题
export function addMoocQuestion(data) {
  return request({
    url: '/edu/moocQuestion',
    method: 'post',
    data: data
  })
}

// 批量导入智慧课堂习题
export function importQuestions(data) {
  return request({
    url: '/edu/moocQuestion/import',
    method: 'post',
    data: data
  })
}

// 修改智慧课堂习题
export function updateMoocQuestion(data) {
  return request({
    url: '/edu/moocQuestion',
    method: 'put',
    data: data
  })
}

// 删除智慧课堂习题
export function delMoocQuestion(questionIds) {
  return request({
    url: '/edu/moocQuestion/' + questionIds,
    method: 'delete'
  })
}

// 将智慧课堂习题移入回收站
export function moveToRecycleBin(questionIds) {
  return request({
    url: '/edu/moocQuestion/recycle/' + questionIds,
    method: 'put'
  })
}

// 从回收站恢复智慧课堂习题
export function restoreFromRecycleBin(questionIds) {
  return request({
    url: '/edu/moocQuestion/restore/' + questionIds,
    method: 'put'
  })
}

// 检查题目是否被试卷管理
export function checkPaperReference(questionIds) {
  return request({
    url: '/edu/moocQuestion/checkPaperReference',
    method: 'post',
    data: questionIds
  })
}

// 导出智慧课堂习题列表
export function exportMoocQuestion(query) {
  return request({
    url: '/edu/moocQuestion/export',
    method: 'post',
    params: query
  })
}
