import request from '@/utils/request.js'
// 新增课程团队成员
export function addOpenCourseTeammate(data) {
  return request({
    url: '/edu/openCourseTeammate',
    method: 'post',
    data: data
  })
}
// 删除课程团队成员
export function delOpenCourseTeammate(studentIds) {
  return request({
    url: '/edu/openCourseTeammate/' + studentIds,
    method: 'delete',
  });
}

// 查询课程团队成员
export function listOpenCourseTeammate(query) {
  return request({
    url: '/edu/openCourseTeammate/listNoPage',
    method: 'get',
    params: query,
  });
}
// 修改课程团队成员
export function exitOpenCourseTeammate(data) {
  return request({
    url: '/edu/openCourseTeammate',
    method: 'put',
    data: data
  })
}