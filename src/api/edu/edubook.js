import request from '@/utils/request'

/**
 * 查询学科信息列表(树形结构)
 * 教务端教材管理用
 */
export function selectSubjectList(query) {
  return request({
    url: '/edu/book/selectSubjectList',
    method: 'get',
    params: query
  })
}

/**
 * 教务端教材管理-查询公开教材列表
 */
export function selectBookList(query) {
  return request({
    url: '/edu/book/selectBookList',
    method: 'get',
    params: query
  })
}

/**
 * 教务端教材管理-查询所有公开教材列表
 */
export function selectAllBookList(query) {
  return request({
    url: '/edu/book/selectAllBookList',
    method: 'get',
    params: query
  })
}

/**
 * 教务端教材管理-查询校本教材列表
 */
export function selectEduBookList(query) {
  return request({
    url: '/edu/book/selectEduBookList',
    method: 'get',
    params: query
  })
}

/**
 * 教务端教材商城-查询教材列表
 */
export function selectEducationalBookList(query) {
  return request({
    url: '/edu/book/selectEducationalBookList',
    method: 'get',
    params: query
  })
}

/**
 * 教务端教材管理-查询教材详情
 */
export function getBookDetail(query) {
  return request({
    url: '/edu/book/getBookDetail',
    method: 'get',
    params: query
  })
}

/**
 * 教务端教材管理-同步
 */
export function pushBook(query) {
  return request({
    url: '/edu/book/pushBook',
    method: 'get',
    params: query
  })
}

/**
 * 教务端教材详情-查询用户
 */
export function selectBookUserList(query) {
  return request({
    url: '/edu/book/selectBookUserList',
    method: 'get',
    params: query
  })
}

/**
 * 教务端教材详情-批量添加教材使用用户
 */
export function importBookUser(data) {
  return request({
    url: '/edu/book/importBookUser',
    method: 'post',
    data: data
  })
}

/**
 * 教务端教材详情-添加教材使用用户
 */
export function addBookUser(data) {
  return request({
    url: '/edu/book/addBookUser',
    method: 'post',
    data: data
  })
}

/**
 * 教务端教材详情-下载导入失败的用户
 */
export function downloadErrorList(data) {
  return request({
    url: '/edu/book/downloadErrorList',
    method: 'post',
    data: data
  })
}

/**
 * 教务端-导出名单
 */
export function exportBookUser(data) {
  return request({
    url: '/edu/book/exportBookUser',
    method: 'post',
    data: data
  })
}

/**
 * 教务端-删除名单
 */
export function removeBookUser(data) {
  return request({
    url: '/edu/book/removeBookUser',
    method: 'post',
    data: data
  })
}

/**
 * 教务端-订单列表
 */
export function getOrderBySchoolList(query) {
  return request({
    url: '/edu/book/getOrderBySchoolList',
    method: 'get',
    params: query
  })
}

/**
 * 教务端-订单详情
 */
export function getOrderDetailBySchool(data) {
  return request({
    url: '/edu/book/getOrderDetailBySchool',
    method: 'post',
    data: data
  })
}

/**
 * 教务端-查询教材详情
 */
export function getRemoteBookDetail(bookId) {
  return request({
    url: `/edu/book/getRemoteBookDetail/${bookId}`,
    method: 'get'
  })
}

/**
 * 查询教材列表(教材使用统计教材列表）
 */
export function getBookListForSchool(query) {
  return request({
    url: '/edu/book/getBookListForSchool',
    method: 'get',
    params: query
  })
}

/**
 * 教务端教材详情-资源包下载
 */
export function downLoadBookFile(data) {
  return request({
    url: '/edu/book/downLoadBookFile',
    method: 'post',
    data: data
  })
}

/**
 * 教务端-获取教材数据总览
 */
export function dataOverview(query) {
  return request({
    url: '/edu/book/dataOverview',
    method: 'get',
    params: query
  })
}

/**
 * Get chapter data list.
 */
export function getChapterDataList(query) {
  return request({
    url: '/edu/book/getChapterDataList',
    method: 'get',
    params: query
  })
}

/**
 * 教务端-导出数据总览
 */
export function exportDataOverview(data) {
  return request({
    url: '/edu/book/exportDataOverview',
    method: 'post',
    data: data
  })
}

/**
 * 教务端-导出应用数据
 */
export function exportAppData(data) {
  return request({
    url: '/edu/book/exportAppData',
    method: 'post',
    data: data
  })
}

/**
 * Get chapter list for select.
 */
export function chapterListForSelect(query) {
  return request({
    url: '/edu/book/chapterListForSelect',
    method: 'get',
    params: query
  })
}

/**
 * 查询教材应用用户数据
 */
export function selectBookApplicationUserData(query) {
  return request({
    url: '/edu/book/selectBookApplicationUserData',
    method: 'get',
    params: query
  })
}

/**
 * 根据教材id和版本id获取教材目录
 */
export function selectChapterByBookId(query) {
  return request({
    url: '/edu/book/selectChapterByBookId',
    method: 'get',
    params: query
  })
}