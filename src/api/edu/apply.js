import request from '@/utils/request'

// 查询互动课堂班级成员申请列表
export function listApply(query) {
  return request({
    url: '/edu/apply/list',
    method: 'get',
    params: query
  })
}

// 查询互动课堂班级成员申请详细
export function getApply(applyId) {
  return request({
    url: '/edu/apply/' + applyId,
    method: 'get'
  })
}

// 新增互动课堂班级成员申请
export function addApply(data) {
  return request({
    url: '/edu/apply',
    method: 'post',
    data: data
  })
}

// 修改互动课堂班级成员申请
export function updateApply(data) {
  return request({
    url: '/edu/apply',
    method: 'put',
    data: data
  })
}

// 删除互动课堂班级成员申请
export function delApply(applyId) {
  return request({
    url: '/edu/apply/' + applyId,
    method: 'delete'
  })
}


// 查询互动课堂班级成员申请列表
export function listForClass(query) {
  return request({
    url: '/edu/apply/listForClass',
    method: 'get',
    params: query
  })
}


// 审核互动课堂班级成员申请
export function auditApply(data) {
  return request({
    url: '/edu/apply/audit',
    method: 'post',
    data: data
  })
}


// 一键审核互动课堂班级成员申请
export function auditAllApply(data) {
  return request({
    url: '/edu/apply/auditAll',
    method: 'post',
    data: data
  })
}
