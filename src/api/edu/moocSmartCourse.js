import request from '@/utils/request'

// 查询互动课堂课程列表（无分页）
export function getListByTeacherIdNoPage(query) {
    return request({
        url: '/edu/course/getListByTeacherIdNoPage',
        method: 'get',
        params: query
    })
}

    // 查询互动课堂列表
    export function listCourse(query) {
        return request({
            url: '/edu/course/list',
            method: 'get',
            params: query
        })
    }

    // 查询本人的智能课程列表
    export function myListCourse(query) {
        return request({
            url: '/edu/course/myList',
            method: 'get',
            params: query
        })
    }

// 查询互动课堂详细
    export function getCourse(courseId) {
        return request({
            url: '/edu/course/' + courseId,
            method: 'get'
        })
    }

// 新增互动课堂
    export function addCourse(data) {
        return request({
            url: '/edu/course',
            method: 'post',
            data: data
        })
    }

// 修改互动课堂
    export function updateCourse(data) {
        return request({
            url: '/edu/course',
            method: 'put',
            data: data
        })
    }

// 删除互动课堂
    export function delCourse(courseId) {
        return request({
            url: '/edu/course/' + courseId,
            method: 'delete'
        })

    }

