import request from '@/utils/request.js'

// 查询教育层次 (下拉列表)
export function listSubject(query) {
  return request({
    url: '/edu/openCourse/selectSubjectList',
    method: 'get',
    params: query
  })
}

// 查询学科分类 (下拉列表)
export function listThirdSubject(query) {
  return request({
    url: '/edu/openCourse/selectThirdSubjectList',
    method: 'get',
    params: query
  })
}

// 保存课程信息
export function saveOpenCourse(data) {
  return request({
    url: '/edu/openCourse',
    method: 'post',
    data
  })
}

// 公开课列表
export function listOpenCourse(query){
  return request({
    url: '/edu/openCourse/list',
    method: 'get',
    params: query
  })
}

export function removeOpenCourse(id) {
  return request({
    url: '/edu/openCourse/' + id,
    method: 'delete',
  })
}
// 查询我加入的公开课列表
export function getMyOpenCourseList(query) {
  return request({
    url: '/edu/openCourse/myList',
    method: 'post',
    data: query
  })
}

export function getOpenCourseContent(query) {
  return request({
    url: '/edu/openCourse/content/'+query,
    method: 'get',

  })
}
