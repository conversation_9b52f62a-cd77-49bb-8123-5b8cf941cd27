import request from '@/utils/request';

// 查询学校管理列表
export function listSchool(query) {
  return request({
    url: '/basic/school/listEducation',
    method: 'get',
    params: query,
  });
}

// 查询学校管理列表(用于下拉框)
export function listSchoolNoPage(query) {
  return request({
    url: '/basic/school/listNoPage',
    method: 'get',
    params: query,
  });
}

// 查询学院列表(用于下拉框)
export function selectAcademy() {
  return request({
    url: '/basic/school/selectAcademy',
    method: 'get',
  });
}

// 查询学院列表(用于下拉框)
export function queryAcademyNoPage() {
  return request({
    url: '/basic/school/queryAcademyNoPage',
    method: 'get',
  });
}

// 查询学院列表(用于新增下级时反显)
export function getAllWithOutLast(query) {
  return request({
    url: '/basic/school/getAllWithOutLast',
    method: 'get',
    params: query,
  });
}

// 查询学校管理详细
export function getSchool(schoolId) {
  return request({
    url: '/basic/school/' + schoolId,
    method: 'get',
  });
}

// 新增学校管理
export function addSchool(data) {
  return request({
    url: '/basic/school',
    method: 'post',
    data: data,
  });
}

// 导入学校信息
export function insertSchoolList(data) {
  return request({
    url: '/basic/school/insertSchoolList',
    method: 'post',
    data: data,
  });
}

// 修改学校管理
export function updateSchool(data) {
  return request({
    url: '/basic/school',
    method: 'put',
    data: data,
  });
}

// 删除学校管理
export function delSchool(schoolId) {
  return request({
    url: '/basic/school/' + schoolId,
    method: 'delete',
  });
}

// 查询学校管理列表
export function listAllSchool(query) {
  return request({
    url: '/basic/school/all',
    method: 'get',
    params: query,
  });
}

// 查询学校列表（根据查询条件）
export function listSchoolByQuery(query) {
  return request({
    url: '/basic/openApi/listSchoolByQuery',
    method: 'get',
    params: query,
  });
}

export function selectEduAcademyList(query){
  return request({
    url: '/basic/school/selectEduAcademyList',
    method: 'get',
    params: query,
  });
}
