import request from '@/utils/request'

// 查询教材资源列表
export function listOpenCoursePlan(query) {
  return request({
    url: '/edu/openCoursePlan/list',
    method: 'get',
    params: query
  })
}

// 查询教材资源详细
export function getOpenCoursePlan(bookFileId) {
  return request({
    url: '/edu/openCoursePlan/' + bookFileId,
    method: 'get'
  })
}

// 新增教材资源
export function addOpenCoursePlan(data) {
  return request({
    url: '/edu/openCoursePlan',
    method: 'post',
    data: data
  })
}

// 修改教材资源
export function updateOpenCoursePlan(data) {
  return request({
    url: '/edu/openCoursePlan',
    method: 'put',
    data: data
  })
}

// 删除教材资源
export function delOpenCoursePlan(planId) {
  return request({
    url: '/edu/openCoursePlan/' + planId,
    method: 'delete'
  })
}
