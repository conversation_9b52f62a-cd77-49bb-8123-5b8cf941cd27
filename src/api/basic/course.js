import request from '@/utils/request';

// 查询学科信息列表
export function listNoPageByStudent(query) {
  return request({
    url: '/basic/course/listNoPageByStudent',
    method: 'get',
    params: query,
  });
}
// 查询公开课详情
export function getInfoByStudent(courseId){
    return request({
        url:'/basic/course/getInfoByStudent/' + courseId,
        method:'get',
    })
}
// 修改公开课详情
export function editCourseInformation(query) {
  return request({
    url: '/basic/course/editCourseInformation',
    method: 'get',
    params: query,
  });
}