import request from '@/utils/request'

// 查询助记卡列表
export function listCard(query) {
  return request({
    url: '/book/card/list',
    method: 'get',
    params: query
  })
}

// 查询助记卡详细
export function getCard(cardId) {
  return request({
    url: '/book/card/' + cardId,
    method: 'get'
  })
}

// 新增助记卡
export function addCard(data) {
  return request({
    url: '/book/card',
    method: 'post',
    data: data
  })
}

// 修改助记卡
export function updateCard(data) {
  return request({
    url: '/book/card',
    method: 'put',
    data: data
  })
}

// 删除助记卡
export function delCard(cardId) {
  return request({
    url: '/book/card/' + cardId,
    method: 'delete'
  })
}

// AI生成助记卡
export function generateFlashCard(data) {
  return request({
    url: '/book/card/generateCard',
    method: 'post',
    data: data
  })
}
