import request from '@/utils/request'

// 查询DUTP-DTB_014学生/教师书架列表
export function listBook(query) {
  return request({
    url: '/book/userBook/getInfoEducation',
    method: 'get',
    params: query
  })
}

// 查询DUTP-DTB_014学生/教师书架详细
export function getBook(userBookId) {
  return request({
    url: '/book/book/' + userBookId,
    method: 'get'
  })
}

// 新增DUTP-DTB_014学生/教师书架
export function addBook(data) {
  return request({
    url: '/book/book',
    method: 'post',
    data: data
  })
}

// 修改DUTP-DTB_014学生/教师书架
export function updateBook(data) {
  return request({
    url: '/book/userBook/editEducation',
    method: 'put',
    data: data
  })
}

// 删除DUTP-DTB_014学生/教师书架
export function delBook(userBookId) {
  return request({
    url: '/book/book/' + userBookId,
    method: 'delete'
  })
}
// 查询DUTP-DTB_014学生/教师书架列表
export function getlistBook(query) {
  return request({
    url: '/book/userBook/list',
    method: 'get',
    params: query
  })
}