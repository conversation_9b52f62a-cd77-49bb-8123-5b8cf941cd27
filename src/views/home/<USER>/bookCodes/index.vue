<template>
  <!-- 头部导航栏 -->
  <headNavComp @toLogin="toLogin" :showBackground="true" />
  <div class="home-container-sty">
    <!-- 主体内容 -->
    <div class="exchange-page">
      <el-form
        v-if="!showSecurityVerification"
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-position="top"
        class="tab-content"
      >
        <!-- 购书码输入 -->
        <h4 class="title" style="margin-left: 170px">购书码</h4>
        <el-form-item prop="bookCode">
          <div class="book-code-inputs">
            <el-input
              v-model="formData.bookCodePart1"
              maxlength="4"
              size="large"
              placeholder="请输入"
              @input="handleInputChange"
              @paste="handlePaste"
            />
            <span>-</span>
            <el-input
              v-model="formData.bookCodePart2"
              maxlength="4"
              size="large"
              placeholder="请输入"
              @input="handleInputChange"
              @paste="handlePaste"
            />
            <span>-</span>
            <el-input
              v-model="formData.bookCodePart3"
              maxlength="4"
              size="large"
              placeholder="请输入"
              @input="handleInputChange"
              @paste="handlePaste"
            />
            <span>-</span>
            <el-input
              v-model="formData.bookCodePart4"
              maxlength="4"
              size="large"
              placeholder="请输入"
              @input="handleInputChange"
              @paste="handlePaste"
            />
          </div>
        </el-form-item>
        <!-- 验证码输入 -->
        <el-form-item prop="verificationCode">
          <el-input
            size="large"
            v-model="formData.verificationCode"
            placeholder="请输入验证码"
            maxlength="4"
          >
            <template #suffix>
              <el-button
                type="primary"
                link
                :disabled="isSendingCode"
                @click="sendVerificationCode"
              >
                <span :class="{ 'gray-text': isSendingCode }">{{
                  sendCodeText
                }}</span>
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <!-- 手机号提示 -->
        <div class="hint-text">验证码发送至注册手机号 <br /></div>
        <div class="hint-text">
          {{ maskPhoneNumber(phoneNumber) || "未绑定手机号" }}
        </div>
        <!-- 确认按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            class="confirm-button"
            @click="handleConfirm"
          >
            确认兑换
          </el-button>
        </el-form-item>
      </el-form>
      <div
        class="security-verification-container flex-col"
        v-else-if="showSecurityVerification"
      >
        <div class="slide-verify-block flex-col justify-between">
          <div class="slide-verify-box flex-col">
            <span class="slide-verify-text">请完成下列验证后继续</span>
            <div class="slide-verify-slider flex-col">
              <slide-verify
                :width="300"
                :height="150"
                :imgs="slideVerifyImages"
                sliderText="按住左边按钮拖动完成上方拼图"
                @success="onSuccess"
                @fail="onFail"
                @refresh="onRefresh"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 底部   -->
  <footComp />
</template>

<script setup>
import { ref, reactive, watch, onMounted } from "vue";
import { ElNotification, ElMessageBox } from "element-plus";
import { sendAliyun } from "@/api/login";
import SlideVerify from "vue3-slide-verify";
import "vue3-slide-verify/dist/style.css";
import { getPurchaseCode, bookCodeExchange } from "@/api/shop/purchaseCode";
import {
  addProhibitionOfExchange,
  getProhibitionOfExchange,
} from "@/api/system/list";
import { getlistBook } from "@/api/book/userBook";
import Cookies from "js-cookie";
import useUserStore from "@/store/modules/user.js";
import footComp from "@/views/home/<USER>/footComp/index.vue";
import headNavComp from "@/views/home/<USER>/headNavComp/index.vue";
import { listLog } from "@/api/system/log";
import { listConfig } from "@/api/system/config";
import { getToken } from "@/utils/auth";
import { gotoReader } from "@/utils/reader";
const userStore = useUserStore();
const userInfo = userStore.getInfo();
const userId = ref();
const route = useRoute();
const router = useRouter();
const code = route.query.code;
const formRef = ref(null); // 新增：定义 form 的 ref
const showSecurityVerification = ref(false);
const activeName = ref("bookCode");

// 获取当前路由对象
const slideVerifyImages = ref([
  new URL("@/assets/images/img.jpg", import.meta.url).href,
  new URL("@/assets/images/img1.jpg", import.meta.url).href,
  new URL("@/assets/images/img2.jpg", import.meta.url).href,
  new URL("@/assets/images/img3.jpg", import.meta.url).href,
  new URL("@/assets/images/img4.jpg", import.meta.url).href,
  new URL("@/assets/images/img5.jpg", import.meta.url).href,
  // 其他图片路径
]);
const exchangeLimit = ref(0);
// --- 数据 ---
const formData = reactive({
  bookCodePart1: null,
  bookCodePart2: null,
  bookCodePart3: null,
  bookCodePart4: null,
  verificationCode: null,
});
const phoneNumber = ref(""); // 模拟手机号
const countdown = ref(60);
const qrCodeUrl = ref("@/assets/images/QRcode.png");

const retryCount = ref(0);
const msg = ref("");
const sendCodeText = ref("获取验证码");
const maxCodeAttempts = 5;
const codeAttempts = ref(0);
const isSendingCode = ref(false);
const bookCodeErrorCount = ref(0); // 新增：购书码错误计数器
const checkData = ref({});
const handleInputChange = () => {
  const fullCode = getFullBookCode(); // 获取完整的购书码
  formRef.value?.clearValidate("bookCode"); // 清除之前的错误提示

  // 当购书码完整并且是有效的 16 位数字时，清除错误提示
  if (/^\d{16}$/.test(fullCode)) {
    formRef.value?.validateField("bookCode");
  } else {
    // 如果不符合规则，展示错误信息
    formRef.value?.validateField("bookCode");
  }
};

const handlePaste = (event) => {
  event.preventDefault(); // 阻止默认粘贴行为
  const pastedText = (event.clipboardData || window.clipboardData).getData('text');

  // 去除所有非数字字符
  const cleanText = pastedText.replace(/[^0-9]/g, '');

  if (cleanText.length === 16) {
    formData.bookCodePart1 = cleanText.slice(0, 4);
    formData.bookCodePart2 = cleanText.slice(4, 8);
    formData.bookCodePart3 = cleanText.slice(8, 12);
    formData.bookCodePart4 = cleanText.slice(12, 16);
  } else {
    ElNotification({
      title: '操作提示',
      message: '粘贴的购书码格式不正确，请重新粘贴',
      type: 'warning',
    });
  }
};
// 通过合并所有部分来获取完整的购书码
function toLogin() {
  router.push({ path: "/login" });
}
const getFullBookCode = () => {
  return (
    formData.bookCodePart1 +
    formData.bookCodePart2 +
    formData.bookCodePart3 +
    formData.bookCodePart4
  );
};
// --- 校验规则 ---
const rules = reactive({
  bookCode: [
    {
      validator: (rule, value, callback) => {
        const fullCode = getFullBookCode();
        if (!/^\d{16}$/.test(fullCode)) {
          callback(new Error("购书码必须是16位纯数字"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  verificationCode: [
    { required: true, message: "请输入验证码", trigger: "blur" },
    {
      pattern: /^[0-9]{4}$/,
      message: "验证码必须是4位数字",
      trigger: "blur",
    },
  ],
});

const bookCodeValid = ref(false);
watch(
  () => formData.bookCode,
  (val) => {
    bookCodeValid.value = /^\d{16}$/.test(val.value);
  }
);

// --- 方法 ---
// 确认兑换
const handleConfirm = async () => {
  if (!(await getCount())) {
    const prohibitionRes = await getProhibitionOfExchange(userId.value);
    if (prohibitionRes.code === 200 && prohibitionRes.data) {
      ElNotification({
        title: '操作提示',
        message: '您的账户未解锁，请稍后再试',
        type: 'error',
      });
      return; // 退出整个函数
    }
    await getCheckData();
    const formEl = formRef.value; // 获取 form 的 ref
    if (!formEl) return;
    formData.bookCode = getFullBookCode(); // 合并购书码
    formEl.validate((valid) => {
      if (valid) {
        if (!checkData.value || formData.bookCode !== checkData.value.code) {
          bookCodeErrorCount.value++;
          if (bookCodeErrorCount.value < 3) {
            ElNotification({
              title: "操作提示",
              message: `无效购书码，您还有 ${3 - bookCodeErrorCount.value} 次机会`,
              type: "warning",
            });
          } else if (bookCodeErrorCount.value >= 3) {
            lockAccount();
          }
          return;
        }
        checkProhibitionOfExchange();
      } else {
      }
    });
  }
};
// 点击事件处理器
const handleClick = () => {};
const onSuccess = () => {
  msg.value = "验证通过";
  countdown.value = 60;
  showSecurityVerification.value = false;
  if (codeAttempts.value >= maxCodeAttempts) {
    ElNotification({
      title: "操作提示",
      message: "超过最大发送次数，请稍后再试",
      type: "error",
    });
    return;
  }
  if (isSendingCode.value) return;
  isSendingCode.value = true;
  sendCodeText.value = `${countdown.value}秒后重新发送`;
  let timer = setInterval(() => {
    countdown.value--;
    sendCodeText.value = `${countdown.value}秒后重新发送`;
    if (countdown.value <= 0) {
      clearInterval(timer);
      isSendingCode.value = false;
      sendCodeText.value = "获取验证码";
      countdown.value = 60;
    }
  }, 1000);
  codeAttempts.value++;
  // 发送验证码的API请求
  sendAliyun({ phone: phoneNumber.value }).then((res) => {
    if (res.code === 200) {
      Cookies.set("verificationCode", res.data, { expires: 5 / (60 * 24) });
      ElNotification({
        title: "操作提示",
        message: "验证码已发送，请查收短信",
        type: "success",
      });
    }
  });
};

const sendVerificationCode = () => {
  showSecurityVerification.value = true;
};

const onFail = () => {
  msg.value = "验证不通过";
  // 刷新
  block.value?.refresh();
};

const onRefresh = () => {};
const lockAccount = () => {
  // 后台拉黑十分钟
  const params = {
    userId: userId.value,
    frozenType: 3,
    frozenScope: 3,
    state: 1,
    endDate: formatDate(new Date(new Date().getTime() + 10 * 60 * 1000)),
  };
  addProhibitionOfExchange(params).then((res) => {
    if (res.code === 200) {
      ElNotification({
        title: "操作提示",
        message: "您的账户已被锁定，请10分钟后重试",
        type: "error",
      });
    }
  });
};
const checkProhibitionOfExchange = async () => {

  try {
    const params = {
      userId: userId.value,
      bookId: checkData.value.bookId,
    };
    // 试用时间判断。判断试用时间是否过期
    const res = await getlistBook(params);
    if (res.code === 200 && res.rows.length > 0) {
      const expireDate = new Date(res.rows[0].expireDate);
      const currentDate = new Date();

      if (expireDate >= currentDate) {
        ElNotification({
          title: '操作提示',
          message: '此教材已兑换，请勿重复兑换',
          type: 'error',
        });
        return; // 退出整个函数
      }
    }
    const exchangeParams = {
      codeId: checkData.value.codeId,
      bookId: checkData.value.bookId,
      userId: userId.value,
      exchangeDate: formatDate(new Date(new Date().getTime() + 10 * 60 * 1000)),
      verificationCode: formData.verificationCode,
      phone: phoneNumber.value,
    };
    const exchangeRes = await bookCodeExchange(exchangeParams);
    if (exchangeRes.code === 200) {
        ElMessageBox({
          title: '购书码',
          message: '已成功兑换购书码，是否开始学习',
          type: 'success',
          iconClass: 'el-icon-success', // 使用自定义图标类名
          showCancelButton: true,
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          customClass: 'custom-message-box', // 自定义样式类名
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              // 确认按钮逻辑
              gotoReader(checkData.value.bookId);
              done();
            } else if (action === 'cancel') {
              // 取消按钮逻辑
              done();
            }
          },
        });
        isSendingCode.value = false;
        sendCodeText.value = "获取验证码";
        countdown.value = 0;
    } else {
      ElNotification({
        title: '操作提示',
        message: exchangeRes.msg,
        type: 'error',
      });
    }
    bookCodeErrorCount.value = 0;
  } catch (error) {
    console.error('检查兑换限制失败:', error);
  }
};
function maskPhoneNumber(phoneNumber) {
  if (!phoneNumber || phoneNumber.length !== 11) {
    return phoneNumber; // 如果不是有效的11位手机号，直接返回原字符串
  }
  return phoneNumber.slice(0, 3) + "****" + phoneNumber.slice(7);
}
/** 查询表集合 */
async function getUserInfo() {
  await userStore.getInfo().then((response) => {
    if (response.code === 200) {
      userInfo.value = response.user;
      userId.value = userInfo.value.userId;
      phoneNumber.value = userInfo.value.phonenumber;
    }
  });
}
/** 查询校验购书码数据 */
async function getCheckData() {
  const params = {
    code: getFullBookCode(),
  };
  const response = await getPurchaseCode(params);
  checkData.value = response.data;
}
/** 查询表集合 */
async function getList() {
  try {
    const response = await getPurchaseCode(params);
    if (response.data) {
      formData.bookCodePart1 = response.data.code.slice(0, 4);
      formData.bookCodePart2 = response.data.code.slice(4, 8);
      formData.bookCodePart3 = response.data.code.slice(8, 12);
      formData.bookCodePart4 = response.data.code.slice(12, 16);
      phoneNumber.value = response.data.phone;
    } else {
      phoneNumber.value = userInfo.value.phonenumber;
    }
  } catch (error) {
    console.error("获取购书码信息失败:", error);
  }
}
function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
const getCount = async () => {
  // 获取当前日期
  const currentDate = new Date();

  // 格式化日期
  const year = currentDate.getFullYear();
  const month = String(currentDate.getMonth() + 1).padStart(2, "0"); // 月份从0开始，所以加1
  const day = String(currentDate.getDate()).padStart(2, "0");
  // 组合成所需的格式
  const formattedDate = `${year}-${month}-${day}`;

  const params = {
    userId: userId.value,
    exchangeDate: formattedDate,
  };
  await listLog(params).then((res) => {
    if (res.code === 200) {
      if (res.data.length >= exchangeLimit.value) {
        ElNotification({
          title: "错误",
          message: "超出今日兑换次数，请明日再次兑换",
          type: "error",
        });
        return false;
      } else {
        return true;
      }
    }
  });
};
const getConfig = () => {
  const params = {
    pageNum: 1,
    pageSize: 10,
  };
  listConfig(params).then((res) => {
    if (res.code === 200) {
      exchangeLimit.value = res.rows[0].exchangeLimit;
    }
  });
};
onMounted(() => {
  if (getToken()) {
    getUserInfo();
    // getList();
    getConfig();
    if (code) {
      formData.bookCodePart1 = code.slice(0, 4);
      formData.bookCodePart2 = code.slice(4, 8);
      formData.bookCodePart3 = code.slice(8, 12);
      formData.bookCodePart4 = code.slice(12, 16);
    }
  } else {
    ElNotification({
      title: "错误",
      message: "请您先进行登录,再进行兑换",
      type: "error",
    });
    // 跳转登录页
    router.push({ path: "/login" });
  }
});
</script>

<style scoped lang="scss">
@import "@/assets/styles/index.scss";

.home-container-sty {
  width: 100%;
  min-height: calc(100vh - 450px);
  margin-top: 189px;
  margin-bottom: 189px;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;
}

.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}

.tab-content {
  padding: 20px;
  width: 500px;
}

.input-label {
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.hint-text {
  margin-left: 5px;
  margin-top: 10px;
  color: #909399;
  font-size: 12px;
}

.confirm-button {
  margin-top: 20px;
  width: 100%;
}

.qr-code-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.qr-code-image {
  width: 200px;
  height: 200px;
}

/* 去掉 tabs 下方横线和边框 */
:deep(.el-tabs__nav-wrap::after) {
  display: none !important;
}

:deep(.el-tabs__item) {
  font-size: var(--el-font-size-base);
  font-weight: 500;
  padding: 0px 65px;
  margin-left: 60px;
}

.gray-text {
  color: #909399;
  /* 灰色 */
}

.security-verification-container {
  margin: -80px 15px 0 50px;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.slide-verify-block {
  width: 374px;
  height: 382px;
  margin: 90px 0px 0px -25px;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.slide-verify-box {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 17px;
  width: 374px;
  height: 335px;
  border: 3.349253731343284px solid rgba(229, 230, 231, 1);
  margin-top: 29px;
}

.slide-verify-text {
  width: 180px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 18px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: justify;
  white-space: nowrap;
  line-height: 18px;
  margin: 25px 0 0 20px;
}

.slide-verify-slider {
  border-radius: 8px;
  width: 329px;
  height: 179px;
  margin: 16px 0 0 22px;
}

.account-locked-message {
  color: red;
  font-size: 16px;
  margin-top: 20px;
  text-align: center;
}

.book-code-inputs {
  margin-top: 20px;
  display: flex;
  align-items: center;
}

.book-code-inputs el-input {
  width: 60px;
  margin-right: 5px;
  /* 调整输入框之间的间距 */
}

.book-code-inputs span {
  margin: 0 5px;
}
</style>
