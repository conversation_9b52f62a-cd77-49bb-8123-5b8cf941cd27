<template>
  <div class="comp-con">
    <div class="search-bar-sty" :class="{ 'no-shadow': props.isNoShadow }">
      <div class="publishing-info-con" v-if="isPublisher">
        <img class="publisher-icon" @click="handleSearchCatalog" src="@/assets/images/home/<USER>" />
        <span class="publisher-name">中信中心出版社</span>
      </div>
      <div class="bar-inner-con">
        <div class="bar-inner-left">
          <input class="search-str-input" v-model="searchStr" placeholder="请输入关键词" />
        </div>
        <div class="bar-inner-right">
          <!-- 鼠标悬浮提示提示“目录搜索” -->
          <el-tooltip class="item" effect="dark" content="目录搜索" placement="bottom">
            <img class="search-option-icon" @click="handleSearchCatalog" src="@/assets/images/home/<USER>" fit="contain" />
          </el-tooltip>
          <div class="search-btn" @click="handleSearch">
            <img class="magnifier-icon" src="@/assets/images/home/<USER>" alt="" />
            <div class="search-btn-text">搜索</div>
          </div>
        </div>
      </div>
      <div class="bar-btn-con">
        <div class="bar-btn" @click="handleSearchAdvance">
          <div class="bar-btn-text">高级搜索</div>
          <img class="bar-btn-arrow" src="@/assets/images/home/<USER>" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="SearchBar">
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';

const searchStr = ref('');
// const emit = defineEmits(['search', 'searchCatalog']);
const router = useRouter();
const props = defineProps({
  isNoShadow: {
    type: Boolean,
    default: false,
  },
  isPublisher: {
    type: Boolean,
    default: false,
  },
  publisherId: {
    type: String,
    default: '',
  },
  publisherName: {
    type: String,
    default: '',
  },
  searchKey: {
    type: String,
    default: '',
  },
});
watch(
  () => props.searchKey,
  (nValue) => {
    searchStr.value = nValue
  }
)
function handleSearch() {
  router.push({
    path: '/search',
    query: {
      searchKey: searchStr.value,
    },
  });
}
import { ElMessage } from 'element-plus';
function handleSearchCatalog() {
  // 如果searchStr没有值的情况，弹出弹出框提示请输入关键词
  if (!searchStr.value) {
    ElMessage.error('请输入关键词'); // 使用 Element Plus 的 ElMessage 提示
    return;
  }
  router.push({
    path: '/search-catalog',
    query: {
      searchKey: searchStr.value,
    },
  });
}

function handleSearchAdvance(searchStr) {
  router.push({
    path: '/search-advanced',
    query: {
      searchKey: searchStr.value,
    },
  });
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index';
.comp-con {
  width: 100%;
  @extend .base-flex-row;
  justify-content: center;
  align-items: center;
  .search-bar-sty {
    width: 60%;
    height: 90px;
    z-index: 2;
    box-shadow: 0px 0px 12px 0px rgba(9, 102, 180, 0.16);
    border-radius: 16px;
    background: #ffffff;
    @extend .base-flex-row;
    justify-content: center;
    align-items: center;
    .publishing-info-con {
      @extend .base-flex-row;
      justify-content: center;
      align-items: center;
      .publisher-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin-right: 17px;
      }
      .publisher-name {
        width: 198px;
        height: 46px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 22px;
        color: #333333;
        line-height: 46px;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 21px;
      }
    }
    .bar-inner-con {
      width: 84%;
      height: 50px;
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #0966b4;
      margin-right: 16px;
      @extend .base-flex-row;
      justify-content: space-between;
      align-items: center;
      .bar-inner-left {
        flex: 1;
        .search-str-input {
          width: 95%;
          height: 100%;
          margin-left: 20px;
          border: none;
          outline: none;
          background: transparent;
          font-size: 18px;
        }
      }
      .bar-inner-right {
        margin-right: 8px;
        @extend .base-flex-row;
        justify-content: flex-end;
        align-items: center;
        .search-option-icon {
          width: 22px;
          height: 21px;
          margin-right: 16px;
          cursor: pointer;
        }
        .search-btn {
          width: 94px;
          height: 40px;
          background: #0966b4;
          border-radius: 8px;
          @extend .base-flex-row;
          justify-content: center;
          align-items: center;
          .magnifier-icon {
            width: 17px;
            height: 16px;
            margin-right: 3px;
            cursor: pointer;
          }
          .search-btn-text {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 18px;
            color: #ffffff;
            text-align: justify;
            font-style: normal;
            cursor: pointer;
          }
        }
        .search-btn:hover {
          opacity: 0.8;
        }
      }
    }
    .bar-btn-con {
      width: 126px;
      height: 50px;
      background: rgba(9, 102, 180, 0.12);
      border-radius: 8px;
      cursor: pointer;
      &:hover {
        background: rgba(9, 102, 180, 0.24);
      }
      @extend .base-flex-row;
      justify-content: center;
      align-items: center;
      .bar-btn {
        @extend .base-flex-row;
        justify-content: center;
        align-items: center;
        .bar-btn-text {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: rgba(9, 102, 180, 1);
          font-style: normal;
          margin-right: 5px;
        }
        .bar-btn-arrow {
          width: 7px;
          height: 12px;
          margin-left: 3px;
          margin-top: 3px;
        }
      }
    }
  }
  .no-shadow {
    box-shadow: 0px 0px 0px 0px;
  }
}
</style>

