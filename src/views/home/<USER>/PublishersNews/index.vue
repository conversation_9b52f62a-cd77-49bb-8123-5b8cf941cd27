<template>
  <div class="comp-con">
    <div class="comp-title-con">
      <img
        class="title-arrow-img"
        src="@/assets/images/home/<USER>"
        alt=""
      />
      <span class="title-text">{{ title }}</span>
      <img
        class="title-arrow-img"
        src="@/assets/images/home/<USER>"
        alt=""
      />
    </div>
    <!-- 01 出版社列表  -->
    <div class="publisher-list-con">
      <publisherListComp
        :dataList="publisherList"
        @changePublisher="doChangePublisher"
      />
    </div>
    <!-- 02 出版社图书跑马灯   -->
    <!-- <div class="data-list-con">
      <carouselComp2 :dataList="currentPublisherBooksList" :publisherId="currentPublisherId" />
    </div>-->

    <div class="data-list-con">
      <carouselComp
        :dataList="currentPublisherBooksList"
        :publisherId="currentPublisherId"
      />
    </div>

    <!-- 测试字体   -->
    <!-- <div class="data-list-con">
         <loadFont01 :dataList="currentPublisherBooksList" :publisherId="currentPublisherId" />
     </div>-->

    <!-- 03 跳转按钮 -->
    <div class="btn-con">
      <div class="btn" @click="gotoMore">进入专区</div>
    </div>
  </div>
</template>

<script setup name="PublishersNews">
import { ref } from "vue";
import carouselComp from "@/views/home/<USER>/PublishersNews/carouselComp.vue";
// import carouselComp2 from '@/views/home/<USER>/PublishersNews/carouselComp3.vue';
// import loadFont01 from '@/views/home/<USER>/PublishersNews/loadFont01.vue';
import publisherListComp from "@/views/home/<USER>/PublishersNews/publisherListComp.vue";

const router = useRouter();
const props = defineProps({
  componentData: {
    type: Object,
    default: {},
  },
});

let title = ref("出版社最新消息");
let publisherList = ref([]);
let currentPublisherBooksList = ref([]);

onMounted(() => {
  title.value = props.componentData.title || "出版社最新消息";
  publisherList.value = props.componentData.houses || [];
  currentPublisherBooksList.value = props.componentData.houses[0].books || [];
});

function gotoMore() {
  router.push({ path: props.componentData.toMoreRoute });
}

const currentPublisherId = ref(-1);

function doChangePublisher(index) {
  console.log("-----index-----");
  console.log(index);
  currentPublisherBooksList.value = [];
  currentPublisherBooksList.value =
    props.componentData.houses[index].books || [];
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/index";

.comp-con {
  width: 100%;
  height: 908px;
  background: url("../../../../assets/images/home/<USER>")
    no-repeat center;
  background-size: cover;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;

  .comp-title-con {
    width: 100%;
    overflow: hidden;
    margin-top: 106px;
    margin-bottom: 59px;
    @extend .base-flex-row;
    justify-content: center;
    align-items: center;

    .title-arrow-img {
      width: 172px;
      height: 24px;
    }

    .title-text {
      width: 182px;
      height: 26px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 26px;
      color: #333333;
      line-height: 26px;
      text-align: justify;
      font-style: normal;
      margin-left: 25px;
      margin-right: 25px;
    }
  }

  .publisher-list-con {
    width: 80%;
    height: 169px;
  }

  .data-list-con {
    width: 80%;
    // 显示动画
    @keyframes slideInFromLeft {
      0% {
        transform: translateX(-100%);
      }
      100% {
        transform: translateX(0);
      }
    }
  }

  .btn-con {
    cursor: pointer;

    .btn {
      width: 176px;
      height: 45px;
      border-radius: 8px;
      border: 1px solid #0966b4;
      text-align: center;
      line-height: 45px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #0966b4;
    }

    .btn:hover {
      transition: all 0.3s ease;
      background: #0966b4;
      color: #ffffff;
    }
  }
}
</style>
