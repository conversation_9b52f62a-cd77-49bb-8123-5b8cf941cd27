<template>
  <div class="comp-con">
    <div class="content-con">
      <div class="comp-title-con">
        <img class="title-arrow-img" src="@/assets/images/home/<USER>" alt="" />
        <span class="title-text">{{ title }}</span>
        <img class="title-arrow-img" src="@/assets/images/home/<USER>" alt="" />
      </div>
      <!-- 01 tab -->
      <div class="tab-head-con">
        <div class="tab-con">
          <div
              class="tab-item"
              :class="{ active: currentAciveTabIndex == index }"
              v-for="(item, index) in data.types"
              :key="index"
              @click="updateCurrentTabIndex(index)"
          >
            {{ item.typeName }}
          </div>
        </div>
        <div class="btn-con">
          <div class="circle-btn" :class="{ disabled: currentAciveTabIndex == 0 }" @click="changeEducational(-1)">
            <img class="btn-img" src="@/assets/images/home/<USER>" alt="" />
          </div>
          <div class="circle-btn" :class="{ disabled: currentAciveTabIndex == data.types?.length - 1 }" @click="changeEducational(1)">
            <img class="btn-img" src="@/assets/images/home/<USER>" alt="" />
          </div>
        </div>
      </div>
      <!-- 02 教材列表   -->
      <div class="data-list-con">
        <div class="books-list">
          <div class="item-card" v-for="(item, index) in books" @click="toDetail(item)" :key="index">
            <div class="list-item">
              <div class="item-left">
                <img class="item-img" :src="item.cover ? item.cover : bookCoverDefault" alt="" />
              </div>
              <div class="item-right">
                <div class="right-top">
                  <div class="item-name">{{ item.bookName }}</div>
                  <div class="item-author">{{ item.authorLabel + ':' + item.authorValue }}</div>
                  <div class="item-author">ISBN：{{ item.isbn }}</div>
                </div>
                <div class="right-bottom">
                  <div class="original-price">{{ '￥' + item.priceCounter }}</div>
                  <div class="selling-price">{{ '￥' + item.priceSale }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 03 跳转按钮 -->
      <div class="btn-con">
        <div class="btn" @click="toMore">查看更多</div>
      </div>
    </div>
  </div>
</template>

<script setup name="ProvincialRecommendedTextbooks">
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import bookCoverDefault from '@/assets/images/book-cover-default.png';
const router = useRouter();
//
const props = defineProps({
  componentData: {
    type: Object,
    default: {},
  },
});
let title = ref('');
const data = ref({});
let books = ref([]);
const currentAciveTabIndex = ref(0);

onMounted(() => {
  title.value = props.componentData.title || '省级教材推荐';
  data.value = props.componentData;
  books.value = data.value.types[currentAciveTabIndex.value].books;
});

function toMore() {
  router.push(data.value.toMoreRoute);
}
//
function toDetail(item) {
  router.push({ path: '/book-detail', query: { key: item.bookId } });
}
const updateCurrentTabIndex = (index) => {
  currentAciveTabIndex.value = index;
  books.value = data.value.types[currentAciveTabIndex.value].books;
};
const changeEducational = (stepValue) => {
  currentAciveTabIndex.value += stepValue;
  if (currentAciveTabIndex.value < 0) {
    currentAciveTabIndex.value = 0;
  } else if (currentAciveTabIndex.value > data.value.types.length - 1) {
    currentAciveTabIndex.value = data.value.types.length - 1;
  }
  books.value = data.value.types[currentAciveTabIndex.value].books;
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index';
.comp-con {
  width: 100%;
  min-height: 708px;
  background: url('../../../../assets/images/home/<USER>') no-repeat center;
  background-size: cover;
  @extend .base-flex-row;
  justify-content: center;
  align-items: center;
  .content-con {
    width: 1400px;
    @extend .base-flex-column;
    justify-content: flex-start;
    align-items: center;
    .comp-title-con {
      width: 75%;
      overflow: hidden;
      margin-top: 90px;
      margin-bottom: 79px;
      @extend .base-flex-row;
      justify-content: center;
      align-items: center;
      .title-arrow-img {
        width: 172px;
        height: 24px;
      }
      .title-text {
        height: 26px;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #333333;
        line-height: 26px;
        text-align: justify;
        font-style: normal;
        margin-left: 25px;
        margin-right: 25px;
      }
    }
    .tab-head-con {
      width: 75%;
      margin-bottom: 44px;
      @extend .base-flex-row;
      justify-content: space-between;
      align-items: center;
      .tab-con {
        @extend .base-flex-row;
        justify-content: flex-start;
        align-items: center;
        .tab-item {
          width: 104px;
          height: 34px;
          background: #e3e9ec;
          border-radius: 27px;
          font-family:
              PingFangSC,
              PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #333333;
          line-height: 34px;
          text-align: center;
          font-style: normal;
          margin-right: 30px;
          cursor: pointer;
        }
        & .active {
          transition: all 0.3s ease;
          background: #0966b4;
          border-radius: 27px;
          color: #ffffff;
        }
        .tab-item:hover {
          opacity: 0.8;
        }
      }
      .btn-con {
        @extend .base-flex-row;
        justify-content: space-between;
        align-items: center;
        .circle-btn {
          width: 34px;
          height: 34px;
          border-radius: 17px;
          background: #ffffff;
          box-shadow: 0px 0px 7px 0px rgba(201, 219, 237, 0.27);
          @extend .base-flex-row;
          justify-content: center;
          align-items: center;
          .btn-img {
            width: 7px;
            height: 12px;
          }
        }
        & .disabled {
          opacity: 0.49;
        }
        .circle-btn:first-child {
          margin-right: 25px;
        }
      }
    }
    .data-list-con {
      width: 100%;
      margin-bottom: 70px;
      .books-list {
        width: 100%;
        @extend .base-flex-row;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .item-card {
          width: 440px;
          height: 246px;
          background: #ffffff;
          box-shadow: 0px 0px 21px 0px #eaeaea;
          border-radius: 16px;
          padding-top: 16px;
          margin-bottom: 10px;
          @extend .base-flex-column;
          justify-content: space-between;
          align-items: center;
          .list-item {
            cursor: pointer;
            @extend .base-flex-row;
            justify-content: center;
            align-items: center;
            .item-left {
              width: 142px;
              height: 197px;
              margin-right: 21px;
              .item-img {
                width: 100%;
                height: 100%;
              }
            }
            .list-item:hover {
              opacity: 0.8;
            }
            .item-right {
              width: 234px;
              height: 197px;
              @extend .base-flex-column;
              justify-content: space-between;
              align-items: flex-start;
              .right-top {
                max-width: 100%;
                .item-name {
                  max-width: 100%;
                  font-family:
                      PingFangSC,
                      PingFang SC;
                  font-weight: 500;
                  font-size: 18px;
                  color: #333333;
                  text-align: left;
                  text-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
                  font-style: normal;
                  margin-bottom: 15px;
                }
                .item-author {
                  max-width: 100%;
                  font-family:
                      PingFangSC,
                      PingFang SC;
                  font-weight: 400;
                  font-size: 14px;
                  color: #999999;
                  text-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
                  text-align: left;
                  font-style: normal;
                  margin-bottom: 15px;
                }
              }
              .right-bottom {
                align-self: flex-end;
                @extend .base-flex-row;
                justify-content: flex-start;
                align-items: center;
                .original-price {
                  margin-right: 14px;
                  font-family:
                      PingFangSC,
                      PingFang SC;
                  font-weight: 400;
                  font-size: 12px;
                  color: #999999;
                  line-height: 12px;
                  text-align: center;
                  font-style: normal;
                  text-decoration: line-through;
                }
                .selling-price {
                  font-family:
                      PingFangSC,
                      PingFang SC;
                  font-weight: 500;
                  font-size: 16px;
                  color: #0966b4;
                  line-height: 16px;
                  text-align: center;
                  font-style: normal;
                }
              }
            }
          }
        }
        .item-card:nth-child(3n) {
          margin-right: 0;
        }
      }
    }
    .btn-con {
      cursor: pointer;
      .btn {
        width: 176px;
        height: 45px;
        line-height: 45px;
        border-radius: 8px;
        border: 1px solid #0966b4;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #0966b4;
        text-align: center;
        font-style: normal;
      }
      .btn:hover {
        transition: all 0.3s ease;
        background: #0966b4;
        color: #ffffff;
      }
    }
  }
}
</style>
