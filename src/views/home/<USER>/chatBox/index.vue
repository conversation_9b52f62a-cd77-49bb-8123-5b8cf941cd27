<template class="chatBox">
  <teleport to="body">
    <div v-if="visible" class="chat-window" :style="windowStyle">
      <div class="chat-header">
        <!-- 左对齐部分（图片和标题） -->
        <div class="left-section">
          <img class="card-head-img" src="@/assets/images/im/customer.png" alt=""/>
          <span class="chat-title">客服</span>
        </div>

        <!-- 右对齐部分（关闭按钮） -->
        <button @click="closeWindow" class="close-btn">×</button>
      </div>

      <div class="chat-body" ref="refMessList">
        <div :class="getItemClass(msg.userType)" v-for="(msg, index) in messages" :key="index">
          <span v-if="msg.userType == 1" class="user">{{ msg.nickName }}&nbsp;{{ msg.createTime }}</span>
          <span v-else class="user">&nbsp;{{ msg.createTime }}</span>


          <div class="message-row">
            <div class="message-status" v-if="msg.userType == 2">
              <span v-if="msg.readFlag == 0"> 未读 </span>
              <span v-else> 已读 </span>
            </div>

            <div :class="getItemMesClass(msg.userType)">
              <div v-if="msg.contentType == 1" class="message-text">{{ msg.chatContent }}</div>
              <div v-else class="message-text">
                <div class="img-con" v-if="msg.chatContent">
                  <!-- 如果文件是图片 -->
                  <div v-if="selectTypeName(msg.chatContent.split('.').pop()) == 'jpg'">
                    <img style="width: 150px;height: auto;display: block;" :src="msg.chatContent" @click="showPreview(msg.chatContent,1)"/>
                  </div>
                  <!-- 如果文件是音频 -->
                  <div v-if="selectTypeName(msg.chatContent.split('.').pop()) == 'mp3'">
                    <audio controls :src="msg.chatContent"></audio>
                  </div>
                  <!--如果文件是视频-->
                  <div v-if="selectTypeName(msg.chatContent.split('.').pop()) == 'avi'">
                    <video style="width: 200px;height: auto;display: block;" @click="showPreview(msg.chatContent,2)" controls :src="msg.chatContent" class="modal-content"></video>
                  </div>

                  <!-- 如果文件是文档 -->
                  <div v-if="selectTypeName(msg.chatContent.split('.').pop()) == 'pdf' || selectTypeName(msg.chatContent.split('.').pop()) == 'doc' || selectTypeName(msg.chatContent.split('.').pop()) == 'ppt' || selectTypeName(msg.chatContent.split('.').pop()) == 'xls'">
                    <img class="icon" v-if="selectTypeName(msg.chatContent.split('.').pop()) == 'pdf'" src="@/assets/svg/pdf.svg" @click="showFile(msg.chatContent)"/>
                    <img class="icon" v-if="selectTypeName(msg.chatContent.split('.').pop()) == 'doc'" src="@/assets/svg/word.svg" @click="showFile(msg.chatContent)"/>
                    <img class="icon" v-if="selectTypeName(msg.chatContent.split('.').pop()) == 'ppt'" src="@/assets/svg/ppt.svg" @click="showFile(msg.chatContent)"/>
                    <img class="icon" v-if="selectTypeName(msg.chatContent.split('.').pop()) == 'xls'" src="@/assets/svg/xls.svg" @click="showFile(msg.chatContent)"/>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="emoji-icons">
        <!-- 表情选择器 -->
        <div class="message-mojo">
          <div>
            <el-popover
                placement="top-start"
                :visible = "popoverEmojiVisible"
                ref="popover"
                width="400"
                trigger="click">
              <template v-slot:reference>
                <!-- 图片点击触发 -->
                <img src="@/assets/images/smileface.png" @click="toggleEmojiPicker" />
              </template>

              <!-- 表格内容 -->
              <div class="bottom-emote" v-for="(item,index) in emojis" :key="index">
                <span @click="handleSelectEmote(items)" class="bottom-emote-item" v-for="(items,index) in item.desc.split(' ')">{{items}}</span>
              </div>
            </el-popover>
          </div>
        </div>
        <div @click="openFileSelector">
          <img src="@/assets/images/folder.png"/>
          <!-- 文件输入框 -->
          <el-upload
              ref="fileInput"
              action="#"
              style="display: none;"
              :http-request="handleFileChange"
          ></el-upload>
        </div>
<!--        <div>-->
<!--          <img src="@/assets/images/picture.png"/>-->
<!--        </div>-->
      </div>
      <!-- 表情选择器 -->
      <div class="chat-footer">
        <div class="chat-container">
          <textarea
              v-model="newMessage"
              class="message-input"
              placeholder="很高兴为您服务，请输入描述您的问题"
              @keyup.enter="sendUserMessage"
              maxlength="70"
          ></textarea>
          <button class="send-button" @click="sendUserMessage">发送</button>
        </div>
      </div>
      <el-dialog title="预览" v-model="isPreviewVisible" width="50%" style="border-radius: 10px" append-to-body>
        <div class="modal-content-wrapper">
          <img v-if="showImg" style="width: 90%;height: auto;display: block;" :src="previewImage" class="modal-content" />
          <video v-else style="width: 90%;height: auto;display: block;" controls :src="previewImage" class="modal-content"></video>
        </div>
      </el-dialog>
    </div>
  </teleport>
</template>

<script setup>
import {ref} from 'vue';
import {OssService} from "@/utils/aliOss.js";
import {previewFile} from "@/utils/index.js";
import {emojis} from "@/utils/optionUtil.js";


const {proxy} = getCurrentInstance();
import {changeMessageState, getDetailList, initChatDetail, sendMes} from '@/api/im/chat.js';
import Vue3EmojiPicker from 'vue3-emoji-picker';
import 'vue3-emoji-picker/css';
import {getToken} from "@/utils/auth.js";
import {chatAiByPrompt} from "@/api/book/ai.js";

// 控制窗口显示
const visible = ref(true);

// 消息列表
// const messages = ref([]);
const messages = ref([
  {user: '智慧教育云客服', time:formatNowDate(), type:1, text: '你好，请问有什么可以帮助您的？'},
]);
// 输入框的内容
const newMessage = ref('');
const lastNewMessage = ref('');
const nowTime = ref('');
const chatId = ref('');
const timer = ref(0)
const isPreviewVisible = ref(false)
const previewImage = ref('')
const popoverEmojiVisible = ref(false);
const showImg = ref(false);

// 控制表情选择器显示
const showEmojiPicker = ref(false);

// 显示最新数据
const refMessList = ref(null);

function getItemClass(type) {
  switch (type) {
    case 1:
      return 'left-align';
    case 2:
      return 'right-align';
    default:
      return 'left-align';
  }
}

// 获取消息列表并滚动到最底部
const scrollToBottom = () => {
  setTimeout(() => {
    if (refMessList.value) {
      refMessList.value.scrollTop = refMessList.value.scrollHeight;
    }
  }, 50); // 50毫秒可以适当调整
};

// watch(messages, () => {
//   nextTick(() => {
//     scrollToBottom();
//   });
// });

function showFile(url) {
  previewFile(url)
}

function getItemMesClass(type) {

  switch (type) {
    case 1:
      return 'left-mes';
    case 2:
      return 'right-mes';
    default:
      return 'left-mes';
  }
}

function showPreview(url,flag){
  isPreviewVisible.value = true
  previewImage.value = url
  if (flag == 1) {
    showImg.value = true
  } else {
    showImg.value = false
  }
}

// 聊天窗口样式
const windowStyle = ref({
  position: 'fixed',
  bottom: '20px',
  right: '130px',
  width: '350px',
  height: '500px',
  border: '1px solid #ddd',
  borderRadius: '10px',
  backgroundColor: '#fff',
  boxShadow: '0px 0px 15px rgba(0, 0, 0, 0.1)',
});

function formatNowDate() {
  const now = new Date();

  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

onMounted(() => {
  if (getToken()) {
    // 更新对话已读未读
    nextTick(() => {
      nowTime.value = formatNowDate();
      getDetail()
    });

    scrollToBottom()
    timer.value = setInterval(() => {
      getDetail()
      changeReadStatus()
    }, 5000); // 10秒轮询一次
  } else {
    // 跳转登录页
    proxy.$router.push({ path: '/login' });
  }
})

/** 初始化聊天*/
const initChat = () => {
  let param = {}
  param.chatContent = lastNewMessage.value
  initChatDetail(param)
  .then((res) => {
    lastNewMessage.value = null
    // newMessage.value = null
    getDetail()
  })
  .catch((error) => {
  });

}

const changeReadStatus = () => {
  let param = {}
  param.userType = 2
  param.chatStatus = 2
  changeMessageState(param).then(response => {
    console.log(response)
    const res = response.data
    // 解析JSON字符串
    const jsonObject = JSON.parse(res);
    // 获取suggest节点的值
    const suggest = jsonObject.data.result.suggest;
    if (suggest == 'block') {
      return false
    } else {
      return true
    }
  })

}

/** 上传文件选择 */
function openFileSelector(){
  proxy.$refs.fileInput.$el.querySelector('input').click();
}

/** 合规*/
function checkMessage() {
  const param = {}
  param.ability = 19
  param.checkBlackWhite = false
  param.question = newMessage.value.replace(/\n+$/, '')
  chatAiByPrompt(param).then(response => {
    console.log(response)
  })

}

/** 发送按钮 */
const sendUserMessage = () => {
  /** 合规*/
  // if (!checkMessage()) {
  //   proxy.$modal.msgError("内容包含潜在风险信息和敏感词汇,不允许发送");
  // }
  lastNewMessage.value = newMessage.value
  if (!newMessage.value.replace(/\n+$/, '')) {
    proxy.$modal.msgError("不能发送空白信息");
    return false;
  } else {
    let param = {}
    param.userType = 2
    param.chatContent = newMessage.value.replace(/\n+$/, '');
    param.contentType = 1
    param.readFlag = 0

    sendMes(param)
    .then((res) => {
      newMessage.value = null
      initChat()
      scrollToBottom();
    })
    .catch((error) => {
    });
  }
};

/** 获取聊天记录 */
const getDetail = () => {
  let param = {}
  param.userType = 2
  getDetailList(param).then((res) => {
    messages.value = res.data
  })
  .catch((error) => {
  });
}

/** 关闭窗口 */
const closeWindow = () => {
  visible.value = false;
};

const selectTypeName = computed(() => {
  return function (extension) {
    if (!extension) return null;

    if (extension === 'doc' || extension === 'docx') {
      return 'doc';
    }

    if (extension === 'pdf') {
      return 'pdf';
    }

    if (extension === 'ppt' || extension === 'pptx') {
      return 'ppt';
    }

    if (extension === 'xls' || extension === 'xlsx') {
      return 'xls';
    }

    if (extension === 'txt') {
      return 'txt';
    }

    if (extension === 'zip') {
      return 'zip';
    }

    if (extension === 'rar') {
      return 'rar';
    }

    if (extension === 'mp3') {
      return 'mp3';
    }

    if (extension === 'mp4' || extension === 'avi' || extension === 'mov') {
      return 'avi';
    }

    if (extension === 'jpg' || extension === 'jpeg' || extension === 'png' || extension === 'gif') {
      return 'jpg';
    }
  };
});

/** 切换表情选择器显示 */
const toggleEmojiPicker = (event) => {
  event.stopPropagation();
  popoverEmojiVisible.value = !popoverEmojiVisible.value
};

function handleSelectEmote(item){
  newMessage.value = newMessage.value || '';
  newMessage.value == null?item:newMessage.value += item;
  popoverEmojiVisible.value = false
}

function handleFileChange(event){
  const res = change(event)
}

const change = async (file) => {
  try {
    // 工具类引用
    const res = await OssService(file.file, (e) => {
      // 返回进度条，做进度条处理
      console.log(e);
    });
    let param = {
      userType: 2,
      chatContent: res.url,
      contentType: 2,
      readFlag: 0,
    };
    sendMes(param)
        .then((res) => {
          newMessage.value = null
          initChat()
        })
        .catch((error) => {
        });
    return res;
  } catch (e) {
    console.log(e, '失败');
    throw e;
  }
}

onUnmounted(() => {
  // 清除定时器以避免内存泄漏
  clearInterval(timer.value);
})

</script>

<style scoped lang="scss">
.chat-window {
  display: flex;
  flex-direction: column;
  font-family: Arial, sans-serif;
  z-index: 60;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #0966b4;
  color: white;
  font-weight: bold;
  width: 100%;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.left-section {
  display: flex;
  align-items: center;
}

.chat-title {
  font-size: 16px;
  margin-left: 10px;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  margin-left: auto;
}

.chat-body {
  flex-grow: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f4f4f4;
  border-bottom: 1px solid #ddd;
  height: 100%;
}

.left-align {
  margin-bottom: 10px;
  text-align: left;
}

.right-align {
  margin-bottom: 10px;
  text-align: right;
}

.user {
  font-weight: bold;
  display: block;
}

.emoji-icons {
  display: flex;
  padding: 10px;
  background-color: #f1f1f1;
  justify-content: flex-start;
  gap: 15px;
}

.emoji-icons img {
  width: 20px;
  height: 20px;
}


.chat-footer {
  display: flex;
  width: 100%;
  background-color: #f1f1f1;
  justify-content: space-between;
}

.file-input {
  display: none;
}

.chat-container {
  position: relative;
  width: 100%;
}

.message-input {
  width: 100%;
  height: 100px;
  border: none;
  background-color: #f1f1f1;
  padding: 10px;
  resize: none;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  font-size: 16px;
  outline: none;
}

.send-button {
  position: absolute;
  bottom: 10px;
  right: 10px;
  padding: 8px 12px;
  background-color: #0966b4;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.send-button:hover {
  background-color: #007bff;
}

.left-mes {
  display: inline-block;
  padding: 10px;
  border-radius: 5px;
  margin-top: 10px;
  background-color: white;
  max-width: 80%;
  word-wrap: break-word;
  white-space: normal;
}

.right-mes {
  display: inline-block;
  padding: 10px;
  border-radius: 5px;
  margin-top: 10px;
  background-color: #0966b4;
  color: white;
  max-width: 80%;
  word-wrap: break-word;
  white-space: normal;
}

.card-head-img {
  width: 25px;
  height: 25px;
  margin-right: 10px;
}

.message-row {
  align-items: center;
  gap: 10px;
}

.message-status {
  display: inline-block;
  font-size: 14px;
  padding: 10px;
  color: #777;
}

.emoji-picker {
  position: absolute;
  bottom: 60px;
  left: 10px;
  z-index: 999;
}
.chatBox{
  position: fixed;
  z-index: 999999
}

.bottom-emote{
  display:grid;
  grid-template-columns: repeat(15,1fr);
  gap:5rpx;
  margin-top: 20rpx;

}

.bottom-emote-item {
  cursor: pointer;
  transition: transform 0.3s;
}

.bottom-emote-item:hover {
  transform: scale(1.5);
}

.bottom-emote-item:active {
  transform: scale(0.95);
}
</style>
