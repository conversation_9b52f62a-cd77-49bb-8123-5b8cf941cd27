<template>
  <div class="comp-container">
    <div class="comp-card">
      <div class="title-con">
        <div class="title">{{ title }}</div>
        <div class="num-popup">
          {{ bookList ? bookList.length : '' }}
        </div>
      </div>
      <div class="book-list-con">
        <div class="book-cell" v-for="(item, index) in bookList" :key="index" @click="handleClick(item)">
          <div class="cell-left"><img class="book-img" :src="item.cover ? item.cover : bookCoverDefault" alt="" /></div>
          <div class="cell-right">
            <div class="book-name">{{ item.bookName }}</div>
            <div class="author-name">编辑：{{ item.bookName }}</div>
            <div class="isbn">ISBN：{{ item.bookName }}</div>
            <div class="price-con">
              <div class="price-con-left">
                <div class="price-sale">￥{{ item.priceSale }}</div>
                <div class="price-counter">￥{{ item.priceCounter }}</div>
              </div>
              <div class="price-con-right">
                <div class="read-num-icon">
                  <el-icon><UserFilled /></el-icon>
                  <!--  <img src="@/assets/images/read-num-icon.png" alt="" />-->
                </div>
                <div class="read-quantity">{{ item.readQuantity }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import bookCoverDefault from '@/assets/images/book-cover-default.png';
import { listByQuery } from '@/api/shop/book.js';
import { toLoginPage } from '@/api/home/<USER>';
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
// import useSiteStore from '@/store/modules/site';
// import { encryptData } from '@/utils/encrypt.js';
const route = useRoute();
const router = useRouter();
let title = ref('');
let schoolId = ref(0);
let bookList = ref([]);

const toLogin = () => {
  toLoginPage();
};

// 点击事件处理器
const handleClick = async (item) => {
  // const publicKey = useSiteStore().publicKey;
  // if (!publicKey) {
  //   console.error('无法获取公钥');
  //   return;
  // }
  // // 使用公钥加密数据
  // const { encryptedData } = await encryptData(item.bookId, publicKey);

  // if (!encryptedData) {
  //   console.error('数据加密失败');
  //   return;
  // }
  router.push({ path: '/book-detail', query: { key: item.bookId } });
};

onMounted(() => {
  title.value = route.query.schoolName;
  schoolId.value = route.query.schoolId;
  //
  const query = { schoolId: schoolId.value };
  // 点击事件处理器
  listByQuery(query)
    .then((res) => {
      if (res.code === 200) {
        if (res.data) {
          bookList.value = res.data;
        }
      }
    })
    .catch((error) => {
    });
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index';
@import '@/assets/styles/common.css';

.comp-container {
  width: 100%;
  padding-bottom: 100px;
  @extend .base-flex-row;
  justify-content: center;
  align-items: center;
  .comp-card {
    margin-top: 50px;
    @extend .base-comp-card;
    .title-con {
      margin-bottom: 20px;
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: flex-start;
      .title {
        height: 20px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 20px;
        color: #333333;
        line-height: 20px;
      }
      .num-popup {
        margin-top: -10px;
        margin-left: 5px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        background-color: rgba(9, 102, 180, 1);
        border-radius: 9px 9px 9px 0px;
        min-height: 16px;
        min-width: 25px;
        text-align: center;
        line-height: 16px;
        padding: 2px 5px;
      }
    }
    .book-list-con {
      width: 100%;
      padding-bottom: 100px;
      @extend .base-flex-row;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      .book-cell {
        margin-bottom: 20px;
        width: 333px;
        height: 192px;
        background: #eff8ff;
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0px 0px 3px 0px rgb(210, 226, 239);
        @extend .base-flex-row;
        justify-content: center;
        align-items: center;
        .cell-left {
          background: #d8d8d8;
          box-shadow: 0px 0px 4px 0px rgba(82, 86, 89, 0.42);
          border-radius: 5px;
          margin-right: 16px;
          .book-img {
            width: 101px;
            height: 144px;
            border-radius: 5px;
          }
        }
        .cell-right {
          flex: 1;
          @extend .base-flex-column;
          justify-content: center;
          align-items: flex-start;
          .book-name {
            width: 100%;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            line-height: 22px;
            text-align: left;
            margin-bottom: 10px;
          }
          .author-name {
            width: 100%;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #333333;
            text-align: left;
            margin-bottom: 10px;
          }
          .isbn {
            width: 100%;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #333333;
            text-align: left;
            margin-bottom: 39px;
          }
          .price-con {
            width: 100%;
            @extend .base-flex-row;
            justify-content: space-between;
            align-items: center;
            .price-con-left {
              @extend .base-flex-row;
              justify-content: flex-start;
              align-items: center;
              .price-sale {
                min-width: 44px;
                height: 12px;
                font-family:
                  PingFangSC,
                  PingFang SC;
                font-weight: 500;
                font-size: 12px;
                color: #ff5454;
                line-height: 12px;
                text-align: left;
                margin-right: 3px;
              }
              .price-counter {
                font-family:
                  PingFangSC,
                  PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #999999;
                line-height: 12px;
                text-align: right;
                text-decoration: line-through;
              }
            }
            .price-con-right {
              @extend .base-flex-row;
              justify-content: flex-end;
              align-items: center;
              .read-num-icon {
                width: 13px;
                height: 13px;
                color: #0966b4;
                margin-right: 5px;
              }
              .read-quantity {
                height: 12px;
                font-family:
                  PingFangSC,
                  PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #0966b4;
                line-height: 12px;
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
}

.group_4 {
  background-color: rgba(239, 248, 255, 1);
  border-radius: 8px;
  width: 333px;
  height: 192px;
}

.group_5 {
  box-shadow: 0px 0px 4px 0px rgba(82, 86, 89, 0.42);
  height: 144px;
  width: 101px;
  margin: 24px 0 0 24px;
}

.box_2 {
  border-radius: 5px;
  width: 101px;
  height: 144px;
}

.group_6 {
  width: 168px;
  height: 139px;
  margin: 26px 24px 0 0;
}

.paragraph_1 {
  width: 168px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 14px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: justify;
  line-height: 22px;
}

.text_12 {
  width: 116px;
  height: 12px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 12px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 12px;
  margin-top: 10px;
}

.text_13 {
  width: 161px;
  height: 12px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 12px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 12px;
  margin-top: 10px;
}

.block_1 {
  position: relative;
  width: 168px;
  height: 13px;
  margin-top: 38px;
}

.text_14 {
  width: 44px;
  height: 12px;
  overflow-wrap: break-word;
  color: rgba(255, 84, 84, 1);
  font-size: 12px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: justify;
  white-space: nowrap;
  line-height: 12px;
}

.text_15 {
  width: 44px;
  height: 12px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 12px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 12px;
  margin: 1px 0 0 4px;
}

.thumbnail_2 {
  width: 13px;
  height: 13px;
  margin-left: 55px;
}

.text_16 {
  width: 5px;
  height: 12px;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 12px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 12px;
  margin: 1px 0 0 3px;
}

.image_1 {
  position: absolute;
  left: 47px;
  top: 6px;
  width: 46px;
  height: 1px;
}

.section_2 {
  width: 1398px;
  height: 192px;
}

.flex-row {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.section_1 {
  width: 232px;
  height: 28px;
}

.text-wrapper_2 {
  background-color: rgba(9, 102, 180, 1);
  border-radius: 9px 9px 9px 0px;
  height: 16px;
  width: 25px;
}

.text_10 {
  width: 200px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 20px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 20px;
  margin-top: 8px;
}

.text_11 {
  width: 9px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 12px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 14px;
  margin: 1px 0 0 8px;
}

.text-strike {
  text-decoration: line-through;
}
</style>
