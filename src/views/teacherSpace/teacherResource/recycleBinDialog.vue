<template>
    <el-dialog
        title="空间回收站"
        :model-value="visible"
        @update:modelValue="$emit('update:visible', $event)"
        width="70%"
        top="5vh"
        append-to-body
        :destroy-on-close="true"
    >
        <div class="recycle-bin-dialog">
            <div class="tool">
                <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
                    <el-form-item label="名称" prop="name">
                        <el-input
                            v-model="queryParams.name"
                            placeholder="请输入文件名称"
                            clearable
                            @keyup.enter="handleQuery"
                        />
                    </el-form-item>
                    <el-form-item label="类型" prop="fileType">
                        <el-select v-model="queryParams.fileType" placeholder="请选择类型" clearable style="width: 200px">
                            <el-option label="全部" value="" />
                            <el-option label="文件夹" value="folder" />
                            <el-option
                                v-for="(type, index) in fileTypes"
                                :key="index"
                                :label="type.label"
                                :value="type.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>

                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button
                            type="primary"
                            plain
                            icon="RefreshLeft"
                            :disabled="multiple"
                            @click="handleRestore"
                        >还原</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            type="danger"
                            plain
                            icon="Delete"
                            :disabled="multiple"
                            @click="handleDelete"
                        >彻底删除</el-button>
                    </el-col>
                    <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
            </div>

            <div v-loading="loading" class="recycle-list">
                <el-table
                    :data="recycleDataList"
                    @selection-change="handleSelectionChange"
                    height="60vh"
                >
                    <el-table-column type="selection" width="55" align="center" />
                    <el-table-column label="序号" width="55" align="center">
                        <template #default="scope">
                            {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
                        </template>
                    </el-table-column>
                    <el-table-column label="名称" align="center" prop="fileName" />
                    <el-table-column label="类型" align="center" prop="type">
                        <template #default="scope">
                            <span v-if="scope.row.type === 'folder'">文件夹</span>
                            <span v-else>{{ getFileTypeName(scope.row.fileType) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="原位置" align="center" prop="folderName" />
                    <el-table-column label="删除时间" align="center" prop="updateTime" width="180">
                        <template #default="scope">
                            <span>{{ parseTime(scope.row.updateTime) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" width="180">
                        <template #default="scope">
                            <el-button
                                type="primary"
                                link
                                icon="RefreshLeft"
                                @click="handleRestore(scope.row)"
                            >还原</el-button>
                            <el-button
                                type="danger"
                                link
                                icon="Delete"
                                @click="handleDelete(scope.row)"
                            >彻底删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination
                    v-show="total > 0"
                    :total="total"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"
                    @pagination="getList"
                />
            </div>
        </div>
    </el-dialog>
</template>

<script setup name="RecycleBinDialog">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from 'vue'
import { recycleList, restore, permanentDelete } from '@/api/edu/moocResource'

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['update:visible', 'restored']);

const { proxy } = getCurrentInstance();

// 文件类型定义
const fileTypes = [
    { label: '图片', value: '1' },
    { label: '音频', value: '2' },
    { label: '视频', value: '3' },
    { label: '虚拟仿真', value: '4' },
    { label: 'AR/VR', value: '5' },
    { label: '3D模型', value: '6' },
    { label: '课件', value: '8' }
];

const loading = ref(false);
const showSearch = ref(true);
const multiple = ref(true);
const total = ref(0);
const recycleDataList = ref([]);
const ids = ref([]);

const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        fileType: undefined
    }
});

const { queryParams } = toRefs(data);

/** 查询回收站列表 */
function getList() {
    loading.value = true;
    recycleList(queryParams.value).then(response => {
        recycleDataList.value = response.rows;
        total.value = response.total;
        loading.value = false;
    }).catch(error => {
        console.error("获取回收站列表失败:", error);
        proxy.$modal.msgError("获取列表失败");
        loading.value = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 还原按钮操作 */
function handleRestore(row) {
    const itemIds = row.resourceId ? [row.resourceId] : ids.value;
    if (itemIds.length === 0) return;
    proxy.$modal.confirm('是否确认还原选中的数据项？').then(function() {
        restore(itemIds.join(',')).then(() => {
            proxy.$modal.msgSuccess("还原成功");
            getList();
            emit('restored');
        }).catch(() => {
            proxy.$modal.msgError("还原失败");
        });
    }).catch(() => {});
}

/** 彻底删除按钮操作 */
function handleDelete(row) {
    const itemIds = row.resourceId ? [row.resourceId] : ids.value;
    if (itemIds.length === 0) return;
    proxy.$modal.confirm('是否确认彻底删除选中的数据项？删除后将无法恢复！').then(function() {
        permanentDelete(itemIds.join(',')).then(() => {
            proxy.$modal.msgSuccess("删除成功");
            getList();
        }).catch(() => {
            proxy.$modal.msgError("删除失败");
        });
    }).catch(() => {});
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.resourceId);
    multiple.value = !selection.length;
}

/** 获取文件类型名称 */
function getFileTypeName(type) {
    const fileType = fileTypes.find(item => item.value === type);
    return fileType ? fileType.label : '未知类型';
}

onMounted(() => {
    getList();
})
</script>

<style lang="scss" scoped>
.tool {
    padding: 0 0 10px;
}

.recycle-list {
    // padding: 20px;
}
</style> 