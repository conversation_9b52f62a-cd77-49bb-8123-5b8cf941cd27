<template>
    <div class="app-container">
        <TeacherSpaceTabs />
        <Cards>
            <div class="tool" style="position: relative;">
                <div class="top-right-container">
                    <el-button
                        type="primary"
                        link
                        icon="Delete"
                        @click="goToRecycleBin"
                    >空间回收站</el-button>
                    <div class="space-usage" v-if="userSpaceInfo.totalSpace > 0">
                        <el-progress
                            :percentage="userSpaceInfo.usagePercentage"
                            :stroke-width="15"
                            :text-inside="true"
                            color="#409EFF"
                        >
                        </el-progress>
                        <div class="space-text">
                            <span>{{ formatSize(userSpaceInfo.usedSpace) }} / {{ formatSize(userSpaceInfo.totalSpace) }}</span>
                        </div>
                    </div>
                </div>
                <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
                    <el-form-item label="名称" prop="folderName">
                        <el-input
                            v-model="queryParams.folderName"
                            placeholder="请输入文件夹名称"
                            clearable
                            @keyup.enter="handleQuery"
                        />
                    </el-form-item>
                
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-dropdown @command="handleAddFile">
                            <el-button type="primary" plain>
                                添加文件<el-icon class="el-icon--right"><arrow-down /></el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item 
                                        v-for="(type, index) in fileTypes" 
                                        :key="index"
                                        :command="type.value"
                                    >
                                        {{ type.label }}
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </el-form-item>
                    <el-form-item>
                        <el-button
                            type="primary"
                            plain
                            icon="Plus"
                            @click="handleAdd"
                    
                        >新建文件夹</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button
                            type="danger"
                            plain
                            icon="Delete"
                            :disabled="multiple"
                            @click="handleDelete"
                        >删除</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button
                            type="warning"
                            plain
                            icon="Rank"
                            :disabled="multiple"
                            @click="handleOpenBulkMoveDialog"
                
                        >批量移动</el-button>
                    </el-form-item>
                    <el-form-item>
                        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                    </el-form-item>
                </el-form>
            </div>
        </Cards>


        <Cards class="top20">
            <!-- 添加前进后退按钮和面包屑导航 -->
            <div class="navigation-bar">
                <div class="nav-buttons">
                    <el-button 
                        icon="ArrowLeft" 
                        :disabled="historyIndex <= 0"
                        @click="handleBack"
                    >后退</el-button>
                    <el-button 
                        icon="ArrowRight"
                        :disabled="historyIndex >= folderHistory.length - 1"
                        @click="handleForward"
                    >前进</el-button>
                </div>
                <el-breadcrumb separator="/" class="breadcrumb-nav">
                    <el-breadcrumb-item 
                        v-for="(folder, index) in folderPath" 
                        :key="index"
                       class="clickable"
                        @click="handleBreadcrumbClick(folder.id)"
                    >
                        {{ folder.name }}
                    </el-breadcrumb-item>
                </el-breadcrumb>
            </div>

            <div v-loading="loading" style="min-height: 200px">
                <div v-if="hasContent" class="folder-grid">
                    <div v-for="item in [...userResourceFolderList, ...userResourceList]"
                         :key="item.type === 'folder' ? `folder_${item.resourceId}` : `file_${item.resourceId}`"
                         class="folder-item"
                         @click="handleFolderClick(item)">
                         <div class="folder-checkbox" @click.stop v-if="item.defaultType != '1'">
                            <el-checkbox
                                v-model="item.isSelected"
                                @change="(val) => handleSelectionChange(item, val)"
                            />
                        </div>
                        <div class="folder-content">
                            <el-popover
                                v-if="item.defaultType == '0' || !item.defaultType"
                                placement="bottom"
                                trigger="hover"
                                popper-style="width:90px;min-width:90px;"
                                v-model:visible="item.popoverVisible">
                                <template #reference>
                                    <el-icon class="more-icon" @click.stop="item.popoverVisible=!item.popoverVisible"><More /></el-icon>
                                </template>
                                <div class="popover-buttons">
                                    <el-button link type="primary" @click.stop="handleMove(item)">移动</el-button>
                                    <el-button link type="primary" @click.stop="handleRename(item)">重命名</el-button>
                                </div>
                            </el-popover>
                            <template v-if="item.type === 'folder'">
                                <el-icon size="70" class="folder-icon"><Folder /></el-icon>
                            </template>
                            <template v-else>
                                <div v-if="item.fileType === '1'" class="preview-container">
                                    <img
                                        :src="item.fileUrl"
                                        :alt="item.fileName"
                                        class="preview-image"
                                    />
                                </div>
                                <div v-else-if="item.fileType === '3'" class="preview-container">
                                    <video
                                        :src="item.fileUrl"
                                        class="preview-video"
                                        preload="metadata"
                                    >
                                        <el-icon size="70"><VideoCamera /></el-icon>
                                    </video>
                                </div>
                                <el-icon v-else size="70" class="folder-icon">
                                    <Headset v-if="item.fileType === '2'" />
                                    <Monitor v-else-if="item.fileType === '4'" />
                                    <Monitor v-else-if="item.fileType === '5'" />
                                    <Box v-else-if="item.fileType === '6'" />
                                    <QuestionFilled v-else-if="item.fileType === '7'" />
                                    <Document v-else-if="item.fileType === '8'" />
                                    <Document v-else />
                                </el-icon>
                            </template>
                            <div class="folder-name">{{ item.folderName || item.fileName }}</div>
                        </div>
                    </div>
                </div>
                <el-empty v-else-if="!loading" description="该文件夹为空" style="padding-top: 60px" />
            </div>

            <pagination
                v-show="total>0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </Cards>

        <!-- 添加或修改个人资源文件夹对话框 -->
        <el-dialog :title="title" v-model="open" width="400px" append-to-body>
            <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
                <el-form-item :label="form.type === 'folder' ? '文件夹名称' : '文件名称'" prop="name">
                    <el-input 
                        v-model="form.name"
                        :placeholder="form.type === 'folder' ? '请输入文件夹名称' : '请输入文件名称'"
                        maxlength="20"
                        show-word-limit
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 移动文件夹对话框 -->
        <el-dialog title="移动到" v-model="moveDialogVisible" width="400px">
            <el-form ref="moveFormRef" :model="moveForm" label-width="80px">
                <el-cascader v-model="moveForm.parentId"
                    :options="folderTreeOptions"
                    :props="{value:'userFolderId',label:'folderName',checkStrictly:true}">
                </el-cascader>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitMove">确 定</el-button>
                    <el-button @click="moveDialogVisible = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 添加图片对话框 -->
        <el-dialog title="添加图片" v-model="imageDialogVisible" width="500px">
            <el-form ref="imageFormRef" :model="imageForm" :rules="imageRules" label-width="100px">
                <el-form-item label="上传图片" prop="fileUrl">
                    <ImageUpload :model-value="imageList" :limit="10"
                        @update:modelValueArray="handleImageChange"
                        @update:modelValue="handleImageChange"/>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button 
                        type="primary" 
                        @click="submitImageForm"
                        :disabled="!isAllUploaded(imageList)"
                    >确 定</el-button>
                    <el-button @click="imageDialogVisible = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 添加视频对话框 -->
        <el-dialog title="添加视频" v-model="videoDialogVisible" width="500px">
            <el-form ref="videoFormRef" :model="videoForm" :rules="videoRules" label-width="100px">
                <el-upload
                    multiple
                    action="#"
                    :auto-upload="false"
                    :on-change="handleVideoChange"
                    :file-list="videoList"
                    :show-file-list="false"
                    :before-upload="() => false"
                    accept="video/*"
                >
                    <el-button type="primary">选择视频</el-button>
                    <template #tip>
                        <div class="el-upload__tip">只能上传mp4/avi文件，且不超过500MB</div>
                    </template>
                </el-upload>

                <!-- 视频列表展示 -->
                <div class="video-list">
                    <el-card v-for="(video, index) in videoList" 
                            :key="index" 
                            class="video-item"
                            shadow="hover">
                        <div class="video-item-content">
                            <div class="video-info">
                                <el-icon><VideoCamera /></el-icon>
                                <span class="video-name">{{ video.name }}</span>
                            </div>
                            <div class="video-controls">
                                <video 
                                    :src="video.url" 
                                    controls 
                                    preload="metadata"
                                    class="video-player"
                                ></video>
                                <el-button 
                                    type="danger" 
                                    link 
                                    @click="removeVideo(index)"
                                    class="remove-btn"
                                >
                                    删除
                                </el-button>
                            </div>
                            <!-- 上传进度条 -->
                            <el-progress v-if="video.uploadProgress > 0 && video.uploadProgress < 100" :percentage="video.uploadProgress" />
                        </div>
                    </el-card>
                </div>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button 
                        type="primary" 
                        @click="submitVideoForm"
                        :disabled="!isAllUploaded(videoList)"
                    >确 定</el-button>
                    <el-button @click="videoDialogVisible = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 添加音频对话框 -->
        <el-dialog title="添加音频" v-model="audioDialogVisible" width="500px">
            <el-form ref="audioFormRef" :model="audioForm" :rules="audioRules" label-width="100px">
                <el-upload
                    multiple
                    action="#"
                    :auto-upload="false"
                    :on-change="handleAudioChange"
                    :on-remove="removeAudio"
                    :file-list="audioList"
                    :show-file-list="false"
                    :before-upload="() => false"
                >
                    <el-button type="primary">选择音频</el-button>
                    <template #tip>
                        <div class="el-upload__tip">只能上传mp3/wav/mpeg文件，且不超过50MB</div>
                    </template>
                </el-upload>

                <!-- 文件列表展示 -->
                <div class="audio-list">
                    <el-card v-for="(audio, index) in audioList" 
                            :key="index" 
                            class="audio-item"
                            shadow="hover">
                        <div class="audio-item-content">
                            <div class="audio-info">
                                <el-icon><Headset /></el-icon>
                                <span class="audio-name">{{ audio.name }}</span>
                            </div>
                            <div class="audio-controls">
                                <audio 
                                    :src="audio.url" 
                                    controls 
                                    preload="metadata"
                                    class="audio-player"
                                ></audio>
                                <el-button 
                                    type="danger" 
                                    link 
                                    @click="removeAudio(index)"
                                    class="remove-btn"
                                >
                                    删除
                                </el-button>
                            </div>
                            <!-- 上传进度条 -->
                            <el-progress v-if="audio.uploadProgress > 0 && audio.uploadProgress < 100" :percentage="audio.uploadProgress" />
                        </div>
                    </el-card>
                </div>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button 
                        type="primary" 
                        @click="submitAudioForm"
                        :disabled="!isAllUploaded(audioList)"
                    >确 定</el-button>
                    <el-button @click="audioDialogVisible = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 添加虚拟仿真/AR/VR/3D模型对话框 -->
        <el-dialog :title="getModelTypeTitle(modelForm.type)" v-model="modelDialogVisible" width="500px">
            <el-form ref="modelFormRef" :model="modelForm" :rules="modelRules" label-width="100px">
                <!-- AR/VR和虚拟仿真上传 -->
                <el-upload
                    multiple
                    action="#"
                    :auto-upload="false"
                    :on-change="handleModelChange"
                    :file-list="modelList"
                    :before-upload="(file) => beforeModelUpload(file, modelForm.type)"
                    :show-file-list="false"
                     :limit="1"
                    :accept="modelForm.type === '4' || modelForm.type === '5' ? '.zip' : 
                    '.obj,.3ds,.stl,.ply,.gltf,.glb,.off,.3dm,.fbx,.dae,.wrl,.3mf,.ifc,.brep,.step,.iges,.fcstd,.bim,.dwg,.dxf,.dwf,.igs,.dwt,.dng,.dwfx,.cf2,.plt'"
                >
                    <el-button type="primary">选择文件</el-button>
                    <template #tip>
                        <div class="el-upload__tip" v-if="modelForm.type === '4'">
                            虚拟仿真文件仅支持ZIP格式，且不超过500MB
                        </div>
                        <div class="el-upload__tip" v-else-if="modelForm.type === '5'">
                            AR/VR文件仅支持ZIP格式，且不超过500MB
                        </div>
                        <div class="el-upload__tip" v-else-if="modelForm.type === '6'" > 支持3D模型文件格式，且不超过500MB</div>
                    </template>
                </el-upload>

                <!-- 修改预览列表显示 -->
                <div class="preview-list">
                    <el-card v-for="(model, index) in modelList" 
                            :key="index" 
                            class="model-item"
                            shadow="hover">
                        <div class="model-item-content">
                            <div class="model-info">
                                <el-icon><Box /></el-icon>
                                <span class="model-name">{{ model.name }}</span>
                            </div>
                            <el-button 
                                type="danger" 
                                link 
                                @click="removeModel(index)"
                                class="remove-btn"
                            >
                                删除
                            </el-button>
                            <!-- 添加进度条 -->
                            <el-progress v-if="model.uploadProgress > 0 && model.uploadProgress < 100" :percentage="model.uploadProgress" />
                        </div>
                    </el-card>
                </div>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button 
                        type="primary" 
                        @click="submitModelForm"
                        :disabled="!isAllUploaded(modelList)"
                    >确 定</el-button>
                    <el-button @click="modelDialogVisible = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 添加课件对话框 -->
        <el-dialog title="添加课件" v-model="coursewareDialogVisible" width="500px">
            <el-form ref="coursewareFormRef" :model="coursewareForm" :rules="coursewareRules" label-width="100px">
                <el-upload
                    multiple
                    action="#"
                    :auto-upload="false"
                    :on-change="handleCoursewareChange"
                    :file-list="coursewareList"
                    :before-upload="() => false"
                    :show-file-list="false"
                >
                    <el-button type="primary">选择课件</el-button>
                    <template #tip>
                        <div class="el-upload__tip">支持 PDF、PPT、Word 等格式文件，且不超过100MB</div>
                    </template>
                </el-upload>

                <!-- 课件列表展示 -->
                <div class="preview-list">
                    <el-card v-for="(courseware, index) in coursewareList" 
                            :key="index" 
                            class="courseware-item"
                            shadow="hover">
                        <div class="courseware-item-content">
                            <div class="courseware-info">
                                <el-icon><Document /></el-icon>
                                <span class="courseware-name">{{ courseware.name }}</span>
                            </div>
                            <el-button
                                type="danger"
                                link
                                @click="removeCourseware(index)"
                                class="remove-btn">
                                删除
                            </el-button>
                            <!-- 上传进度条 -->
                            <el-progress v-if="courseware.uploadProgress > 0 && courseware.uploadProgress < 100" :percentage="courseware.uploadProgress" />
                        </div>
                    </el-card>
                </div>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button 
                        type="primary" 
                        @click="submitCoursewareForm"
                        :disabled="!isAllUploaded(coursewareList)"
                    >确 定</el-button>
                    <el-button @click="coursewareDialogVisible = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 图片预览对话框 -->
        <el-dialog 
            v-model="imagePreviewVisible" 
            title="图片预览"
            width="800px"
            :destroy-on-close="true"
            center
        >
            <div class="image-preview-container">
                <img :src="previewUrl" alt="预览图片" style="max-width: 100%; max-height: 70vh;">
            </div>
        </el-dialog>

        <!-- 音频预览对话框 -->
        <el-dialog 
            v-model="audioPreviewVisible" 
            :title="previewFileName"
            width="500px"
            :destroy-on-close="true"
        >
            <div class="audio-preview-container">
                <audio controls style="width: 100%">
                    <source :src="previewUrl" type="audio/mpeg">
                    您的浏览器不支持音频播放
                </audio>
            </div>
        </el-dialog>

        <!-- 视频预览对话框 -->
        <el-dialog 
            v-model="videoPreviewVisible" 
            :title="previewFileName"
            width="800px"
            :destroy-on-close="true"
        >
            <div class="video-preview-container">
                <video controls style="width: 100%">
                    <source :src="previewUrl" type="video/mp4">
                    您的浏览器不支持视频播放
                </video>
            </div>
        </el-dialog>

        <!-- 文件预览对话框 -->
        <el-dialog 
            v-model="filePreviewVisible" 
            :title="previewFileName"
            width="90%"
            :destroy-on-close="true"
            fullscreen
        >
            <div class="file-preview-container">
                <iframe :src="previewUrl" frameborder="0" style="width: 100%; height: 80vh;"></iframe>
            </div>
        </el-dialog>
        <!-- 回收站弹窗 -->
        <recycle-bin-dialog
            v-if="recycleBinVisible"
            v-model:visible="recycleBinVisible"
            @restored="handleRestored"
        />
    </div>
</template>

<script setup name="MoocResourceFolder">
import TeacherSpaceTabs from "@/views/teacherSpace/TeacherSpaceTabs.vue";
import {resourceList, getMoocResourceFolder, delMoocResourceFolder, addMoocResourceFolder, updateMoocResourceFolder, listMoocResourceFolderAll} from "@/api/edu/moocResourceFolder";
import {addMoocResource,updateMoocResource,moveToRecycle, getUserSpace } from "@/api/edu/moocResource";
import {OssService} from "@/utils/aliOss.js";
import { previewFile } from '@/utils/index.js'
import { onActivated } from 'vue';
import RecycleBinDialog from './recycleBinDialog.vue';

const { proxy } = getCurrentInstance();

const userSpaceInfo = ref({
    totalSpace: 0,
    usedSpace: 0,
    usagePercentage: 0
});

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function delayedFetchUserSpace() {
    await sleep(1500); // 等待1.5秒
    await fetchUserSpace(); // 刷新一次
}

async function fetchUserSpace() {
    try {
        console.log("获取用户空间信息");
        const response = await getUserSpace();
        if(response.data){
            const spaceData = response.data;
            userSpaceInfo.value = {
                totalSpace: spaceData.totalSpace,
                usedSpace: spaceData.usedSpace,
                usagePercentage: parseFloat(((spaceData.usagePercentage || 0) * 100).toFixed(2))
            };
        }
    } catch (error) {
        proxy.$modal.msgError("获取空间信息失败");
        console.error("获取用户空间信息失败:", error);
    }
}

function formatSize(bytes) {
    if (!bytes || bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 文件类型定义
const fileTypes = [
    { label: '图片', value: '1' },
    { label: '音频', value: '2' },
    { label: '视频', value: '3' },
    { label: '虚拟仿真', value: '4' },
    { label: 'AR/VR', value: '5' },
    { label: '3D模型', value: '6' },
    // { label: '习题', value: '7' },
    { label: '课件', value: '8' }
];

const userResourceFolderList = ref([]);
const userResourceList = ref([]); // 新增文件列表
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const moveDialogVisible = ref(false);
const moveForm = ref({
    currentId: null,
    parentId: null,
    type: '', // 新增type字段，用于区分文件('file')还是文件夹('folder')
    resourceId: null // 新增resourceId字段，用于存储文件ID
});
const isBulkMovingOperation = ref(false); // True if move dialog is for bulk operation

// 文件上传相关变量
const imageDialogVisible = ref(false);
const videoDialogVisible = ref(false);
const audioDialogVisible = ref(false);
const modelDialogVisible = ref(false);
const coursewareDialogVisible = ref(false);
const uploadProgress = ref(0);
const recycleBinVisible = ref(false);

const imageList = ref([]);
const audioList = ref([]);
const videoList = ref([]);
const modelList = ref([]);
const coursewareList = ref([]);


const imageForm = ref({
    fileName: '',
    fileUrl: '',
    type: '1'
});

const videoForm = ref({
    fileName: '',
    fileUrl: '',
    type: '3'
});

const audioForm = ref({
    fileName: '',
    fileUrl: '',
    type: '2'
});

const modelForm = ref({
    fileName: '',
    fileUrl: '',
    type: '4'
});

const coursewareForm = ref({
    fileName: '',
    fileUrl: '',
    type: '8'
});

// 文件上传规则
const imageRules = {
    fileUrl: [{ 
        required: true, 
        message: '请上传图片', 
        trigger: 'change',
        validator: (rule, value, callback) => {
            if (imageList.value.length === 0) {
                callback(new Error('请上传图片'));
            } else {
                callback();
            }
        }
    }]
};

const videoRules = {
    fileUrl: [{ required: true, message: '请上传视频', trigger: 'change' }]
};

const audioRules = {
    fileUrl: [{ required: true, message: '请上传音频', trigger: 'change' }]
};

const modelRules = {
    type: [{ required: true, message: '请选择文件类型', trigger: 'change' }],
    fileUrl: [{ required: true, message: '请上传文件', trigger: 'change' }]
};

const coursewareRules = {
    fileUrl: [{ required: true, message: '请上传课件', trigger: 'change' }]
};

// 文件夹树形数据
const folderTreeData = ref([]);
const folderTreeOptions = ref([]);

// 添加前进后退相关数据
const folderHistory = ref([]);
const historyIndex = ref(-1);
const MAX_HISTORY = 5;

// 添加面包屑相关数据
const folderPath = ref([
    { id: 0, name: '我的资源' }
]); 

const data = reactive({
  form: {
    parentId: 0 // 默认父级ID为0
  },
  queryParams: {
    pageNum: 1,
    pageSize: 30,
    folderName: null,
    parentId: 0, // 默认父级ID为0
    userId: null,
    type: null
  },
  rules: {
    name: [
      { required: true, message: '请输入名称', trigger: 'blur' },
      { max: 20, message: '名称不能超过20个字符', trigger: 'blur' }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

let getListCallCount = 0;

// 处理添加文件按钮
function handleAddFile(type) {
    // 重置所有文件列表
    imageList.value = [];
    videoList.value = [];
    audioList.value = [];
    modelList.value = [];
    coursewareList.value = [];
    
    // 重置表单数据
    imageForm.value = {
        fileName: '',
        fileUrl: '',
        type: '1'
    };
    videoForm.value = {
        fileName: '',
        fileUrl: '',
        type: '3'
    };
    audioForm.value = {
        fileName: '',
        fileUrl: '',
        type: '2'
    };
    modelForm.value = {
        fileName: '',
        fileUrl: '',
        type: '4'
    };
    coursewareForm.value = {
        fileName: '',
        fileUrl: '',
        type: '8'
    };

    switch(type) {
        case '1':
            imageDialogVisible.value = true;
            break;
        case '3':
            videoDialogVisible.value = true;
            break;
        case '2':
            audioDialogVisible.value = true;
            break;
        case '4':
        case '5':
        case '6':
            modelForm.value.type=type;
            modelDialogVisible.value = true;
            break;
        case '8':
            coursewareDialogVisible.value = true;
            break;
    }
}

function getModelTypeTitle(type) {
  // 根据文件类型返回对应的标题
  for (const item of fileTypes) {
    if (item.value === type) {
      return `添加${item.label}`
    }
  }
  return '未知文件类型'
}

function handleImageChange(data) {
    let rawList = [];
    // 检查接收到的是字符串还是数组
    if (typeof data === 'string') {
        // 如果是字符串，我们无法获取文件大小，因此size设为0
        if(data) {
           rawList = data.split(',').map(url => ({ url: url, name: url, size: 0 }));
        }
    } else if (Array.isArray(data)) {
        // 如果是数组，直接使用，它应该包含了我们需要的所有信息
        rawList = data;
    }

    imageList.value = rawList.map(item => {
        // 确保每个项目都有ossUrl和size属性，以保持数据结构一致
        return {
            ...item,
            ossUrl: item.url,
            size: item.size || 0
        };
    });
}

// 提交表单
async function submitImageForm() {
    proxy.$refs.imageFormRef.validate(async (valid) => {
        if (valid) {
            try {
                console.log('imageList.value',imageList.value)
                // 遍历imageList中的每个图片
                for (const image of imageList.value) {
                  let fileName = '';
                  if (image.name){
                     fileName = image.name;
                  }else {
                    const urlParts = image.url.split('/');
                     fileName = decodeURIComponent(urlParts[urlParts.length - 1]);
                  }
                    const imageData = {
                        fileName: fileName, // 使用URL中的文件名
                        fileUrl: image.url,
                        fileType: '1',
                        folderId: queryParams.value.parentId, // 添加当前文件夹ID
                        fileSize: image.size, // 添加文件大小
                    };
                    await addMoocResource(imageData);
                }
                proxy.$modal.msgSuccess("添加成功");
                imageDialogVisible.value = false;
                imageList.value = [];
                getList();
                delayedFetchUserSpace();
            } catch (error) {
                proxy.$modal.msgError("添加失败：" + error.message);
            }
        }
    });
}

// 音频相关处理函数
async function handleAudioChange(file) {
    try {
        const isAudio = file.raw.type.startsWith('audio/');
        const isValidSize = file.raw.size <= 50 * 1024 * 1024; // 50MB
        if (!isAudio) {
            proxy.$modal.msgError("请上传音频文件");
            return;
        }
        if (!isValidSize) {
            proxy.$modal.msgError("文件大小不能超过50MB");
            return;
        }
        const localUrl = URL.createObjectURL(file.raw);
        const audioFile = { name: file.raw.name, url: localUrl, file: file.raw, uploadProgress: 0 };
        audioList.value.push(audioFile);

        const ossUrl = await OssService(file.raw, (progress) => {
            const index = audioList.value.findIndex(item => item.name === file.raw.name);
            if (index !== -1) {
                audioList.value[index].uploadProgress = Math.floor(progress * 100);
            }
        });
        const index = audioList.value.findIndex(item => item.name === file.raw.name);
        if (index !== -1) {
            audioList.value[index].ossUrl = ossUrl.url;
            audioList.value[index].uploadProgress = 100;
        }
    } catch (error) {
        proxy.$modal.msgError("上传失败：" + error.message);
        const index = audioList.value.findIndex(item => item.name === file.raw.name);
        if (index !== -1) {
            audioList.value.splice(index, 1);
        }
    }
}

async function submitVideoForm() {
    proxy.$refs.videoFormRef.validate(async (valid) => {
        if (valid) {
            try {
                for (const video of videoList.value) {
                    const videoData = { fileName: video.name, fileUrl: video.ossUrl, fileType: '3', folderId: queryParams.value.parentId, fileSize: video.file.size };
                    await addMoocResource(videoData);
                }
                proxy.$modal.msgSuccess("添加成功");
                videoDialogVisible.value = false;
                videoList.value.forEach(video => { if (video.url) { URL.revokeObjectURL(video.url); } });
                videoList.value = [];
                getList();
                delayedFetchUserSpace();
            } catch (error) {
                proxy.$modal.msgError("添加失败：" + error.message);
            }
        }
    });
}

// 提交音频表单
async function submitAudioForm() {
    proxy.$refs.audioFormRef.validate(async (valid) => {
        if (valid) {
            try {
                for (const audio of audioList.value) {
                    const audioData = { fileName: audio.name, fileUrl: audio.ossUrl, fileType: '2', folderId: queryParams.value.parentId, fileSize: audio.file.size };
                    await addMoocResource(audioData);
                }
                proxy.$modal.msgSuccess("添加成功");
                audioDialogVisible.value = false;
                audioList.value.forEach(audio => { if (audio.url) { URL.revokeObjectURL(audio.url); } });
                audioList.value = [];
                getList();
                delayedFetchUserSpace();
            } catch (error) {
                proxy.$modal.msgError("添加失败：" + error.message);
            }
        }
    });
}

async function submitModelForm() {
    proxy.$refs.modelFormRef.validate(async (valid) => {
        if (valid) {
            if (modelList.value.length === 0) {
                proxy.$modal.msgError("请选择文件");
                return;
            }
            try {
                for (const model of modelList.value) {
                    const modelData = { fileName: model.name, fileUrl: model.ossUrl, fileType: modelForm.value.type, folderId: queryParams.value.parentId, fileSize: model.file.size };
                    await addMoocResource(modelData);
                }
                proxy.$modal.msgSuccess("添加成功");
                modelDialogVisible.value = false;
                modelList.value = [];
                getList();
                delayedFetchUserSpace();
            } catch (error) {
                proxy.$modal.msgError("添加失败：" + error.message);
            }
        }
    });
}

// 获取所有文件夹数据并构建树形结构
async function getAllFolders() {
    try {
        const response = await listMoocResourceFolderAll();
        console.log('listMoocResourceFolderAll', response)
        if (!isBulkMovingOperation.value && moveForm.value.currentId && moveForm.value.type === 'folder') {
            const currentMovingFolderId = moveForm.value.currentId;
            const childIdsOfMovingFolder = getChildFolderIds(currentMovingFolderId);
            folderTreeData.value = response.rows.filter(folder =>
                folder.userFolderId !== currentMovingFolderId &&
                !childIdsOfMovingFolder.includes(folder.userFolderId)
            );
        } else {
            folderTreeData.value = response.rows;
        }
        buildFolderTree();
    } catch (error) {
        console.error("获取文件夹数据失败:", error);
    }
}

// 构建文件夹树
function buildFolderTree() {

    const validFolders = folderTreeData.value;
    const tree = [];
    const map = {};
    validFolders.forEach(folder => {
        map[folder.userFolderId] = { ...folder, children: [] };
    });
    validFolders.forEach(folder => {
        const node = map[folder.userFolderId];
        if (folder.parentId == 0) {
            tree.push(node);
        } else {
            if (map[folder.parentId]) {
                map[folder.parentId].children.push(node);
            }
        }
    });
    folderTreeOptions.value = [{ userFolderId: 0, folderName: '根目录', children: tree }];
}

// 获取所有子文件夹ID
function getChildFolderIds(parentId) {
    const childIds = [];
    const findChildren = (pid) => {
        folderTreeData.value.forEach(folder => {
            if (folder.parentId === pid) {
                childIds.push(folder.userFolderId);
                findChildren(folder.userFolderId);
            }
        });
    };
    findChildren(parentId);
    return childIds;
}

/** 查询个人资源文件夹列表和文件列表 */
async function getList() {
  loading.value = true;
  try {

 

    const response = await resourceList(queryParams.value);
    const allItems = response.rows || [];
    console.log('allItems',allItems)
    userResourceFolderList.value = allItems
      .filter(item => item.type === 'folder')
      .map(folder => ({ ...folder, resourceId: folder.userFolderId, folderName: folder.name, type: 'folder', isSelected: false }));
    userResourceList.value = allItems
      .filter(item => item.type !== 'folder')
      .map(file => ({ ...file, fileName: file.name, isSelected: false }));
    total.value = response.total;

    if (queryParams.value.parentId === 0 && allItems.length === 0 && !queryParams.value.type && !queryParams.value.folderName && getListCallCount < 1) {
      getListCallCount++;
      try {
        await addMoocResourceFolder({ folderName: "默认文件夹", parentId: 0, defaultType: '1' });
        await getList();
      } catch (error) {
        proxy.$modal.msgError("创建默认文件夹失败：" + error.message);
      }
    }
  } catch (error) {
    proxy.$modal.msgError("获取列表失败：" + error.message);
  } finally {
    loading.value = false;
    selectedItems.value = { folders: [], files: [] };
  }
}

// 处理后退操作
function handleBack() {
    if (historyIndex.value > 0) {
        historyIndex.value--;
        const prevState = folderHistory.value[historyIndex.value];
        queryParams.value.parentId = prevState.parentId;
        form.value.parentId = prevState.parentId;
        folderPath.value = [...prevState.folderPath];
        getList();
    }
}

// 处理前进操作
function handleForward() {
    if (historyIndex.value < folderHistory.value.length - 1) {
        historyIndex.value++;
        const nextState = folderHistory.value[historyIndex.value];
        queryParams.value.parentId = nextState.parentId;
        form.value.parentId = nextState.parentId;
        folderPath.value = [...nextState.folderPath];
        getList();
    }
}

// 添加历史记录
function addHistory() {
    // 删除当前位置之后的历史记录
    folderHistory.value = folderHistory.value.slice(0, historyIndex.value + 1);
    
    // 添加新的历史记录
    folderHistory.value.push({
        parentId: queryParams.value.parentId,
        folderPath: [...folderPath.value]
    });
    
    // 如果超过最大历史记录数，删除最早的记录
    if (folderHistory.value.length > MAX_HISTORY) {
        folderHistory.value.shift();
    } else {
        historyIndex.value++;
    }
}

// 处理面包屑点击
function handleBreadcrumbClick(folderId) {
    // 找到点击的面包屑索引
    const index = folderPath.value.findIndex(item => item.id === folderId);
    if (index !== -1) {
        // 更新当前目录ID
        queryParams.value.parentId = folderId;
        form.value.parentId = folderId;
        // 更新面包屑路径
        folderPath.value = folderPath.value.slice(0, index + 1);
        // 添加到历史记录
        addHistory();
        // 重新获取列表
        getList();
    }
}

// 处理文件夹点击
function handleFolderClick(item) {
    if (item.type == 'folder') {
        // 文件夹导航逻辑保持不变
        console.log('文件夹点击',item)
        console.log('文件夹点击',item.resourceId)
        queryParams.value.parentId = item.resourceId;
        form.value.parentId = item.resourceId;
        folderPath.value.push({
            id: item.resourceId,
            name: item.folderName
        });
        addHistory();
        getList();
    } else {
        // 处理文件预览
        handleFilePreview(item);
    }
}


// 图片预览
function previewImage(url) {
    const images = [{ url }];
    proxy.$imagePreview.show(images);
}

// 音频预览
function previewAudio(file) {
    proxy.$modal.show({
        title: file.fileName,
        width: '500px',
        content: `
            <div style="text-align: center;">
                <audio controls style="width: 100%;">
                    <source src="${file.fileUrl}" type="audio/mpeg">
                    您的浏览器不支持音频播放
                </audio>
            </div>
        `
    });
}

// 视频预览
function previewVideo(file) {
    proxy.$modal.show({
        title: file.fileName,
        width: '800px',
        content: `
            <div style="text-align: center;">
                <video controls style="width: 100%;">
                    <source src="${file.fileUrl}" type="video/mp4">
                    您的浏览器不支持视频播放
                </video>
            </div>
        `
    });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    userFolderId: null,
    folderName: null,
    parentId: queryParams.value.parentId, // 使用当前目录ID
    userId: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    delFlag: null,
    name: null, // 重置名称
    resourceId: null, // 重置资源ID
    folderId: null // 重置文件夹ID
  };
  proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 修改选中项存储结构
const selectedItems = ref({
  folders: [], // 存储选中的文件夹
  files: []    // 存储选中的文件
});

// 多选框选中数据
function handleSelectionChange(item, isSelected) {
    console.log('多选框选中数据',item)
  if (item.type === 'folder') {
    if (isSelected) {
      selectedItems.value.folders.push(item.userFolderId);
    } else {
      const index = selectedItems.value.folders.indexOf(item.userFolderId);
      if (index > -1) {
        selectedItems.value.folders.splice(index, 1);
      }
    }
  } else {
    if (isSelected) {
      selectedItems.value.files.push(item.resourceId);
      console.log('多选框选中数据',selectedItems.value.files)
    } else {
      const index = selectedItems.value.files.indexOf(item.resourceId);
      if (index > -1) {
        selectedItems.value.files.splice(index, 1);
      }
    }
  }

  // 更新单选和多选状态
  const totalSelected = selectedItems.value.folders.length + selectedItems.value.files.length;
  single.value = totalSelected !== 1;
  multiple.value = totalSelected === 0;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  form.value.type='folder';
  open.value = true;
  title.value = "新建文件夹";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _userFolderId = row.userFolderId || ids.value
  getMoocResourceFolder(_userFolderId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改文件夹";
  });
}
/** 删除按钮操作 */
function handleDelete() {
    const { folders, files } = selectedItems.value;
    const allIds = [...folders, ...files];

    if (allIds.length === 0) {
        proxy.$modal.msgWarning("请选择要删除的项目");
        return;
    }

    proxy.$modal.confirm('是否确认删除？').then(async () => {
        try {
            await moveToRecycle(allIds.join(','));
            proxy.$modal.msgSuccess("删除成功");
            
            // 清空选择
            selectedItems.value = { folders: [], files: [] };
            
            getList();
            if (folders.length > 0) {
                getAllFolders();
            }
            delayedFetchUserSpace();
            
        } catch (error) {
            proxy.$modal.msgError("删除失败: " + error.message);
        }
    }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('edu/moocResourceFolder/export', {
    ...queryParams.value
  }, `moocResourceFolder_${new Date().getTime()}.xlsx`)
}

function goToRecycleBin() {
    recycleBinVisible.value = true;
}

function handleRestored() {
    getList();
    getAllFolders();
    delayedFetchUserSpace();
}

function handleOpenBulkMoveDialog() {
    isBulkMovingOperation.value = true;
    moveDialogVisible.value = true;
}

/** 移动按钮操作 */
function handleMove(item) {

    
    moveForm.value = {
        currentId: item.type === 'folder' ? item.userFolderId : null,
        parentId: null,
        type: item.type === 'folder' ? 'folder' : 'file',
        resourceId: item.type === 'folder' ? null : item.resourceId
    };
    buildFolderTree(); // 重建文件夹树
    moveDialogVisible.value = true;
}

/** 重命名按钮操作 */
function handleRename(item) {
    reset();
    // 根据类型设置不同的表单数据
    if (item.type === 'folder') {
        form.value = {
            type: 'folder',
            userFolderId: item.userFolderId,
            name: item.folderName,
            parentId: item.parentId
        };
    } else {
        form.value = {
            type: 'file',
            resourceId: item.resourceId,
            name: item.fileName,
            folderId: item.folderId
        };
    }
    open.value = true;
    title.value = "重命名";
}

/** 提交表单 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (form.value.type === 'folder') {
                if (form.value.userFolderId) {
                    // 更新文件夹
                    updateMoocResourceFolder({
                        userFolderId: form.value.userFolderId,
                        folderName: form.value.name,
                        parentId: form.value.parentId
                    }).then(response => {
                        proxy.$modal.msgSuccess("修改成功");
                        open.value = false;
                        getList();
                        getAllFolders(); // 更新文件夹树
                    });
                } else {
                    // 新增文件夹
                    addMoocResourceFolder({
                        folderName: form.value.name,
                        parentId: form.value.parentId
                    }).then(response => {
                        proxy.$modal.msgSuccess("新增成功");
                        open.value = false;
                        getList();
                        getAllFolders(); // 更新文件夹树
                    });
                }
            } else {
                // 文件只有更新操作
                updateMoocResource({
                    resourceId: form.value.resourceId,
                    fileName: form.value.name,
                    folderId: form.value.folderId
                }).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

// 新建一个辅助函数来执行单个项目的移动
async function executeMoveItem(itemId, itemType, targetFolderId) {
    if (itemType === 'folder') {
        return updateMoocResourceFolder({
            userFolderId: itemId,
            parentId: targetFolderId
        });
    } else {
        return updateMoocResource({
            resourceId: itemId,
            folderId: targetFolderId
        });
    }
}

/** 提交移动操作 */
async function submitMove() {
    if (!moveForm.value.parentId || moveForm.value.parentId.length === 0) {
        proxy.$modal.msgWarning("请选择目标文件夹");
        return;
    }

    console.log('选中的文件',selectedItems.value)

    const targetFolderId = moveForm.value.parentId[moveForm.value.parentId.length - 1];

    if (isBulkMovingOperation.value) {
        const { folders, files } = selectedItems.value;
        if (folders.length === 0 && files.length === 0) {
            proxy.$modal.msgWarning("没有选择任何文件或文件夹进行移动");
            return;
        }

        // 检查是否将文件夹移动到自身或其子文件夹中
        if (folders.includes(targetFolderId)) {
            proxy.$modal.msgWarning("不能将文件夹移动到自身或其子文件夹中");
            return;
        }

        try {
            const movePromises = [];
            // 移动文件夹
            if (folders.length > 0) {
                folders.forEach(folderId => {
                    movePromises.push(executeMoveItem(folderId, 'folder', targetFolderId));
                });
            }

            // 移动文件
            if (files.length > 0) {
                files.forEach(resourceId => {
                    console.log('准备移动数据：', resourceId+'|'+targetFolderId)
                    movePromises.push(executeMoveItem(resourceId, 'file', targetFolderId));
                });
            }
            
            await Promise.all(movePromises);

            proxy.$modal.msgSuccess("批量移动成功");
            selectedItems.value = { folders: [], files: [] }; // 清空选择
            multiple.value = true; // 重置批量操作按钮状态
            single.value = true; // 重置批量操作按钮状态
        } catch (error) {
            proxy.$modal.msgError("批量移动失败：" + error.message);
        } finally {
            isBulkMovingOperation.value = false; // 重置批量移动状态
            moveDialogVisible.value = false;
            getList();
            getAllFolders(); // 更新文件夹树
        }
    } else {
        // 单个项目移动逻辑
        try {
            console.log('单个项目移动逻辑',moveForm.value.type == 'folder' ? moveForm.value.currentId : moveForm.value.resourceId)
            await executeMoveItem(
                moveForm.value.type === 'folder' ? moveForm.value.currentId : moveForm.value.resourceId,
                moveForm.value.type,
                targetFolderId
            );
            proxy.$modal.msgSuccess("移动成功");
        } catch (error) {
            proxy.$modal.msgError("移动失败：" + error.message);
        } finally {
            moveDialogVisible.value = false;
            getList();
            getAllFolders(); // 更新文件夹树
        }
    }
}


async function handleVideoChange(file) {
    try {
        // 验证文件类型和大小
        const isVideo = file.raw.type.startsWith('video/');
        const isValidSize = file.raw.size <= 500 * 1024 * 1024; // 500MB
        
        if (!isVideo) {
            proxy.$modal.msgError("请上传视频文件");
            return;
        }
        
        if (!isValidSize) {
            proxy.$modal.msgError("文件大小不能超过500MB");
            return;
        }

        // 创建本地预览URL
        const localUrl = URL.createObjectURL(file.raw);
        
        // 创建新的视频文件对象
        const videoFile = {
            name: file.raw.name,
            url: localUrl,
            file: file.raw,
            uploadProgress: 0
        };
        
        // 添加到视频列表
        videoList.value.push(videoFile);

        // 上传到OSS并获取URL
        const ossUrl = await OssService(file.raw, (progress) => {
            // 更新特定文件的上传进度
            const index = videoList.value.findIndex(item => item.name === file.raw.name);
            if (index !== -1) {
                videoList.value[index].uploadProgress = Math.floor(progress * 100);
            }
        });
        
        // 更新视频文件的URL为OSS URL
        const index = videoList.value.findIndex(item => item.name === file.raw.name);
        if (index !== -1) {
            videoList.value[index].ossUrl = ossUrl.url;
            videoList.value[index].uploadProgress = 100;
        }
        
    } catch (error) {
        proxy.$modal.msgError("上传失败：" + error.message);
        // 从列表中移除失败的文件
        const index = videoList.value.findIndex(item => item.name === file.raw.name);
        if (index !== -1) {
            videoList.value.splice(index, 1);
        }
    }
}

function removeVideo(index) {
    // 释放本地预览URL
    if (videoList.value[index].url) {
        URL.revokeObjectURL(videoList.value[index].url);
    }
    videoList.value.splice(index, 1);
}

// 音频相关
function removeAudio(index) {
    // 释放本地预览URL
    if (audioList.value[index].url) {
        URL.revokeObjectURL(audioList.value[index].url);
    }
    audioList.value.splice(index, 1);
}

// 修改模型文件相关的处理函数
async function handleModelChange(file, fileList) {
    try {

        // 验证文件大小
        const isValidSize = file.raw.size <= 1024 * 1024 * 1024; // 1GB
        
        if (modelForm.value.type === '4' || modelForm.value.type === '5') {
            const isValidExtension = file.raw.name.endsWith('.zip');
            if (!isValidExtension) {
                proxy.$modal.msgError("文件类型不匹配，虚拟仿真和AR/VR文件必须是ZIP格式");
                return;
            }
        }

        if (!isValidSize) {
            proxy.$modal.msgError("文件大小不能超过1GB");
            return;
        }

        // 创建本地预览对象
        const modelFile = {
            name: file.raw.name,
            file: file.raw,
            uploadProgress: 0
        };
        
        // 添加到模型列表
        modelList.value.push(modelFile);

        // 上传到OSS并获取URL
        const ossUrl = await OssService(file.raw, (progress) => {
            // 更新特定文件的上传进度
            const index = modelList.value.findIndex(item => item.name === file.raw.name);
            if (index !== -1) {
                modelList.value[index].uploadProgress = Math.floor(progress * 100);
            }
        });
        
        // 更新文件的OSS URL
        const index = modelList.value.findIndex(item => item.name === file.raw.name);
        if (index !== -1) {
            modelList.value[index].ossUrl = ossUrl.url;
            modelList.value[index].uploadProgress = 100;
        }
        
    } catch (error) {
        proxy.$modal.msgError("上传失败：" + error.message);
        // 从列表中移除失败的文件
        const index = modelList.value.findIndex(item => item.name === file.raw.name);
        if (index !== -1) {
            modelList.value.splice(index, 1);
        }
    }
}

function removeModel(index) {
    modelList.value.splice(index, 1);
}


// 上传文件前的校验
function beforeModelUpload(file, type) {
    const isValidSize = file.size <= 500 * 1024 * 1024; // 500MB
    
    if ((type === '4' || type === '5')) {
        if(!file.name.endsWith('.zip')) {
            proxy.$modal.msgError("请上传ZIP格式的文件");
            return false;
        }
    }
    
    if (!isValidSize) {
        proxy.$modal.msgError("文件大小不能超过500MB");
        return false;
    }
    
    return true;
}

// 上传进度处理
function handleProgress(event) {
    uploadProgress.value = Math.round((event.loaded / event.total) * 100);
}

// 上传完成后重置进度
function resetProgress() {
    uploadProgress.value = 0;
}

function isAllUploaded(fileList) {
    if (!fileList || fileList.length === 0) {
        return false;
    }
    return fileList.every(file => {
        // 检查是否有ossUrl（上传完成）且进度为100
        return file.ossUrl && (!file.uploadProgress || file.uploadProgress === 100);
    });
}

// 处理课件文件变化
async function handleCoursewareChange(file) {
    try {
        // 验证文件大小
        const isValidSize = file.raw.size <= 100 * 1024 * 1024; // 100MB
        
        if (!isValidSize) {
            proxy.$modal.msgError("文件大小不能超过100MB");
            return false;
        }

        // 创建本地预览对象
        const coursewareFile = {
            name: file.raw.name,
            file: file.raw,
            uploadProgress: 0
        };
        
        // 添加到课件列表
        coursewareList.value.push(coursewareFile);

        // 上传到OSS并获取URL
        const ossUrl = await OssService(file.raw, (progress) => {
            // 更新特定文件的上传进度
            const index = coursewareList.value.findIndex(item => item.name === file.raw.name);
            if (index !== -1) {
                coursewareList.value[index].uploadProgress = Math.floor(progress * 100);
            }
        });
        
        // 更新文件的OSS URL
        const index = coursewareList.value.findIndex(item => item.name === file.raw.name);
        if (index !== -1) {
            coursewareList.value[index].ossUrl = ossUrl.url;
            coursewareList.value[index].uploadProgress = 100;
        }
        
    } catch (error) {
        proxy.$modal.msgError("上传失败：" + error.message);
        // 从列表中移除失败的文件
        const index = coursewareList.value.findIndex(item => item.name === file.raw.name);
        if (index !== -1) {
            coursewareList.value.splice(index, 1);
        }
    }
}

// 移除课件
function removeCourseware(index) {
    coursewareList.value.splice(index, 1);
}

// 提交课件表单
async function submitCoursewareForm() {
    proxy.$refs.coursewareFormRef.validate(async (valid) => {
        if (valid) {
            try {
                // 遍历课件列表中的每个文件
                for (const courseware of coursewareList.value) {
                    const coursewareData = {
                        fileName: courseware.name,
                        fileUrl: courseware.ossUrl,
                        fileType: '8', // 课件类型
                        folderId: queryParams.value.parentId,
                        fileSize: courseware.file.size // 添加文件大小
                    };
                    
                    // 为每个课件调用添加资源API
                    await addMoocResource(coursewareData);
                }
                
                proxy.$modal.msgSuccess("添加成功");
                coursewareDialogVisible.value = false;
                // 清空课件列表
                coursewareList.value = [];
                // 重新获取列表
                getList();
                delayedFetchUserSpace();
            } catch (error) {
                proxy.$modal.msgError("添加失败：" + error.message);
            }
        }
    });
}

// 初始化历史记录
folderHistory.value.push({
    parentId: 0,
    folderPath: [...folderPath.value]
});
historyIndex.value = 0;

// 初始化时获取所有文件夹数据
getAllFolders();
getList();

// 预览相关的响应式变量
const imagePreviewVisible = ref(false);
const audioPreviewVisible = ref(false);
const videoPreviewVisible = ref(false);
const filePreviewVisible = ref(false);
const previewUrl = ref('');
const previewFileName = ref('');
// 修改预览处理函数
function handleFilePreview(file) {
    previewUrl.value = file.fileUrl;
    previewFileName.value = file.fileName;
    switch (file.fileType) {
        case '1': // 图片
        if (file.fileUrl) {
                previewFile(file.fileUrl);
            } else {
                proxy.$modal.msgError("文件地址无效");
            }
            break;
        case '2': // 音频
            audioPreviewVisible.value = true;
            break;
        case '3': // 视频
            videoPreviewVisible.value = true;
            break;
        case '4': // 虚拟仿真
            // proxy.$modal.msgError("该类型文件预览功能暂未开放");
            console.log(111, `${import.meta.env.VITE_RESOURCE_BASE_URL}${file.resourceId}`)
            window.open( file.fileUrl, '_blank');
            break;
        case '5': // AR/VR
            console.log(222, `${import.meta.env.VITE_RESOURCE_BASE_URL}${file.resourceId}`)
            window.open( file.fileUrl, '_blank');
            // proxy.$modal.msgError("该类型文件预览功能暂未开放");
            break;
        case '6': // 3D模型
            previewFile(file.fileUrl);
            break;
        case '8': // 课件
        default:
            if (file.fileUrl) {
                previewFile(file.fileUrl);
            } else {
                proxy.$modal.msgError("文件地址无效");
            }
            break;
    }
}

// 在组件激活时刷新列表
onActivated(() => {
  console.log("组件激活");
  getList();
  fetchUserSpace();
});

const hasContent = computed(() => {
    return userResourceFolderList.value.length > 0 || userResourceList.value.length > 0;
});
</script>
<style lang="scss" scoped>
    .top-right-container {
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 10;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
    }
    .space-usage {
        width: 200px;
        margin-top: 8px;
        :deep(.el-progress-bar__innerText) {
            color: black;
        }
    }
    .space-text {
        margin-top: 5px;
        font-size: 12px;
        color: #909399;
        text-align: right;
    }
    .app-container {
        position: relative;
        min-height: 60vh;
    }
    .tool{
        padding: 20px 0 0;
    }

    .top20{
        margin-top: 10px;
    }

    .pagination{
        float: right;
        margin:20px 0;
    }

    .navigation-bar {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        
        .nav-buttons {
            margin-right: 20px;
            
            .el-button {
                margin-right: 10px;
            }
        }
    }

    .breadcrumb-nav {
        flex-grow: 1;
        
        .clickable {
            cursor: pointer;
                color: #409EFF;
                
                &:hover {
                    color: #66b1ff;
                }
        }
    }

    .folder-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 20px;
        padding: 20px;
        min-height: 40vh;
    }

    .folder-item {
        position: relative;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        padding: 10px;
        cursor: pointer;
        transition: all 0.3s;
        height: 190px;
        &:hover {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .folder-content{
            margin:auto;
            margin-top: 10px;
        }
    }

    .folder-checkbox {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 1;
    }

    .folder-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px 10px;
        text-align: center;
        position: relative;

        .more-icon {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            z-index: 10;
        }
    }

    .folder-icon {
        margin-bottom: 10px;
        color: #409EFF;
        width: 70px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .folder-name {
        font-size: 14px;
        margin-bottom: 10px;
        word-break: break-all;
    }

    .folder-actions {
        display: none;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.9);
        padding: 5px;
        border-radius: 0 0 4px 4px;
    }

    .folder-item:hover .folder-actions {
        display: flex;
        justify-content: center;
        gap: 10px;
    }

    .audio-list {
        margin-top: 20px;
        
        .audio-item {
            margin-bottom: 12px;
            
            .audio-item-content {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }
            
            .audio-info {
                display: flex;
                align-items: center;
                gap: 8px;
                
                .el-icon {
                    font-size: 20px;
                    color: #409EFF;
                }
                
                .audio-name {
                    font-size: 14px;
                    color: #606266;
                    word-break: break-all;
                }
            }
            
            .audio-controls {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 10px;
                
                .audio-player {
                    flex: 1;
                    height: 36px;
                }
                
                .remove-btn {
                    flex-shrink: 0;
                }
            }
        }
    }

    // 调整音频播放器样式
    audio {
    }

    .video-list {
        margin-top: 20px;
        
        .video-item {
            margin-bottom: 12px;
            
            .video-item-content {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }
            
            .video-info {
                display: flex;
                align-items: center;
                gap: 8px;
                
                .el-icon {
                    font-size: 20px;
                    color: #409EFF;
                }
                
                .video-name {
                    font-size: 14px;
                    color: #606266;
                    word-break: break-all;
                }
            }
            
            .video-controls {
                display: flex;
                flex-direction: column;
                gap: 8px;
                
                .video-player {
                    width: 100%;
                    max-height: 200px;
                }
                
                .remove-btn {
                    align-self: flex-end;
                }
            }
        }
    }

    .preview-list {
        margin-top: 20px;
        
        .model-item {
            margin-bottom: 12px;
            
            .model-item-content {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }
            
            .model-info {
                display: flex;
                align-items: center;
                gap: 8px;
                
                .el-icon {
                    font-size: 20px;
                    color: #409EFF;
                }
                
                .model-name {
                    font-size: 14px;
                    color: #606266;
                    word-break: break-all;
                }
            }
            
            .remove-btn {
                align-self: flex-end;
            }
        }
    }

    .courseware-item {
        margin-bottom: 12px;
        
        .courseware-item-content {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 12px;
            
            .courseware-info {
                display: flex;
                align-items: center;
                gap: 8px;
                
                .el-icon {
                    font-size: 20px;
                    color: #409EFF;
                }
                
                .courseware-name {
                    font-size: 14px;
                    color: #606266;
                    word-break: break-all;
                }
            }
            
            .remove-btn {
                align-self: flex-end;
            }

            .el-progress {
                margin-top: 8px;
            }
        }
    }

    .image-preview-container,
    .audio-preview-container,
    .video-preview-container,
    .file-preview-container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
    }

    .image-preview-container img {
        object-fit: contain;
    }

    .video-preview-container video {
        max-height: 70vh;
    }

    .audio-preview-container audio {
        width: 100%;
    }

    .file-preview-container iframe {
        border: none;
        width: 100%;
        height: 80vh;
    }

    .preview-container {
        width: 100px;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        border-radius: 4px;
        background-color: #f5f7fa;
        margin-bottom: 10px;
    }

    .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .preview-video {
        width: 100%;
        height: 100%;
        object-fit: cover;

        
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.1);
        }
    }


    .popover-buttons {
    display: flex;
    flex-direction: column;
    align-items: stretch;

    .popover-button {
        padding: 8px 0;
        justify-content: center;
        
        &:not(:last-child) {
            border-bottom: 1px solid #EBEEF5;
        }
    }
}
// 不同类型资源上传的样式
.el-upload {
    &.type-3d {
        margin-bottom: 10px;
    }
    
    &.type-ar-vr, &.type-simulation {
        margin-bottom: 10px;
    }
}
.my-popper{
  width: 90px;
}

.tool .el-form-item {
    margin-right: 10px;
}
</style>