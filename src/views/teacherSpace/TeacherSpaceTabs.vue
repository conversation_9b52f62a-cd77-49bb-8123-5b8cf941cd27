<template>
  <div class="navigation-tabs">
    <el-tabs :model-value="activeTab" @tab-click="handleTabClick">
      <el-tab-pane
        v-for="tab in tabs"
        :key="tab.name"
        :label="tab.label"
        :name="tab.name"
        :disabled="tab.name === activeTab"
      ></el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="TeacherSpaceTabs">
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

const tabs = [
  { label: '我的资源', name: 'TeacherResource' },
  { label: '我的题库', name: 'TeacherQuestion' },
];

const activeTab = computed(() => {
    return route.name;
});

const handleTabClick = (tab) => {
  const targetTabName = tab.props.name;
  if (targetTabName !== activeTab.value) {
    router.push({ name: targetTabName });
  }
};
</script>

<style lang="scss" scoped>
.navigation-tabs {
  background-color: #fff;
  padding: 0 20px;
  border-radius: 4px;
  margin-bottom: 0px;
  border: 1px solid #e6ebf5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
}
</style> 