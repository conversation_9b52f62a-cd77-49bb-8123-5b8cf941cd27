<template>
    <div class="app-container">
        <TeacherSpaceTabs />
        <Cards style="min-height: 70vh;">
            <el-row :gutter="20">
                <!-- 左侧目录 -->
                <el-col :span="6">
                    <Cards>
                        <Catalog :catalogs="catalogList" @click-catalog="handleCatalogClick"
                            @add-catalog="submitCatalogForm" @delete-catalog="handleDeleteCatalog"
                            @update-catalog="submitCatalogForm" @move-catalog="submitCatalogForm"
                            @refresh-catalogs="getCatalogList" @search="getCatalogList" />
                    </Cards>
                </el-col>

                <!-- 右侧内容区 -->
                <el-col :span="18">
                    <!-- 搜索和工具栏卡片 -->
                    <Cards>
                        <div class="tool">
                            <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch"
                                label-width="68px">
                                <el-form-item label="类型" prop="questionType">
                                    <el-select v-model="queryParams.questionType" placeholder="请选择小题类型" clearable
                                        style="width: 150px;">
                                        <el-option v-for="item in questionTypes" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="题目" prop="questionContent">
                                    <el-input v-model="queryParams.questionContent" placeholder="请输入题目关键词" clearable
                                        @keyup.enter="handleQuery" />
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                                </el-form-item>
                            </el-form>

                            <el-row :gutter="10" class="mb8">
                                      <!-- 添加全选功能到工具栏 -->
                                      <el-col :span="1.5">
                                    <el-checkbox 
                                        class="select-all-checkbox"
                                        v-model="allSelected"
                                        @change="selectAll"
                                    >
                                        全选
                                    </el-checkbox>
                                </el-col>
                                <el-col :span="1.5">
                                    <el-button type="primary" plain icon="Plus" @click="handleAdd"
                                       >新增</el-button>
                                </el-col>
                                <el-col :span="1.5">
                                    <el-button type="success" plain icon="Upload" @click="handleImport"
                                       >导入</el-button>
                                </el-col>
                                <el-col :span="1.5">
                                    <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                                       >删除</el-button>
                                </el-col>
                
                          
                                <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                            </el-row>
                        </div>
                    </Cards>

                    <!-- 表格卡片 -->
                    <Cards class="top20">

                        <div v-loading="loading" class="question-list">
                            <div v-if="!bookQuestionList.length" class="empty-placeholder">
                                暂无题目数据
                            </div>
                            <div v-for="question in bookQuestionList" :key="question.questionId" class="question-card">
                            <div class="question-header">
                                    <div class="question-info">
                                        <el-checkbox 
                                            v-model="question.selected"
                                            @change="handleSelectionChange"
                                            class="question-checkbox"
                                        ></el-checkbox>
                                        <el-tag size="small" type="success">
                                            {{ getQuestionTypeName(question.questionType) }}
                                        </el-tag>
                                    </div>
                                    <div class="operation">
                                        <el-popover popper-style="min-width: 90px;max-width: 90px;" placement="bottom" trigger="hover">
                                            <template #reference>
                                                <el-button class="more-btn" link>
                                                    <el-icon>
                                                        <More />
                                                    </el-icon>
                                                </el-button>
                                            </template>
                                            <div class="popover-buttons">
                                                <el-button link type="primary" style="margin-left: 10px;" @click="handleView(question)">查看</el-button>
                                                <el-button link type="primary" @click="handleUpdate(question)">编辑</el-button>
                                                <el-button link type="primary" @click="handleMoveQuestion(question)">移动</el-button>
                                                <el-button link type="danger" @click="handleDelete(question)">删除</el-button>
                                            </div>
                                        </el-popover>
                                    </div>
                                </div>

                                <!-- 题目内容区 -->
                                <div class="question-content">
                      
                                <QuestionPreview 
                                    :question=question
                                    :showAnswerButton="true"
                                    :key="`question-${question.questionId}-${version}`"
                                />
                            </div>
                            </div>
                        </div>

                        <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum"
                            v-model:limit="queryParams.pageSize" @pagination="getList" />
                    </Cards>

                    <!-- 添加或修改数字教材习题对话框 -->
                    <el-dialog :title="title" v-model="open" width="900px" append-to-body>
                        <el-form ref="bookQuestionRef" :model="form" :rules="rules" label-width="100px" :key="open" class="left-aligned-form">
                            <el-form-item label="类型" prop="questionType">
                                <el-select v-model="form.questionType" placeholder="请选择题目类型" :disabled="!!form.questionId" @change="initOptions(form.questionType)">
                                    <el-option v-for="item in questionTypes" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>

                            <el-form-item label="备注" prop="questionRemark">
                                <el-input v-model="form.questionRemark" type="textarea" :rows="3" placeholder="请输入答题要求" />
                            </el-form-item>

                            <el-form-item label="题干" prop="questionContent">
                                <editor v-model="form.questionContent" :min-height="150" />
                            
                                <div v-if:test="form.questionType == 3">
                                    <p>  <el-button type="primary" link @click="appendBlanks">
                                        <el-icon><Plus /></el-icon>
                                        添加填空
                                    </el-button></p>
                                    <p>
                                    <el-checkbox
                                        v-model="form.disorder"
                                        :true-label="2"
                                        :false-label="1"
                                    >
                                        答案支持乱序
                                    </el-checkbox>
                                </p>
                                </div>
                            </el-form-item>

                            <!-- 选项编辑区域（单选、多选、判断题） -->
                            <template v-if="[1, 2, 7].includes(form.questionType)">
                                <el-form-item label="选项" prop="questionContent">
                                <div class="options-editor">
                                    <div class="options-header">
                                        <!-- 只在单选和多选题时显示添加选项按钮 -->
                                        <el-button v-if="[1, 2].includes(form.questionType)" type="primary" link
                                            @click="addOption">添加选项</el-button>
                                    </div>
                                    <div v-for="(option, index) in form.options" :key="index" class="option-item">
                                        <el-row :gutter="10" class="option-row">
                                            <el-col :span="2" class="option-label">
                                                {{ String.fromCharCode(65 + index) }}
                                            </el-col>
                                            <el-col :span="16">
                                                <editor v-if="form.questionType !== 7" v-model="option.optionContent"
                                                    :min-height="80" />
                                                <div v-else>{{ index === 0 ? '正确' : '错误' }}</div>
                                            </el-col>
                                            <el-col :span="4">
                                                <!-- 多选题使用复选框 -->
                                                <el-checkbox v-if="form.questionType === 2" v-model="option.rightFlag">正确答案</el-checkbox>
                                                
                                                <!-- 单选题和判断题使用单选按钮 -->
                                                <el-radio v-else v-model="correctOptionIndex" :label="index" @change="handleRadioChange(index)">
                                                    正确答案
                                                </el-radio>
                                            </el-col>
                                            <el-col :span="2">
                                                <el-button v-if="[1, 2].includes(form.questionType)" type="danger" link
                                                    @click="removeOption(index)">
                                                    <el-icon>
                                                        <Delete />
                                                    </el-icon>
                                                </el-button>
                                            </el-col>
                                        </el-row>
                                    </div>
                                </div>
                                </el-form-item>
                            </template>

                            <!-- 填空题答案显示 -->
                            <template v-if="form.questionType === 3">
                                <el-form-item label="预览">
                                    <div class="editor-wrapper">
                                        <div class="content" v-html="formatBlankQuestion(form.questionContent, form.questionType)"></div>
                                        <div class="editor-overlay"></div>
                                    </div>
                                </el-form-item>
                            </template>

                            <!-- 排序题选项 -->
                            <template v-if="form.questionType === 4">
                                <div class="options-editor">
                                    <div class="options-header">
                                        <span class="subtitle">排序项设置（请按正确顺序添加）</span>
                                        <el-button type="primary" link @click="addOption">添加选项</el-button>
                                    </div>
                                    <el-row v-for="(option, index) in form.options" :key="index" class="option-row">
                                        <el-col :span="2">{{ index + 1 }}</el-col>
                                        <el-col :span="18">
                                            <editor v-model="option.optionContent" :min-height="80" />
                                        </el-col>
                                        <el-col :span="4">
                                            <el-button-group>
                                                <el-button @click="moveOption(index, 'up')" :disabled="index === 0">
                                                    <el-icon>
                                                        <ArrowUp />
                                                    </el-icon>
                                                </el-button>
                                                <el-button @click="moveOption(index, 'down')"
                                                    :disabled="index === form.options.length - 1">
                                                    <el-icon>
                                                        <ArrowDown />
                                                    </el-icon>
                                                </el-button>
                                                <el-button type="danger" @click="removeOption(index)">
                                                    <el-icon>
                                                        <Delete />
                                                    </el-icon>
                                                </el-button>
                                            </el-button-group>
                                        </el-col>
                                    </el-row>
                                </div>
                            </template>

                            <!-- 连线题选项 -->
                            <template v-if="form.questionType === 5">
                                <div class="matching-editor">
                                    <matching-question-picker v-model="matchingData"
                                        @update:modelValue="handleMatchingUpdate" />
                                </div>
                            </template>

                            <!-- 简答题答案 -->
                            <template v-if="form.questionType === 6 || form.questionType === 8">
                                <el-form-item label="参考答案" prop="rightAnswer">
                                    <editor v-model="form.rightAnswer" :min-height="120" />
                                </el-form-item>
                            </template>

                   

                            <!-- 解析区域 -->
                            <el-form-item label="解析" prop="analysis">
                                <editor v-model="form.analysis" :min-height="120" />
                            </el-form-item>

                            <!-- 编程题相关模板 -->
                            <template v-if="form.questionType === 8">
                                <el-form-item label="语言" prop="language">
                                    <el-select v-model="form.language" placeholder="请选择编程语言">
                                        <el-option v-for="item in programmingLanguageList" :key="item.language"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="代码" prop="questionContent">
                                    <code-editor v-model="form.code" :language="form.language || 'javascript'"
                                        :key="'code-editor-' + form.questionId" theme="light" />
                                </el-form-item>
                            </template>


                        </el-form>
                        <template #footer>
                            <div class="dialog-footer">
                                <el-button type="primary" @click="submitForm">确 定</el-button>
                                <el-button @click="cancel">取 消</el-button>
                            </div>
                        </template>
                    </el-dialog>

                    <!-- 添加或修改目录对话框 -->
                    <el-dialog :title="title" v-model="catalogOpen" width="400px" append-to-body>
                        <el-form ref="catalogRef" :model="catalogForm" :rules="catalogRules" label-width="100px">
                            <el-form-item label="目录名称" prop="folderName">
                                <el-input v-model="catalogForm.folderName" placeholder="请输入目录名称" maxlength="50"
                                    show-word-limit />
                            </el-form-item>
                        </el-form>
                        <template #footer>
                            <div class="dialog-footer">
                                <el-button type="primary" @click="submitCatalogForm">确 定</el-button>
                                <el-button @click="cancelCatalog">取 消</el-button>
                            </div>
                        </template>
                    </el-dialog>

                    <!-- 移动目录对话框 -->
                    <el-dialog title="移动到" v-model="moveCatalogDialogVisible" width="400px">
                        <el-form ref="moveFormRef" :model="moveForm" label-width="80px">
                            <el-cascader v-model="moveForm.parentId" :options="folderTreeOptions"
                                :props="{value:'folderId',label:'folderName',checkStrictly:true}">
                            </el-cascader>
                        </el-form>
                        <template #footer>
                            <div class="dialog-footer">
                                <el-button type="primary" @click="submitMove">确 定</el-button>
                                <el-button @click="moveCatalogDialogVisible = false">取 消</el-button>
                            </div>
                        </template>
                    </el-dialog>

                    <!-- 移动题目对话框 -->
                    <el-dialog title="移动到" v-model="moveQuestionDialogVisible" width="400px">
                        <el-form ref="moveQuestionFormRef" :model="moveQuestionForm" label-width="80px">
                            <el-cascader v-model="moveQuestionForm.folderId" :options="folderTreeOptions"
                                :props="{value:'folderId',label:'folderName',checkStrictly:true}">
                            </el-cascader>
                        </el-form>
                        <template #footer>
                            <div class="dialog-footer">
                                <el-button type="primary" @click="submitMoveQuestion">确 定</el-button>
                                <el-button @click="moveQuestionDialogVisible = false">取 消</el-button>
                            </div>
                        </template>
                    </el-dialog>

                    <el-dialog title="导入题目" v-model="importDialogVisible" width="500px">
                        <el-form ref="importFormRef" :model="importForm" :rules="importRules" label-width="100px">
                            <!-- 添加模板下载按钮 -->
                            <div class="template-download mb-10">
                                <el-button 
                                    type="primary" 
                                    link 
                                    @click="downloadTemplate('normal')"
                                    v-if="importForm.importType === 'normal'">
                                    <el-icon><Download /></el-icon>
                                    下载普通题型模板
                                </el-button>
                                <el-button 
                                    type="primary" 
                                    link 
                                    @click="downloadTemplate('matching')"
                                    v-if="importForm.importType === 'matching'">
                                    <el-icon><Download /></el-icon>
                                    下载连线题模板
                                </el-button>
                            </div>

                            <!-- 添加题型选择 -->
                            <el-form-item label="导入类型" prop="importType">
                                <el-radio-group v-model="importForm.importType">
                                    <el-radio :label="'normal'">普通题型</el-radio>
                                    <el-radio :label="'matching'">连线题</el-radio>
                                </el-radio-group>
                            </el-form-item>

                            <el-upload multiple action="#" :auto-upload="false" :on-change="handleImportChange"
                                :file-list="importList" :before-upload="() => false" :show-file-list="false"
                                accept=".xlsx,.xls">
                                <el-button type="primary">选择文件</el-button>
                                <template #tip>
                                    <div class="el-upload__tip">请上传Excel格式文件，且不超过10MB</div>
                                </template>
                            </el-upload>

                            <!-- 文件列表展示 -->
                            <div class="preview-list">
                                <el-card v-for="(file, index) in importList" :key="index" class="import-item"
                                    shadow="hover">
                                    <div class="import-item-content">
                                        <div class="import-info">
                                            <el-icon>
                                                <Document />
                                            </el-icon>
                                            <span class="import-name">{{ file.name }}</span>
                                        </div>
                                        <el-button type="danger" link @click="removeImportFile(index)" class="remove-btn">
                                            删除
                                        </el-button>
                                        <!-- 上传进度条 -->
                                        <el-progress v-if="file.uploadProgress > 0 && file.uploadProgress < 100"
                                            :percentage="file.uploadProgress" :format="percent => `${percent}%`"
                                            status="success" />
                                    </div>
                                </el-card>
                            </div>
                        </el-form>
                        <template #footer>
                            <div class="dialog-footer">
                                <el-button type="primary" @click="submitImportForm" :disabled="!isAllUploaded(importList)">确
                                    定</el-button>
                                <el-button @click="importDialogVisible = false">取 消</el-button>
                            </div>
                        </template>
                    </el-dialog>

                    <!-- 添加查看题目对话框 -->
                    <el-dialog :title="title" v-model="viewOpen" width="800px" append-to-body>
                        <el-form ref="viewQuestionRef" :model="form" label-width="100px" class="left-aligned-form">
                            <el-form-item label="类型">
                                <el-select v-model="form.questionType" disabled>
                                    <el-option v-for="item in questionTypes" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="题干">
                                <div class="editor-wrapper">
                                    <editor v-model="form.questionContent" :min-height="150" :readonly="true" />
                                    <div class="editor-overlay"></div>
                                </div>
                            </el-form-item>

                            <!-- 选项显示区域（单选、多选、判断题） -->
                            <template v-if="[1, 2, 7].includes(form.questionType)">
                                <el-form-item label="选项">
                                <div class="options-editor">
                                    <div class="options-header">
                                    </div>
                                    <div v-for="(option, index) in form.options" :key="index" class="option-item">
                                        <el-row :gutter="10" class="option-row">
                                            <el-col :span="2" class="option-label">
                                                {{ String.fromCharCode(65 + index) }}
                                            </el-col>
                                            <el-col :span="18">
                                                <div class="editor-wrapper">
                                                    <editor v-if="form.questionType !== 7" v-model="option.optionContent"
                                                        :min-height="80" :readonly="true" />
                                                    <div class="editor-overlay"></div>
                                                </div>
                                            </el-col>
                                            <el-col :span="4">
                                                <el-tag v-if="option.rightFlag" type="success" style="margin-left: 15px;">正确答案</el-tag>
                                            </el-col>
                                        </el-row>
                                    </div>
                                </div>
                                </el-form-item>
                            </template>


                            <!-- 排序题选项显示 -->
                            <template v-if="form.questionType === 4">
                                <div class="options-editor">
                                    <div class="options-header">
                                        <span class="subtitle">排序项</span>
                                    </div>
                                    <el-row v-for="(option, index) in form.options" :key="index" class="option-row">
                                        <el-col :span="2">{{ index + 1 }}</el-col>
                                        <el-col :span="22">
                                            <div class="editor-wrapper">
                                                <editor v-model="option.optionContent" :min-height="80" :readonly="true" />
                                                <div class="editor-overlay"></div>
                                            </div>
                                        </el-col>
                                    </el-row>
                                </div>
                            </template>

                            <!-- 连线题显示 -->
                            <template v-if="form.questionType === 5">
                                <div class="matching-editor">
                                    <matching-question-picker 
                                        v-model="viewMatchingData" 
                                        :readonly="true"
                                        :key="viewOpen" 
                                    />
                                </div>
                            </template>

                            <!-- 简答题答案显示 -->
                            <template v-if="form.questionType === 6">
                                <el-form-item label="参考答案">
                                    <div class="editor-wrapper">
                                        <editor v-model="form.rightAnswer" :min-height="120" :readonly="true" />
                                        <div class="editor-overlay"></div>
                                    </div>
                                </el-form-item>
                            </template>

                            <!-- 解析显示 -->
                            <el-form-item label="解析" v-if="form.analysis">
                                <div class="editor-wrapper">
                                    <editor v-model="form.analysis" :min-height="120" :readonly="true" />
                                    <div class="editor-overlay"></div>
                                </div>
                            </el-form-item>

                            <!-- 备注显示 -->
                            <el-form-item label="备注" v-if="form.questionRemark">
                                <el-input v-model="form.questionRemark" type="textarea" :rows="3" :readonly="true" />
                            </el-form-item>

                            <!-- 编程题相关显示 -->
                            <template v-if="form.questionType === 8">
                                <el-form-item label="编程语言">
                                    <el-select v-model="form.language" disabled>
                                        <el-option v-for="item in programmingLanguageList" :key="item.language"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="代码">
                                    <code-editor v-model="form.code" :language="form.language || 'javascript'"
                                    :key="'code-editor-' + form.questionId" :readOnly="true" theme="light" />
                                </el-form-item>
                            </template>
                        </el-form>
                        <template #footer>
                            <div class="dialog-footer">
                                <el-button @click="viewOpen = false">关 闭</el-button>
                            </div>
                        </template>
                    </el-dialog>
                </el-col>
            </el-row>
        </Cards>
    </div>
</template>

<script setup name="BookQuestion">
import TeacherSpaceTabs from "@/views/teacherSpace/TeacherSpaceTabs.vue";
import { listMoocQuestionWithOptions, getMoocQuestion, delMoocQuestion, addMoocQuestion, updateMoocQuestion, importQuestions, moveToRecycleBin, checkPaperReference } from "@/api/edu/moocQuestion.js";

import { tree, addFolder, updateFolder, delFolder } from "@/api/edu/moocQuestionFolder.js";
import Catalog from '@/components/QuestionPreview/catalog/index.vue'
import { de } from "element-plus/es/locales.mjs";
// 修改导入路径
import MatchingQuestion from '@/views/openClass/components/resources/MatchingQuestion.vue'
import MatchingQuestionPicker from '@/views/openClass/components/resources/MatchingQuestionPicker.vue'
import {questionTypeOptions } from '@/utils/optionUtil'
import * as XLSX from 'xlsx'
import QuestionPreview from '@/components/QuestionPreview/index.vue'
import DeleteModal from '@/components/DeleteModal/index.vue'
import { isHtmlTag } from "@/utils/matchUtil.js";
const { proxy } = getCurrentInstance();

const bookQuestionList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const version = ref(0);

const importDialogVisible = ref(false);
const importList = ref([]);
const importForm = ref({
    file: null,
    fileList: [],
    importType: 'normal' // 默认为普通题型
});

// 添加删除相关的响应式变量
const deleteModalVisible = ref(false);
const currentDeleteItem = ref(null);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    folderId: null,
    questionType: null,
    questionContent: null,
    rightAnswer: null,
    analysis: null,
    chapterId: null,
    bookId: null,
    disorder: null,
    sort: null,
  },
  rules: {
    questionType: [
      { required: true, message: "题目类型不能为空", trigger: "change" }
    ],

    questionContent: [
      { required: true, message: '题干不能为空', trigger: 'blur' }
    ]
  },

});

const { queryParams, form, rules } = toRefs(data);

const allSelected = ref(false);

// 在导入语句后添加题型常量
const questionTypes = questionTypeOptions;

/** 查询数字教材习题列表 */
function getList() {
  loading.value = true;
  listMoocQuestionWithOptions(queryParams.value).then(response => {
    bookQuestionList.value = response.rows;

    // 处理编程题的代码内容
    bookQuestionList.value = bookQuestionList.value.map(item => {
      if (item.questionType === 8) {
        try {
          item.code = getCodeContent(item.codeContent) || '';
          item.language = getProgrammingLanguage(item.codeContent) || 'javascript';
        } catch (e) {
          console.error('解析编程题内容失败:', e);
          item.code = '';
          item.language = 'javascript';
        }
      }
      return item;
    });

    total.value = response.total;
    loading.value = false;
    // 更新版本号，触发视图更新
    version.value++;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    questionId: null,
    questionType: 1,
    questionContent: '',
    rightAnswer: '',
    analysis: '',
    chapterId: null,
    bookId: null,
    disorder: 1,
    sort: 0,
    folderId: queryParams.value.folderId || null,
    options: [],
    language: 'javascript'
  };

  // 根据题型设置不同的默认选项
  initOptions(form.value.questionType);

  // 重置表单校验状态
  nextTick(() => {
    if (proxy.$refs["bookQuestionRef"]) {
      proxy.$refs["bookQuestionRef"].resetFields();
    }
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange() {
  ids.value = bookQuestionList.value
    .filter(item => item.selected)
    .map(item => item.questionId);
  multiple.value = ids.value.length === 0;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  form.value.folderId = queryParams.value.folderId; // 设置当前选中的目录ID
  open.value = true;
  title.value = "添加数字教材习题";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _questionId = row.questionId || ids.value;
  getMoocQuestion(_questionId).then(response => {
    // 解码富文本内容
    const data = response.data;
    data.questionContent = safeDecode(data.questionContent || '');
    data.analysis = safeDecode(data.analysis || '');
    data.rightAnswer = safeDecode(data.rightAnswer || '');
    data.questionRemark = safeDecode(data.questionRemark || '');
    
    // 处理选项数据
    if (data.options) {
      data.options = data.options.map(option => ({
        ...option,
        optionContent: safeDecode(option.optionContent || ''),
        rightFlag: option.rightFlag === 1
      }));
      
      // 对于单选题和判断题，设置correctOptionIndex
      if ([1, 7].includes(data.questionType)) {
        const correctIndex = data.options.findIndex(opt => opt.rightFlag);
        correctOptionIndex.value = correctIndex >= 0 ? correctIndex : -1;
      }
      
      // 处理连线题的选项
      if (data.questionType === 5) {
        // 初始化 matchingData
        matchingData.value = {
          leftOptions: [],
          rightOptions: [],
          matchingPairs: []
        };

        // 分离左右选项
        const leftOptions = data.options
          .filter(opt => opt.optionPosition === 1)
          .sort((a, b) => a.sort - b.sort)
          .map((opt, index) => ({
            id: index,
            content: safeDecode(opt.optionContent || '')
          }));

        const rightOptions = data.options
          .filter(opt => opt.optionPosition === 2)
          .sort((a, b) => a.sort - b.sort)
          .map((opt, index) => ({
            id: index,
            content: safeDecode(opt.optionContent || '')
          }));

        // 解析匹配对
        let pairs = [];
        try {
          if (data.rightAnswer) {
            const answers = typeof data.rightAnswer === 'string' 
              ? JSON.parse(data.rightAnswer) 
              : data.rightAnswer;
            
            pairs = answers.map(pair => ({
              left: {
                id: parseInt(pair.source)
              },
              right: {
                id: parseInt(pair.target)
              }
            }));
          }
        } catch (e) {
          console.error('解析连线答案失败:', e);
          pairs = [];
        }

        // 更新 matchingData
        matchingData.value = {
          leftOptions,
          rightOptions,
          matchingPairs: pairs
        };
      }
    }
    
    // 处理编程题的内容
    if (data.questionType === 8) {
      try {
        const codeContent = JSON.parse(data.codeContent || '{}');
        data.code = codeContent.code || '';
        data.language = codeContent.language || 'javascript';
      } catch (e) {
        console.error('解析编程题内容失败:', e);
      }
    }
    
    form.value = data;
    open.value = true;
    title.value = "修改数字教材习题";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["bookQuestionRef"].validate(valid => {
    if (valid) {
      // 处理不同题型的数据
      const formData = { ...form.value };
      
      // 添加填空题内容验证
      if (formData.questionType === 3) {
        if (isHtmlTag(formData.questionContent)) {
            proxy.$modal.msgError("填空内容不能包含样式");
              return;
        }
      }
      
      // 原有代码
      formData.folderId = queryParams.value.folderId || formData.folderId || 0;
      
      // 处理编程题的代码内容
      if (formData.questionType === 8) {
        formData.codeContent = safeEncode(JSON.stringify({
          code: formData.code || '',
          language: formData.language || 'javascript'
        }));
      }
      
      // URL编码富文本内容
      formData.questionContent = safeEncode(formData.questionContent || '');
      formData.analysis = safeEncode(formData.analysis || '');
      formData.rightAnswer = safeEncode(formData.rightAnswer || '');
      
      // 处理选项中的富文本内容，并添加递增的 sort 值
      if (formData.options) {
        formData.options = formData.options.map((option, index) => ({
          ...option,
          optionContent: safeEncode(option.optionContent || ''),
          rightFlag: option.rightFlag ? 1 : 0,
          sort: index // 添加递增的 sort 值
        }));
      }
      
      // 处理连线题
      if (formData.questionType === 5) {
        // 重置选项和答案
        formData.options = [];
        
        // 处理左侧选项
        matchingData.value.leftOptions.forEach((left, index) => {
          formData.options.push({
            optionContent: safeEncode(left.content || ''),
            optionPosition: '1',
            sort: index // 左侧选项的 sort 从 0 开始递增
          });
        });

        // 处理右侧选项
        matchingData.value.rightOptions.forEach((right, index) => {
          formData.options.push({
            optionContent: safeEncode(right.content || ''),
            optionPosition: '2',
            sort: index // 右侧选项的 sort 从 0 开始递增
          });
        });

        // 处理连线对
        formData.rightAnswer = JSON.stringify(
          matchingData.value.matchingPairs.map(pair => ({
            source: pair.left.id,
            target: pair.right.id
          }))
        );
      }

      //处理解析
      formData.analysis = safeEncode(formData.analysis || '');
      if (formData.questionId != null) {
        updateMoocQuestion(formData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMoocQuestion(formData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 改为移入回收站 */
function handleDelete(row) {
    const questionIds = row.questionId
        ? [row.questionId]
        : bookQuestionList.value.filter(item => item.selected).map(item => item.questionId);

    if (questionIds.length === 0) {
        proxy.$modal.msgWarning("请选择要删除的题目");
        return;
    }

    checkPaperReference(questionIds).then(response => {
        const isReferenced = response.code === 200 && response.data === true;
        
        const confirmMessage = isReferenced
            ? '该题目已被试卷占用，删除后试卷将缺少题目，确定要删除吗？'
            : '确认要将选中的题目删除吗？';
            
        const title = isReferenced ? '警告' : '提示';
        const confirmButtonText = isReferenced ? '确认删除' : '确认';

        proxy.$modal.confirm(confirmMessage, title, {
            confirmButtonText: confirmButtonText,
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            moveToRecycleBin(questionIds.join(',')).then(() => {
                proxy.$modal.msgSuccess("已成功删除");
                getList();
                allSelected.value = false;
                handleSelectionChange();
            });
        }).catch(() => {});
    });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('book/bookQuestion/export', {
    ...queryParams.value
  }, `bookQuestion_${new Date().getTime()}.xlsx`)
}

// 添加目录相关数据和方法
const catalogList = ref([])

// 获取目录列表
async function getCatalogList(searchForm) {
    try {
        // 处理搜索表单
        const queryForm = searchForm || {};
        queryForm.pageSize=999;
        const response = await tree(queryForm);
        
        // 直接使用后端返回的树结构
        const folders = response.data || [];
        
        // 如果没有目录且不是搜索状态，创建默认目录
        if (folders.length === 0 && !searchForm?.folderName) {
            const defaultCatalog = {
                folderName: '默认目录',
                parentId: 0,
                orderNum: 1,
                defaultType: 1
            };
            
            await addFolder(defaultCatalog);
            // 重新获取目录列表
            const newResponse = await tree();
            const newFolders = newResponse.data || [];
            
            // 更新状态
            catalogList.value = newFolders;
            folderTreeData.value = flattenTree(newFolders);
            
            // 设置默认选中第一个目录
            if (newFolders.length > 0) {
                const firstFolder = newFolders[0];
                queryParams.value.folderId = firstFolder.folderId;
                handleCatalogClick(firstFolder);
                getList();
            }
        } else {
            // 更新状态
            catalogList.value = folders;
            folderTreeData.value = flattenTree(folders);
            
            // 如果没有选中的目录，默认选中第一个
            if (!queryParams.value.folderId && folders.length > 0) {
                const firstFolder = folders[0];
                queryParams.value.folderId = firstFolder.folderId;
                handleCatalogClick(firstFolder);
                getList();
            }
        }
        
        // 构建移动目录的树形选项
        buildFolderTree();
        
    } catch (error) {
        console.error("获取目录数据失败:", error);
        proxy.$modal.msgError("获取目录数据失败");
    }
}

// 将树形结构扁平化为数组的辅助函数
function flattenTree(tree) {
    const result = [];
    
    function flatten(nodes) {
        if (!nodes) return;
        
        for (const node of nodes) {
            result.push({...node});
            if (node.children && node.children.length > 0) {
                flatten(node.children);
            }
        }
    }
    
    flatten(tree);
    return result;
}

// 优化移动目录树的构建
function buildFolderTree() {
    if (!folderTreeData.value) return;
    
    // 更新树形选项
    folderTreeOptions.value = [{
        folderId: '0',
        folderName: '根目录',
        children: catalogList.value
    }];
}



// 提交移动操作
async function submitMove() {
    if (moveForm.value.parentId) {
        try {
            const targetFolderId = moveForm.value.parentId[moveForm.value.parentId.length - 1];
            
            await updateFolder({
                folderId: moveForm.value.currentId,
                parentId: targetFolderId
            });
            
            proxy.$modal.msgSuccess("移动成功");
            moveCatalogDialogVisible.value = false;
            getCatalogList();

        } catch (error) {
            proxy.$modal.msgError("移动失败：" + error.message);
        }
    } else {
        proxy.$modal.msgWarning("请选择目标目录");
    }
}

// 添加一个变量来跟踪当前选中的选项索引
const correctOptionIndex = ref(-1);


const matchingData = ref({
  leftOptions: [],
  rightOptions: [],
  matchingPairs: []
});

// 添加 viewMatchingData 变量用于查看模式
const viewMatchingData = ref({
  leftOptions: [],
  rightOptions: [],
  matchingPairs: []
});

const handleMatchingUpdate = (newValue) => {
  // 更新表单中的连线题数据
  form.value.options = [];
  form.value.rightAnswer = [];

  // 处理左侧选项
  newValue.leftOptions.forEach((left, index) => {
    form.value.options.push({
      optionContent: left.content,
      optionPosition: '1',
      sort: index
    });
  });

  // 处理右侧选项
  newValue.rightOptions.forEach((right, index) => {
    form.value.options.push({
      optionContent: right.content,
      optionPosition: '2',
      sort: index
    });
  });

  // 处理连线对
  form.value.rightAnswer = JSON.stringify(newValue.matchingPairs.map(pair => ({
    source: pair.left.id,
    target: pair.right.id
  })));
};



// 在 script setup 中添加获取题型名称的方法
const getQuestionTypeName = (type) => {
    const questionType = questionTypes.find(item => item.value === type)
    return questionType ? questionType.label : '未知题型'
}

// 在 script setup 中添加判断正确选项的方法
const isCorrectOption = (rightAnswer, option) => {
    // 根据题目类型和答案格式进行判断
    if (typeof rightAnswer === 'string') {
        // 如果答案是字符串形式，可能是以逗号分隔的多个答案
        const correctAnswers = rightAnswer.split(',');
        return correctAnswers.includes(option.value);
    }
    return false;
};

// 添加判断是否显示选项的方法
const shouldShowOptions = (questionType) => {
    // 填空题(type=3)不显示选项
    return questionType !== 3;
};

// 添加处理填空题格式的方法
const formatBlankQuestion = (content, questionType) => {
    if (questionType !== 3) return content;
    
    // 将 ### answer ### 格式转换为带下划线和答案的HTML
    return content.replace(/###(.*?)###/g, (match, answer) => {
        return `<span class="blank-line"><span class="blank-answer">${answer}</span></span>`;
    });
};

// 添加选项
function addOption() {
    if (!form.value.options) {
        form.value.options = [];
    }
    form.value.options.push({
        optionContent: '',
        rightFlag: false
    });
}

// 移除选项
function removeOption(index) {
    form.value.options.splice(index, 1);
}

// 移动选项位置
function moveOption(index, direction) {
    const options = form.value.options;
    if (direction === 'up' && index > 0) {
        [options[index], options[index - 1]] = [options[index - 1], options[index]];
    } else if (direction === 'down' && index < options.length - 1) {
        [options[index], options[index + 1]] = [options[index + 1], options[index]];
    }
}

// 检查是否有其他正确选项（用于单选题）
function hasOtherCorrectOption(currentIndex) {
    return form.value.options.some((option, index) => 
        index !== currentIndex && option.rightFlag
    );
}

// 添加连线题选项
function addMatchingOption(side) {
    const target = side === 'left' ? 'leftOptions' : 'rightOptions';
    if (!form.value[target]) {
        form.value[target] = [];
    }
    form.value[target].push({ content: '' });
}

// 移除连线题选项
function removeMatchingOption(side, index) {
    const target = side === 'left' ? 'leftOptions' : 'rightOptions';
    form.value[target].splice(index, 1);
}

// 添加初始化选项的函数
function initOptions(questionType) {
  if (questionType === 7) { // 判断题
    form.value.options = [
      { optionContent: '正确', rightFlag: false, optionType: null, optionIndex: 0 },
      { optionContent: '错误', rightFlag: false, optionType: null, optionIndex: 1 }
    ];
    correctOptionIndex.value = -1; // 重置单选按钮状态
  } else if ([1, 2].includes(questionType)) {
    // 单选或多选题，如果选项少于2个，则重置为默认4个选项
    form.value.options = [
      { optionContent: '', rightFlag: false, optionType: null, optionIndex: 0 },
      { optionContent: '', rightFlag: false, optionType: null, optionIndex: 1 },
      { optionContent: '', rightFlag: false, optionType: null, optionIndex: 2 },
      { optionContent: '', rightFlag: false, optionType: null, optionIndex: 3 }
    ];
    if (questionType === 1) {
      correctOptionIndex.value = -1; // 重置单选按钮状态
    }
  }
}

// 添加处理单选按钮变化的方法
function handleRadioChange(selectedIndex) {
  // 遍历所有选项，只将选中的选项设置为正确答案
  form.value.options.forEach((option, index) => {
    option.rightFlag = index === selectedIndex;
  });
}

// 目录相关的响应式变量
const catalogOpen = ref(false);
const moveCatalogDialogVisible = ref(false);
const catalogForm = ref({
    folderId: null,
    folderName: null,
    parentId: 0
});
const moveForm = ref({
    currentId: null,
    parentId: null
});
const folderTreeOptions = ref([]);
const folderTreeData = ref([]);

const catalogRules = {
    folderName: [
        { required: true, message: "目录名称不能为空", trigger: "blur" },
        { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" }
    ]
};

// 取消目录对话框
function cancelCatalog() {
    catalogOpen.value = false;
    resetCatalogForm();
}

// 重置目录表单
function resetCatalogForm() {
    catalogForm.value = {
        folderId: null,
        folderName: null,
        parentId: 0
    };
    proxy.resetForm("catalogRef");
}

// 在组件挂载时获取目录列表
onMounted(() => {
    getCatalogList();
});

// 添加处理目录点击的函数
function handleCatalogClick(catalog) {
    if (!catalog) return;
    
    // 更新当前选中的目录ID
    queryParams.value.folderId = catalog.folderId || catalog.catalogId;
    
    // 重置分页到第一页
    queryParams.value.pageNum = 1;
    
    // 获取该目录下的题目列表
    getList();
}

// 添加处理目录相关的其他函数
function handleAddCatalog(row) {
    resetCatalogForm();
    catalogForm.value.parentId = row.folderId || 0;
    catalogOpen.value = true;
    title.value = "添加目录";
}

function handleUpdateCatalog(data) {
    resetCatalogForm();
    catalogForm.value = { ...data };
    catalogOpen.value = true;
    title.value = "修改目录";
}

function handleDeleteCatalog(data) {
    delFolder(data.folderId).then(() => {
        getCatalogList();
        proxy.$modal.msgSuccess("删除成功");
    });
}

function handleMoveCatalog(data) {
    moveForm.value.currentId = data.folderId;
    moveForm.value.parentId = null;
    moveCatalogDialogVisible.value = true;
    buildFolderTree();
}

// 提交目录表单
function submitCatalogForm(formData) {
    // formData 包含了必要的字段，直接使用
    if (formData.folderId != null) {
        updateFolder(formData).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            getCatalogList();
        });
    } else {
        addFolder(formData).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            getCatalogList();
        });
    }
}

// 添加解析编程题内容的辅助函数
const getCodeContent = (questionContent) => {
    try {
        const codeData = JSON.parse(safeDecode(questionContent || '{}'));
        return codeData.code || '';
    } catch (e) {
        console.error('解析代码内容失败:', e);
        return '';
    }
};

const getProgrammingLanguage = (questionContent) => {
    try {
        const codeData = JSON.parse(safeDecode(questionContent || '{}'));
        return codeData.language || 'javascript';
    } catch (e) {
        console.error('解析编程语言失败:', e);
        return 'javascript';
    }
};


function handleImport() {
    importDialogVisible.value = true;
    importList.value = [];
    importForm.value = {
        file: null,
        fileList: [],
        importType: 'normal'
    };
}


const handleImportChange = (file) => {
    // 检查文件类型
    const isExcel = file.raw.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                    file.raw.type === 'application/vnd.ms-excel'
    if (!isExcel) {
        proxy.$modal.msgError('只能上传Excel文件！')
        return
    }
    // 检查文件大小
    const isLt10M = file.raw.size / 1024 / 1024 < 10
    if (!isLt10M) {
        proxy.$modal.msgError('文件大小不能超过 10MB!')
        return
    }
    
    file.uploadProgress = 0
    importList.value.push(file)
}

const removeImportFile = (index) => {
    importList.value.splice(index, 1)
}

const submitImportForm = async () => {
    if (importList.value.length === 0) {
        proxy.$modal.msgWarning('请选择要导入的文件')
        return
    }
    
    try {
        const allQuestionData = [] // 收集所有题目数据
        
        for (let file of importList.value) {
            const formData = new FormData()
            formData.append('file', file.raw)
            formData.append('importType', importForm.value.importType)
            formData.append('folderId', queryParams.value.folderId)
            
            // 读取Excel文件内容
            const workbook = await readExcelFile(file.raw)
            const worksheet = workbook.Sheets[workbook.SheetNames[0]]
            const rows = XLSX.utils.sheet_to_json(worksheet)
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i]
                
                // 根据导入类型处理不同的模板
                if (importForm.value.importType === 'matching') {
                    // 连线题模板处理
                    if (!row['题干']||!row['选项']) {
                        console.warn('跳过无效行:', row)
                        continue
                    }
                    
                    const questionData = {
                        questionType: 5, // 连线题类型
                        questionContent: safeEncode(row['题干'] || ''),
                        analysis: safeEncode(row['解析'] || ''),
                        folderId: queryParams.value.folderId,
                        options: []
                    }
                    
                    const leftOptions = [];
                    const rightOptions = [];
                    
                    // 处理左侧选项
                    for (let j = 'A'.charCodeAt(0); j <= 'E'.charCodeAt(0); j++) {
                        const letter = String.fromCharCode(j);
                        const content = row[`左侧项-${letter}`];
                        if (content) {
                            leftOptions.push({
                                optionContent: safeEncode(content),
                                optionPosition: '1',
                                sort: j - 'A'.charCodeAt(0)
                            });
                        }
                    }
                    
                    // 处理右侧选项
                    for (let j = 1; j <= 5; j++) {
                        const content = row[`右侧项-${j}`];
                        if (content) {
                            rightOptions.push({
                                optionContent: safeEncode(content),
                                optionPosition: '2',
                                sort: j - 1
                            });
                        }
                    }
                    
                    // 合并所有选项
                    questionData.options = [...leftOptions, ...rightOptions];
                    
                    // 处理连线答案
                    const connections = (row['选项'] || '').split(',').map(pair => {
                        const [left, right] = pair.split('-');
                        return {
                            source: left.charCodeAt(0) - 'A'.charCodeAt(0),
                            target: parseInt(right) - 1
                        };
                    });
                    
                    questionData.rightAnswer = JSON.stringify(connections);
                    allQuestionData.push(questionData);
                } else {
                    // 普通题型模板处理
                    if (!row['题型'] || !row['题干']) {
                        console.warn('跳过无效行:', row)
                        continue
                    }

                                    
                    const questionData = {
                        questionType: getQuestionTypeValue(row['题型']),
                        questionContent: safeEncode(row['题干'] || ''),
                        rightAnswer: safeEncode(row['正确答案'] || ''),
                        analysis: safeEncode(row['答案解析'] || ''),
                        folderId: queryParams.value.folderId,
                        options: []
                    }

                                              // 添加填空题内容验证
                                              if (questionData.questionType === 3) {
                        const content = row['题干'] || '';
                       
                        if ( isHtmlTag(content)) {
                            proxy.$modal.msgError(`第${i+1}行填空题内容包含样式，已跳过该题目`);
                                    break;
                            }
                        }
                    
                    // 处理普通题型的选项
                    if ([1, 2, 4, 7].includes(questionData.questionType)) {
                        // 保持原有的选项处理逻辑
                        if (questionData.questionType === 7) { // 判断题
                            questionData.options = [
                                {
                                    optionContent: safeEncode('正确'),
                                    rightFlag: row['正确答案'] === 'A' ? 1 : 0,
                                    optionIndex: 0
                                },
                                {
                                    optionContent: safeEncode('错误'),
                                    rightFlag: row['正确答案'] === 'B' ? 1 : 0,
                                    optionIndex: 1
                                }
                            ]
                        } else if (questionData.questionType === 4) { // 排序题
                            const sortedOptions = [];
                            for (let j = 1; j <= 5; j++) {
                                const optionContent = row[`选项${j}`]
                                if (optionContent) {
                                    sortedOptions.push({
                                        optionContent: safeEncode(optionContent),
                                        rightFlag: 0,
                                        optionIndex: j - 1,
                                        sort: j - 1
                                    });
                                }
                            }
                            questionData.options = sortedOptions;
                        } else { // 单选题和多选题
                            for (let j = 1; j <= 5; j++) {
                                const optionContent = row[`选项${j}`]
                                if (optionContent) {
                                    const rightAnswer = row['正确答案'] || ''
                                    const rightFlag = rightAnswer.includes(String.fromCharCode(64 + j))
                                    
                                    questionData.options.push({
                                        optionContent: safeEncode(optionContent),
                                        rightFlag: rightFlag ? 1 : 0,
                                        optionIndex: j - 1
                                    })
                                }
                            }
                        }
                    }
                    allQuestionData.push(questionData);
                }
                
    
                file.uploadProgress = Math.floor(((i + 1) / rows.length) * 100)
            }
        }
        
        // 一次性提交所有题目数据
        await importQuestions(allQuestionData).then(response => {
            proxy.$modal.msgSuccess(response.msg)
            importDialogVisible.value = false
            importList.value = []
            getList()
        })
    } catch (error) {
        console.error('导入失败:', error)
        proxy.$modal.msgError('导入失败：' + (error.message || '请检查文件格式是否正确'))
    }
}

const isAllUploaded = (list) => {
    return list.length > 0 && list.every(file => !file.uploadProgress || file.uploadProgress === 100)
}

// 添加下载模板方法
function downloadTemplate(type) {
    const templateName = type === 'matching' ? '连线题目导入模板.xlsx' : '普通题目导入模板.xlsx';
    window.location.href = `/${templateName}`;
}

// 添加读取Excel文件的辅助函数
const readExcelFile = (file) => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (e) => {
            try {
                const data = new Uint8Array(e.target.result)
                const workbook = XLSX.read(data, { type: 'array' })
                resolve(workbook)
            } catch (error) {
                reject(error)
            }
        }
        reader.onerror = (error) => reject(error)
        reader.readAsArrayBuffer(file)
    })
}

// 修改题型转换函数，利用已有的 questionTypes 数组
const getQuestionTypeValue = (typeText) => {
    const questionType = questionTypes.find(type => type.label === typeText);
    return questionType ? questionType.value : 1; // 默认返回单选题类型
}

// 添加 viewOpen ref
const viewOpen = ref(false);

// 添加查看按钮处理函数
function handleView(row) {
    reset();
    const questionId = row.questionId;
    getMoocQuestion(questionId).then(response => {

    debugger;
        // 解码富文本内容
        const data = response.data;
        data.questionContent = safeDecode(data.questionContent || '');
        data.analysis = safeDecode(data.analysis || '');
        data.rightAnswer = safeDecode(data.rightAnswer || '');
        data.questionRemark = safeDecode(data.questionRemark || '');
        
        // 处理选项数据
        if (data.options) {
            data.options = data.options.map(option => ({
                ...option,
                optionContent: safeDecode(option.optionContent || ''),
                rightFlag: option.rightFlag === 1
            }));
            
            // 处理连线题的选项
            if (data.questionType === 5) {
                // 初始化 viewMatchingData
                viewMatchingData.value = {
                    leftOptions: [],
                    rightOptions: [],
                    matchingPairs: []
                };

        
                // 分离左右选项
                const leftOptions = data.options
                    .filter(opt => opt.optionPosition === 1)
                    .sort((a, b) => a.sort - b.sort)
                    .map((opt, index) => ({
                        id: index,
                        content: safeDecode(opt.optionContent || '')
                    }));

                const rightOptions = data.options
                    .filter(opt => opt.optionPosition === 2)
                    .sort((a, b) => a.sort - b.sort)
                    .map((opt, index) => ({
                        id: index,
                        content: safeDecode(opt.optionContent || '')
                    }));

                // 解析匹配对
                let pairs = [];
                try {
                    if (data.rightAnswer) {
                        const answers = typeof data.rightAnswer === 'string' 
                        ? JSON.parse(data.rightAnswer) 
                        : data.rightAnswer;
                        
                        pairs = answers.map(pair => ({
                            left: {
                                id: parseInt(pair.source)
                            },
                            right: {
                                id: parseInt(pair.target)
                            }
                        }));
                    }
                } catch (e) {
                    console.error('解析连线答案失败:', e);
                    pairs = [];
                }

                // 更新 viewMatchingData
                viewMatchingData.value = {
                    leftOptions,
                    rightOptions,
                    matchingPairs: pairs
                };
            }
        }
        
        // 处理编程题的代码内容
        if (data.questionType === 8) {
            try {
                const codeContent = JSON.parse(data.codeContent || '{}');
                data.code = codeContent.code || '';
                data.language = codeContent.language || 'javascript';
            } catch (e) {
                console.error('解析编程题内容失败:', e);
                data.code = '';
                data.language = 'javascript';
            }
        }
        
        form.value = data;
        viewOpen.value = true;
        title.value = "查看题目";
    });
}

// 添加移动题目相关的响应式变量
const moveQuestionDialogVisible = ref(false);
const moveQuestionForm = ref({
    questionId: null,
    folderId: null
});

// 添加处理移动题目的函数
function handleMoveQuestion(question) {
    moveQuestionForm.value.questionId = question.questionId;
    moveQuestionForm.value.folderId = null;
    moveQuestionDialogVisible.value = true;
    buildFolderTree(); // 确保树形数据是最新的
}

// 提交移动题目操作
async function submitMoveQuestion() {
    if (moveQuestionForm.value.folderId) {
        try {
            const targetFolderId = moveQuestionForm.value.folderId[moveQuestionForm.value.folderId.length - 1];
            
            // 调用更新接口，只传递必要的字段
            await updateMoocQuestion({
                questionId: moveQuestionForm.value.questionId,
                folderId: targetFolderId
            });
            
            proxy.$modal.msgSuccess("移动成功");
            moveQuestionDialogVisible.value = false;
            getList(); // 刷新题目列表
        } catch (error) {
            proxy.$modal.msgError("移动失败：" + error.message);
        }
    } else {
        proxy.$modal.msgWarning("请选择目标目录");
    }
}

// 添加跳转到回收站的方法
function goToRecycleBin() {
    proxy.$router.push({
        path: '/resource/userQuestionRecycle',
    });
}

// 添加安全的编码和解码函数
function safeEncode(content) {
    if (!content) return '';
    try {
        // 检查内容是否已经被编码
        const isEncoded = (str) => {
            try {
                return decodeURIComponent(str) !== str;
            } catch (e) {
                return false;
            }
        };
        
        // 如果已经编码过，直接返回
        if (isEncoded(content)) {
            return content;
        }
        return encodeURIComponent(content);
    } catch (e) {
        console.error('编码内容失败:', e);
        return content;
    }
}

function safeDecode(content) {
  if (!content) return ''
  try {
    // 检查内容是否已经被编码
    const isEncoded = (str) => {
      try {
        // 尝试解码，如果解码后的结果与原字符串不同，说明是编码过的
        const decoded = decodeURIComponent(str)
        return decoded !== str
      } catch (e) {
        // 如果解码失败，说明可能包含特殊字符，此时返回 false
        return false
      }
    }

    // 如果内容已经被编码，则进行解码
    if (isEncoded(content)) {
      return decodeURIComponent(content)
    }
    
    // 如果内容未被编码或解码失败，直接返回原内容
    return content
  } catch (e) {
    console.error('解码内容失败:', e)
    return content
  }
}


// 添加语言映射函数
const mapLanguageToEditor = (language) => {
    // 使用 programmingLanguageList 创建映射
    const languageMap = {
        'html': 'markup',
        'shell': 'bash',
        'xml': 'markup'
    };
    
    // 将 programmingLanguageList 中的所有语言添加到映射中
    programmingLanguageList.forEach(item => {
        // 只有当映射中没有特殊处理的情况下，才使用原始值
        if (!languageMap[item.value]) {
            languageMap[item.value] = item.value;
        }
    });
    
    return languageMap[language] || language || 'javascript';
};

// 全选方法
function selectAll(checked) {
    if (checked) {
        // 选中所有checkbox
        bookQuestionList.value.forEach(item => {
            item.selected = true;
        });
        // 更新ids数组和multiple状态
        ids.value = bookQuestionList.value.map(item => item.questionId);
        multiple.value = false;
    } else {
        // 取消选中所有checkbox
        bookQuestionList.value.forEach(item => {
            item.selected = false;
        });
        // 清空ids数组并禁用删除按钮
        ids.value = [];
        multiple.value = true;
    }
}

function appendBlanks() {
    // 获取当前题干内容
    let content = form.value.questionContent || '';
    // 添加默认内容，不包含HTML标签
    const blankContent = '请输入正确答案';
    // 在题干后面添加填空标记
    content = content.replace(/<\/p>/, ` ###${blankContent}### </p>`);
    // 更新题干内容
    form.value.questionContent = content;
}

</script>
<style lang="css">
.blank-line {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    min-width: 100px;
    margin: 0 5px;
    
    .blank-answer {
        color: #409EFF;
        font-size: 14px;
        margin-bottom: 2px;
    }
    
    &::after {
        content: '';
        width: 100%;
        height: 1px;
        background-color: #000;
        display: block;
    }
}
</style>

<style lang="scss" scoped>
    .tool{
        padding: 20px 0 0;
    }

    .top20{
        margin-top: 20px;
    }

    .pagination{
        float: right;
        margin:20px 0;
    }

    .app-container {
        padding: 20px;
    }

    .question-list {
        display: grid;
        grid-gap: 20px;
        padding: 10px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .question-card {
        position: relative;
        border: 1px solid #dcdfe6;
        border-radius: 8px;
        padding: 15px;
        background: #fff;
        width: 100%;
        box-sizing: border-box;
        overflow-wrap: break-word;
        word-wrap: break-word;
        min-width: 0;
        max-width: 100%;
        overflow-x: hidden;
        
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            
            .question-info {
                display: flex;
                align-items: center;
                gap: 10px;
                
                .question-checkbox {
                    margin: 0;
                }
                
                .el-tag {
                    margin: 0;
                }
            }
            
            .operation {
                margin-left: auto;
            }
        }
        
        &:hover {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
        
        .question-content {
            max-width: 100%;
            overflow-x: hidden;
            
            .remark-section {
                padding: 0 15px 10px 15px; // 与题干内容对齐，下方增加一些间距
                color: #909399; // 备注文字颜色
                font-size: 14px; // 备注文字大小
                word-break: break-all; // 备注内容过长时换行
            }

            .content-item {
                margin-bottom: 15px;
                
                .label {
                    font-weight: bold;
                    margin-bottom: 8px;
                    color: #606266;
                }
                
                .content {
                    color: #303133;
                    line-height: 1.6;
                    word-break: break-word;
                    overflow-wrap: break-word;
                }

                :deep(.code-editor-container) {
                    margin: 10px 0;
                }
            }

            .options-area {
                margin: 10px 0;
                
                .option-item {
                    margin-bottom: 8px;
                    padding: 5px 10px;
                    
                    &:hover {
                        background-color: #f5f7fa;
                    }
                }
            }
        }
    }

    .option-content {
        display: flex;
        align-items: center;
        
        .option-label {
            margin-right: 8px;
            font-weight: bold;
        }
        
        .correct-icon {
            margin-left: 8px;
            color: #67c23a;
        }
    }

    .popover-buttons {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .blank-line {
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        min-width: 100px;
        margin: 0 5px;
        
        .blank-answer {
            color: #409EFF;
            font-size: 14px;
            margin-bottom: 2px;
        }
        
        &::after {
            content: '';
            width: 100%;
            height: 1px;
            background-color: #000;
            display: block;
        }
    }

    .options-editor {
        margin-bottom: 20px;
        margin-left: 35px;
        .options-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;

            .subtitle {
                font-weight: bold;
                color: #606266;
            }
        }

        .option-row {
            margin-bottom: 10px;
            align-items: center;

            .option-label {
                text-align: center;
                line-height: 32px;
                font-weight: bold;
            }
        }
    }

    .matching-editor {
        margin-bottom: 20px;
        
        .column-header {
            font-weight: bold;
            margin-bottom: 10px;
            color: #606266;
        }
        
        .matching-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            
            :deep(.el-button) {
                margin-left: 10px;
            }
        }
    }

    .mb-10 {
        margin-bottom: 10px;
    }

    .template-download {
        display: flex;
        gap: 20px;
        justify-content: center;
        margin-bottom: 20px;
        padding: 10px;
        border-bottom: 1px solid #ebeef5;
    }

    .import-item {
        margin-bottom: 10px;
        
        .import-item-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            
            .import-info {
                display: flex;
                align-items: center;
                gap: 8px;
                
                .el-icon {
                    font-size: 20px;
                    color: #909399;
                }
                
                .import-name {
                    color: #606266;
                }
            }
        }
    }

    .editor-wrapper {
      position: relative;

      .editor-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.01);
        z-index: 10;
        cursor: not-allowed;
      }
    }

    .operation {
      .more-btn {
        padding: 4px 8px;

        &:hover {
          background-color: #f5f7fa;
          border-radius: 4px;
        }

        .el-icon {
          font-size: 16px;
          color: #909399;
        }
      }
    }

    /* 添加左对齐表单样式 */
    .left-aligned-form {
        :deep(.el-form-item) {
            text-align: left;
            margin-bottom: 22px;
            margin-right: 40px;
            
            .el-form-item__label {
                text-align: left;
                padding-left: 0;
                width: 100px !important;
                float: left;
            }
            
            .el-form-item__content {
                width: calc(100% - 100px);
            }
        }
    }
    
    /* 确保编辑器和选择器等组件也是左对齐的 */
    .left-aligned-form :deep(.el-select),
    .left-aligned-form :deep(.el-input),
    .left-aligned-form :deep(.editor-container),
    .left-aligned-form :deep(.code-editor-container) {
        width: 100%;
        text-align: left;
    }
    
    /* 单选题和判断题的选项样式 */
    .option-radio-group {
        display: flex;
        align-items: center;
    }
    
    /* 确保选项文字对齐 */
    .option-radio-group .el-radio,
    .option-radio-group .el-checkbox {
        display: flex;
        align-items: center;
        margin-right: 20px;
    }
    
    /* 减少判断题选项之间的间距 */
    .option-radio-group .el-radio + .el-radio,
    .option-radio-group .el-checkbox + .el-checkbox {
        margin-left: 15px;
    }
    
    /* 空数据占位样式 */
    .empty-placeholder {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
        color: #909399;
        font-size: 14px;
        background-color: #f5f7fa;
        border-radius: 4px;
        margin: 20px 0;
    }
    
    /* 全选样式 */
    .select-all-container {
        margin-bottom: 10px;
    }
    
    .select-all-checkbox {
        font-weight: bold;
        color: #409EFF;
    }
</style>