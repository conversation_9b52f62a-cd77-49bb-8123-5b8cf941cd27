<!-- @Author: <PERSON><PERSON><PERSON><PERSON> -->
<template>

  <el-container style="background-color: #F1F5FB">
<!--    <el-header>-->
<!--      <headNavComp @toLogin="toLogin" :showBackground="true" />-->
<!--    </el-header>-->
    <el-main>
      <div class="main-header">
        <div>
          <el-row :gutter="20" class="filters">
            <el-col :span="8" :offset="3">
              <el-breadcrumb :separator-icon="ArrowRight" class="breadcrumb_text">
                <el-breadcrumb-item :to="{ path: '/index' }"
                >首页</el-breadcrumb-item
                >
                <el-breadcrumb-item> 全部公开课</el-breadcrumb-item>
              </el-breadcrumb>
            </el-col>
          </el-row>
        </div>
        <div>
          <el-form-item prop="">
            <el-select placeholder="状态" clearable style="width: 260px" v-model="selectedStatus" @change="handleStatusChange">
              <el-option label="全部" value="" />
              <el-option
                v-for="item in approvalStatusOptions"
                :key="`approval_${item.value}`"
                :label="item.label"
                :value="`approval_${item.value}`"
              />
              <el-option
                v-for="item in reviewStatusOptions"
                :key="`review_${item.value}`"
                :label="item.label"
                :value="`review_${item.value}`"
              />
            </el-select>
          </el-form-item>
        </div>
      </div>
      <div class="main-course">
        <div class="main-btns">
          <el-button id="btn-add" type="primary" round @click="courseAddHandle">新建公开课</el-button>
        </div>
<!--        <div class="main-body">-->
          <courseList :listData="listData" @delete-course="handleDeleteCourse"/>
<!--        </div>-->
        <div class="pagination">
          <el-pagination
              v-model:current-page="queryParams.pageNum"
              v-model:page-size="queryParams.pageSize"
              layout="prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-main>
    <el-dialog v-model="courseAddVisible" :title="title" width="1000" @close="courseAddVisible = false">
      <courseAddForm 
        ref="courseAddFormRef" 
        v-if="courseAddVisible"
        @refreshPage="onRefreshPage"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitCourse">提交申请</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </el-container>
</template>
<script setup>
/* import */
import { ref, reactive, watch, onMounted } from "vue";
import headNavComp from "@/views/home/<USER>/headNavComp/index.vue";
import courseList from "./components/courseList.vue";
import courseAddForm from "./components/courseAddForm.vue";
import {listOpenCourse, removeOpenCourse} from '@/api/edu/openCourse.js';
import {ArrowRight} from "@element-plus/icons-vue";
import { getOptionDesc,approvalStatusOptions,reviewStatusOptions } from "@/utils/optionUtil";

/* data */
const total = ref(0);
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 8,
  approvalStatus: null,  // 审批状态
  reviewStatus: null     // 评审状态
})
const title = ref('新建公开课')
const courseAddVisible = ref(false);
const courseAddFormRef = ref(null);
const listData = ref([]);
const selectedStatus = ref('');

/* methods*/
const handleSizeChange = (val) => {
  queryParams.value.pageSize = val
  getList()
}

// 分页页码改变
const handleCurrentChange = (val) => {
  queryParams.value.pageNum = val
  getList()
}

// 新增dialog弹窗
function courseAddHandle(){
  courseAddVisible.value = true
}

// 保存事件
function submitCourse(){
  courseAddFormRef.value.submitForm();
}

// 页面刷新
function onRefreshPage(){
  queryParams.value.pageNum = 1
  getList();
  courseAddVisible.value = false;
}

// 查询列表
function getList(){
  listOpenCourse(queryParams.value).then(res => {
    if (res.code === 200){
      listData.value  = res.rows;
      total.value  = res.total;
    }
  })
}

// 取消
function cancel(){
  courseAddVisible.value = false
}

// 处理删除课程
function handleDeleteCourse(item) {
  // 这里可以调用删除API
  removeOpenCourse(item.courseId).then(res => {
    if (res.code === 200) {
      // 删除成功后刷新列表
      getList();
    }
  }).catch(error => {
    console.error('删除失败:', error);
  });
}

// 下拉检索框
function handleStatusChange(value) {
  console.log('选中的状态:', value);
  if (value) {
    if (value.startsWith('approval_')) {
      const actualValue = value.replace('approval_', '');
      queryParams.value.applyStatus = actualValue;
      queryParams.value.step = 1;
    } else if (value.startsWith('review_')) {
      const actualValue = value.replace('review_', '');
      queryParams.value.applyStatus = actualValue;
      queryParams.value.step = 2;
    }
  } else {
    queryParams.value.applyStatus = '';
    queryParams.value.step = '';
  }

  queryParams.value.pageNum = 1;
  getList();
}

function init(){
  getList();
}

onMounted(() => {
  init();
})
</script>


<style  lang="scss" scoped>
.main-header {
  width: 80%;
  height: 60px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .filters {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .breadcrumb_text {
    width: 263px;
    height: 16px;
    font-family:
        Alibaba PuHuiTi 2,
        Alibaba PuHuiTi 20;
    font-weight: normal;
    font-size: 1rem;
    color: #666666;
    line-height: 37px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.main-course {
  width: 80%;
  height: 860px;
  margin: 0 auto;
  border-radius: 10px;
  background-color: #ffffff;
  .main-btns{
    width: 100%;
    height: 60px;
    position: relative;
    #btn-add{
      position: absolute;
      top: 10px;
      left: 10px;
    }
  }
}
.pagination {
  float: right;
  margin: 20px;
}
</style>