<template>
  <div class="course-section">
    <!-- 筛选栏 -->
    <el-row :gutter="20" class="filters">
      <el-col :span="8" :offset="3"
        ><span class="filter-label">开课状态</span></el-col
      >
      <el-col :span="4" :offset="1">
        <el-select
          v-model="subjectId"
          placeholder="教育层次"
          size="large"
          @change="getList()"
        >
          <el-option
            v-for="item in educationOptions"
            :key="item.subjectId"
            :label="item.subjectName"
            :value="item.subjectId"
          />
        </el-select>
      </el-col>
      <el-col :span="4" :offset="1">
        <el-select
          v-model="thirdSubjectId"
          placeholder="学科分类"
          size="large"
          @change="getList()"
        >
          <el-option
            v-for="item in classification"
            :key="item.subjectId"
            :label="item.subjectName"
            :value="item.subjectId"
          />
        </el-select>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="filters">
      <el-col :span="18" :offset="3">
        <el-tabs
          type="border-card"
          v-model="editableTabsValue"
          @tab-change="filteredCourses(editableTabsValue)"
        >
          <el-tab-pane v-for="label in elTabPaneLabel" :label="label">
            <!-- 课程卡片网格 -->
            <div class="course-grid" v-if="courses.length > 0">
              <el-card
                v-for="course in courses"
                :key="course.courseId"
                class="course-card"
              >
                <img
                  :src="course.courseCover ? course.courseCover : bookCoverDefault"
                  class="course-cover"
                  alt="课程封面"
                  @click="goToDetail(course.courseId)"
                />
                <div class="course-info">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="course.courseName"
                    placement="bottom-start"
                    :style="{ whiteSpace: 'normal' }"
                  >
                    <h3 class="course-title">
                      {{ course.courseName }}
                    </h3>
                  </el-tooltip>
                  <!-- 描述 tooltip 完整显示 -->
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="course.courseDescription"
                    placement="bottom-start"
                    :style="{ whiteSpace: 'normal' }"
                  >
                    <p class="course-desc">{{ course.courseDescription }}</p>
                  </el-tooltip>
                  <div class="course-footer">
                    <span
                      ><img :src="person" class="person-img" />{{
                        course.learnersNum
                      }}
                      人学习</span
                    >
                  </div>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="course.teacherTeamNames"
                    placement="bottom-start"
                    :style="{ whiteSpace: 'normal' }"
                  >
                    <div class="course-tags">
                      <img :src="star" class="person-img" />
                      教学团队：
                      <span type="info">{{ course.teacherTeamNames }}</span>
                    </div>
                  </el-tooltip>
                </div>
              </el-card>
            </div>
            <div v-else class="no-courses-container">
              <img :src="noOpenCourse" class="no-open-course-img" />
              <span class="course-span">暂无公开课</span>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="OpenCourseList">
import { ref, onMounted } from "vue";
import { listDutpSubjectEducation } from "@/api/basic/subject";
import bookCoverDefault from '@/assets/images/book-cover-default.png';
import person from "@/assets/images/openCourse/person.png";
import star from "@/assets/images/openCourse/star.png";
import noOpenCourse from "@/assets/images/openCourse/no_open_course.png";
import { listNoPageByStudent } from "@/api/basic/course";
import { useRouter } from "vue-router";
// 获取当前路由对象
const route = useRoute();
const router = useRouter();
// 定义 props
const props = defineProps({
  searchKey: {
    type: String,
    default: "",
  },
});
const parm = ref({
  courseStatus: null,
  subjectId: null,
  thirdSubjectId: null,
  realName: null,
  courseName: null,
});
// 筛选状态
const editableTabsValue = ref("0");
const courseStatus = ref(null);
const searchStr = ref("");
const subjectId = ref("");
const thirdSubjectId = ref("");
const educationOptions = ref([]);
const classification = ref([]);
const elTabPaneLabel = ref(["全部", "即将开课", "开课中", "已结束"]);
// 模拟的课程数据（mock 数据）
const courses = ref([]);
watch(
  () => props.searchKey,
  (nValue) => {
    searchStr.value = nValue;
    parm.value.realName = nValue;
    parm.value.courseName = nValue;
    getList(parm.value);
  }
);
// 计算属性：根据状态和筛选条件过滤课程列表
const filteredCourses = (key) => {
  if (key) {
    courseStatus.value = key == 0 ? null : key == 1 ? 0 : key == 2 ? 1 : 2;
  }
  parm.value.courseStatus = courseStatus.value;
  getList();
};

const getDutpSubject = () => {
  listDutpSubjectEducation().then((res) => {
    educationOptions.value = res.data[1];
    classification.value = res.data[3];
  });
};
const getList = () => {
  parm.value.subjectId = subjectId.value;
  parm.value.thirdSubjectId = thirdSubjectId.value;
  listNoPageByStudent(parm.value).then((res) => {
    courses.value = res.data;
  });
};
const goToDetail= (courseId) => {
  const query = { key: courseId };
  router.push({ name: 'OpenCourseDetail', query: query });
};
onMounted(() => {
  if(route.query.key != null || route.query.key != '' || route.query.key != undefined){
    parm.value.realName = route.query.key;
    parm.value.courseName = route.query.key;
  }
  getDutpSubject();
  getList();
  console.log("parm.value,", parm.value);
});
</script>

<style scoped>
.filters {
  margin-top: 20px;
  margin-bottom: 20px;
}
.status-tabs {
  flex: 1;
}
.course-grid {
  /* 使用 CSS Grid 实现响应式卡片布局 */
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fill, minmax(322px, 1fr));
}
.course-card {
  background: #f2f5fa;
  border-radius: 14px 14px 14px 14px;
  border: 2px solid #e6ebf5;
}
.course-cover {
  cursor: pointer;
  width: 100%;
  aspect-ratio: 3 / 2; /* 直接指定宽高比 */
  border-radius: 4px 4px 0 0;
}
.person-img {
  width: 16px;
  height: 16px;
  border-radius: 100%; /* 可选，圆形头像样式 */
  margin: 0px 5px -2px 0px;
}
.no-courses-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 784px;
}
.no-open-course-img {
  width: 182px;
  height: 182px;
}
.course-info {
  padding: 0px 15px 0px 15px;
  width: 315px;
}
.course-title {
  height: 20px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 18px;
  color: #000000;
  line-height: 18px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.course-desc {
  height: 20px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  color: #585e76;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.course-span {
  width: 183px;
  height: 22px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  color: #b6bfcc;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-left: 15px;
}
.course-tags {
  margin: 20px 0px 0px 10px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  color: #585e76;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.course-footer {
  margin-top: 10px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  color: #969aaa;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.el-col {
  border-radius: 4px;
}
.bg-purple-dark {
  background: #99a9bf;
}
.bg-purple {
  background: #d3dce6;
}
.bg-purple-light {
  background: #e5e9f2;
}
.grid-content {
  border-radius: 4px;
  min-height: 36px;
}
.filter-label {
  font-weight: normal;
  font-size: 24px;
  color: #333333;
  line-height: 40px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
:deep(.el-tabs__header .el-tabs__nav .el-tabs__item) {
  flex-grow: 1;
  text-align: center;
}

/* 选中状态样式覆盖 */
:deep(.el-tabs__header .el-tabs__nav .el-tabs__item.is-active) {
  color: #ffffff !important;
  background: linear-gradient(90deg, #226bff 0%, #0448d2 100%);
}

/* 覆盖默认样式 */
:deep(.el-tabs__header .el-tabs__nav) {
  display: flex;
  width: 100%;
  overflow: hidden; /* 确保圆角不被子元素溢出破坏 */
}
/* 保留原有基础样式 */
:deep(.el-tabs__header .el-tabs__nav .el-tabs__item:first-child) {
  border-top-left-radius: 10px !important;
  height: 54px;
}
/* 保留原有基础样式 */
:deep(.el-tabs__header .el-tabs__nav .el-tabs__item) {
  height: 54px;
}
:deep(.el-tabs__header .el-tabs__nav .el-tabs__item:last-child) {
  border-top-right-radius: 10px !important;
  height: 54px;
}
:deep(.el-tabs--border-card) {
  min-height: 870px;
}
:deep(.el-tabs--border-card > .el-tabs__header) {
  border-radius: 10px 10px 0px 0px !important;
  overflow: hidden; /* 防止子元素溢出破坏圆角 */
  height: 54px;
}
:deep(.el-tabs__item) {
  color: #000000 !important;
  background-color: #ffffff;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 18px;
  color: #000000;
  line-height: 18px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
:deep(.el-tabs__content) {
  background: #f2f5fa;
}
.floating-btn {
  position: absolute;
  top: 66%;
  left: 18%;
  transform: translateX(-50%);
  z-index: 10;
  width: 8%;
  height: 9%;
  border-radius: 10px 10px 10px 10px;
}
:deep(.el-card__body) {
  padding: 10px 10px 10px 10px !important;
}
:deep(.el-tabs) {
  border-radius: 10px 10px 10px 10px;
}
:deep(.el-tabs__content) {
  border-radius: 0px 0px 10px 10px;
}
</style>
