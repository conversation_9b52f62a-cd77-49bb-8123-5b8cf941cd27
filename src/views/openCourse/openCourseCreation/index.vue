<template>
  <div style="background: #f2f5fa">
    <!-- 筛选栏 -->
    <el-row :gutter="20" class="filters">
      <el-col :span="8" :offset="3">
        <el-breadcrumb :separator-icon="ArrowRight" class="breadcrumb_text">
          <el-breadcrumb-item :to="{ path: '/open-course-list' }"
            >公开课</el-breadcrumb-item
          >
          <el-breadcrumb-item> 公开课创建</el-breadcrumb-item>
        </el-breadcrumb>
      </el-col>
      <el-col :span="4" :offset="6" style="text-align: right">
        <el-button class="btn" @click="handleMyCourseClick">我的课程</el-button>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="filters">
      <el-col :span="18" :offset="3">
        <el-card class="class-info-container">
          <el-row
            v-if="courses.approvalStatus == '3' || courses.reviewStatus == '3'"
            style="margin-bottom: 10px; min-height: 200px"
          >
            <el-col :span="19" class="class-name-container">
              <span class="class-name-item">
                驳回原因
                <div class="applyRemark-class">{{ courses.applyRemark }}</div>
                <span class="class-name-item"> 课程介绍 </span>
              </span>
            </el-col>
            <el-col :span="4" class="to-examine-col">
              <!-- 未通过 -->
              <img
                v-if="
                  courses.approvalStatus == '2' && courses.reviewStatus == '3'
                "
                :src="nopass"
                width="88px"
                height="69px"
              />
              <!-- 已驳回 -->
              <img
                v-else-if="
                  courses.approvalStatus == '3' && courses.reviewStatus == '0'
                "
                :src="reject"
                width="88px"
                height="69px"
              />
            </el-col>
          </el-row>
          <el-row v-else style="margin-bottom: 10px">
            <el-col :span="19" class="class-name-container">
              <span
                class="class-name-item"
                :style="{ color: isShow ? '#333333' : '#999999' }"
                @click="getInfo()"
              >
                课程信息
                <div v-if="isShow" class="class-name-border"></div>
              </span>
              <span
                v-if="courses.approvalStatus == '2'"
                class="class-name-item"
                :style="{ color: !isShow ? '#333333' : '#999999' }"
                @click="handleStuClick(courses.courseId)"
              >
                课程内容
                <div v-if="!isShow" class="class-name-border"></div>
              </span>
            </el-col>
            <el-col :span="4" class="to-examine-col">
              <!-- 待确认 -->
              <img
                v-if="
                  courses.approvalStatus == '1' && courses.reviewStatus == '0'
                "
                :src="confirm"
                width="88px"
                height="69px"
              />
              <!-- 制作中 -->
              <img
                v-else-if="
                  courses.approvalStatus == '2' && courses.reviewStatus == '0'
                "
                :src="make"
                width="88px"
                height="69px"
              />
              <!-- 待审核 -->
              <img
                v-else-if="
                  courses.approvalStatus == '2' && courses.reviewStatus == '1'
                "
                :src="toexamine"
                width="88px"
                height="69px"
              />
              <!-- 已发布 -->
              <img
                v-else-if="
                  courses.approvalStatus == '2' && courses.reviewStatus == '2'
                "
                :src="alreadyPublished"
                width="88px"
                height="69px"
              />
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="5">
              <el-carousel indicator-position="outside" class="course-cover">
                <el-carousel-item
                  v-for="(resource, index) in courses.courseImage?.split(',')"
                  :key="index"
                >
                  <!-- 动态资源组件 -->
                  <template v-if="isVideoResource(resource)">
                    <video
                      :src="resource"
                      class="course-cover"
                      controls
                      :style="{
                        width: '100%',
                        height: 'auto',
                        aspectRatio: '3/2',
                      }"
                    ></video>
                  </template>
                  <template v-else>
                    <img
                      :src="resource"
                      class="course-cover"
                      @error="handleImageError"
                      :style="{
                        width: '100%',
                        height: 'auto',
                        aspectRatio: '3/2',
                      }"
                    />
                  </template>
                </el-carousel-item>
              </el-carousel>
            </el-col>
            <el-col :span="19">
              <el-row>
                <el-col :span="24" class="row class-title"
                  >{{ courses.courseNameLabel }}
                </el-col>
                <el-col :span="23" class="row class-content"
                  >{{ courses.courseDescriptionLabel }}
                </el-col>
                <el-col :span="24" class="row middle-row">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="courses.teacherTeamNames"
                    placement="bottom-start"
                    :style="{ whiteSpace: 'normal' }"
                  >
                    <div class="course-tags">
                      <img :src="star" class="person-img" />
                      授课老师：
                      <span type="info">{{ courses.teacherTeamNames }}</span>
                    </div>
                  </el-tooltip>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <div v-if="isShow">
            <el-row type="flex" justify="center" style="margin-top: 40px">
              <el-col :span="12">
                <el-form
                  :model="courses"
                  :rules="rules"
                  ref="formRef"
                  label-width="120px"
                >
                  <el-form-item label="课程名称：" prop="courseName">
                    <el-input
                      placeholder="请输入"
                      v-model="courses.courseName"
                      :disabled="courses.approvalStatus !== '3'"
                    ></el-input>
                  </el-form-item>

                  <el-form-item label="教育层次：" prop="subjectId">
                    <el-select
                      v-model="courses.subjectId"
                      placeholder="请选择教育层次"
                      :disabled="courses.approvalStatus !== '3'"
                    >
                      <el-option
                        v-for="item in educationOptions"
                        :key="item.subjectId"
                        :label="item.subjectName"
                        :value="item.subjectId"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="学科分类：" prop="thirdSubjectId">
                    <el-select
                      v-model="courses.thirdSubjectId"
                      placeholder="请选择学科分类"
                      :disabled="courses.approvalStatus !== '3'"
                    >
                      <el-option
                        v-for="item in classification"
                        :key="item.subjectId"
                        :label="item.subjectName"
                        :value="item.subjectId"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="课程介绍：" prop="courseDescription">
                    <el-input
                      type="textarea"
                      placeholder="请输入文字"
                      v-model="courses.courseDescription"
                      :disabled="courses.approvalStatus !== '3'"
                    ></el-input>
                  </el-form-item>

                  <el-form-item label="课程介绍/图片：" prop="images">
                    <el-upload
                      class="avatar-uploader"
                      v-model:file-list="courses.courseImages"
                      :http-request="upload"
                      :action="uploadUrl"
                      :before-upload="handleBeforeUpload"
                      :on-success="handleUploadSuccess"
                      :on-error="handleUploadError"
                      list-type="picture-card"
                      :on-preview="handlePictureCardPreview"
                      :on-remove="handleRemove"
                      :limit="5"
                      :disabled="courses.approvalStatus !== '3'"
                    >
                      <el-icon>
                        <Plus />
                      </el-icon>
                      <template #tip>
                        <div class="el-upload__tip upload-instructions-text">
                          最多可上传5个文件，视频仅支持MP4格式，图片仅支持png、jpg、jpeg格式，单个文件大小不超过500MB
                        </div>
                      </template>
                      <!-- 自定义文件列表项 -->
                      <template #file="{ file }">
                        <div class="custom-file-item-wrapper">
                          <template v-if="isVideoResource(file.url)">
                            <video
                              :src="file.url"
                              controls
                              style="width: 100%; height: 100%"
                            ></video>
                          </template>
                          <template v-else>
                            <el-image :src="file?.url || loding" />
                          </template>
                          <div
                            v-if="courses.courseCover === file?.url"
                            class="cover-check"
                            title="封面"
                          >
                            <el-icon><Check /></el-icon>
                          </div>
                          <div
                            class="custom-file-actions"
                            v-if="courses.approvalStatus == '3'"
                          >
                            <div class="icon-row">
                              <el-icon
                                class="action-icon"
                                title="预览"
                                @click.stop="handlePictureCardPreview(file)"
                              >
                                <ZoomIn />
                              </el-icon>
                              <el-icon
                                class="action-icon"
                                title="删除"
                                @click.stop="handleRemove(file)"
                              >
                                <Delete />
                              </el-icon>
                            </div>
                            <el-button
                              type="success"
                              size="small"
                              icon="el-icon-star-on"
                              class="cover-btn"
                              @click.stop="setAsCover(file)"
                              :disabled="courses.courseCover === file?.url"
                              >设为封面</el-button
                            >
                          </div>
                        </div>
                      </template>
                    </el-upload>
                    <el-dialog v-model="dialogVisible" width="800px">
                      <img
                        w-full
                        :src="dialogImageUrl"
                        style="width: 100%; height: 100%"
                        alt="Preview Image"
                      />
                    </el-dialog>
                  </el-form-item>
                </el-form>
              </el-col>
              <el-col :span="24" style="text-align: center">
                <el-button @click="resetForm" class="resetBtn">
                  <span style="color: #000">取消</span></el-button
                >
                <el-button
                  type="primary"
                  class="submitBtn"
                  @click="confirmForm"
                  :disabled="courses.approvalStatus !== '3'"
                  >确认</el-button
                >
              </el-col>
            </el-row>
          </div>
          <div v-else>
            <el-row type="flex" justify="center" style="margin-top: 40px">
              <el-col :span="14">
                <el-row class="step-container">
                  <el-col :span="6" class="step-column">
                    <el-button
                      :class="
                        step1selFlag
                          ? 'step-button-selected'
                          : 'step-button-unselected'
                      "
                      @click="handleStepClick(1)"
                      ><el-image
                        :src="step1CompleteFlag ? checkMark : exclamationMark"
                        class="step-img"
                      />
                      <span class="step-button-font">开课计划</span></el-button
                    >
                  </el-col>
                  <el-col :span="6" class="step-column">
                    <el-button
                      :class="
                        step2selFlag
                          ? 'step-button-selected'
                          : 'step-button-unselected'
                      "
                      @click="handleStepClick(2)"
                      ><el-image
                        :src="step2CompleteFlag ? checkMark : exclamationMark"
                        class="step-img"
                      />
                      <span class="step-button-font">课程团队</span></el-button
                    >
                  </el-col>
                  <el-col :span="6" class="step-column">
                    <el-button
                      :class="
                        step3selFlag
                          ? 'step-button-selected'
                          : 'step-button-unselected'
                      "
                      @click="handleStepClick(3)"
                      ><el-image
                        :src="step3CompleteFlag ? checkMark : exclamationMark"
                        class="step-img"
                      />
                      <span class="step-button-font">课程设计</span></el-button
                    >
                  </el-col>
                  <el-col
                    :span="6"
                    class="step-column"
                    v-if="step3CompleteFlag && courses.reviewStatus == '2'"
                  >
                    <el-button
                      :class="
                        step4selFlag
                          ? 'step-button-selected'
                          : 'step-button-unselected'
                      "
                      @click="handleStepClick(4)"
                      ><el-image
                        :src="step4CompleteFlag ? checkMark : exclamationMark"
                        class="step-img"
                      />
                      <span class="step-button-font"> 发布设置</span></el-button
                    >
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
            <el-row class="filters">
              <el-col :span="5">
                <el-button
                  v-if="step1ShowFlag"
                  class="add-course-plan-container-btn"
                  @click="
                    ((courseScheduleDialogVisible = true),
                    (addFlag = true),
                    (courseScheduleForm = {}))
                  "
                  ><span class="span-style">添加开课计划</span></el-button
                >
                <el-button
                  v-if="step2ShowFlag"
                  class="add-course-plan-container-btn"
                  @click="addTeamMember"
                  ><span class="span-style">添加课程团队</span></el-button
                >
              </el-col>
              <el-col :span="10">
                <el-alert
                  title="完成全部课程设计配置后，才可进行发布。"
                  type="warning"
                  show-icon
                  :closable="false"
                />
              </el-col>
            </el-row>
            <!--开课计划-->
            <el-row class="filters" v-if="step1ShowFlag">
              <el-col :span="24">
                <el-table :data="openCoursePlanList.rows" style="width: 100%">
                  <el-table-column type="index" label="序号" width="50" />
                  <el-table-column prop="name" label="学期" max-width="240" />
                  <el-table-column
                    prop="startTime"
                    label="开课日期"
                    max-width="300"
                  />
                  <el-table-column
                    prop="endTime"
                    label="结课日期"
                    max-width="300"
                  />
                  <el-table-column label="操作" max-width="120">
                    <template #default="scope">
                      <el-button
                        link
                        type="primary"
                        class="custom-delete-btn"
                        @click="handleEdit(scope.$index, scope.row)"
                        >编辑</el-button
                      >
                      <el-button
                        link
                        type="primary"
                        class="custom-delete-btn"
                        @click="handleDelete(scope.$index, scope.row)"
                        >删除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
                <pagination
                  :total="openCoursePlanList.total"
                  v-model:page="queryParams.pageNum"
                  v-model:limit="queryParams.pageSize"
                  @pagination="getOpenCoursePlanList(courses.courseId)"
                />
              </el-col>
            </el-row>
            <!--课程团队-->
            <el-row
              :gutter="20"
              v-if="step2ShowFlag"
              v-for="(item, index) in openOpenCourseTeammateList"
              class="teacher-card"
            >
              <!-- 左侧头像区域 -->
              <el-col :span="2" class="teacher-avatar-container">
                <el-row>
                  <el-col :span="24">
                    <el-image
                      :src="openCourseCreationDel"
                      @click="openCourseCreationDelClick(item.teammateId)"
                      class="teacher-card-img"
                    />
                  </el-col>
                  <el-col :span="24">
                    <el-image
                      :src="openCourseCreationEdit"
                      @click="openCourseCreationEditClick(item)"
                      class="teacher-card-img"
                    />
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="6" class="teacher-info-container">
                <div
                  :style="{
                    backgroundImage:
                      index % 2 === 0
                        ? `url(${backgroundPurple})`
                        : `url(${backgroundYellow})`,
                  }"
                  class="teacher-avatar-bg-size"
                >
                  <el-avatar
                    class="course-avatar"
                    :src="item.avatar ? item.avatar : bookCoverDefault"
                  />
                </div>
              </el-col>

              <!-- 右侧信息区域 -->
              <el-col :span="6" class="teacher-label-col">
                <el-row style="margin-bottom: 10px">
                  <el-col :span="24" class="teacher-label"
                    >教师名字:{{ item.name }}</el-col
                  >
                </el-row>
                <el-row style="margin-bottom: 10px">
                  <el-col :span="24" class="teacher-label"
                    >学校职称:{{ item.title }}</el-col
                  >
                </el-row>
              </el-col>
              <el-col :span="10" class="teacher-brief-introduction">
                <span class="teacher-title">教师简介:</span>
                <span class="teacher-course-introduce">
                  {{ item.description }}
                </span>
              </el-col>
            </el-row>
            <!--课程设计-->
            <el-row :span="20" class="filters"  v-if="step3ShowFlag">
              <el-col :span="24" v-if="courses.courseContentList">
                <el-card class="outline-card">
                  <el-collapse
                    v-model="activeNames"
                    expand-icon-position="left"
                    v-for="(item, index) in courses.courseContentList"
                  >
                    <el-collapse-item :name="item.chapterId">
                      <template #title>
                        <!-- 自定义图标 -->
                        <img
                          :src="getTitleIcon(item.chapterId)"
                          alt="状态图标"
                          class="title-icon"
                        />
                        <!-- 原始文本 -->
                       <span class="outline-card-chapter-name"> {{ item.chapterName }}</span>
                      </template>
                      <el-collapse
                        v-model="activeNamesChildren"
                        expand-icon-position="left"
                        class="children-collapse"
                        v-for="(item, index) in item.children"
                      >
                        <el-collapse-item :name="item.chapterId">
                          <template #title>
                            <!-- 自定义图标 -->
                            <img
                              :src="getTitleIconChildren(item.chapterId)"
                              alt="状态图标"
                              class="title-icon"
                            />
                            <!-- 原始文本 -->
                            <span class="outline-card-chapter-name"> {{ item.chapterName }}</span>
                          </template>
                          <div
                            class="outline-card-content"
                            v-for="(resources, index) in item.resources"
                            :key="index"
                          >
                            <span class="resource-index">{{ index + 1 }}.</span>
                            <span class="outline-card-resource-title"> {{ resources.resourceTitle }}</span>
                          </div>
                        </el-collapse-item>
                      </el-collapse>
                    </el-collapse-item>
                  </el-collapse>
                </el-card>
              </el-col>
              <el-col :span="24" class="no-courses-container" v-else>
                <el-image
                  :src="noCoursewareAvailable"
                  class="no-open-course-img"
                />
                <div class="course-design-container">
                  <span class="course-span">当前暂无授课课件，点击</span>
                  <el-button link type="primary">
                    <span
                      style="color: #226bff"
                      @click="openCourseCreationAddClick()"
                      >【导入课件】</span
                    ></el-button
                  >
                </div>
              </el-col>
            </el-row>
            <!--发布设置-->
            <el-row
              class="filters"
              v-if="step4ShowFlag"
              type="flex"
              justify="center"
            >
              <el-col :span="14">
                <el-row>
                  <el-col :span="24">
                    <div class="course-design-container">
                      <span class="course-design-span">是否允许报名</span>
                      <el-image
                        :src="blackExclamationMark"
                        class="black-exclamation-mark"
                      />
                    </div>
                  </el-col>
                  <el-col :span="24" class="course-design-radio">
                    <el-radio-group v-model="allowJoinFlag">
                      <el-radio value="1" class="course-design-span"
                        >允许</el-radio
                      >
                      <el-radio value="0" class="course-design-span"
                        >不允许</el-radio
                      >
                    </el-radio-group>
                  </el-col>
                  <el-col :span="24">
                    <div class="course-design-container">
                      <span class="course-design-span">课程公开范围</span>
                      <el-image
                        :src="blackExclamationMark"
                        class="black-exclamation-mark"
                      />
                    </div>
                  </el-col>
                  <el-col :span="24" class="course-design-radio">
                    <el-radio-group v-model="openJoinFlag">
                      <el-radio value="1" class="course-design-span"
                        >不限制</el-radio
                      >
                      <el-radio value="0" class="course-design-span"
                        >仅限本校学生</el-radio
                      >
                    </el-radio-group>
                  </el-col>
                  <span class="course-design-setting-span">设定后不可修改</span>
                </el-row>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24" style="text-align: center">
                <el-button @click="resetForm" class="resetBtn">
                  <span style="color: #000">取消</span></el-button
                >
                <el-button
                  v-if="step3ShowFlag"
                  type="primary"
                  class="submitBtn"
                  style="width: 90px"
                  @click="submitForm"
                  >提交审核</el-button
                >
                <el-button
                  v-else-if="!step4ShowFlag && !step3ShowFlag"
                  type="primary"
                  class="submitBtn"
                  @click="nextClick"
                  >下一步</el-button
                >
                <el-button
                  v-if=" step4ShowFlag"
                  type="primary"
                  class="submitBtn"
                  @click="publishVersionClick"
                  >发版</el-button
                >
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
  <!--添加开课计划弹窗-->
  <el-dialog
    v-model="courseScheduleDialogVisible"
    :title="addFlag ? '添加开课计划' : '修改开课计划'"
    width="25%"
  >
    <el-form
      :model="courseScheduleForm"
      :rules="courseScheduleRules"
      ref="courseScheduleFormRef"
      label-width="100px"
    >
      <el-form-item label="学期" prop="name">
        <el-input
          v-model="courseScheduleForm.name"
          style="width: 50%"
          placeholder="请输入学期"
        ></el-input>
      </el-form-item>
      <el-form-item label="开课日期" prop="startTime">
        <el-date-picker
          v-model="courseScheduleForm.startTime"
          type="datetime"
          format="YYYY/MM/DD HH:mm:ss"
          placeholder="选择开课日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="结课日期" prop="endTime">
        <el-date-picker
          v-model="courseScheduleForm.endTime"
          type="datetime"
          format="YYYY/MM/DD HH:mm:ss"
          placeholder="选择结课日期"
        ></el-date-picker>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          @click="courseScheduleDialogVisible = false"
          class="resetBtn"
        >
          <span style="color: #000">取消</span></el-button
        >
        <el-button
          type="primary"
          class="submitBtn"
          @click="courseScheduleConfirm"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
  <!--添加课程团队-->
  <AddTeamMember
    v-model="showDialog"
    :form="teacherForm"
    @submit="onSubmitForm"
    @update="handleUpdate"
    @refresh="getOpenCourseTeammate"
  />
  <!--添加课程设计弹窗-->
  <el-dialog
    v-model="courseDesignDialogVisible"
    :show-close="false"
    width="50%"
  >
    <!-- 自定义头部 -->
    <template #header>
      <div class="dialog-header">
        <h3>选择课件</h3>
      </div>
    </template>

    <!-- 可滚动内容区域 -->
    <el-main class="scrollable-content" style="overflow: auto;height: 530px;">
      <el-row :gutter="20">
        <el-radio-group v-model="selectedCoursewareId">
          <el-col
            :span="8"
            v-for="(item, index) in courseDesignList"
            :key="index"
          >
            <div class="courseware-container">
              <div class="image-container">
                <img :src="item.coursewareCover" class="courseware-img" />
                <el-radio
                  class="radio-overlay"
                  :value="item.coursewareDesignId"
                ></el-radio>
              </div>
              <div class="courseware-info">
                <el-col :span="24">
                  <span class="courseware-name-span">{{
                    item.coursewareName
                  }}</span>
                </el-col>
                <el-col :span="24">
                  <span class="course-desc-span"
                    >创建教师：{{ item.realName }}</span
                  >
                </el-col>
                <el-col :span="24">
                  <span class="course-desc-span"
                    >创建时间：{{ item.createTime }}</span
                  >
                </el-col>
              </div>
            </div>
          </el-col>
        </el-radio-group>
      </el-row>
    </el-main>

    <!-- 固定底部 -->
    <template #footer>
      <span class="dialog-footer">
        <el-button
          @click="courseDesignDialogVisible = false"
          class="resetBtn"
        >
          <span style="color: #000">取消</span>
        </el-button>
        <el-button
          type="primary"
          class="submitBtn"
          @click="courseDesignConfirm"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup name="OpenCourseDetail">
import { ElNotification } from "element-plus";
import { ArrowRight } from "@element-plus/icons-vue";
import { ElMessageBox } from "element-plus";
import bookCoverDefault from "@/assets/images/book-cover-default.png";
import star from "@/assets/images/openCourse/star.png";
import checkMark from "@/assets/images/openCourse/check_mark.png";
import openCourseCreationDel from "@/assets/images/openCourse/openCourseCreation_del.png";
import openCourseCreationEdit from "@/assets/images/openCourse/openCourseCreation_edit.png";
import exclamationMark from "@/assets/images/openCourse/exclamation_mark.png";
import noCoursewareAvailable from "@/assets/images/openCourse/no_courseware available.png";
import blackExclamationMark from "@/assets/images/openCourse/black_exclamation_mark.png";
import backgroundPurple from "@/assets/images/openCourse/background_purple.png";
import backgroundYellow from "@/assets/images/openCourse/background_yellow.png";
import arrowDown from "@/assets/images/openCourse/arrow_down.png";
import arrowRight from "@/assets/images/openCourse/arrow_right.png";
import alreadyPublished from "@/assets/images/openCourse/already_published.png";
import reject from "@/assets/images/openCourse/reject.png";
import confirm from "@/assets/images/openCourse/confirm.png";
import nopass from "@/assets/images/openCourse/nopass.png";
import make from "@/assets/images/openCourse/make.png";
import toexamine from "@/assets/images/openCourse/toexamine.png";
import { getInfoByStudent, editCourseInformation } from "@/api/basic/course";
import loding from "@/assets/images/loding.png";
import { listOpenCourseStudent } from "@/api/edu/openCourseStudent";
import { OssService } from "@/utils/aliOss.js";
import { listDutpSubjectEducation } from "@/api/basic/subject";
import {
  listOpenCoursePlan,
  getOpenCoursePlan,
  addOpenCoursePlan,
  updateOpenCoursePlan,
  delOpenCoursePlan,
} from "@/api/basic/openCoursePlan";
import Pagination from "@/components/Pagination";
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import AddTeamMember from "@/views/openCourse/components/addTeamMember.vue";
import {
  delOpenCourseTeammate,
  listOpenCourseTeammate,
} from "@/api/edu/openCourseTeammate";
import { listCoursewareDesign, getCoursewareDesignResources } from "@/api/edu/coursewareDesign";
import { editOpenCourseApply } from "@/api/edu/openCourseApply";
const courseScheduleDialogVisible = ref(false);
const showDialog = ref(false);
const courseDesignDialogVisible = ref(false);
const courseDesignList = ref([]);
const selectedCoursewareId = ref(null);

// 后台接口获取教师列表
const teacherForm = ref({});
watch(
  () => showDialog.value,
  (newVal) => {
    console.log("showDialog 变化为:", newVal);
  }
);
const onSubmitForm = (formData) => {
  console.log("提交的表单数据:", formData);
  // 在这里执行实际的保存操作
};
const courseScheduleForm = ref({
  name: "",
  startTime: "",
  endTime: "",
});

const courseScheduleRules = {
  name: [{ required: true, message: "请输入学期", trigger: "blur" }],
  startTime: [{ required: true, message: "请选择开课日期", trigger: "change" }],
  endTime: [{ required: true, message: "请选择结课日期", trigger: "change" }],
};

const courseScheduleFormRef = ref(null);
const addFlag = ref(true);
// 新增修改计划提交
const courseScheduleConfirm = () => {
  courseScheduleFormRef.value.validate((valid) => {
    if (valid) {
      courseScheduleForm.value.startTime = formatDate(
        courseScheduleForm.value.startTime
      );
      courseScheduleForm.value.endTime = formatDate(
        courseScheduleForm.value.endTime
      );
      if (addFlag.value) {
        step1CompleteFlag.value = true;
        courseScheduleForm.value.courseId = courses.value.courseId;
        courseScheduleForm.value.sort = 0;
        // 表单校验通过，执行确认操作
        addOpenCoursePlan(courseScheduleForm.value).then((res) => {
          if (res.code === 200) {
            ElMessage({
              type: "success",
              message: "添加成功",
            });
            // 获取开课计划
            getOpenCoursePlanList(courses.value.courseId);
          }
        });
      } else {
        // 表单校验通过，执行确认操作
        updateOpenCoursePlan(courseScheduleForm.value).then((res) => {
          if (res.code === 200) {
            ElMessage({
              type: "success",
              message: "修改成功",
            });
            // 获取开课计划
            getOpenCoursePlanList(courses.value.courseId);
          }
        });
      }
      courseScheduleDialogVisible.value = false;
    } else {
      ElMessage.error("表单校验失败，请检查输入");
      return false;
    }
  });
};
function formatDate(dateStr) {
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
const { proxy } = getCurrentInstance();
// 完成状态标识符
const step1selFlag = ref(true);
const step2selFlag = ref(false);
const step3selFlag = ref(false);
const step4selFlag = ref(false);
const step1ShowFlag = ref(true);
const step2ShowFlag = ref(false);
const step3ShowFlag = ref(false);
const step4ShowFlag = ref(false);
const step1CompleteFlag = ref(false);
const step2CompleteFlag = ref(false);
const step3CompleteFlag = ref(false);
const step4CompleteFlag = ref(false);
const allowJoinFlag = ref("1");
const openJoinFlag = ref("1");
const educationOptions = ref([]);
const classification = ref([]);
const formRef = ref();
// 校验规则
const rules = {
  courseName: [{ required: true, message: "请输入课程名称", trigger: "blur" }],
  subjectId: [{ required: true, message: "请选择教育层次", trigger: "change" }],
  thirdSubjectId: [
    { required: true, message: "请选择学科分类", trigger: "change" },
  ],
  courseDescription: [
    { required: true, message: "请输入课程介绍", trigger: "blur" },
  ],
  images: [
    {
      validator: (rule, value, callback) => {
        if (courses.value.courseImages.length === 0) {
          callback(new Error("请上传至少一张图片"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
};
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + "/file/upload"); // 上传的图片服务器地址
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  courseId: null,
});
const total = ref(0);
const openCourseData = ref([]);
const handleEdit = (index, row) => {
  courseScheduleForm.value = JSON.parse(JSON.stringify(row));
  addFlag.value = false;
  courseScheduleDialogVisible.value = true;
};
const handleDelete = (index, row) => {
  ElMessageBox.confirm("是否确认删除该条记录?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      const planIds = [row.planId];
      delOpenCoursePlan(planIds).then((res) => {
        if (res.code === 200) {
          ElMessage({
            type: "success",
            message: "删除成功",
          });
          // 获取开课计划
          getOpenCoursePlanList(courses.value.courseId);
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消删除",
      });
    });
};
const isShow = ref(true);
// 获取当前路由对象
const route = useRoute();
const courseId = route.query.key;
const courses = ref({
  courseImages: [],
});
// 当前激活面板列表
const activeNames = ref([]);
const activeNamesChildren = ref([]);

// 根据面板状态返回对应图标
const getTitleIcon = (name) => {
  return activeNames.value.includes(name) ? arrowDown : arrowRight;
};
// 根据面板状态返回对应图标
const getTitleIconChildren = (name) => {
  return activeNamesChildren.value.includes(name) ? arrowDown : arrowRight;
};
const getInfo = () => {
  isShow.value = true;
  getInfoByStudent(courseId).then((res) => {
    courses.value = res.data;
    courses.value.courseImages = (res.data.courseImage || "")
      .split(",")
      .map((url) => ({ url }));
    courses.value.courseNameLabel = courses.value.courseName;
    courses.value.courseDescriptionLabel = courses.value.courseDescription;
    // 获取开课计划
    getOpenCoursePlanList(res.data.courseId);
    // 获取课程团队
    getOpenCourseTeammate();
  });
};
const handleEditClick = () => {};
// 资源类型判断方法
const isVideoResource = (url) => {
  const videoExtensions = [".mp4", ".webm", ".ogg", ".mov", ".avi"];
  console.log(
    url,
    videoExtensions.some((ext) => url.toLowerCase().endsWith(ext))
  );
  return videoExtensions.some((ext) => url.toLowerCase().endsWith(ext));
};

// 错误处理方法
const handleImageError = (e) => {
  e.target.src = bookCoverDefault;
};
const handleStuClick = (courseId) => {
  isShow.value = false;
  queryParams.value.courseId = courseId;
  listOpenCourseStudent(queryParams.value).then((res) => {
    openCourseData.value = res.rows;
    total.value = res.total;
  });
};
// 预览图片
const handlePictureCardPreview = (uploadFile) => {
  console.log(uploadFile);
  dialogImageUrl.value = uploadFile.url;
  dialogVisible.value = true;
};

// 删除图片
const handleRemove = (uploadFile) => {
  const index = courses.value.courseImages
    ? courses.value.courseImages.findIndex(
        (item) => item?.url && item.url === uploadFile?.url
      )
    : -1;
  if (index !== -1) {
    courses.value.courseImages.splice(index, 1);
  }
};
// 设为封面
function setAsCover(file) {
  if (isVideoResource(file.url)) {
    proxy.$modal.msgError("视频文件不可设为封面");
  } else {
    courses.value.courseCover = file?.url;
    proxy.$modal.msgSuccess("已设为封面");
  }
}
// 上传失败处理
function handleUploadError() {}
// 上传成功处理
function handleUploadSuccess(res, file) {}
// 上传前校检格式和大小
function handleBeforeUpload(file) {
  const imageTypes = ["image/jpeg", "image/jpg", "image/png"];
  const videoTypes = ["video/mp4"];
  const validTypes = [...imageTypes, ...videoTypes];

  const isValidType = validTypes.includes(file.type);
  const isValidSize = file.size <= 500 * 1024 * 1024; // 500MB

  // 校验文件类型
  if (!isValidType) {
    proxy.$modal.msgError(`文件类型不支持!`);
    return false;
  }

  // 校验文件大小
  if (!isValidSize) {
    proxy.$modal.msgError(`文件大小超过限制!`);
    return false;
  }
  return true;
}
const syncFile = async (file) => {
  try {
    // 工具类引用
    const res = await OssService(file.file);
    return res;
  } catch (e) {
    throw e;
  }
};
function upload(file) {
  return syncFile(file);
}
const nextClick = () => {
  if (step1ShowFlag.value) {
    if (openCoursePlanList.value.rows.length == 0) {
      ElMessage.warning("请完成开课计划");
      return;
    }
    step1ShowFlag.value = false;
    step2ShowFlag.value = true;
    step2selFlag.value = true;
  } else if (step2ShowFlag.value) {
    step2ShowFlag.value = false;
    step3ShowFlag.value = true;
    step3selFlag.value = true;
  } else if (step3ShowFlag.value) {
    step3ShowFlag.value = false;
    step4ShowFlag.value = true;
    step4selFlag.value = true;
  }
};
const publishVersionClick = () => { 
  const parm = {
    courseId: courses.value.courseId,
    courseStatus: '1',
    allowJoinFlag: allowJoinFlag.value,
    openJoinFlag: openJoinFlag.value
  };
  editCourseInformation(parm).then((res) => {
    if (res.code === 200) {
      ElNotification({
        title: "操作提示",
        message: "发布成功",
        type: "success",
      });
      getInfo();
    }
  });
  step4CompleteFlag.value = true;
};
const submitForm = () => {
  const parm = {
    courseId: courses.value.courseId,
    step: '2',
    applyStatus: '1',
    applyRemark: ' ',
  };
  editOpenCourseApply(parm).then((res) => { 
    if (res.code === 200) {
        ElNotification({
          title: "操作提示",
          message: "修改成功",
          type: "success",
        });
        getInfo();
      }
  });
};
const confirmForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      const urls = [];
      courses.value.courseImages.forEach((item) => {
        if (item.response) {
          urls.push(item.response.url);
        } else {
          urls.push(item.url);
        }
      });
      const parm = {
        courseId: courses.value.courseId,
        courseName: courses.value.courseName,
        courseDescription: courses.value.courseDescription,
        courseImage: urls.join(","),
        courseCover: courses.value.courseCover,
        subjectId: courses.value.subjectId,
        thirdSubjectId: courses.value.thirdSubjectId,
      };
      editCourseInformation(parm).then((res) => {
        if (res.code === 200) {
          ElNotification({
            title: "操作提示",
            message: "修改成功",
            type: "success",
          });
          getInfo();
        }
      });
    } else {
      console.log("表单校验失败");
      return false;
    }
  });
};
const handleStepClick = (index) => {
  if(index == 1){
    step1ShowFlag.value = true;
    step2ShowFlag.value = false;
    step3ShowFlag.value = false;
    step4ShowFlag.value = false;
  }else if(index == 2){
    if(step1CompleteFlag){
      step1ShowFlag.value = false;
      step2ShowFlag.value = true;
      step3ShowFlag.value = false;
      step4ShowFlag.value = false;
    } else {
      // 提示请完成上一步
      ElNotification({
        title: "操作提示",
        message: "请完成上一步",
        type: "error",
      });
      return;
    }
  }else if(index == 3){
    if(step3CompleteFlag){
      step1ShowFlag.value = false;
      step2ShowFlag.value = false;
      step3ShowFlag.value = true;
      step4ShowFlag.value = false;
    } else {
      // 提示请完成上一步
      ElNotification({
        title: "操作提示",
        message: "请完成上一步",
        type: "error",
      });
      return;
    }
  }else if(index == 4){
    if(step3CompleteFlag){
      step1ShowFlag.value = false;
      step2ShowFlag.value = false;
      step3ShowFlag.value = false;
      step4ShowFlag.value = true;
    } else {
      // 提示请完成上一步
      ElNotification({
        title: "操作提示",
        message: "请完成上一步",
        type: "error",
      });
      return;
    }
  }

};
const getDutpSubject = () => {
  listDutpSubjectEducation().then((res) => {
    educationOptions.value = res.data[1];
    classification.value = res.data[3];
  });
};
const openCoursePlanList = ref([]);
// 获取开课计划
const getOpenCoursePlanList = (courseId) => {
  queryParams.value.courseId = courseId;
  listOpenCoursePlan(queryParams.value).then((res) => {
    if (res.code === 200) {
      openCoursePlanList.value = res;
      // 设置显示状态
      step1CompleteFlag.value = openCoursePlanList.value.total > 0;
    }
  });
};
const openOpenCourseTeammateList = ref([]);
// 获取课程团队
const getOpenCourseTeammate = () => {
  listOpenCourseTeammate({ courseId: courseId }).then((res) => {
    if (res.code === 200) {
      openOpenCourseTeammateList.value = res.data;
      // 设置显示状态
      step2CompleteFlag.value = openOpenCourseTeammateList.value.length > 0;
      if (step2CompleteFlag.value) {
        step1ShowFlag.value = false;
        step2ShowFlag.value = true;
        step2selFlag.value = true;
      }
      // 设置课程设计状态
      step3CompleteFlag.value = courses.value.courseContentList?.length > 0;
      if (step3CompleteFlag.value) {
        step2ShowFlag.value = false;
        step3ShowFlag.value = true;
        step3selFlag.value = true;
      }
      if(step3CompleteFlag.value && courses.value.reviewStatus == '2'){
        step2ShowFlag.value = false;
        step3ShowFlag.value = false;
        step4selFlag.value = true;
        step4ShowFlag.value =true;
      }
      if(courses.value.courseStatus != '0'){
        step4CompleteFlag.value = true;
      }
      allowJoinFlag.value = courses.value.allowJoinFlag;
      openJoinFlag.value = courses.value.openJoinFlag;
    }
  });
};
const openCourseCreationDelClick = (teammateId) => {
  ElMessageBox.confirm("确定要删除该成员吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      delOpenCourseTeammate([teammateId]).then((res) => {
        if (res.code === 200) {
          ElNotification({
            title: "操作提示",
            message: "删除成功",
            type: "success",
          });
          getOpenCourseTeammate();
        }
      });
    })
    .catch(() => {
      // 用户点击取消时的处理
      ElNotification({
        title: "操作提示",
        message: "已取消删除",
        type: "info",
      });
    });
};
import { deepClone } from "@/utils";

const openCourseCreationEditClick = (item) => {
  teacherForm.value = deepClone(item);
  showDialog.value = true;
};
const handleUpdate = (updatedForm) => {
  Object.assign(teacherForm.value, updatedForm);
};
const addTeamMember = () => {
  showDialog.value = true;
  teacherForm.value = {
    courseId: courseId,
    sort: 0,
    avatar: "",
    name: "",
    schoolName: "",
    title: "",
    description: "",
  };
};
const openCourseCreationAddClick = () => {
  courseDesignDialogVisible.value = true;
  listCoursewareDesign().then((res) => {
    if(res.code === 200){
      courseDesignList.value = res.data;
    }
  });
};
const courseDesignConfirm =  () => { 
  getCoursewareDesignResources(selectedCoursewareId.value).then((res) => { 
    if(res.code === 200){
      courses.value.courseContentList = res.data;
      const parm = {
        courseId: courses.value.courseId,
        courseDesignId: selectedCoursewareId.value
      };
      editCourseInformation(parm).then((res) => {
        if (res.code === 200) {
          courseDesignDialogVisible.value = false;
          step3CompleteFlag.value = true;
        }
      });
    }
  });
};
onMounted(() => {
  getInfo();
  getDutpSubject();
});
</script>
<style lang="scss" scoped>
.breadcrumb_text {
  width: 263px;
  height: 16px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1rem;
  color: #666666;
  line-height: 37px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.filters {
  margin-top: 20px;
  margin-bottom: 20px;
}
:root {
  --primary-gradient: linear-gradient(90deg, #226bff 0%, #0448d2 100%);
}
.btn {
  width: 111px;
  height: 40px;
  background-image: linear-gradient(90deg, #226bff 0%, #0448d2 100%);
  background-color: #226bfe;
  border-radius: 4px 4px 4px 4px;
}
:deep(.el-button > span) {
  align-items: center;
  display: inline-flex;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1rem;
  color: #ffffff;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.class-name {
  margin-top: 10px;
  margin-bottom: 20px;
  height: 24px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1.5rem;
  color: #333333;
  line-height: 24px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.class-title {
  height: 22px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1.375rem;
  color: #333333;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.class-content {
  min-height: 55px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1rem;
  color: #585e76;
  line-height: 24px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.course-cover {
  width: 100%;
  aspect-ratio: 3 / 2; /* 直接指定宽高比 */
  border-radius: 14px 14px 14px 14px;
}
.course-cover img {
  width: 100%;
  aspect-ratio: 3 / 2; /* 直接指定宽高比 */
}
.row {
  margin: 10px 10px 10px 20px;
}
.study-btn {
  width: 111px;
  height: 40px;
  background-image: linear-gradient(90deg, #226bff 0%, #0448d2 100%);
  background-color: #226bfe;
  border-radius: 20px 20px 20px 20px;
}
.open-course-img {
  width: 40px;
  height: 40px;
}
.middle-row {
  max-width: 236px;
  height: 32px;
  background: #f2f5fa;
  text-align: center;
  border-radius: 8px 8px 8px 8px;
  display: flex;
  align-items: center;
}
.middle-row-text {
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1rem;
  color: #585e76;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-left: 10%;
}
.middle-row-text-content {
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1rem;
  color: #333333;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-left: 10%;
  max-width: 9vw;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tea-team {
  margin-left: 5%;
}
.tea-card {
  width: 100%; /* 填充父容器 */
  height: auto; /* 自动高度 */
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 2px solid #e6ebf5;
  aspect-ratio: 1/1;
  margin-bottom: 10px;
}

.tea-card img {
  width: 100%;
  height: auto;
  aspect-ratio: 2 / 1;
}

.tea-card :deep(.el-card__body) {
  padding: 10px 0px 10px 0px !important;
  box-sizing: border-box;
}
.course-grid {
  padding: 0vw 0.8vw 0vw 0vw;
}
.course-info {
  padding: 15px 0px 20px 10px; /* 增加底部内边距 */
}
.course-tea-name {
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1.125rem;
  color: #333333;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.course-sch {
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1rem;
  color: #585e76;

  text-align: left;
  font-style: normal;
  text-transform: none;
}
.course-introduce {
  margin-top: 10px;
  margin-bottom: 10px; /* 添加底部间距 */
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1rem;
  color: #969aaa;
  text-align: left;
  font-style: normal;
  text-transform: none;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  /* 新增动态计算 */
  -webkit-line-clamp: 3; /* 最小2行，最大5行 */
  -webkit-box-orient: vertical;
}
@media (min-width: 1500px) and (max-width: 1800px) {
  .course-introduce {
    -webkit-line-clamp: 2;
  }
  .course-avatar {
    width: 110px !important;
    height: 110px !important;
  }
  // 按钮字体样式
  .step-button-font {
    font-size: 16px !important;
    margin: 10px 5px 11px 5px !important;
  }
  .step-button-unselected {
    height: 55px !important;
  }
  .step-button-selected {
    height: 55px !important;
  }
  .step-img {
    width: 25px !important;
    height: 25px !important;
  }
}
@media (min-width: 1200px) and (max-width: 1500px) {
  .course-introduce {
    -webkit-line-clamp: 1;
  }
  .course-avatar {
    width: 100px !important;
    height: 100px !important;
  }
  // 按钮字体样式
  .step-button-font {
    font-size: 14px !important;
    margin: 10px 5px 11px 5px !important;
  }
  .step-button-unselected {
    height: 45px !important;
  }
  .step-button-selected {
    height: 45px !important;
  }
  .step-img {
    width: 20px !important;
    height: 20px !important;
  }
}
@media (min-width: 1900px) {
  .course-introduce {
    -webkit-line-clamp: 3;
  }
  .course-avatar {
    width: 136px;
    height: 136px;
  }
}
.course-avatar {
  width: 136px;
  height: 136px;
}
.outline-card {
  border-radius: 10px 10px 10px 10px;
  border: 1px solid #e6ebf5;
}
.outline-card :deep(.el-card__body) {
  padding: 0px 0px 0px 0px !important;
}
:deep(.el-collapse-item__header) {
  height: 60px;
  align-items: center;
  background-color: #f7f8f9;
  border: none;
  border-bottom: 1px solid #ebeef500;
  color: var(--el-collapse-header-text-color);
  cursor: pointer;
  display: flex;
  font-size: var(--el-collapse-header-font-size);
  font-weight: 500;
  line-height: var(--el-collapse-header-height);
  outline: none;
  padding: 0;
  transition: border-bottom-color var(--el-transition-duration);
  width: 100%;
}
:deep(.el-collapse-item__wrap) {
  background-color: var(--el-collapse-content-bg-color);
  border-bottom: 1px solid var(--el-collapse-border-color);
  box-sizing: border-box;
  overflow: hidden;
  will-change: height;
  margin-left: 50px;
}
:deep(.el-collapse-item__arrow) {
  font-weight: 300;
  transition: transform var(--el-transition-duration);
  margin-left: 10px;
}
:deep(.el-collapse-item__title) {
  flex: auto;
  text-align: left;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 22px;
  color: #333333;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
/* 隐藏默认折叠箭头 */
:deep(.el-collapse-item__arrow) {
  display: none !important;
}
.title-icon {
  width: 12px;
  height: 12px;
  margin-left: 20px;
  margin-bottom: 2px;
  margin-right: 10px;
}
.outline-card-content {
  margin-top: 20px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 18px;
  color: #333333;
  line-height: 18px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.warn-text {
  color: #e6a23c;
  font-size: 1rem;
  line-height: 24px;
  margin-left: 20px;
}
.class-name-container {
  cursor: pointer;
  display: flex;
  gap: 30px; /* 控制两个标题间距 */
  align-items: flex-start;
  margin-top: 10px;
  margin-bottom: 20px;
  margin-top: 10px;
  margin-bottom: 20px;
  height: 24px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1.5rem;
  color: #333333;
  line-height: 24px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.class-name-item {
  position: relative;
  padding-bottom: 8px; /* 为下划线留出空间 */
}

.class-name-border {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 95px;
  height: 2px;
  background: linear-gradient(153deg, #c7c7c7 0%, #696969 100%);
  border-radius: 2px;
  transition: all 0.3s ease;
}

/* 添加悬停效果（可选） */
.class-name-item:hover .class-name-border {
  transform: scaleX(1.1);
  opacity: 0.9;
}
.class-info-container {
  border-radius: 10px 10px 10px 10px;
  background-image: url(../../../assets/images/openCourse/card_background.png);
  background-size: contain; /* 保持图片完整显示 */
  background-position: right top; /* 顶部居中对齐 */
  background-repeat: no-repeat;
}
:deep(.custom-delete-btn > span) {
  color: #409eff;
}
:deep(.custom-delete-btn > span:hover) {
  color: rgb(159.5, 206.5, 255);
}
:deep(.el-collapse) {
  --el-collapse-border-color: #ebeef500;
  --el-collapse-header-height: 48px;
  --el-collapse-header-bg-color: var(--el-fill-color-blank);
  --el-collapse-header-text-color: var(--el-text-color-primary);
  --el-collapse-header-font-size: 13px;
  --el-collapse-content-bg-color: var(--el-fill-color-blank);
  --el-collapse-content-font-size: 13px;
  --el-collapse-content-text-color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-collapse-border-color);
  border-top: 1px solid var(--el-collapse-border-color);
}
:deep(.children-collapse .el-collapse-item__header) {
  height: 60px;
  align-items: center;
  background-color: #fcfcfc00;
  border: none;
  border-bottom: 1px solid #ebeef500;
  color: var(--el-collapse-header-text-color);
  cursor: pointer;
  display: flex;
  font-size: var(--el-collapse-header-font-size);
  font-weight: 500;
  line-height: var(--el-collapse-header-height);
  outline: none;
  padding: 0;
  transition: border-bottom-color var(--el-transition-duration);
  width: 100%;
}
.resource-index {
  font-weight: bold;
  color: #333;
}
.to-examine-col {
  text-align: right;
  margin-top: 20px;
  margin-left: 25px;
}
.applyRemark-class {
  margin-top: 20px;
  margin-bottom: 20px;
  background: #fff1f0;
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #ffccc7;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  color: #333333;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.course-tags {
  margin: 0px 0px 0px 10px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  color: #585e76;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.person-img {
  width: 16px;
  height: 16px;
  border-radius: 100%; /* 可选，圆形头像样式 */
  margin: 0px 5px -2px 0px;
}
.upload-instructions {
  width: 100%;
  display: block;
}
.upload-instructions-text {
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 14px;
  color: #999999;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.submitBtn {
  width: 60px;
  height: 32px;
  background: #226bff;
  box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.04);
  border-radius: 2px 2px 2px 2px;
  .active {
    background: #1e5fd6;
    border-color: #1a52b3;
    box-shadow:
      inset 0 1px 3px rgba(0, 0, 0, 0.1),
      0 1px 0 rgba(255, 255, 255, 0.1);
    transform: translateY(1px);
  }
}
.resetBtn {
  width: 60px;
  height: 32px;
  background: #ffffff;
  box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.02);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #d9d9d9;
}
.submitBtn:active {
  background: #1e5fd6;
  border-color: #1a52b3;
  box-shadow:
    inset 0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.1);
  transform: translateY(1px);
}

.resetBtn:active {
  background: #f0f0f0;
  border-color: #bbb;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  transform: translateY(1px);
}
// 步骤样式
.step-container {
  height: 80px;
  background: #f2f5fa;
  border-radius: 8px 8px 8px 8px;
  display: flex;
  align-items: center;
}
// 步骤列样式
.step-column {
  text-align: center;
}
// 步骤选中按钮样式
.step-button-selected {
  width: 79%;
  height: 64px;
  background: linear-gradient(90deg, #226bff 0%, #0448d2 100%), #ff9500;
  border-radius: 8px 8px 8px 8px;
}
.step-img {
  width: 28px;
  height: 28px;
}
// 步骤未选中按钮样式
.step-button-unselected {
  width: 79%;
  height: 64px;
  background:
    linear-gradient(90deg, #226bff 0%, #0448d2 100%), #ff9500, #ffffff;
  border-radius: 8px 8px 8px 8px;
}
:deep(.step-button-unselected.el-button > span) {
  color: #333333;
}
// 按钮字体样式
.step-button-font {
  font-family:
    Be Vietnam Pro,
    Be Vietnam Pro;
  font-weight: 600;
  font-size: 18px;
  margin: 10px 5px 15px 5px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
// 添加开课计划
.add-course-plan-container-btn {
  width: 143px;
  height: 40px;
  background: linear-gradient(90deg, #226bff 0%, #0448d2 100%);
  border-radius: 20px 20px 20px 20px;
  .span-style {
    font-family:
      Alibaba PuHuiTi 2,
      Alibaba PuHuiTi 20;
    font-weight: normal;
    font-size: 16px;
    color: #ffffff;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.course-team {
  height: 172px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 2px solid #e6ebf5;
}

.teacher-card {
  height: 172px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 2px solid #e6ebf5;
  margin-top: 20px;
  margin-bottom: 20px;
}

.teacher-avatar-container {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.teacher-avatar-wrapper {
  position: relative;
  width: 120px;
  height: 120px;
}

.teacher-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #dcdfe6;
}

.teacher-avatar-actions {
  position: absolute;
  left: -40px;
  top: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.teacher-label {
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  height: 56px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-direction: column;
}
.teacher-card-img {
  width: 40%;
}
.teacher-info-container {
  display: flex;
  align-items: flex-end;
}
.teacher-avatar-bg-size {
  width: 266px;
  height: 154px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.teacher-brief-introduction {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.teacher-title {
  margin-top: -80px;
  width: 100px;
  height: 22px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.teacher-course-introduce {
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1rem;
  color: #969aaa;
  text-align: left;
  font-style: normal;
  text-transform: none;
  overflow: auto;
  background: #f2f5fa;
  border-radius: 0px 0px 0px 0px;
  width: 100%;
  height: 120px;
}
.teacher-label-col {
  text-align: center;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-direction: column;
}
.no-open-course-img {
  width: 182px;
  height: 182px;
}
.no-courses-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 784px;
}
.course-span {
  height: 22px;
  line-height: 21px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  color: #b6bfcc;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
// 课程设计
.course-design-container {
  display: flex;
  align-items: center;
  margin: 0px 0px 20px 30px;
}
.black-exclamation-mark {
  margin-left: 5px;
  width: 20px;
  height: 20px;
}
.course-design-span {
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  color: #333333;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.course-design-radio {
  margin: 0px 0px 30px 50px;
}
.course-design-setting-span {
  margin: -30px 0px 30px 50px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
:deep(.avatar-uploader .el-upload) {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}
.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  text-align: center;
}
:deep(.el-upload-list) {
  display: flex !important;
  flex-direction: row !important;
  gap: 16px;
  margin-top: 10px;
  flex-wrap: wrap;
  align-items: flex-start;
}
:deep(.el-upload-list__item) {
  margin: 0 !important;
  width: 150px !important; // 修改为图片一致的宽度
  height: 150px !important; // 修改为图片一致的高度
  display: flex !important;
  align-items: center;
  justify-content: center;
  background: transparent;
  padding: 0 !important; // 去掉默认padding
  border-radius: 6px;
  overflow: hidden;
}
.custom-file-item-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  .el-image {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    background: #fafbfc;
    border: none; // 移除图片自身的边框
  }
  &:hover .custom-file-actions {
    opacity: 1;
    pointer-events: auto;
  }
}
.custom-file-actions {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background: rgba(0, 0, 0, 0.45);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  border-radius: 6px;
  z-index: 2;
}
.custom-file-item-wrapper:hover .custom-file-actions {
  opacity: 1;
  pointer-events: auto;
}
.icon-row {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 18px;
}
.action-icon {
  color: #fff;
  font-size: 24px;
  cursor: pointer;
  transition: color 0.2s;
}
.action-icon:hover {
  color: #409eff;
}
.cover-btn {
  background: #52c41a;
  margin-top: 8px;
  width: 90px;
  padding: 0 8px;
}
.cover-check {
  position: absolute;
  right: 6px;
  top: 6px;
  background: #52c41a;
  color: #fff;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-size: 18px;
}
.courseware-img {
  width: 100%;
  display: block;
  aspect-ratio: 2 / 1;
}
.courseware-container {
  border: 1px solid #bbbbbb;
}
.courseware-info {
  padding: 10px;
  .el-col {
    margin-top: 10px;
  }
}
.image-container {
  position: relative;
  display: inline-block;
}

.custom-image {
  width: 300px; /* 根据实际图片尺寸调整 */
  height: 200px;
}

.radio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  padding: 4px 8px;
}
.courseware-name-span {
  font-weight: normal;
  font-size: 18px;
  color: #000000;
  line-height: 18px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.course-desc-span {
  font-weight: normal;
  font-size: 16px;
  color: #585e76;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.outline-card-chapter-name{
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 22px;
  color: #333333;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.outline-card-resource-title{
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 18px;
  color: #333333;
  line-height: 18px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
