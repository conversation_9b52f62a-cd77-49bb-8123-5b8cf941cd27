<template>
  <div style="background: #f2f5fa">
    <!-- 筛选栏 -->
    <el-row :gutter="20" class="filters">
      <el-col :span="8" :offset="3">
        <el-breadcrumb :separator-icon="ArrowRight" class="breadcrumb_text">
          <el-breadcrumb-item :to="{ path: '/open-course-list' }"
            >公开课</el-breadcrumb-item
          >
          <el-breadcrumb-item> 公开课详情</el-breadcrumb-item>
        </el-breadcrumb>
      </el-col>
      <el-col :span="4" :offset="6" style="text-align: right">
        <el-button class="btn" @click="handleMyCourseClick">我的课程</el-button>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="filters">
      <el-col :span="18" :offset="3">
        <el-card class="class-info-container">
          <el-row>
            <el-col :span="24" class="class-name">课程介绍</el-col>
            <el-col :span="5">
              <el-carousel indicator-position="outside" class="course-cover">
                <el-carousel-item
                  v-for="(resource, index) in courses.courseImage?.split(',')"
                  :key="index"
                >
                  <!-- 动态资源组件 -->
                  <template v-if="isVideoResource(resource)">
                    <video
                      :src="resource"
                      class="course-cover"
                      controls
                      :style="{
                        width: '100%',
                        height: 'auto',
                        aspectRatio: '3/2',
                      }"
                    ></video>
                  </template>
                  <template v-else>
                    <img
                      :src="resource"
                      class="course-cover"
                      @error="handleImageError"
                      :style="{
                        width: '100%',
                        height: 'auto',
                        aspectRatio: '3/2',
                      }"
                    />
                  </template>
                </el-carousel-item>
              </el-carousel>
            </el-col>
            <el-col :span="19">
              <el-row>
                <el-col :span="24" class="row class-title"
                  >{{ courses.courseName }}
                </el-col>
                <el-col :span="23" class="row class-content"
                  >{{ courses.courseDescription }}
                </el-col>
                <el-col :span="24" class="row class-content">
                  <!-- 先按课程状态分层 -->
                  <template v-if="courses.courseStatus === '2'">
                    <!-- 已结束的课程 -->
                    <el-button
                      v-if="courses.myClass === 1"
                      class="study-btn"
                      @click="handleExitTheCourse(courses.courseId)"
                    >
                      退出课程
                    </el-button>
                  </template>
                  <template v-else>
                    <!-- 进行中的课程 -->
                    <span
                      v-if="courses.allowJoinFlag === '0'"
                      class="warn-text"
                    >
                      该课程暂未开放报名，敬请期待！
                    </span>
                    <el-button
                      v-else-if="courses.myClass === 0"
                      class="study-btn"
                      @click="handleJoinLearning(courses.courseId)"
                    >
                      加入学习
                    </el-button>
                    <el-button
                      v-else
                      class="study-btn"
                      style="text-align: center"
                      @click="handleMyCourseClick"
                    >
                      去学习
                    </el-button>
                  </template>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="24">
              <!-- <el-col :span="24" class="filters">
              <el-row class="middle-row">
                <el-col :span="6">
                  <el-row :span="24">
                      <el-col :span="3" :offset="6" > <img :src="teachingTeam" class="open-course-img"></el-col>
                      <el-col :span="15">
                        <el-row :span="24">
                        <el-col :span="24" class = "middle-row-text">开课时间</el-col>
                        <el-col :span="24" class = "middle-row-text-content">
                          2025-05-12日
                        </el-col>
                    </el-row>
                      </el-col>
                  </el-row>
                </el-col>
                <el-col :span="12">
                  <el-row :span="24">
                      <el-col :span="3" > <img :src="teachingTeam" class="open-course-img"></el-col>
                      <el-col :span="21">
                        <el-row :span="24">
                        <el-col :span="24" class = "middle-row-text tea-team">教学团队</el-col>
                        <el-col :span="24" class = "middle-row-text-content tea-team">
                              王美艳,李小刚,王美艳
                        </el-col>
                    </el-row>
                      </el-col>
                  </el-row>
                </el-col>
                <el-col :span="6">
                  <el-row :span="24">
                      <el-col :span="3" :offset="6" > <img :src="teachingTeam" class="open-course-img"></el-col>
                      <el-col :span="15">
                        <el-row :span="24">
                        <el-col :span="24" class = "middle-row-text">学习人数</el-col>
                        <el-col :span="24" class = "middle-row-text-content">
                          9999999
                        </el-col>
                    </el-row>
                      </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </el-col> -->
              <el-row class="middle-row">
                <el-col :span="8">
                  <el-row :span="24">
                    <el-col :span="10" style="text-align: right">
                      <img :src="startingTime" class="open-course-img"
                    /></el-col>
                    <el-row :span="20">
                      <el-col :span="24" class="middle-row-text"
                        >开课时间</el-col
                      >
                      <el-col :span="24" class="middle-row-text-content">{{
                        courses.openTime
                      }}</el-col>
                    </el-row>
                  </el-row>
                </el-col>
                <el-col :span="8">
                  <el-row :span="24">
                    <el-col :span="10" style="text-align: right">
                      <img :src="teachingTeam" class="open-course-img"
                    /></el-col>
                    <el-row :span="20">
                      <el-col :span="24" class="middle-row-text tea-team"
                        >教学团队</el-col
                      >
                      <el-col
                        :span="24"
                        class="middle-row-text-content tea-team"
                      >
                        <el-tooltip
                          class="item"
                          effect="dark"
                          :content="courses.teacherTeamNames"
                          placement="bottom-start"
                          :style="{ whiteSpace: 'normal' }"
                        >
                          {{ courses.teacherTeamNames }}
                        </el-tooltip>
                      </el-col>
                    </el-row>
                  </el-row>
                </el-col>
                <el-col :span="8">
                  <el-row :span="24">
                    <el-col :span="8" style="text-align: right">
                      <img :src="numberOfLearners" class="open-course-img"
                    /></el-col>
                    <el-row :span="20">
                      <el-col :span="24" class="middle-row-text"
                        >学习人数</el-col
                      >
                      <el-col :span="24" class="middle-row-text-content">{{
                        courses.learnersNum
                      }}</el-col>
                    </el-row>
                  </el-row>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <el-row :span="20" class="filters">
            <el-col :span="24" class="class-name">课程团队</el-col>
            <el-col
              :span="6"
              v-for="(teacherTeam, index) in courses.teacherTeams"
              :key="teacherTeam.courseId"
              class="course-grid"
            >
              <el-card class="tea-card">
                <el-row :span="20">
                  <el-col
                    :span="24"
                    :style="{
                      backgroundImage:
                        index % 2 === 0
                          ? `url(${backgroundPurple})`
                          : `url(${backgroundYellow})`,
                      width: '100%',
                      height: 'auto',
                      aspectRatio: '2 / 1',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }"
                    ><el-avatar class="course-avatar" :src="teacherTeam.avatar"
                  /></el-col>
                </el-row>
                <el-row :span="20" class="course-info">
                  <el-col :span="24">
                    <span class="course-tea-name">{{ teacherTeam.name }}</span>
                  </el-col>
                  <el-col :span="24">
                    <span class="course-sch">
                      {{ teacherTeam.schoolName }}</span
                    >
                  </el-col>
                  <el-col :span="24">
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="teacherTeam.description"
                      placement="bottom-start"
                      :style="{ whiteSpace: 'normal' }"
                    >
                      <span class="course-introduce">
                        {{ teacherTeam.description }}
                      </span>
                    </el-tooltip>
                  </el-col>
                </el-row>
              </el-card>
            </el-col>
          </el-row>
          <el-row :span="20" class="filters">
            <el-col :span="24" class="class-name">课程大纲</el-col>
            <el-col :span="24">
              <el-card class="outline-card">
                <el-collapse
                  v-model="activeNames"
                  expand-icon-position="left"
                  v-for="(item, index) in courses.courseContentList"
                >
                  <el-collapse-item :name="index">
                    <template #title>
                      <!-- 自定义图标 -->
                      <img
                        :src="getTitleIcon(index)"
                        alt="状态图标"
                        class="title-icon"
                      />
                      <!-- 原始文本 -->
                      {{ item.chapterName }}
                    </template>
                    <div
                      class="outline-card-content"
                      v-for="(children, index) in item.children"
                      :key="index"
                    >
                      {{ children.chapterName }}
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="OpenCourseDetail">
import { ArrowRight } from "@element-plus/icons-vue";
import bookCoverDefault from "@/assets/images/book-cover-default.png";
import numberOfLearners from "@/assets/images/openCourse/number_of_learners.png";
import startingTime from "@/assets/images/openCourse/starting_time.png";
import teachingTeam from "@/assets/images/openCourse/teaching_team.png";
import backgroundPurple from "@/assets/images/openCourse/background_purple.png";
import backgroundYellow from "@/assets/images/openCourse/background_yellow.png";
import arrowDown from "@/assets/images/openCourse/arrow_down.png";
import arrowRight from "@/assets/images/openCourse/arrow_right.png";
import { getInfoByStudent } from "@/api/basic/course";
import {
  addOpenCourseStudent,
  exitTheCourse,
} from "@/api/edu/openCourseStudent";
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";

const router = useRouter();
// 获取当前路由对象
const route = useRoute();
const courseId = route.query.key;
const courses = ref({});
// 当前激活面板列表
const activeNames = ref([]);

// 根据面板状态返回对应图标
const getTitleIcon = (name) => {
  return activeNames.value.includes(name) ? arrowDown : arrowRight;
};
const getInfo = () => {
  getInfoByStudent(courseId).then((res) => {
    courses.value = res.data;
  });
};
const handleJoinLearning = (courseId) => {
  addOpenCourseStudent({ courseId: courseId }).then((res) => {
    if (res.code === 200) {
      ElMessage.success("加入成功");
      ElMessageBox.confirm(
        "确认前往学习页面吗？",
        "学习提示", 
        {
          confirmButtonText: "确认前往",
          cancelButtonText: "取消操作",
          type: "info",
          iconClass: "el-icon-question",
        }
      )
        .then(() => {
          // 用户确认后执行跳转
          // router.push("/open-course-list");
        })
        .catch(() => {
          // 用户取消后的反馈
          ElMessage.info("已取消操作");
          getInfo();
        });
    }
  });
};
const handleExitTheCourse = (courseId) => {
  exitTheCourse({ courseId: courseId }).then((res) => {
    if (res.code === 200) {
      ElMessage.success("退出成功");
      getInfo();
    }
  });
};
// 资源类型判断方法
const isVideoResource = (url) => {
  const videoExtensions = [".mp4", ".webm", ".ogg", ".mov", ".avi"];
  console.log(
    url,
    videoExtensions.some((ext) => url.toLowerCase().endsWith(ext))
  );
  return videoExtensions.some((ext) => url.toLowerCase().endsWith(ext));
};

// 错误处理方法
const handleImageError = (e) => {
  e.target.src = bookCoverDefault;
};
const searchStr = ref("");
// 定义 props
const props = defineProps({
  searchKey: {
    type: String,
    default: "",
  },
});
watch(
  () => props.searchKey,
  (nValue) => {
    searchStr.value = nValue;
    const query = { key: searchStr.value };
    router.push({ name: 'OpenCourseList', query: query });
  }
);
onMounted(() => {
  getInfo();
});
</script>
<style scoped>
.breadcrumb_text {
  width: 263px;
  height: 16px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1rem;
  color: #666666;
  line-height: 37px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.filters {
  margin-top: 20px;
  margin-bottom: 20px;
}
:root {
  --primary-gradient: linear-gradient(90deg, #226bff 0%, #0448d2 100%);
}
.btn {
  width: 111px;
  height: 40px;
  background-image: linear-gradient(90deg, #226bff 0%, #0448d2 100%);
  background-color: #226bfe;
  border-radius: 4px 4px 4px 4px;
}
:deep(.el-button > span) {
  align-items: center;
  display: inline-flex;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1rem;
  color: #ffffff;

  text-align: left;
  font-style: normal;
  text-transform: none;
}
.class-name {
  margin-top: 10px;
  margin-bottom: 20px;
  height: 24px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1.5rem;
  color: #333333;
  line-height: 24px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.class-title {
  height: 22px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1.375rem;
  color: #333333;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.class-content {
  min-height: 55px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1rem;
  color: #585e76;
  line-height: 24px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.course-cover {
  width: 100%;
  aspect-ratio: 3 / 2; /* 直接指定宽高比 */
  border-radius: 14px 14px 14px 14px;
}
.course-cover img {
  width: 100%;
  aspect-ratio: 3 / 2; /* 直接指定宽高比 */
}
.row {
  margin: 10px 10px 10px 20px;
}
.study-btn {
  width: 111px;
  height: 40px;
  background-image: linear-gradient(90deg, #226bff 0%, #0448d2 100%);
  background-color: #226bfe;
  border-radius: 20px 20px 20px 20px;
}
.open-course-img {
  width: 40px;
  height: 40px;
}
.middle-row {
  margin-top: 20px;
  background: #f2f5fa;
  height: 90px;
  text-align: center;
  border-radius: 8px 8px 8px 8px;
  display: flex;
  align-items: center;
}
.middle-row-text {
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1rem;
  color: #585e76;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-left: 10%;
}
.middle-row-text-content {
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1rem;
  color: #333333;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-left: 10%;
  max-width: 9vw;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tea-team {
  margin-left: 5%;
}
.tea-card {
  width: 100%; /* 填充父容器 */
  height: auto; /* 自动高度 */
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 2px solid #e6ebf5;
  aspect-ratio: 1/1;
}

.tea-card img {
  width: 100%;
  height: auto;
  aspect-ratio: 2 / 1;
}

.tea-card :deep(.el-card__body) {
  padding: 10px 0px 10px 0px !important;
  box-sizing: border-box;
}
.course-grid {
  padding: 0vw 0.8vw 0vw 0vw;
}
.course-info {
  padding: 15px 0px 20px 10px; /* 增加底部内边距 */
}
.course-tea-name {
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1.125rem;
  color: #333333;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.course-sch {
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1rem;
  color: #585e76;

  text-align: left;
  font-style: normal;
  text-transform: none;
}
.course-introduce {
  margin-top: 10px;
  margin-bottom: 10px; /* 添加底部间距 */
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 1rem;
  color: #969aaa;
  text-align: left;
  font-style: normal;
  text-transform: none;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  /* 新增动态计算 */
  -webkit-line-clamp: 3; /* 最小2行，最大5行 */
  -webkit-box-orient: vertical;
}
@media (min-width: 1500px) and (max-width: 1800px) {
  .course-introduce {
    -webkit-line-clamp: 2;
  }
  .course-avatar {
    width: 110px !important;
    height: 110px !important;
  }
}
@media (min-width: 1200px) and (max-width: 1500px) {
  .course-introduce {
    -webkit-line-clamp: 1;
  }
  .course-avatar {
    width: 100px !important;
    height: 100px !important;
  }
}
@media (min-width: 1900px) {
  .course-introduce {
    -webkit-line-clamp: 3;
  }
  .course-avatar {
    width: 136px;
    height: 136px;
  }
}
.course-avatar {
  width: 136px;
  height: 136px;
}
.outline-card {
  border-radius: 10px 10px 10px 10px;
  border: 1px solid #e6ebf5;
}
.outline-card :deep(.el-card__body) {
  padding: 0px 0px 0px 0px !important;
}
:deep(.el-collapse-item__header) {
  height: 60px;
  align-items: center;
  background-color: #f7f8f9;
  border: none;
  border-bottom: 1px solid #ebeef500;
  color: var(--el-collapse-header-text-color);
  cursor: pointer;
  display: flex;
  font-size: var(--el-collapse-header-font-size);
  font-weight: 500;
  line-height: var(--el-collapse-header-height);
  outline: none;
  padding: 0;
  transition: border-bottom-color var(--el-transition-duration);
  width: 100%;
}
:deep(.el-collapse-item__wrap) {
  background-color: var(--el-collapse-content-bg-color);
  border-bottom: 1px solid var(--el-collapse-border-color);
  box-sizing: border-box;
  overflow: hidden;
  will-change: height;
  margin-left: 50px;
}
:deep(.el-collapse-item__arrow) {
  font-weight: 300;
  transition: transform var(--el-transition-duration);
  margin-left: 10px;
}
:deep(.el-collapse-item__title) {
  flex: auto;
  text-align: left;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 22px;
  color: #333333;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
/* 隐藏默认折叠箭头 */
:deep(.el-collapse-item__arrow) {
  display: none !important;
}
.title-icon {
  width: 12px;
  height: 12px;
  margin-left: 20px;
  margin-bottom: 2px;
  margin-right: 10px;
}
.outline-card-content {
  margin-top: 20px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 18px;
  color: #333333;
  line-height: 18px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.warn-text {
  color: #e6a23c;
  font-size: 1rem;
  line-height: 24px;
  margin-left: 20px;
}
.class-info-container {
  border-radius: 10px 10px 10px 10px;
}
</style>
