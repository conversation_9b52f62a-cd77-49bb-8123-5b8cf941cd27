<!-- @Author: <PERSON><PERSON><PERSON><PERSON> -->
<template>
  <el-form
      :model="form"
      :rules="rules"
      ref="formRef"
      label-width="120px"
  >
    <el-row class="mt20">
      <el-col :span="22" :offset="1">
        <el-form-item label="课程名称" prop="courseName" required>
          <el-input v-model="form.courseName" placeholder="请输入课程名称" maxlength="200"/>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row class="mt20">
      <el-col :span="10"  :offset="1">
        <el-form-item label="教育层次" prop="subjectId" required>
          <el-select placeholder="教育层次" v-model="form.subjectId" clearable @change="getThirdSubjectList">
            <el-option
                v-for="item in subjectList"
                :key="item.subjectId"
                :label="item.subjectName"
                :value="item.subjectId"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="10" :offset="2">
        <el-form-item label="学科分类" prop="thirdSubjectId" required>
          <el-select placeholder="学科分类" v-model="form.thirdSubjectId" clearable>
            <el-option
                v-for="item in thirdSubjectList"
                :key="item.subjectId"
                :label="item.subjectName"
                :value="item.subjectId"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="mt20">
      <el-col :span="22"  :offset="1">
        <el-form-item label="课程介绍" prop="courseDescription" required>
          <el-input
              v-model="form.courseDescription"
              type="textarea"
              :rows="4"
              :maxlength="400"
              show-word-limit
              resize="none"
              placeholder="请输入课程介绍"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row class="mt20">
      <el-col :span="20"  :offset="1">
        <el-form-item label="课程介绍/图片" prop="bookId">
          <el-upload
              ref="uploadRef"
              v-model:file-list="form.courseImage"
              class="avatar-uploader"
              :action="uploadUrl"
              :http-request="upload"
              :before-upload="handleBeforeUpload"
              :on-error="handleUploadError"
              :limit="5"
              :on-exceed="handleExceed"
          >
            <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
            <template #tip>
              <div class="el-upload__tip">
                最多可上传5个文件，视频仅支持MP4格式，图片仅支持png、jpg、jpeg格式，单个文件大小不超过500MB
              </div>
            </template>
            <!-- 自定义文件列表项 -->
            <template #file="{ file }">
              <div class="custom-file-item-wrapper">
                <!-- 图片显示 -->
                <el-image
                    v-if="isImageFile(file)"
                    :src="file.response?.url || loding"
                />
                <!-- 视频显示 -->
                <div v-else-if="isVideoFile(file)" class="video-preview">
                  <video
                    :src="file.response?.url"
                    class="video-thumbnail"
                    preload="metadata"
                    muted
                  ></video>
                  <div class="video-overlay">
                    <el-icon class="play-icon"><VideoPlay /></el-icon>
                    <span class="video-label">视频</span>
                  </div>
                </div>

                <div
                    v-if="isImageFile(file) && form.courseCover === (file.response?.url)"
                    class="cover-check"
                    title="封面"
                >
                  <el-icon><Check /></el-icon>
                </div>

                <div class="custom-file-actions">
                  <div class="icon-row">
                    <el-icon
                      class="action-icon"
                      :title="isVideoFile(file) ? '播放' : '预览'"
                      @click.stop="handlePictureCardPreview(file)"
                    >
                      <ZoomIn v-if="isImageFile(file)" />
                      <VideoPlay v-else />
                    </el-icon>
                    <el-icon
                      class="action-icon"
                      title="删除"
                      @click.stop="handleRemove(file)"
                    >
                      <Delete />
                    </el-icon>
                  </div>
                  <!-- 只有图片才显示设为封面按钮 -->
                  <el-button
                      v-if="isImageFile(file)"
                      type="success"
                      size="small"
                      icon="el-icon-star-on"
                      class="cover-btn"
                      @click.stop="setAsCover(file)"
                      :disabled="form.courseCover === (file.response?.url)"
                  >设为封面</el-button>
                  <div v-else class="file-type-badge">
                    <el-tag type="info" size="small">视频文件</el-tag>
                  </div>
                </div>
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-col>
    </el-row>
    <el-dialog
        v-model="dialogVisible"
        title="预览"
        width="800px"
        append-to-body
    >
      <img
          :src="dialogImageUrl"
          style="display: block; max-width: 100%; margin: 0 auto"
      />
    </el-dialog>
  </el-form>
</template>

<script setup>
/* import */
import { ref, reactive, watch, onMounted,getCurrentInstance, toRefs } from "vue";
import { Plus, ZoomIn, Delete, Check, VideoPlay } from '@element-plus/icons-vue';
import { listSubject,listThirdSubject,saveOpenCourse } from '@/api/edu/openCourse.js';
import loding from "@/assets/images/loding.png";
import {OssService} from "@/utils/aliOss.js";
const { proxy } = getCurrentInstance();

/* data */
const data = reactive({
  form:{
    courseName:'',
    subjectId:'',
    thirdSubjectId:'',
    courseDescription:'',
    courseImage:[],
    courseCover:'',
  },
  rules: {
    courseName: [{ required: true, message: "课程名称不能为空", trigger: "change" },],
    subjectId: [{ required: true, message: "教育层次不能为空", trigger: "change" },],
    thirdSubjectId: [{ required: true, message: "学科分类不能为空", trigger: "change" },],
    courseDescription: [{ required: true, message: "课程介绍不能为空", trigger: "change" },],
  },
})

const { form, rules } = toRefs(data);
const formRef = ref(null);
const subjectList = ref([]); // 教育层次列表
const thirdSubjectList = ref([]); // 学科分类列表

const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/file/upload'); // 上传的图片服务器地址
const iconUrl = ref();
const dialogImageUrl = ref('');
const dialogVisible = ref(false);

/* methods*/
const emit = defineEmits(['refreshPage']);

// 重置
const resetForm = () => {
  formRef.value.resetFields()
}

// 查询教育层次
function getSubjectList() {
  listSubject({}).then(response => {
    if (response.code === 200) {
      subjectList.value = response.data;
    }else{
      console.log("查询教育层次失败");
    }
  });
}

// 查询学科分类 教育层次选项更改时
function getThirdSubjectList(val) {
  form.value.thirdSubjectId = '';
  listThirdSubject({subjectId : val}).then(response => {
    if (response.code === 200) {
      thirdSubjectList.value = response.data;
    }else{
      console.log("查询学科分类失败");
    }
  })
}

// 保存
function submitForm(){
  proxy.$refs["formRef"].validate((valid) => {
    if (valid == true){
      if (form.value.id != null) {
        // 编辑逻辑 有业务的话再加上
      } else {
        if (form.value.courseImage.length == 0){
          proxy.$modal.msgError('请上传文件');
          return
        }
        // 检查是否有图片文件，如果有图片必须设置封面
        const hasImage = form.value.courseImage.some(file => isImageFile(file));
        if (hasImage && form.value.courseCover == ''){
          proxy.$modal.msgError('请设置封面');
          return
        }
        // 图片用逗号拼接
        const submitData = {
          ...form.value,
          courseImage: form.value.courseImage
              .map(item => item.response?.url)
              .filter(Boolean)
              .join(',')
        };
        saveOpenCourse(submitData).then(response => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          emit('refreshPage', response);
        });
      }
    }
  });
}

// 上传文件
function upload(file) {
  return syncFile(file);
}

// 上传OSS
const syncFile = async (file) => {
  try {
    // 工具类引用
    const res = await OssService(file.file);
    return res;
  } catch (e) {
    throw e;
  }
};

// 上传前校检格式和大小
function handleBeforeUpload(file) {
  console.log(file);
  const imageType = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg'];
  const videoType = ['video/mp4'];
  const isImage = imageType.includes(file.type);
  const isVideo = videoType.includes(file.type);

  //检验文件格式
  if (!isImage && !isVideo) {
    proxy.$modal.msgError(`文件格式错误! 仅支持图片(jpg、png、svg)和视频(mp4)格式`);
    return false;
  }

  // 检验文件大小 (500MB)
  const isLt500M = file.size / 1024 / 1024 < 500;
  if (!isLt500M) {
    proxy.$modal.msgError('文件大小不能超过500MB!');
    return false;
  }

  return true;
}


// 上传失败处理
function handleUploadError() {}
const uploadRef = ref();
import { genFileId } from 'element-plus'

// 处理文件数量超出限制的回调
const handleExceed = (files, fileList) => {
  proxy.$modal.msgError('最多只能上传5个文件');
};

// 预览文件
const handlePictureCardPreview = (uploadFile) => {
  console.log(uploadFile);
  if (isVideoFile(uploadFile)) {
    // 视频文件，直接在新窗口打开
    window.open(uploadFile.response.url, '_blank');
  } else {
    // 图片文件，弹窗预览
    dialogImageUrl.value = uploadFile.response.url;
    dialogVisible.value = true;
  }
};

// 删除图片
const handleRemove = (uploadFile) => {
  const index = form.value.courseImage
      ? form.value.courseImage.findIndex(item =>
          item.response?.url && item.response.url === uploadFile.response?.url
      )
      : -1;
  if (index !== -1) {
    form.value.courseImage.splice(index, 1);
  }
};

// 判断文件是否为图片
function isImageFile(file) {
  const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg'];
  return imageTypes.includes(file.raw?.type || file.type);
}

// 判断文件是否为视频
function isVideoFile(file) {
  const videoTypes = ['video/mp4'];
  return videoTypes.includes(file.raw?.type || file.type);
}

// 设为封面
function setAsCover(file) {
  // 只有图片才能设为封面
  if (!isImageFile(file)) {
    proxy.$modal.msgError('只有图片可以设为封面!');
    return;
  }
  form.value.courseCover = file.response?.url;
  proxy.$modal.msgSuccess('已设为封面');
}

/* 暴露函数 */
defineExpose({ submitForm });

onMounted(() => {
  // 清空表单
  resetForm();
  getSubjectList();
});

</script>

<style lang="scss" scoped>
.mt20{
  margin-top: 20px;
}
:deep(.avatar-uploader .el-upload) {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}
.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  text-align: center;
}
:deep(.el-upload-list) {
  display: flex !important;
  flex-direction: row !important;
  gap: 16px;
  margin-top: 10px;
  flex-wrap: wrap;
  align-items: flex-start;
}
:deep(.el-upload-list__item) {
  margin: 0 !important;
  width: 150px !important;      // 修改为图片一致的宽度
  height: 150px !important;     // 修改为图片一致的高度
  display: flex !important;
  align-items: center;
  justify-content: center;
  background: transparent;
  padding: 0 !important;        // 去掉默认padding
  border-radius: 6px;
  overflow: hidden;
}
.custom-file-item-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  .el-image {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    background: #fafbfc;
    border: none; // 移除图片自身的边框
  }
  &:hover .custom-file-actions {
    opacity: 1;
    pointer-events: auto;
  }
}
.custom-file-actions {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background: rgba(0,0,0,0.45);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  border-radius: 6px;
  z-index: 2;
}
.custom-file-item-wrapper:hover .custom-file-actions {
  opacity: 1;
  pointer-events: auto;
}
.icon-row {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 18px;
}
.action-icon {
  color: #fff;
  font-size: 24px;
  cursor: pointer;
  transition: color 0.2s;
}
.action-icon:hover {
  color: #409EFF;
}
.cover-btn {
  margin-top: 8px;
  width: 90px;
  padding: 0 8px;
}
.cover-check {
  position: absolute;
  right: 6px;
  top: 6px;
  background: #52c41a;
  color: #fff;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  font-size: 18px;
}

/* 视频预览样式 */
.video-preview {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 6px;
  overflow: hidden;

  .video-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
  }

  .video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 6px;

    .play-icon {
      font-size: 32px;
      color: #fff;
      margin-bottom: 4px;
    }

    .video-label {
      color: #fff;
      font-size: 12px;
      font-weight: 500;
    }
  }
}

/* 文件类型标识 */
.file-type-badge {
  margin-top: 8px;

  .el-tag {
    font-size: 11px;
    padding: 2px 6px;
  }
}
</style>
<style>

</style>