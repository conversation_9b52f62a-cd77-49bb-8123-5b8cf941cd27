<template>
  <el-dialog
    :title="form.teammateId ? '修改团队成员' : '添加团队成员'"
    :modelValue="modelValue"
    width="30%"
    :before-close="handleClose"
    center
  >
    <!-- 表单主体 -->
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="right"
    >
      <!-- 头像上传 -->
      <el-form-item label="头像" prop="avatar">
        <el-upload
          class="avatar-uploader"
          :show-file-list="false"
          :http-request="upload"
          :action="uploadUrl"
          :before-upload="handleBeforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
        >
          <img v-if="form?.avatar" :src="form?.avatar" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </el-form-item>

      <!-- 姓名（下拉选教师） -->
      <el-form-item label="姓名" prop="name">
        <el-select
          v-model="form.name"
          placeholder="请选择教师名称"
          clearable
          @change="onNameChange"
        >
          <el-option
            v-for="item in teacherOptions"
            :key="item.userId"
            :label="item.realName"
            :value="item.userId"
          />
        </el-select>
      </el-form-item>

      <!-- 学院（自动填充，禁用编辑） -->
      <el-form-item label="学院" prop="schoolName">
        <el-input
          v-model="form.schoolName"
          disabled
          placeholder="教师所属学院"
        />
      </el-form-item>

      <!-- 职称/职务 -->
      <el-form-item label="职称/职务" prop="title">
        <el-input
          v-model="form.title"
          maxlength="100"
          show-word-limit
          placeholder="请输入职称/职务"
        />
      </el-form-item>

      <!-- 教师简介 -->
      <el-form-item label="教师简介" prop="description">
        <el-input
          type="textarea"
          v-model="form.description"
          :rows="4"
          maxlength="500"
          show-word-limit
          placeholder="请输入教师简介"
        />
      </el-form-item>
    </el-form>

    <!-- 底部按钮：取消 / 确定 -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script setup>
import { OssService } from "@/utils/aliOss.js";
import { Plus } from "@element-plus/icons-vue";
import { listNotPage } from "@/api/system/user";
import { getSchool } from "@/api/basic/school";
import {
  addOpenCourseTeammate,
  exitOpenCourseTeammate,
} from "@/api/edu/openCourseTeammate";
import { ref, onMounted } from "vue";
const { proxy } = getCurrentInstance();
const teacherOptions = ref([]);
const handleClose = () => {
  formRef.value.resetFields(); // 清除表单校验和值
  emit("update:modelValue", false); // 关闭对话框
};
// 校验规则
const rules = {
  name: [{ required: true, message: "请选择教师名称", trigger: "change" }],
  title: [{ required: true, message: "请填写职称/职务", trigger: "blur" }],
  description: [{ required: true, message: "请填写教师简介", trigger: "blur" }],
};
const emit = defineEmits(["update"]);
const props = defineProps({
  form: {},
  modelValue: Boolean,
});
const formRef = ref(null);
const form = computed(() => props.form);
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + "/file/upload"); // 上传的图片服务器地址
const handleConfirm = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (form.value.teammateId) {
          const response = await exitOpenCourseTeammate(form.value);
          if (response.code === 200) {
            proxy.$modal.msgSuccess("修改团队成员成功");
            emit("update:modelValue", false);
            emit("refresh"); 
          }
        } else {
          const response = await addOpenCourseTeammate(form.value);
          if (response.code === 200) {
            proxy.$modal.msgSuccess("添加团队成员成功");
            emit("update:modelValue", false);
            emit("refresh"); 
          }
        }
      } catch (error) {
        console.error("提交失败:", error);
        proxy.$modal.msgError("添加团队成员失败");
      }
    }
  });
};
function upload(file) {
  return syncFile(file);
}
const syncFile = async (file) => {
  try {
    // 工具类引用
    const res = await OssService(file.file);
    return res;
  } catch (e) {
    throw e;
  }
};
// 上传前校检格式和大小
function handleBeforeUpload(file) {
  const imageTypes = ["image/jpeg", "image/jpg", "image/png"];
  const validTypes = [...imageTypes];

  const isValidType = validTypes.includes(file.type);
  const isValidSize = file.size <= 1 * 1024 * 1024; // 1MB

  // 校验文件类型
  if (!isValidType) {
    proxy.$modal.msgError(`文件类型不支持!`);
    return false;
  }

  // 校验文件大小
  if (!isValidSize) {
    proxy.$modal.msgError(`文件大小超过限制!`);
    return false;
  }
  return true;
}
// 上传成功处理
function handleUploadSuccess(res, file) {
  form.value.avatar = res.url;
}
// 上传失败处理
function handleUploadError() {}
function onNameChange(userId) {
  const selectedItem = teacherOptions.value.find(
    (item) => item.userId === userId
  );
  getSchool(selectedItem.schoolId).then((res) => {
    form.value.schoolName = res.data.schoolName;
  });
}
onMounted(() => {
  listNotPage({ userType: "2" }).then((res) => {
    teacherOptions.value = res.data;
  });
});
</script>

<style scoped>
.avatar-uploader .avatar {
  width: 80px;
  height: 80px;
  display: block;
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 80px;
  height: 80px;
  text-align: center;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px; 
}
</style>
