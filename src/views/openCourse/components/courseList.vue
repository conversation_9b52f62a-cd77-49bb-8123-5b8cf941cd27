<!-- @Author: <PERSON><PERSON><PERSON><PERSON> -->
<template>
  <div class="main-body">
    <div v-for="item in listData" :key="item.courseId" class="course-item"> <!--这块需要遍历数据了-->
      <div class="course-item-box">
        <div class="item-box-img">
          <img @click="goToDetail(item.courseId)"  v-if="item.courseCover" :src="item.courseCover" style="width:100%;height:100%;object-fit:cover;border-radius:10px;" />
          <div v-else style="width:100%;height:100%;display: flex;justify-content: center;align-items: center;color: #707173;">暂无封面</div>
          <div class="course-item-box-icon" @click="handleDelete(item)" title="删除课程">
            <el-icon><DeleteFilled /></el-icon>
          </div>
        </div>
        <div class="item-box-content">
          <div class="item-box-content-title" :title="item.courseName">
            {{ item.courseName.length > 10 ? item.courseName.slice(0, 10) + '...' : item.courseName }}
            <el-tag
                effect="plain"
                :type="getTagType(item)"
                round
            >
              {{ getOptionDescCom(item) }}
            </el-tag>
          </div>
          <div class="item-box-content-text" :title="item.courseDescription">{{ item.courseDescription && item.courseDescription.length > 14 ? item.courseDescription.slice(0, 14) + '...' : item.courseDescription }}</div>
          <div class="item-box-content-other">
            <span>发布时间:{{ item.createTime }}</span>
            <span class="person-info"><img :src="person" class="person-img" />{{ item.learnersNum }}人学习</span>
          </div>
          <div class="item-box-content-teacher" :title="`授课老师：${item.realName || ''}`">
            <el-icon size="20" color="#FFB670"> <img :src="star" class="person-img" /> </el-icon>
            <span class="teacher-text">授课老师：{{ item.realName && item.realName.length > 9 ? item.realName.slice(0, 9) + '...' : (item.realName || '') }}</span>
          </div>
        </div>
      </div>
    </div>
</div>
</template>
<script setup>
/* import */
import { ref, reactive, watch, onMounted } from "vue";
import { View, DeleteFilled } from "@element-plus/icons-vue";
import { ElMessageBox, ElMessage } from 'element-plus';
import person from "@/assets/images/openCourse/person.png";
import star from "@/assets/images/openCourse/star.png";
import { getOptionDesc,approvalStatusOptions,reviewStatusOptions } from "@/utils/optionUtil";
const router = useRouter();

/* data */
const props = defineProps({
  listData: {
    type: Array,
    default: () => []
  }
});


/* emits */
const emit = defineEmits(['delete-course']);

/* methods */
const handleDelete = (item) => {
  ElMessageBox.confirm(
    `确定要删除课程"${item.courseName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      customClass: 'delete-confirm-dialog'
    }
  ).then(() => {
    // 发射删除事件给父组件处理
    emit('delete-course', item);
    ElMessage({
      type: 'success',
      message: '删除成功!'
    });
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '已取消删除'
    });
  });
};

function getTagType(data){
  if (data.applyStatus == 1){
    return 'success';
  }else if (data.applyStatus == 2){
    return 'warning';
  }else if (data.applyStatus == 3){
    return 'danger';
  }
}
function getOptionDescCom(data){
  if (data.step == 1){
    console.log(getOptionDesc(approvalStatusOptions, data.applyStatus));
    return getOptionDesc(approvalStatusOptions, data.applyStatus);
  }else if(data.step == 2){
    return getOptionDesc(reviewStatusOptions, data.applyStatus);
  }
}
const goToDetail = (courseId) => {
  const query = { key: courseId };
  router.push({ name: 'OpenCourseToExamine', query: query });
};
</script>
<style lang="scss" scoped>
.main-body {
  width: 100%;
  min-height: 670px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px 2%;
}
.course-item{
  height: 350px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  .course-item-box{
    width: 90%;
    height: 100%;
    padding: 10px;
    border-radius: 10px;
    border: 2px solid #EBECF3;
    display: flex;
    flex-direction: column;
    .item-box-img{
      width: 100%;
      height: 55%;
      background-color: #EBECF3;
      border-radius: 10px;
      position: relative;
      .course-item-box-icon{
        position: absolute;
        top: 8px;
        right: 8px;
        width: 32px;
        height: 32px;
        background: #FFFFFF;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(90px);

        &:hover {
          background: rgba(255, 59, 48, 0.9);
          transform: scale(1.1);
          box-shadow: 0 4px 12px rgba(255, 59, 48, 0.4);
        }

        .el-icon {
          color: #000000;
          font-size: 16px;
          transition: all 0.2s ease;
        }

        &:hover .el-icon {
          transform: scale(1.1);
          color: #ffffff;
        }
      }
    }
    .item-box-content{
      width: 100%;
      height: 45%;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      padding: 10px;
      .item-box-content-title{
        font-size: 18px;
        color: #000000;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .item-box-content-text{
        font-size: 15px;
        color: #70707B;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        //padding: 5px 5px 5px 15px;
      }
      .item-box-content-other{
        font-size: 12px;
        color: #B1B4B7;
        display: flex;
        justify-content: space-between;
        align-items: center;
        //padding: 5px 5px 5px 15px;
        .person-info {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
      .item-box-content-teacher{
        align-self: flex-start;
        display: flex;
        align-items: center;
        background-color: #F3F6FC;
        border: none;
        border-radius: 5px;
        font-size: 15px;
        color: #787E87;
        padding: 5px 15px 5px 10px;
        max-width: 100%;
        overflow: hidden;
        .teacher-text {
          white-space: nowrap;
          flex: 1;
        }
      }
      .person-img {
        width: 16px;
        height: 16px;
        border-radius: 100%;
      }
    }
  }
}

/* 删除确认对话框样式 */
:deep(.delete-confirm-dialog) {
  border-radius: 12px;

  .el-message-box__header {
    padding: 20px 20px 10px;

    .el-message-box__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .el-message-box__content {
    padding: 10px 20px 20px;

    .el-message-box__message {
      font-size: 14px;
      color: #606266;
    }
  }

  .el-message-box__btns {
    padding: 10px 20px 20px;

    .el-button--primary {
      background: linear-gradient(135deg, #ff3b30, #ff6b6b);
      border: none;
      border-radius: 8px;
      padding: 10px 20px;
      font-weight: 500;

      &:hover {
        background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(255, 59, 48, 0.3);
      }
    }

    .el-button--default {
      border-radius: 8px;
      padding: 10px 20px;
      font-weight: 500;
      border: 1px solid #dcdfe6;

      &:hover {
        background: #f5f7fa;
        border-color: #c0c4cc;
        transform: translateY(-1px);
      }
    }
  }
}
</style>