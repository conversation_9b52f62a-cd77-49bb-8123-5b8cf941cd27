<template>
  <div class="content-right-con">
    <div class="right-top-con">
      <div class="menu-con">
        <el-menu
          :default-active="activeIndex"
          class="el-menu-demo"
          mode="horizontal"
          @select="handleSelect"
        >
          <el-menu-item index="9">全部</el-menu-item>
          <el-menu-item index="0">待审核</el-menu-item>
          <el-menu-item index="2">未通过</el-menu-item>
          <el-menu-item index="1">已通过</el-menu-item>
          <el-menu-item index="3">已过期</el-menu-item>
        </el-menu>
      </div>
      <el-input
        v-model="queryCriteria"
        style="width: 240px; margin-right: 45px"
        placeholder="请输入教材名称/ISBN"
      >
        <template #append>
          <el-button @click="handleSearch()" :icon="Search" />
        </template>
      </el-input>
    </div>
    <div style="min-height: 550px">
      <div v-for="item in items" :key="item.id">
        <div class="group_22 flex-col">
          <div class="text-wrapper_5 flex-row">
            <span class="text_17">ISBN：{{ item.isbn }}</span>
            <!-- <span class="large-button">主编：{{ item.editor }}</span> -->
          </div>
          <div class="section_3 flex-row">
            <div class="container">
              <span class="text_18">申请时间：{{ item.trialTime }}</span>
              <!-- <span v-if="item.status === 2" class="text_18">未通过原因：{{ item.rejectReason }}</span> -->
            </div>
            <span class="text_19_ah">
              {{
                item.trialPeriod ? "试用起止日期：" + item.trialPeriod : ""
              }}&nbsp;&nbsp;试用天数：{{ item.trialDays }}天</span
            >
          </div>
          <div class="text-wrapper_7 flex-row justify-between">
            <span class="text_22">{{ item.bookName }}</span>
            <span class="text_23" :class="getStatusClass(item.status)">{{
              getStatusText(item)
            }}</span>
            <el-button
              class="large-button"
              plain
              size="large"
              :style="{
                'margin-left':
                  item.status === 2 || item.status === 0 ? '480px' : '400px',
              }"
              @click="showDetails(item)"
              >查看详情</el-button
            >

            <el-button
              class="text_26"
              style="width: 85px; margin-left: 10px"
              v-if="item.status === 1 && !item.activationTime"
              type="primary"
              size="large"
              @click="handleActivate(item)"
              >去激活 ></el-button
            >
            <div
              class="text_25"
              v-if="
                (item.status === 1 && item.activationTime) ||
                item.status === 4 ||
                item.status === 3
              "
              :style="{ color: 'blue' }"
            >
              {{ "已激活" }}
            </div>
          </div>
          <div class="section_6 flex-col">
            <img :src="item.cover ? item.cover : bookCoverDefault" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <el-dialog
    title="申请详情"
    v-model="dialogVisible"
    style="margin-top: 20vh !important; font-weight: bold"
    width="25%"
    @close="handleClose"
  >
    <el-row :gutter="24" style="color: black; font-weight: 400">
      <el-col :span="18" class="dialog-col">
        <span>ISBN:</span>
        <span>{{ currentItem.isbn }}</span>
        <!-- <el-input v-else style="width: 80%;margin-left: 23px;" v-model="currentItem.isbn"></el-input> -->
      </el-col>
      <el-col :span="6" class="dialog-col">
        <span :class="getStatusClass(currentItem.status)">
          {{ getStatusText(currentItem) }}
        </span>
      </el-col>
      <el-col :span="24" class="dialog-col">
        <span>申请时间:</span>
        <span>{{ currentItem.trialTime }}</span>
        <!-- <el-date-picker v-else v-model="currentItem.trialTime" format="YYYY-MM-DD" type="date" placeholder="请输入申请时间"
          :size="size" /> -->
      </el-col>
      <el-col :span="24" class="dialog-col">
        <span>申请原因:</span>
        <span>{{ currentItem.applicationReason }}</span>
        <!-- <el-input v-else style="width: 80%;" v-model="currentItem.applicationReason"></el-input> -->
      </el-col>
      <el-col :span="24" class="dialog-col">
        <span>申请材料:</span>
        <img
          src="@/assets/icons/svg/PDF.svg"
          style="margin: 0px 10px -4px 5px"
          v-if="!isEditing"
        />
        <br />
        <span
          style="color: blue; cursor: pointer"
          @click="previewFile(item)"
          v-if="!isEditing"
          v-for="(item, index) in currentItem.resFileList"
          :key="index"
        >
          {{ item.fileName }}<br />
        </span>
        <!-- <el-button type="text" @click="previewFile(currentItem.examineFileUrl)" v-if="!isEditing">预览</el-button> -->
        <el-upload
          v-else
          style="margin-left: 62px; margin-top: -23px"
          :name="fileName"
          v-model:file-list="currentItem.resFileList"
          :http-request="upload"
          :action="uploadUrl"
          :before-upload="handleBeforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :limit="1"
          :on-exceed="handleExceed"
        >
          <el-button type="primary">上传文件</el-button>
        </el-upload>
      </el-col>
      <el-col :span="24" class="dialog-col" v-if="!isEditing">
        <span>审核时间:</span>
        <span>{{ currentItem.examineTime }}</span>
        <!-- <el-date-picker v-else v-model="currentItem.trialTime" format="YYYY-MM-DD" type="date" placeholder="请输入申请时间"
          :size="size" /> -->
      </el-col>
      <el-col
        :span="24"
        class="dialog-col"
        v-if="!isEditing && currentItem.status === 2"
      >
        <span>驳回原因:</span>
        <span>{{ currentItem.reject }}</span>
        <!-- <el-input v-else style="width: 80%;" v-model="currentItem.applicationReason"></el-input> -->
      </el-col>
    </el-row>
    <template #footer>
      <div
        style="display: flex; justify-content: center"
        v-if="currentItem.status === '0'"
      >
        <el-button plain @click="editItem">编辑</el-button>
        <el-button
          type="primary"
          style="width: 180px"
          @click="confirm(currentItem)"
          >确定</el-button
        >
      </div>
      <div v-else style="display: flex; justify-content: center">
        <el-button style="width: 180px" @click="dialogVisible = false"
          >关闭</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="applicationHistory">
import { ElNotification } from "element-plus";
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { Search } from "@element-plus/icons-vue";
import { Base64 } from "js-base64";
import { OssService } from "@/utils/aliOss.js";
import { listApply, updateApply } from "@/api/shop/trial";
import { listPurchaseCode } from "@/api/shop/purchaseCode";
import { addFile, deleteFile, listFile } from "@/api/system/file";
import useUserStore from "@/store/modules/user.js";
import bookCoverDefault from "@/assets/images/book-cover-default.png";
// import { decryptData } from '@/utils/encrypt.js';
const userStore = useUserStore();
const { proxy } = getCurrentInstance();
const router = useRouter();
const activeIndex = ref("9"); // 初始值是 9 表示“全部”
const queryCriteria = ref("");
const dialogVisible = ref(false);
const isEditing = ref(false);
const currentItem = ref({});
// 上传头像的API URL
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + "/file/upload");
// 文件列表
const items = ref([]);
// 获取当前路由对象
const route = useRoute();
const bookId = route.query.key;
const bookIdDecrypt = ref();
const status = ref(null);
// 辅助函数：将日期格式化为 "yyyy-MM-dd HH:mm:ss"
function formatDate(date) {
  const d = new Date(date); // 创建日期对象
  const year = d.getFullYear(); // 获取年份
  const month = String(d.getMonth() + 1).padStart(2, "0"); // 获取月份，注意月份从 0 开始，需要加 1
  const day = String(d.getDate()).padStart(2, "0"); // 获取日期
  return `${year}-${month}-${day}`; // 返回格式化后的日期
}

// 点击菜单时更新 activeIndex 的值
const handleSelect = (index) => {
  activeIndex.value = index; // 转换为数字
  status.value = index;
  if (activeIndex.value === "9") {
    status.value = null;
  }
  getInfo();
};

// 根据选中的菜单项过滤数据
// const filteredItems = computed(() => {
//   if (activeIndex.value === '9') {
//     return items.value; // 全部，返回所有数据
//   } else {
//     return items.value.filter(item => item.status === activeIndex.value); // 根据 status 筛选
//   }
// });
// 获取状态对应的类名
const getStatusClass = (status) => {
  switch (status) {
    case 2:
      return "status-unprocessed"; // 未通过
    case 1:
      return "status-processed"; // 已通过
    case 0:
      return "status-processing"; // 待审核
    case 3:
      return "status-expired"; // 已过期
    case 4:
      return "status-processed"; // 试用中
    default:
      return "";
  }
};
// 上传失败处理
function handleUploadError() {
  proxy.$modal.msgError("图片插入失败");
}
async function handleActivate(item) {
  const userInfo = await userStore.getInfo();
  if (userInfo.user.userType === "2") {
    const params = {
      bookId: item.bookId,
      state: 2,
      codeType: 2,
      userId: userInfo.user.userId,
    };
    await listPurchaseCode(params).then((res) => {
      if (res.code === 200) {
        if (res.rows.length === 0) {
          ElNotification({
            title: "操作提示",
            message: "未查询到购书码",
            type: "warning",
          });
        }
        router.push({ path: "/book-codes", query: { code: res.rows[0].code } });
      }
    });
  } else {
    router.push({ path: "/identity" });
  }
}
// 获取状态对应的文本
const getStatusText = (item) => {
  switch (item.status) {
    case 0:
      return "待审核";
    case 1:
      if(item.trialPeriod){
        return "试用中";
      }else{
        return "已通过";
      }
    case 2:
      return "未通过";
    case 3:
      return "已过期";
    default:
      return "";
  }
};
// 显示详情弹出框
const showDetails = (item) => {
  currentItem.value = Object.assign({}, item);
  isEditing.value = false;
  dialogVisible.value = true;
};

// 关闭弹出框
const handleClose = () => {
  dialogVisible.value = false;
};

// 编辑项
const editItem = () => {
  // 实现编辑逻辑
  isEditing.value = !isEditing.value;
};

// 确认操作
const confirm = async (currentItem) => {
  try {
    // 实现确认逻辑
    dialogVisible.value = false;
    const fileList = [];
    if (currentItem.resFileList.length > 0) {
      for (const item of currentItem.resFileList) {
        if (item.response === undefined || item.response === "undefined") {
          item.fileId = "";
          fileList.push(item);
        } else {
          // 追加文件
          const parm = {
            fileName: item.name,
            fileUrl: item.response.url,
            fileType: item.name.substring(item.name.lastIndexOf(".") + 1),
            fileSize: item.size,
            businessId: currentItem.applyId,
          };
          fileList.push(parm);
        }
      }
      const res = await addFile(fileList);
    } else {
      await deleteFile({ businessId: currentItem.applyId });
    }

    // const updateResult = await updateApply({
    //   ...currentItem,
    //   examineFileUrl: fileIdList ? fileIdList.join(',') : '',
    //   trialTime: formatDate(currentItem.trialTime)
    // });
    getInfo();
    if (res.code !== 200) {
      throw new Error("更新失败");
    }
  } catch (error) {
    console.error("确认过程中发生错误:", error);
  }
};

// 预览文件
const previewFile = (item) => {
  // 实现预览文件逻辑
  const url = item.fileUrl;
  const encodedUrl = Base64.encode(url);
  const previewUrl =
    import.meta.env.VITE_ONLINE_PREVIEW + encodeURIComponent(encodedUrl);
  window.open(previewUrl);
};
// 上传
const syncFile = async (file) => {
  const res = await OssService(file.file);
  return res;
};
// 处理文件数量超出限制的回调
const handleExceed = (files, fileList) => {
  ElNotification({
    title: "提示",
    message: "只能上传一个文件",
    type: "warning",
  });
};

// 上传接口
function upload(file) {
  return syncFile(file);
}
// 上传成功处理
function handleUploadSuccess(res, file) {
  // 如果上传成功
}
// 上传前校检格式和大小
function handleBeforeUpload(file) {
  const imageType = ["image/jpeg", "image/jpg", "image/png", "image/svg"];
  const isImage = imageType.includes(file.type);
  //检验文件格式
  if (!isImage) {
    proxy.$modal.msgError(`文件格式错误!`);
    return false;
  }
  return true;
}
// 定义 listApply 函数
const getInfo = async () => {
  try {
    const queryParams = ref({
      bookName: queryCriteria.value,
      isbn: queryCriteria.value,
      bookId: bookIdDecrypt.value ? bookIdDecrypt.value : null,
      status: status.value,
    });
    const response = await listApply(queryParams.value);
    items.value = response.data;
    // 函数：根据 activationTime 和 trialDays 计算试用期
    items.value.forEach((data) => {
      if (data.activationTime && data.trialDays) {
        // 判断是否存在激活时间和试用天数
        const activationDate = new Date(data.activationTime); // 将 activationTime 转为 Date 对象

        // 设置试用期开始时间为激活时间
        const startDate = new Date(activationDate);

        // 计算试用期结束时间：在激活时间基础上加上试用天数
        const endDate = new Date(activationDate);
        endDate.setDate(activationDate.getDate() + data.trialDays); // setDate 会自动处理跨月、跨年等情况

        // 格式化开始和结束时间为 "yyyy-MM-dd HH:mm" 格式
        const formattedStartDate = formatDate(startDate);
        const formattedEndDate = formatDate(endDate);

        // 生成试用期字符串
        data.trialPeriod = `${formattedStartDate}~${formattedEndDate}`;
      }
      // 查询文件信息
      listFile({ businessId: data.applyId }).then((response) => {
        response.rows.forEach((item) => {
          item.name = item.fileName;
          item.url = item.fileUrl;
        });
        data.resFileList = response.rows;
      });
    });
  } catch (error) {
    console.error("Error fetching data:", error);
  }
};
onMounted(async () => {
  // bookIdDecrypt.value = await decryptData({ encryptedData: bookId });
  bookIdDecrypt.value = bookId;
  await getInfo();
  bookIdDecrypt.value = null;
});
const handleSearch = () => {
  getInfo();
};
</script>

<style scoped lang="scss">
@import "@/assets/styles/index.scss";

.content-right-con {
  width: 1239px;
  min-height: 750px;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(182, 182, 182, 1);
  background-color: rgba(255, 255, 255, 1);
  margin-left: -90px;

  .right-top-con {
    width: 100%;
    border-bottom: 1px solid #e5e6e7;
    margin-bottom: 10px;
    @extend .base-flex-row;
    justify-content: space-between;
    align-items: center;

    .menu-con {
      margin-left: 57px;

      .el-menu-demo {
        width: 600px;
        border-bottom: none !important;
      }
    }

    .filter-con {
      margin-right: 61px;
    }
  }
}

.group_22 {
  background-color: rgba(255, 255, 255, 1);
  height: 156px;
  border: 1px solid rgba(229, 230, 231, 1);
  width: 1155px;
  position: relative;
  margin: 0px 0 0 41px;
}

.text-wrapper_5 {
  height: 50px;
  margin: 39px 0 0 131px;
}

.text_17 {
  width: 300px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  line-height: 70px;
}

.large-button {
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  margin-top: 5px;
}

.text_26 {
  overflow-wrap: break-word;
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  margin-top: 5px;
}

.text_25 {
  overflow-wrap: break-word;
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  margin: 15px;
}

.section_3 {
  width: 1087px;
  height: 50px;
  margin: 3px 0 14px 35px;
}

.text_18 {
  width: 300px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  line-height: 30px;
  margin-left: 96px;
}

.text_19_ah {
  width: 500px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 14px;
  font-weight: normal;
  text-align: end;
  line-height: 30px;
  margin-left: 157px;
}

.text-wrapper_7 {
  position: absolute;
  left: 131px;
  top: 14px;
  width: 991px;
  height: 50px;
}

.text_22 {
  width: 240px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 50px;
}

.text_23 {
  width: 42px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  line-height: 50px;
  margin-left: 100px;
}

.section_6 {
  border-radius: 2px;
  height: 109px;
  width: 81px;
  position: absolute;
  left: 32px;
  top: 22px;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.container {
  display: flex;
  flex-direction: column;
}

.status-unprocessed {
  color: red;
}

.status-processed {
  color: green;
}

.status-processing {
  color: blue;
}

.status-expired {
  color: gray;
}

.dialog-col {
  margin-top: 10px;
  margin-bottom: 10px;
}
</style>
