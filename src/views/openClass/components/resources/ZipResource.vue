<!-- 压缩包资源组件（类型6） -->
<template>
  <div class="resource-content-wrapper">
    <div class="zip-content">
      <div class="zip-info">
        <i class="el-icon-document-copy zip-icon"></i>
        <p>{{ resource.resourceContent || '压缩包资源' }}</p>
      </div>
      <el-button type="primary" size="small" @click="downloadZip">下载压缩包</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ZipResource',
  props: {
    resource: {
      type: Object,
      required: true
    }
  },
  methods: {
    downloadZip() {
      // 处理压缩包下载逻辑
      if (this.resource.resourceContent && this.resource.resourceContent.startsWith('http')) {
        window.open(this.resource.resourceContent, '_blank');
      } else {
        this.$message.warning('无法下载压缩包：资源链接无效');
      }
    }
  }
}
</script>

<style scoped>
.resource-content-wrapper {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fcfcfc;
  font-size: 14px;
  color: #606266;
  line-height: 1.7;
}

.zip-content {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.zip-info {
  display: flex;
  align-items: center;
}

.zip-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}
</style> 