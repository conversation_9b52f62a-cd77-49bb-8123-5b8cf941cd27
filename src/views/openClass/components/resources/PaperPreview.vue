<template>

      <div class="preview-container">
        <div class="paper-info">
          <h2 class="paper-title">{{ previewData.paperTitle }}</h2>
          <div class="paper-meta">
            <span v-if="paperType !== '2'">总分: {{ previewData.totalScore }}分</span>
            <el-divider v-if="paperType !== '2'" direction="vertical" />
            <span>题目数量: {{ previewData.questionQuantity }}道</span>
    
          </div>
        </div>
  
        <el-divider />
  
        <!-- 题目列表 -->
        <div class="questions-list">
          <div v-for="(collection, collectionIndex) in previewData.groupedQuestions" 
               :key="collectionIndex" 
               class="question-collection">
            <div class="collection-header">
              <h3>{{ getQuestionTypeName(collection.questionType) }}
                <template v-if="paperType !== '2'">
                  （共{{ collection.questions.length }}题，共{{ collection.totalScore }}分）
                </template>
                <template v-else>
                  （共{{ collection.questions.length }}题）
                </template>
              </h3>
            </div>
            <div v-for="(question, index) in collection.questions" 
                 :key="question.questionId" 
                 class="question-item">
              <div class="question-header">
                <span class="question-index">第{{ index+1 }}题</span>
                <span v-if="paperType !== '2'" class="question-score">({{ question.score || 0 }}分)</span>
              </div>
              <QuestionPreview :question="question" />
              <el-divider v-if="index !== collection.questions.length - 1" />
            </div>
            <el-divider v-if="collectionIndex !== Object.keys(previewData.groupedQuestions).length - 1" />
          </div>
        </div>
      </div>
  </template>
  
  <script setup>
  import { ref, computed, watch } from 'vue';
  import QuestionPreview from '@/components/QuestionPreview/index.vue';
  import { getPaper } from "@/api/edu/moocSmartCourseTestPaper";
  import { questionTypeOptions } from '@/utils/optionUtil.js';
  
  const questionTypeMap = questionTypeOptions.reduce((acc, item) => {
    acc[item.value] = item.label;
    return acc;
  }, {});
  
  const props = defineProps({
    paperId: {
      type: [String, Number],
      default: null
    },
    paperType: {
      type: String,
      default: '2'
    }
  });
  
  const emit = defineEmits(['update:modelValue']);
  
  const dialogVisible = ref(false);
  const previewData = ref({
    paperTitle: '',
    totalScore: 0,
    questionQuantity: 0,
    groupedQuestions: []
  });
  
  // 监听 paperId 变化，如果不是通过 v-model 控制，则直接加载数据
  watch(() => props.paperId, (newVal) => {
    // modelValue有明确定义时，说明由外部v-model控制显隐，听从v-model的watch
    if (props.modelValue === undefined && newVal) {
      loadPreviewData();
    }
  }, { immediate: true });
  
  // 计算标题
  const title = computed(() => {
    return props.paperType === '2' ? '作业预览' : '试卷预览';
  });
  
  // 监听 modelValue 变化
  watch(() => props.modelValue, (val) => {
    dialogVisible.value = val;
    if (val && props.paperId) {
      loadPreviewData();
    }
  });
  
  // 监听 dialogVisible 变化
  watch(() => dialogVisible.value, (val) => {
    emit('update:modelValue', val);
  });
  
  // 获取题型名称的方法
  const getQuestionTypeName = (type) => {
    return questionTypeMap[type] || '未知题型';
  };
  
  // 加载预览数据
  async function loadPreviewData() {
    try {
      const response = await getPaper(props.paperId);
      console.log('paperData', response);
      const paperData = response.data;


      const questionsData = paperData.moocSmartCourseTestPaperQuestionCollectionList || [];
  
      const groupedQuestions = {};
  
      questionsData.forEach(collection => {
        collection.questionList.forEach(item => {
          const question = item.questionContent;
          // 使用题目自身的 questionType 进行分组
          const actualQuestionType = question.questionType;

          if (!groupedQuestions[actualQuestionType]) {
            groupedQuestions[actualQuestionType] = {
              questionType: actualQuestionType,
              questions: [],
              totalScore: 0,
              // 使用原集合的 sort 值作为新分组的排序依据，以保证大题顺序
              sort: collection.sort || 0
            };
          }
          
          const score = Number(item.questionScore || 0);
          groupedQuestions[actualQuestionType].questions.push({
            ...question,
            sort: item.sort, // 使用题目自己的 sort
            score: score
          });
          groupedQuestions[actualQuestionType].totalScore += score;
        });
      });
  
      // 将对象转换为数组，然后按题型集合的sort属性排序
      const sortedGroupedQuestions = Object.values(groupedQuestions).sort((a, b) => (a.sort || 0) - (b.sort || 0));
  
      // 更新预览数据
      previewData.value = {
        paperTitle: paperData.paperTitle,
        totalScore: paperData.totalScore,
        questionQuantity: paperData.questionQuantity,
        groupedQuestions: sortedGroupedQuestions
      };
    } catch (error) {
      console.error('获取试卷预览数据失败:', error);
      // 使用 proxy.$modal 可能会报错，因为这是一个组件
      // 可以通过 emit 一个事件让父组件处理错误
      emit('load-error', '获取预览数据失败');
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .preview-container {
    height: 100%;
    padding: 20px;
    overflow-y: auto;
    width: 100%;
    display: flex;
    flex-direction: column;
  
    .paper-info {
      text-align: center;
      margin-bottom: 20px;
  
      .paper-title {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin: 0 0 15px 0;
      }
  
      .paper-meta {
        color: #606266;
        font-size: 14px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
  
        .el-divider--vertical {
          display: none;
        }
      }
    }
  
    .questions-list {
      width: 100%;
      display: flex;
      flex-direction: column;
      
      .question-collection {
        background-color: #fff;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 24px;
  
        .collection-header {
          padding: 16px 0;
          margin-bottom: 20px;
  
          h3 {
            color: #333;
            font-size: 18px;
            font-weight: 600;
            text-align: left;
            margin: 0;
          }
        }
  
        .question-item {
          background-color: #fafafa;
          border-radius: 6px;
          padding: 16px;
          margin-bottom: 16px;
  
          .question-header {
            flex-direction: row;
            align-items: center;
            margin-bottom: 16px;
            border-bottom: 1px solid #e8e8e8;
            padding-bottom: 12px;
  
            .question-index {
              font-size: 16px;
              color: #1890ff;
              margin-right: 12px;
            }
  
            .question-score {
              color: #ff4d4f;
              font-weight: 500;
            }
          }
        }
      }
    }
  
    .paper-info {
      .paper-meta {
        flex-direction: row;
        justify-content: center;
        gap: 24px;
  
        .el-divider--vertical {
          display: inline-block;
          height: 16px;
          margin: 0;
        }
  
        span {
          font-size: 15px;
          color: #1890ff;
          font-weight: 500;
        }
      }
    }
  }
  
  // 自定义滚动条样式
  .preview-container {
    &::-webkit-scrollbar {
      width: 6px;
    }
  
    &::-webkit-scrollbar-thumb {
      background-color: #909399;
      border-radius: 3px;
    }
  
    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }
  
  // 添加 dialog 相关样式
  :deep(.el-dialog) {
    margin-top: 5vh !important;
    height: 90vh;
    display: flex;
    flex-direction: column;
  
    .el-dialog__body {
      flex: 1;
      overflow: hidden;
      padding: 0;
    }
  }
  
  // 为富文本中的代码块添加黑框样式
  :deep(pre) {
    background-color: #1e1e1e;
    color: #d4d4d4;
    border-radius: 4px;
    padding: 16px;
    margin: 12px 0;
    overflow-x: auto;
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
    line-height: 1.5;
    border: 1px solid #333;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  </style> 