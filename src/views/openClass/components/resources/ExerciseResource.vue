<!-- 题库资源组件（类型5） -->
<template>
  <div class="resource-content-wrapper">
    <div class="homework-content">
    
      <!-- 题目预览部分 -->
      <div class="question-display">
        <QuestionPreview 
          v-if="resource.userQuestion"
          :question="resource.userQuestion"
          :showAnswerButton="false"
          :key="`question-${resource.userQuestion.questionId}`"
        />
        <div v-else class="empty-placeholder">
          暂无题目数据
        </div>
      </div>
      
    </div>
  </div>
</template>

<script>
import QuestionPreview from '@/components/QuestionPreview/index.vue'

export default {
  name: 'ExerciseResource',
  components: {
    QuestionPreview
  },
  props: {
    resource: {
      type: Object,
      required: true
    }
  },

  created() {
    // 如果resource中包含question数据，则进行处理
    if (this.resource.questionData) {
      this.question = this.parseQuestionData(this.resource.questionData)
    }
  },
  methods: {
    // 解析题目数据
    parseQuestionData(data) {
      try {
        // 如果数据是字符串，尝试解析为JSON
        if (typeof data === 'string') {
          data = JSON.parse(data)
        }
        
        // 处理编程题的代码内容
        if (data.questionType === 8) {
          try {
            data.code = this.getCodeContent(data.codeContent) || ''
            data.language = this.getProgrammingLanguage(data.codeContent) || 'javascript'
          } catch (e) {
            console.error('解析编程题内容失败:', e)
            data.code = ''
            data.language = 'javascript'
          }
        }
        
        return data
      } catch (e) {
        console.error('解析题目数据失败:', e)
        return null
      }
    },
    
    // 解析编程题内容的辅助函数
    getCodeContent(questionContent) {
      try {
        const codeData = JSON.parse(this.safeDecode(questionContent || '{}'))
        return codeData.code || ''
      } catch (e) {
        console.error('解析代码内容失败:', e)
        return ''
      }
    },
    
    getProgrammingLanguage(questionContent) {
      try {
        const codeData = JSON.parse(this.safeDecode(questionContent || '{}'))
        return codeData.language || 'javascript'
      } catch (e) {
        console.error('解析编程语言失败:', e)
        return 'javascript'
      }
    },
    
    // 安全解码函数
    safeDecode(content) {
      if (!content) return ''
      try {
        // 检查内容是否已经被编码
        const isEncoded = (str) => {
          try {
            // 尝试解码，如果解码后的结果与原字符串不同，说明是编码过的
            const decoded = decodeURIComponent(str)
            return decoded !== str
          } catch (e) {
            // 如果解码失败，说明可能包含特殊字符，此时返回 false
            return false
          }
        }

        // 如果内容已经被编码，则进行解码
        if (isEncoded(content)) {
          return decodeURIComponent(content)
        }
        
        // 如果内容未被编码或解码失败，直接返回原内容
        return content
      } catch (e) {
        console.error('解码内容失败:', e)
        return content
      }
    }
  }
}
</script>

<style scoped>
.resource-content-wrapper {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fcfcfc;
  font-size: 14px;
  color: #606266;
  line-height: 1.7;
}

.homework-content {
  padding: 10px;
}

.question-display {
  margin: 15px 0;
  padding: 10px;
  border-radius: 4px;
  background-color: #fff;
}

.empty-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
  color: #909399;
  font-size: 14px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin: 10px 0;
}

.homework-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}
</style> 