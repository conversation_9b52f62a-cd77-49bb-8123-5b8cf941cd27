<!-- 资源渲染器组件 -->
<template>
  <div class="resource-container">
    <!-- 根据资源类型条件显示header -->
    <div class="resource-header" v-if="!NO_HEADER_TYPES.includes(resource.resourceType)">
      <span class="resource-type-label">{{ resourceTypeLabel }}</span>
      <div class="resource-title">{{ resource.resourceTitle || '无标题资源' }}</div>
      <!-- 对于类型2资源，显示"打开"按钮 -->
      <el-button 
        v-if="resource.resourceType === '2'" 
        type="primary" 
        @click="previewResource" 
        class="preview-resource-btn"
      >
        打开
        <i class="el-icon-view"></i>
      </el-button>
      <!-- 对于类型6资源，显示"下载"按钮 -->
      <el-button 
        v-else-if="resource.resourceType === '6'" 
        type="primary" 
        @click="downloadZip" 
        class="download-resource-btn"
      >
        下载
        <i class="el-icon-download"></i>
      </el-button>
      <!-- 对于其他非常驻展开类型的资源，显示收起/展开按钮 -->
      <el-button 
        v-else-if="!ALWAYS_EXPANDED_TYPES.includes(resource.resourceType)" 
        type="primary" 
        @click="toggleExpand" 
        class="expand-resource-btn"
      >
        {{ isExpanded ? '收起' : '展开' }}
        <i :class="isExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
      </el-button>
    </div>

    <!-- 视频资源和虚拟仿真始终展开 -->
    <component 
      v-if="ALWAYS_EXPANDED_TYPES.includes(resource.resourceType)" 
      :is="resourceComponent"
      :resource="resource"
      :videoProgress="videoProgress"
    ></component>

    <!-- 其他类型资源 - 可折叠内容 -->
    <el-collapse-transition v-else>
      <component 
        v-show="isExpanded" 
        :is="resourceComponent"
        :resource="resource"
      ></component>
    </el-collapse-transition>
  </div>
</template>

<script>
import { ResourceTypeMap, ResourceComponentMap } from './index';
import usePreview from '@/store/modules/preview.js';
import { getPreviewFileUrl } from '@/utils/index.js';

// 始终展开的资源类型列表
const ALWAYS_EXPANDED_TYPES = ['1','3','4', '7'];
// 不需要显示header的资源类型列表
const NO_HEADER_TYPES = ['1','4','7'];

export default {
  name: 'ResourceRenderer',
  components: {},
  props: {
    resource: {
      type: Object,
      required: true
    },
    videoProgress: {
      type: Number,
      default: 0
    },
    defaultExpanded: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const previewStore = usePreview();
    return {
      previewStore
    };
  },
  data() {
    return {
      isExpanded: this.defaultExpanded,
      ALWAYS_EXPANDED_TYPES,
      NO_HEADER_TYPES,
    };
  },
  computed: {
    resourceTypeLabel() {
      return ResourceTypeMap[this.resource.resourceType] || '未知类型';
    },
    resourceComponent() {
      return ResourceComponentMap[this.resource.resourceType] || null;
    }
  },
  methods: {
    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    },
    previewResource() {
      // 使用弹窗预览资源文件
      const url = this.resource.resourceContent;
      if (url) {
        this.previewStore.setData({
          url: url,
          title: this.resource.resourceTitle || '资源预览'
        });
      } else {
        this.$message.warning('资源链接不可用');
      }
    },
    downloadZip() {
      // 下载压缩包资源
      const url = this.resource.resourceContent;
      if (url && url.startsWith('http')) {
        window.open(url, '_blank');
      } else {
        this.$message.warning('资源链接不可用');
      }
    }
  }
};
</script>

<style scoped>
.resource-container {
  width: 100%;
  background: #FFFFFF;
  border-radius: 10px;
  padding: 20px;
  box-sizing: border-box;
  margin-bottom: 0px;
}

.resource-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  width: 100%;
}

.resource-type-label {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  background-color: #409EFF;
  padding: 2px 10px;
  border-radius: 12px;
  margin-right: 10px;
}

.resource-title {
  flex-grow: 1;
  padding: 8px 12px;
  background-color: #e9f4ff;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  margin-right: 15px;
  min-height: 36px;
  display: flex;
  align-items: center;
}

.expand-resource-btn,
.preview-resource-btn,
.download-resource-btn {
  min-width: 100px;
}

.expand-resource-btn i,
.preview-resource-btn i,
.download-resource-btn i {
  margin-left: 5px;
}
</style> 