<!-- 普通资源组件（类型2） -->
<template>
  <div class="resource-content-wrapper">
    <div class="resource-download">
      <p>{{ resource.resourceContent }}</p>
      <el-button type="primary" size="small" v-if="hasDownloadableContent">下载资源</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FileResource',
  props: {
    resource: {
      type: Object,
      required: true
    }
  },
  computed: {
    hasDownloadableContent() {
      return this.resource.resourceContent && this.resource.resourceContent.trim() !== '';
    }
  }
}
</script>

<style scoped>
.resource-content-wrapper {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fcfcfc;
  font-size: 14px;
  color: #606266;
  line-height: 1.7;
}

.resource-download {
  padding: 10px;
}
</style> 