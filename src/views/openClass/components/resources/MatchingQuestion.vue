<template>
    <div class="matching-container" ref="containerRef">
      <!-- 左侧选项 -->
      <div class="matching-left">
        <div v-for="leftOption in leftOptions" 
             :key="'left-' + leftOption.optionId"
             class="option-item"
             :id="'left-' + leftOption.optionId"
             @click="selectOption('left', leftOption)"
             :class="{'selected': leftOption.selected}">
          <div v-html="leftOption.optionContent" class="option-content"/>
        </div>
      </div>
  
      <!-- 右侧选项 -->
      <div class="matching-right">
        <div v-for="rightOption in rightOptions" 
             :key="'right-' + rightOption.optionId"
             class="option-item"
             :id="'right-' + rightOption.optionId"
             @click="selectOption('right', rightOption)"
             :class="{'selected': rightOption.selected}">
          <div v-html="rightOption.optionContent" class="option-content"/>
        </div>
      </div>
  
      <!-- 连线 SVG -->
      <svg class="lines-svg">
          <!-- 用户绘制的线 -->
          <g v-for="(line, index) in lines"
             :key="'line-group-' + index"
             @click="deleteLine(index)"
             class="line-group">
            <line
                :x1="line.x1" :y1="line.y1"
                :x2="line.x2" :y2="line.y2"
                class="line-hitbox"
            />
            <line
                :x1="line.x1" :y1="line.y1"
                :x2="line.x2" :y2="line.y2"
                :class="getLineClass(line)"
            />
          </g>
          <!-- 正确答案的线 -->
          <line v-for="(line, index) in answerKeyLines"
                :key="'answer-line-' + index"
                :x1="line.x1" :y1="line.y1"
                :x2="line.x2" :y2="line.y2"
                class="answer-key-line"
          />
      </svg>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
  
  const props = defineProps({
    options: {
      type: Array,
      required: true
    },
    rightAnswer: {
      type: String,
      required: true
    },
    showAnswer: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  });
  
  const emit = defineEmits(['update:userMatches']);
  
  const containerRef = ref(null);
  const leftOptions = ref([]);
  const rightOptions = ref([]);
  const lines = ref([]); // { x1, y1, x2, y2, leftId, rightId, correct: null | boolean }
  const answerKeyLines = ref([]); // 新增：用于存放正确答案的线
  const selectedLeft = ref(null);
  const selectedRight = ref(null);
  
  // #region --- Initialization and Cleanup ---
  
  onMounted(() => {
    // 分离并初始化选项
    const initOptions = (opts) => opts.map(opt => ({ ...opt, selected: false, sort: opt.sort || 0 })).sort((a, b) => a.sort - b.sort);
    leftOptions.value = initOptions(props.options.filter(opt => opt.optionPosition === 1));
    rightOptions.value = initOptions(props.options.filter(opt => opt.optionPosition === 2));
    
    const resizeObserver = new ResizeObserver(() => {
      updateAllLines();
    });
    if (containerRef.value) {
      resizeObserver.observe(containerRef.value);
    }
    
    onUnmounted(() => {
      resizeObserver.disconnect();
    });
  });
  
  // #endregion
  
  // #region --- Drawing and Selection Logic ---
  
  const selectOption = (side, option) => {
    if (props.showAnswer || props.disabled) return;
  
    if (side === 'left') {
      if (selectedLeft.value?.optionId === option.optionId) {
        selectedLeft.value = null;
        option.selected = false;
        return;
      }
      leftOptions.value.forEach(o => o.selected = false);
      option.selected = true;
      selectedLeft.value = option;
    } else { // side === 'right'
      if (selectedRight.value?.optionId === option.optionId) {
        selectedRight.value = null;
        option.selected = false;
        return;
      }
      rightOptions.value.forEach(o => o.selected = false);
      option.selected = true;
      selectedRight.value = option;
    }
  
    if (selectedLeft.value && selectedRight.value) {
      drawLine();
    }
  };
  
  const drawLine = () => {
      const startId = selectedLeft.value.optionId;
      const endId = selectedRight.value.optionId;
  
      // 检查完全相同的线是否已存在，防止重复
      const lineExists = lines.value.some(line => line.leftId === startId && line.rightId === endId);
      if (lineExists) {
        // 重置选项，不做任何操作
        selectedLeft.value.selected = false;
        selectedRight.value.selected = false;
        selectedLeft.value = null;
        selectedRight.value = null;
        return;
      }
  
      const startElem = document.getElementById('left-' + startId);
      const endElem = document.getElementById('right-' + endId);
      const startCoords = getElementCoords(startElem);
      const endCoords = getElementCoords(endElem);
  
      lines.value.push({
          x1: startCoords.right, y1: startCoords.centerY,
          x2: endCoords.left,  y2: endCoords.centerY,
          leftId: startId,     rightId: endId,
          correct: null // 初始状态为null，表示未判断
      });
  
      // 重置选项
      selectedLeft.value.selected = false;
      selectedRight.value.selected = false;
      selectedLeft.value = null;
      selectedRight.value = null;
      
      emitUserMatches();
  };
  
  const deleteLine = (index) => {
      if (props.showAnswer || props.disabled) return;
      lines.value.splice(index, 1);
      emitUserMatches();
  };
  
  const getElementCoords = (element) => {
    if (!element || !containerRef.value) return null;
    const containerRect = containerRef.value.getBoundingClientRect();
    const elemRect = element.getBoundingClientRect();
    return {
      left: elemRect.left - containerRect.left,
      right: elemRect.right - containerRect.left,
      top: elemRect.top - containerRect.top,
      centerY: (elemRect.top - containerRect.top) + elemRect.height / 2
    };
  };
  
  const emitUserMatches = () => {
      const userMatches = lines.value.map(line => ({
          source: leftOptions.value.findIndex(o => o.optionId === line.leftId),
          target: rightOptions.value.findIndex(o => o.optionId === line.rightId)
      }));
      emit('update:userMatches', userMatches);
  };
  
  // #endregion
  
  // #region --- Line and Answer Management ---
  
  const updateAllLines = async () => {
      await nextTick();
      const newLines = [];
      lines.value.forEach(line => {
          const leftElement = document.getElementById('left-' + line.leftId);
          const rightElement = document.getElementById('right-' + line.rightId);
          if (leftElement && rightElement) {
              const startCoords = getElementCoords(leftElement);
              const endCoords = getElementCoords(rightElement);
              newLines.push({
                  ...line,
                  x1: startCoords.right, y1: startCoords.centerY,
                  x2: endCoords.left, y2: endCoords.centerY
              });
          }
      });
      lines.value = newLines;

      // 同时更新答案线
      if (props.showAnswer) {
          updateAnswerKeyLines();
      }
  };
  
  const updateAnswerKeyLines = () => {
      const newAnswerKeyLines = [];
      const answers = JSON.parse(props.rightAnswer);
      answers.forEach(answer => {
          const leftOption = leftOptions.value[answer.source];
          const rightOption = rightOptions.value[answer.target];
          if (leftOption && rightOption) {
              const leftElement = document.getElementById('left-' + leftOption.optionId);
              const rightElement = document.getElementById('right-' + rightOption.optionId);
              if (leftElement && rightElement) {
                  const startCoords = getElementCoords(leftElement);
                  const endCoords = getElementCoords(rightElement);
                  newAnswerKeyLines.push({
                      x1: startCoords.right, y1: startCoords.centerY,
                      x2: endCoords.left, y2: endCoords.centerY
                  });
              }
          }
      });
      answerKeyLines.value = newAnswerKeyLines;
  }
  
  watch(() => props.showAnswer, (newVal) => {
      if (newVal) {
          // 禁用所有选择
          selectedLeft.value = null;
          selectedRight.value = null;
          leftOptions.value.forEach(o => o.selected = false);
          rightOptions.value.forEach(o => o.selected = false);
  
          // 判断用户连线的对错
          const answers = JSON.parse(props.rightAnswer);
          lines.value.forEach(line => {
              const leftIndex = leftOptions.value.findIndex(o => o.optionId === line.leftId);
              const rightIndex = rightOptions.value.findIndex(o => o.optionId === line.rightId);
              line.correct = answers.some(a => a.source === leftIndex && a.target === rightIndex);
          });
          
          // 显示正确答案线
          updateAnswerKeyLines();
      } else {
          // 重置用户线的对错状态
          lines.value.forEach(line => line.correct = null);
          // 清空答案线
          answerKeyLines.value = [];
      }
  });
  
  const getLineClass = (line) => {
      if (line.correct === true) return 'drawn-line correct';
      if (line.correct === false) return 'drawn-line incorrect';
      return 'drawn-line user-line';
  };
  
  // #endregion
  </script>
  
  <style scoped>
  .matching-container {
    display: flex;
    justify-content: space-between;
    position: relative;
    padding: 20px;
    min-height: 200px;
    user-select: none;
  }
  
  .matching-left, .matching-right {
    width: 45%;
    position: relative;
    z-index: 1;
  }
  
  .option-item {
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
    background: #FFF;
    transition: all 0.3s;
    cursor: pointer;
  }
  
  .option-item:hover {
    border-color: #409EFF;
  }
  
  .option-item.selected {
    border-color: #409EFF;
    box-shadow: 0 0 5px rgba(64, 158, 255, 0.5);
  }
  
  .lines-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
  }
  
  .line-group {
    pointer-events: auto; /* 使线可以被点击 */
    cursor: pointer;
  }
  
  .line-hitbox {
    stroke: transparent;
    stroke-width: 10;
  }
  
  .drawn-line {
    stroke-width: 2.5;
    transition: stroke 0.3s;
    pointer-events: none;
  }
  
  .user-line {
    stroke: #409EFF;
  }
  
  .correct {
    stroke: #67C23A;
  }
  
  .incorrect {
    stroke: #F56C6C;
  }
  
  .answer-key-line {
    stroke: #67C23A; /* 正确答案的绿色 */
    stroke-width: 2;
    stroke-dasharray: 6, 6; /* 虚线样式 */
    pointer-events: none; /* 答案线不可交互 */
  }
  
  .option-content :deep(img) {
    max-width: 85%;
    height: auto;
    object-fit: contain;
    display: block;
    margin: 0 auto;
  }
  </style>