<!-- 图文资源组件（类型1） -->
<template>
  <div class="resource-content-wrapper">
    <div class="text-content">
      <p v-if="isRichText" v-html="resource.resourceContent"></p>
      <p v-else>{{ resource.resourceContent }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TextResource',
  props: {
    resource: {
      type: Object,
      required: true
    }
  },
  computed: {
    isRichText() {
      // 检查内容是否包含闭合的HTML标签
      const content = this.resource.resourceContent || '';
      // 匹配常见的闭合标签或自闭合标签
      return /<([a-z]+)(?:\s[^>]*)?>(.*?)<\/\1>|<([a-z]+)(?:\s*)[^>]*\/>/i.test(content);
    }
  }
}
</script>

<style scoped>
.resource-content-wrapper {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fcfcfc;
  font-size: 14px;
  color: #606266;
  line-height: 1.7;
}

.text-content {
  padding: 10px;
}
</style> 