<!-- 虚拟仿真资源组件（类型3） -->
<template>
  <div 
    class="virtual-simulation-container"
    @click="previewResource"
  >
    <div class="simulation-wrapper" style="cursor: pointer;">
      <iframe 
        v-if="simulationUrl" 
        :src="simulationUrl" 
        class="simulation-frame" 
        allowfullscreen
      ></iframe>
      <div v-else class="simulation-placeholder">
        虚拟仿真内容加载中或未提供
      </div>
    </div>


    <!-- 预览弹窗 -->
    <el-dialog
      :title="resource.title || '虚拟仿真资源预览'"
      :visible.sync="previewDialogVisible"
      width="900px"
    >
      <div style="height: 600px; width: 100%;">
        <iframe 
          :src="simulationUrl" 
          class="preview-frame" 
          allowfullscreen
        ></iframe>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'VirtualSimulationResource',
  props: {
    resource: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      previewDialogVisible: false
    }
  },
  computed: {
    simulationUrl() {
      // 处理仿真URL，确保有效
      if (this.resource.resourceContent && this.resource.resourceContent.startsWith('http')) {
        return this.resource.resourceContent;
      }
      return '';
    }
  },
  methods: {
    previewResource() {
      if (!this.simulationUrl) {
        this.$message.error('无效的仿真资源链接');
        return;
      }
      this.previewDialogVisible = true;
    }
  }
}
</script>

<style scoped>
.virtual-simulation-container {
  width: 100%;
  position: relative;
}

.simulation-wrapper {
  width: 100%;
  height: 506px;
  background: rgba(0,0,0,0.1);
  overflow: hidden;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.simulation-frame, .preview-frame {
  width: 100%;
  height: 100%;
  border: none;
}

.simulation-placeholder {
  font-size: 16px;
  color: #909399;
}

.simulation-meta {
  font-size: 14px;
  color: #606266;
  padding: 10px 0;
}
</style> 