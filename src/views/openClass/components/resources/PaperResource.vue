<template>
  <div class="paper-resource-content">
    <div v-if="!resource || !resource.resourceContent" class="loading-container">试卷信息不可用</div>
    <paper-preview v-else :paper-id="resource.resourceContent" />
  </div>
</template>

<script>
import PaperPreview from './PaperPreview.vue';

export default {
  name: 'PaperResource',
  components: {
    PaperPreview
  },
  props: {
    resource: {
      type: Object,
      required: true
    }
  }
};
</script>

<style scoped>
.paper-resource-content {
  width: 100%;
  padding: 10px 0;
  box-sizing: border-box;
}

.loading-container {
  padding: 40px 20px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}
</style> 