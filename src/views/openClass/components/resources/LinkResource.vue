<!-- 链接资源组件（类型4） -->
<template>
   <div class="link-content">
      <el-link :href="resource.resourceContent" underline="always"  type="primary">
        {{ resource.resourceTitle }}
        <el-icon style="margin-left: 5px;"><Link /></el-icon>
      </el-link>
    </div>
</template>

<script>
import { Link } from '@element-plus/icons-vue'

export default {
  name: 'LinkResource',
  components: {
    Link
  },
  props: {
    resource: {
      type: Object,
      required: true
    }
  },
  methods: {
    openExternalLink() {
      // 提取链接内容
      const url = this.resource.resourceContent.startsWith('http') 
        ? this.resource.resourceContent 
        : `https://${this.resource.resourceContent}`;
      
      window.open(url, '_blank');
    }
  }
}
</script>

<style scoped>

</style> 