<!-- 视频资源组件（类型7） -->
<template>
  <div class="video-container">
    <div class="video-wrapper">
      <video 
        controls 
        width="100%" 
        :src="videoUrl" 
        class="main-video-player" 
        poster="https://via.placeholder.com/800x450.png?text=Video+Player"
      >
        您的浏览器不支持视频标签
      </video>
    </div>
    <div class="video-meta">
      <p>本视频学习进度：{{ videoProgress }}%</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VideoResource',
  props: {
    resource: {
      type: Object,
      required: true
    },
    videoProgress: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      defaultVideoPlaceholder: 'https://via.placeholder.com/800x450.png?text=Video+Not+Available'
    }
  },
  computed: {
    videoUrl() {
      if (this.resource.resourceType === '7' && this.resource.resourceContent) {
        return this.resource.resourceContent.startsWith('http') 
          ? this.resource.resourceContent 
          : 'https://www.w3schools.com/html/mov_bbb.mp4'; // 默认视频
      }
      return this.defaultVideoPlaceholder;
    }
  }
}
</script>

<style scoped>
.video-container {
  width: 100%;
}

.video-wrapper {
  width: 100%;
  height: 506px;
  background: rgba(0,0,0,0.2);
  overflow: hidden;
  margin-bottom: 15px;
}

.main-video-player {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-meta {
  font-size: 14px;
  color: #606266;
}
</style> 