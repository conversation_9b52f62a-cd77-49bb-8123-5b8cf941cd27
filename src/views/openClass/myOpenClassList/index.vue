<template>
  <div class="filters">
    <div class="filter-content">
      <el-breadcrumb :separator-icon="ArrowRight" class="breadcrumb_text">
        <el-breadcrumb-item :to="{ path: '/open-course-list' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>我的公开课</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
  <div class="page-background">
    <el-card class="app-container">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick" :animated="false" ref="myTabs">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="即将开课" name="upcoming"></el-tab-pane>
        <el-tab-pane label="开课中" name="ongoing"></el-tab-pane>
        <el-tab-pane label="已结束" name="finished"></el-tab-pane>

        <div class="course-list" ref="courseListContainer">
          <el-card v-for="course in filteredCourses" :key="course.courseId" shadow="never">
            <el-row :span="7" :gutter="28" style="margin-top: 5px;">
              <el-col class="course-image-col">
                <img :src="course.courseCover || defaultImage" class="course-image" alt="course_cover">
              </el-col>
              <el-col :span="14"  class="course-details-col">
                <div class="course-details">
                  <h3>
                    {{ course.courseName }}
                    <el-tag size="small" :type="getStatusType(course.courseStatus)" style="margin-left: 10px;">
                      {{ formatStatus(course.courseStatus) }}
                    </el-tag>
                  </h3>
                  <p class="course-description">{{ course.courseDescription }}</p>
                  <p class="course-meta">
                    教学团队：{{ course?.teammates?.map(item => item.name).join(', ') }} &nbsp;&nbsp;&nbsp;&nbsp;
                    学习人数：{{ course.learnersNum||0 }}人
                  </p>
                </div>
              </el-col>
              <el-col :span="2" :offset="1"  class="action-col" >
                <el-button 
                  type="primary" 
                  @click="startLearning(course)" 
                  :disabled="course.courseStatus !== '1'"
                  v-if="course.courseStatus === '1'">开始学习</el-button>
                <el-button 
                  type="primary" 
                  @click="startLearning(course)" 
                  v-else-if="course.courseStatus === '0'">进入课程</el-button>
                <el-button 
                  type="info" 
                  disabled 
                  v-else>{{ getButtonText(course.courseStatus) }}</el-button>
              </el-col>
            </el-row>
          </el-card>
           <el-empty v-if="filteredCourses.length === 0" description="暂无课程"></el-empty>
        </div>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>

import { getMyOpenCourseList } from '@/api/edu/openCourse.js'; // 导入API函数
import { ArrowRight } from "@element-plus/icons-vue";

export default {
  name: 'MyOpenClassList',
  components: {
    // OpenClassLayout // 更新注册的组件名称
  },

  data() {
    return {
      activeTab: 'all', // 将 activeTab 重新添加到 data 中
      defaultImage: 'https://via.placeholder.com/300x180.png?text=Course+Image', // 默认图片
      courses: [], // 初始化为空数组，将从API获取
      loading: false, // 加载状态
    };
  },
  computed: {

   filteredCourses() {
     return this.courses;
   }
  },
  methods: {
    handleTabClick(tab) {
    

      this.$nextTick(() => {
        this.fetchCourses(); // 切换tab时重新获取数据
        if (this.$refs.myTabs && this.$refs.myTabs.$el && this.$refs.myTabs.$el.parentElement) {
          const parentWidth = this.$refs.myTabs.$el.parentElement.clientWidth;
          if (parentWidth > 0) { //确保获取到的宽度有效
               this.$refs.myTabs.$el.style.width = parentWidth + 'px';
               // 可选：强制重绘内部的course-list，如果el-tabs宽度正确后，内部列表仍有问题
               if (this.$refs.courseListContainer) {
                 this.$refs.courseListContainer.style.width = '100%'; // 确保它重新计算
               }
          }
        }
      });
    },
    getStatusType(status) {
      // 0未开始 1进行中 2已结束
      switch (status) {
        case '0': return 'warning'; // 即将开课 - 黄色
        case '1': return 'success'; // 开课中 - 绿色
        case '2': return 'info';    // 已结束 - 灰色
        default: return 'info';
      }
    },
    formatStatus(status) {
      // 0未开始 1进行中 2已结束
      switch (status) {
        case '0': return '即将开课';
        case '1': return '开课中';
        case '2': return '已结束';
        default: return '未知';
      }
    },
    getButtonText(status) {
      switch (status) {
        case '0': return '进入课程';
        case '2': return '已结束';
        default: return '开始学习';
      }
    },
    startLearning(course) {
      this.$router.push({
        path: 'learn-open-class',
        query: {
          courseId: course.courseId
        }
      });
    },
    // 获取课程列表数据
    fetchCourses() {
      this.loading = true;
      const params = {}; // 初始化一个空对象来存放参数
      // course_status: 0未开始(upcoming) 1进行中(ongoing) 2已结束(finished)
      const statusMapToParam = {
        upcoming: '0',
        ongoing: '1',
        finished: '2',
      };

    
      if (this.activeTab !== 'all') {
        params.courseStatus = statusMapToParam[this.activeTab];
      }

      getMyOpenCourseList(params) // 传递构建好的参数对象
        .then(response => {
          if (response && response.rows) {
            this.courses = response.rows.map(course => ({
              courseId: course.courseId,
              courseName: course.courseName,
              courseCover: course.courseCover,
              courseDescription: course.courseDescription,
              courseStatus: course.courseStatus,
              teachers: course.teacherTeamNames || [], // 确保是数组
              studentCount: course.learnersNum || 0, // 确保是数字
              ...course
            }));
          } else {
            this.courses = []; // 清空或显示错误提示
            this.$message.error('获取课程列表失败: 数据格式不正确');
          }
          this.loading = false;
        })
        .catch(error => {
          console.error("获取课程列表失败:", error);
          this.courses = []; // 清空课程
          this.loading = false;
          this.$message.error('获取课程列表失败，请稍后再试');
        });
    }
  },
  created() {
     this.fetchCourses(); // 组件创建时获取数据 (默认为 'all')
  }
};
</script>

<style scoped>
.page-background {
  background-color: #f0f2f5;
  min-height: 100vh;
  padding: 20px;
}

.filters {
  margin-bottom: 10px;
  margin-top: 20px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-sizing: border-box;
}

.breadcrumb_text {
  font-size: 14px;
  font-weight: 400;
  line-height: 40px;
}

/* .app-container 样式中的 max-width, margin: 0 auto, background, border-radius, padding 等
   可能需要调整或移除，因为这些现在可能由 OpenClassLayout 控制或与之冲突。
   例如，如果 OpenClassLayout 提供了背景色，这里的背景色可能就不需要了。
   如果 OpenClassLayout 的 .auto-com-con 已经处理了内容的宽度和居中，
   这里的 max-width 和 margin: 0 auto 也许可以移除。
*/
.app-container {
  max-width: 1400px; /* 保持内容区域的最大宽度，或者根据OpenClassLayout调整 */
  min-height: 1077px; /* 根据内容自适应更好，但按要求设置 */
  margin: 0 auto; /* 如果OpenClassLayout的auto-com-con已居中，则可移除 */
  background: #FFFFFF; /* 如果希望内容区域有独立背景，则保留 */
  border-radius: 10px; /* 如果希望内容区域有圆角，则保留 */
  padding: 20px; 
  box-sizing: border-box;
  width: 100%; /* 确保在 OpenClassLayout 的 slot 中占据可用宽度 */

}

/* 可以添加一个样式来定位返回按钮，如果上面的行内样式不够灵活 */
/*
.back-button {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}
*/

::v-deep .el-tabs__item {
  width: 112px;
  height: 36px !important; /* 覆盖element-ui默认高度 */
  line-height: 36px !important; /* 垂直居中文本 */
  text-align: center;
  background: #FFFFFF;
  border-radius: 4px !important;
  border: 1px solid #7E88A5;
  margin-right: 10px !important; /* tab之间的间距 */
  font-size: 14px; /* 调整字体大小以适应按钮 */
  padding: 0 0px;
  color: #7E88A5;
}

::v-deep .el-tabs__item.is-active {
  background: #555B74 !important;
  color: #FFFFFF !important;
  border: 1px solid #555B74 !important;
}

::v-deep .el-tabs__active-bar {
  display: none; /* 通常自定义tab时会隐藏默认的活动指示条 */
}

::v-deep .el-tabs__nav-wrap::after {
  display: none;
}



.course-list {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px; /* 卡片之间的间距 */
  max-height: 981px; /* Calculated based on app-container min-height and surrounding elements */
  overflow-y: auto; /* Enable vertical scroll when content overflows */
  padding-right: 5px; /* Add a little padding to prevent content from touching the scrollbar */
  width: 100%; /* 更正：明确设置宽度 */
  box-sizing: border-box; /* 新增：确保 padding 和 border 被包含在宽度计算之内 */
}

/* Optional: Prettier scrollbar for WebKit browsers */
.course-list::-webkit-scrollbar {
  width: 8px;
}

.course-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.course-list::-webkit-scrollbar-thumb {
  background: #c1c1c1; /* Lighter scrollbar thumb */
  border-radius: 10px;
}

.course-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8; /* Darker on hover */
}

/* 课程卡片整体样式 */
::v-deep .el-card {
  width: 100%; /* 卡片宽度充满其列容器，最大1350px由父级控制 */
  max-width: 1350px; /* 严格控制最大宽度 */
  margin-left: auto; /* 在flex列容器中使其居中 */
  margin-right: auto; /* 在flex列容器中使其居中 */
  /* height: 200px; */ /* 固定高度可能导致内容溢出，改为min-height */
  min-height: 200px;
  border-radius: 0px !important; /* 按要求去掉圆角 */
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1); /* 可以保留或自定义阴影 */
  border: none !important; /* 去掉边框 */
}



::v-deep .el-card__body {
  padding: 0 !important; /* 去掉默认padding，由内部row控制 */
  display: flex; /* 使内部row能撑满card body */
  height: 100%;
}

/* 卡片内部的行，设置为flex容器 */
.el-row {
  width: 100%;
  min-height: 200px; /* 与图片高度保持一致 */
  display: flex;
  align-items: center; /* 垂直居中对齐所有列 */
  padding: 0px 20px; /* 给卡片内容一些左右内边距 */
  box-sizing: border-box;
}


.course-image {
  width: 300px;
  height: 200px;
  object-fit: cover;
  border-radius: 14px;
}

.course-image-col {
  flex: 0 0 300px; /* 固定图片列的宽度为300px，不增长不收缩 */
}

.course-details-col {
  flex-grow: 1; /* 详情列占据剩余空间 */
  display: flex;
  flex-direction: column;
  justify-content: center; /* 垂直居中内容 */
  padding-left: 28px; /* 与图片间距 */
  height: 100%; /* 确保能撑满 */
  min-height: 200px; /* 确保最小高度 */
}

.course-details h3 {
  /* width: 282px; */ /* 固定宽度可能不佳，让其自适应 */
  max-width: 100%;
  /* height: 22px; */ /* 由line-height和内容决定 */
  font-family: 'Alibaba PuHuiTi 2.0', 'Alibaba PuHuiTi 20', sans-serif;
  font-weight: normal;
  font-size: 22px;
  color: #333333;
  line-height: 22px; /* 保持与字体大小一致 */
  margin-top: 0;
  margin-bottom: 12px; /* 调整与描述的间距 */
  display: flex;
  align-items: center;
}

.course-details h3 .el-tag { /* 课程状态标签 */
  width: auto; /* 根据内容自适应宽度 */
  min-width: 84px; /* 最小宽度 */
  height: 26px;
  line-height: 24px; /* 调整使文字居中 */
  border-radius: 20px;
  /* border: 1px solid #6BC694; */ /* 边框颜色应动态设置 */
  margin-left: 10px;
  font-size: 12px; /* 标签内文字大小 */
  display: inline-flex; /* 使内部元素居中 */
  align-items: center;
  justify-content: center;
  padding: 0 10px; /* 增加左右padding */
}

/* 根据状态动态调整标签边框和颜色 */
.el-tag.el-tag--success { /* 开课中 */
  border: 1px solid #6BC694;
  background-color: #f0f9eb !important; /* Element UI success 浅色背景 */
  color: #67c23a !important; /* Element UI success 颜色 */
}
.el-tag.el-tag--warning { /* 即将开课 */
  border: 1px solid #E6A23C; /* 假设用橙色边框 */
  background-color: #fdf6ec !important;
  color: #E6A23C !important;
}
.el-tag.el-tag--info { /* 已结束 */
  border: 1px solid #909399; /* 假设用灰色边框 */
  background-color: #f4f4f5 !important;
  color: #909399 !important;
}

.course-description {
  /* width: 727px; */ /* 固定宽度可能不佳，让其自适应 */
  max-width: 100%;
  /* height: 132px; */ /* 改用line-clamp控制行数 */
  font-family: 'Alibaba PuHuiTi 2.0', 'Alibaba PuHuiTi 20', sans-serif;
  font-size: 14px;
  color: #606266;
  line-height: 1.8; /* 增加行高以适应多行 */
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 4; /* 调整为显示4行，原132px高度大约是4-5行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: calc(1.8em * 4); /* 确保至少有4行的高度 */
  border-radius: 0px; /* 按要求 */
}

.course-meta {
  /* width: 178px; */ /* 固定宽度可能不佳 */
  /* height: 24px; */ /* 由行高和内容决定 */
  font-family: 'Alibaba PuHuiTi 2.0', 'Alibaba PuHuiTi 20', sans-serif;
  font-weight: normal;
  font-size: 16px;
  color: #585E76;
  line-height: 24px;
  margin-top: auto; /* 将meta推到底部，如果详情区高度不固定 */
}

.action-col {
  display: flex;
  align-items: center;
  justify-content: flex-end; /* 按钮靠右 */
  flex-shrink: 0; /* 防止操作列被压缩 */
}

.action-col .el-button {
  width: 119px;
  height: 48px;
  background: linear-gradient( 90deg, #226BFF 0%, #0448D2 100%);
  border-radius: 30px;
  border: none; /* 去掉默认边框 */
  color: white;
  font-size: 16px; /* 调整按钮字体大小 */
  display: flex; /* 新增 */
  align-items: center; /* 新增：垂直居中 */
  justify-content: center; /* 新增：水平居中 */
}



/* 响应式调整，针对小屏幕 */
@media (max-width: 768px) {
  .el-row {
    flex-direction: column;
    align-items: flex-start; /* 左对齐 */
    padding: 15px;
    min-height: auto; /* 移除固定最小高度 */
  }
  .course-image-col, .course-details-col, .action-col {
    width: 100%;
    padding-left: 0; /* 移除列之间的特定padding */
    margin-bottom: 15px; /* 添加堆叠时的间距 */
  }
  .course-image {
    width: 100%; /* 图片宽度自适应 */
    height: auto; /* 图片高度自适应 */
    max-height: 250px; /* 限制最大高度 */
  }
  .course-details-col {
    min-height: auto;
    order: 2; /* 详情在图片之后 */
  }
   .action-col {
    order: 3; /* 按钮在最后 */
    justify-content: flex-start; /* 按钮靠左 */
    margin-bottom: 0;
  }
  .course-details h3 {
    font-size: 20px;
  }
  .course-description {
    -webkit-line-clamp: 3; /* 小屏幕减少显示行数 */
    min-height: calc(1.8em * 3);
  }
}
</style>
