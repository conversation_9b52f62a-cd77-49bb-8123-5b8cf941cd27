<template>
  <div class="home-page-sty">
    <div class="home-nav-container">
        <headerSection @toLogin="toLogin" theme="dark" />
    </div>
    <!-- Page-specific content will be injected here -->
    <div class="auto-com-con">
      <slot></slot>
    </div>
    <PasswordChangePrompt />
    <!-- 底部   -->
    <footComp />
    <!-- 联系客服 -->
    <customerServiceBar @sendMessage="receiveMessage" :key="componentBKey" />
  </div>
</template>

<script setup name="MainLayout">
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from 'vue-router';

// Adjust paths if these components are located elsewhere or should be commonized further
import banner from "@/views/openClass/components/Banner/index.vue";
import headerSection from "@/views/openClass/components/headerSection/index.vue";
import footComp from "@/views/home/<USER>/footComp/index.vue";
import customerServiceBar from "@/views/home/<USER>/customerServiceBar/index.vue";
import PasswordChangePrompt from "@/components/PasswordChangePrompt/index.vue";

const router = useRouter();
const route = useRoute();

const isChatBoxVisible = ref(false); // For customerServiceBar logic
const componentBKey = ref(0); // Used to re-render customerServiceBar if needed

const toLogin = () => {
  router.push({ path: "/login" });
};

const receiveMessage = (data) => {
  isChatBoxVisible.value = data;
  // componentBKey.value++; // Increment key if customerServiceBar needs explicit re-rendering on message
};

// Optional: Include common utility functions like openDev if all pages using this layout need it
function openDev() {
  // console.log(route)
  const dev = route.query.dev9527;
  if (dev) {
    sessionStorage.setItem('OPEN_DEV', "1");
  }
}

onMounted(() => {
  openDev();
});
</script>

<style scoped lang="scss">
@import "@/assets/styles/index.scss"; // Assuming this is a global/base style import

.home-page-sty {
  width: 100%;
  @extend .base-flex-column; // Make sure .base-flex-column is defined in index.scss or imported
  justify-content: flex-start;
  align-items: center;
  min-height: 100vh; // Ensure layout takes at least full viewport height
  background-color: #f0f2f5; // Example background for the page, adjust as needed
}

.home-nav-container {
  width: 100%;
  height: 76px; // Height from original openClass/index.vue
  position: relative;
  background-color: #fff;
  border-bottom: 1px solid #e5e5e5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); // 添加阴影
  
  .nav-con {
    width: 100%;
    height: 76px;
    position: absolute;
    top: 11px;
    left: 0px;
    z-index: 2;
  
  }
  .banner-con {
    width: 100%;
    height: 76px;
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 1;
  }
}

.auto-com-con { // This is where the <slot> content goes
  width: 100%;
  flex: 1; // Allows this section to grow and push footer down
  display: flex;
  flex-direction: column;
  align-items: center; // Center content horizontally
  padding-top: 20px; // Add some space above the slotted content
  padding-bottom: 20px; // Add some space below the slotted content
}

// Styles for PasswordChangePrompt, footComp, customerServiceBar will be from their own components.
// Add any other layout-specific styles here.
</style> 