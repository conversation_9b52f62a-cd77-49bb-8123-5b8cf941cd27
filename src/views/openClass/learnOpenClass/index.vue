<template>
      <div class="filters">
      <div class="filter-content">
        <el-breadcrumb :separator-icon="ArrowRight" class="breadcrumb_text">
          <el-breadcrumb-item :to="{ path: '/open-course-list' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/my-open-classes' }">我的公开课</el-breadcrumb-item>
          <el-breadcrumb-item>课程学习</el-breadcrumb-item>
        </el-breadcrumb>
        <el-button class="btn" type="primary" @click="handleMyCourseClick">我的课程</el-button>
      </div>
    </div>
  <div class="page-background">
    <el-card class="app-container">

      <div class="learn-open-class-container">

        <el-row :gutter="10" class="main-content-area">
          <!-- Left Sidebar: Course Info and Outline -->
          <el-col :span="7" class="left-sidebar">
            <el-card class="course-info-preview" shadow="never">
              <h3 class="course-title">{{ course.name }}</h3>
              <p class="description">{{ course.description }}</p>
              <div class="meta-actions-row">
                <div class="meta-info">
                  <p class="meta">教学团队：{{ course.teachers.join(', ') }}</p>
                  <p class="meta">开课时间：{{ course.openDate }}</p>
                  <p class="meta">学习人数：{{ course.studentCount }}人</p>
                </div>
                <el-button type="primary" plain class="exit-course-btn" @click="exitCourse">退出课程</el-button>
              </div>
            </el-card>

            <el-card class="course-catalog" shadow="never">
              <el-collapse v-model="activeChapterNames" accordion>
                <el-collapse-item 
                  v-for="chapter in course.chapters" 
                  :key="chapter.id" 
                  :title="chapter.title" 
                  :name="chapter.id"
                  class="chapter-item"
                >
                  <ul class="section-list">
                    <li 
                      v-for="section in chapter.sections" 
                      :key="section.id" 
                      @click="selectSection(section)" 
                      :class="{ 'active-section': currentSection && currentSection.id === section.id }"
                      class="section-item"
                    >
                      <div class="section-title-wrapper">
                        <span>{{ section.title }}</span>
                      </div>
                    </li>
                  </ul>
                </el-collapse-item>
              </el-collapse>
            </el-card>
          </el-col>

          <!-- Right Content: Dynamic Resources Area -->
          <el-col :span="17" class="right-content">
            <h2 class="current-section-title">{{ currentSection ? currentSection.displayName : '请选择一个课时进行学习' }}</h2>
            
            <div v-loading="loading">
              <!-- 动态资源区域 -->
              <resource-renderer
                v-for="resource in currentResources"
                :key="resource.chapterResourceId"
                :resource="resource"
                :videoProgress="currentSection ? currentSection.videoProgress : 0"
                :defaultExpanded="resource.resourceType === '3' || resource.resourceType === '7'"
              />

              <!-- 没有资源时显示提示 -->
              <el-empty v-if="currentResources.length === 0" description="该小节暂无学习资源"></el-empty>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
    
    <!-- 添加全局预览弹窗组件 -->
    <PreviewDialog />
  </div>
</template>

<script>
// import OpenClassLayout from '@/views/openClass/layouts/OpenClassLayout.vue'; // Assuming this path is correct
import { getOpenCourseContent } from '@/api/edu/openCourse';
import { exitTheCourse } from '@/api/edu/openCourseStudent';
import ResourceRenderer from '@/views/openClass/components/resources/ResourceRenderer.vue';
import { ResourceTypeMap } from '@/views/openClass/components/resources';
import PreviewDialog from '@/views/reader/sub/Tool/PreviewDialog.vue';
import { ArrowRight } from "@element-plus/icons-vue";

export default {
  name: 'LearnOpenClass',
  components: {
    // OpenClassLayout
    ResourceRenderer,
    PreviewDialog
  },
  data() {
    return {
      courseId: null,
      defaultVideoPlaceholder: 'https://via.placeholder.com/800x450.png?text=Video+Not+Available',
      course: {
        id: 1,
        name: '',
        description: '',
        teachers: [],
        openDate: '',
        studentCount: 0,
        chapters: []
      },
      activeChapterNames: [], // 默认打开的章节ID
      currentSection: null,
      currentResources: [], // 当前小节的所有资源
      loading: false
    };
  },
  methods: {
    // 处理我的课程按钮点击
    handleMyCourseClick() {
      this.$router.push({ path: '/my-open-classes' });
    },
    
    // 获取资源类型标签
    getResourceTypeLabel(type) {
      return ResourceTypeMap[type] || '未知类型';
    },
    
    // 判断是否有可下载内容
    hasDownloadableContent(resource) {
      return resource.resourceContent && resource.resourceContent.trim() !== '';
    },
    
    // 获取资源URL
    getResourceUrl(resource) {
      if ((resource.resourceType === '3' || resource.resourceType === '7') && resource.resourceContent) {
        return resource.resourceContent.startsWith('http') 
          ? resource.resourceContent 
          : 'https://www.w3schools.com/html/mov_bbb.mp4'; // 默认视频
      }
      return this.defaultVideoPlaceholder;
    },
    
    // 打开外部链接
    openExternalLink(resource) {
      const url = resource.resourceContent.startsWith('http') 
        ? resource.resourceContent 
        : `https://${resource.resourceContent}`;
      
      window.open(url, '_blank');
    },
    
    // 选择小节
    selectSection(section) {
      this.currentSection = section;
      // 获取资源并按resourceSort字段排序
      const resources = section.resources || [];
      this.currentResources = resources.sort((a, b) => a.resourceSort - b.resourceSort);
    },
    
    exitCourse() {
      this.$confirm('确定退出该课程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        exitTheCourse({ courseId: this.courseId }).then(response => {
          if (response.code === 200) {
            this.$message.success('退出课程成功');
            this.$router.push({ path: '/my-open-classes' });
          } else {
            this.$message.error(response.msg || '退出课程失败');
          }
        }).catch(error => {
          console.error('退出课程时发生错误:', error);
          this.$message.error('退出课程失败，请稍后再试');
        });
      }).catch(() => {
        // 用户点击取消，无需操作
      });
    },
    
    // 获取课程内容
    fetchCourseContent() {
      this.loading = true;
      getOpenCourseContent(this.courseId)
        .then(response => {
          if (response.code === 200 && response.data) {
            // 更新课程信息
            const data = response.data;
            // 处理教师团队信息，可能是字符串或数组
            let teachers = [];
            if (data.teammates && Array.isArray(data.teammates)) {
              teachers = data.teammates.map(item => item.name);
            } else if (data.teacherTeamNames) {
              teachers = typeof data.teacherTeamNames === 'string'
                ? data.teacherTeamNames.split(',')
                : data.teacherTeamNames;
            }
            
            // 基本课程信息
            this.course = {
              id: data.courseId || this.courseId,
              name: data.courseName || '',
              description: data.courseDescription || '',
              teachers: teachers,
              openDate: data.openTime || '',
              studentCount: data.learnersNum || 0,
              chapters: []
            };
            
            // 如果有课件设计信息，则处理章节和小节
            if (data.coursewareDesigns && data.coursewareDesigns.coursewareDesignFolders) {
              const folders = data.coursewareDesigns.coursewareDesignFolders;
              // 转换章节数据结构
              this.course.chapters = this.convertChapters(folders);
              
              // 如果有章节，则默认打开第一个章节
              if (this.course.chapters && this.course.chapters.length > 0) {
                this.activeChapterNames = [this.course.chapters[0].id];
                
                // 如果有小节，则默认选择第一个章节的第一个小节
                if (this.course.chapters[0].sections && this.course.chapters[0].sections.length > 0) {
                  this.currentSection = this.course.chapters[0].sections[0];
                  this.selectSection(this.currentSection);
                }
              }
            }
          } else {
            this.$message.error('获取课程内容失败');
          }
        })
        .catch(error => {
          console.error('获取课程内容出错:', error);
          this.$message.error('获取课程内容出错');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    
    // 转换章节数据结构
    convertChapters(folders) {
      if (!folders || !folders.length) return [];
      
      return folders
        .filter(folder => folder.parentId === '0') // 只取顶级章节
        .map((chapter, chapterIndex) => {
          // 找到当前章节的所有小节
          const sections = this.extractSections(chapter, folders, chapterIndex);
          
          return {
            id: chapter.chapterId,
            title: chapter.chapterName,
            sections: sections
          };
        });
    },
    
    // 提取小节数据
    extractSections(chapter, allFolders, chapterIndex) {
      if (!chapter.children || !chapter.children.length) return [];
      
      return chapter.children.map((section, sectionIndex) => {
        // 获取小节的所有资源
        const resources = section.coursewareDesignResources || [];
        
        // 查找视频类型的资源
        const videoResource = resources.find(res => res.resourceType === '7'); // 类型7是视频
        const videoUrl = videoResource ? videoResource.resourceContent : '';
        
        return {
          id: section.chapterId,
          title: section.chapterName,
          displayName: `${chapter.chapterName} ${section.chapterName}`,
          progress: 0, // 默认进度为0
          videoUrl: videoUrl || 'https://www.w3schools.com/html/mov_bbb.mp4', // 默认视频或从资源中提取
          videoProgress: 0, // 默认视频进度为0
          resources: resources // 保存小节的所有资源
        };
      });
    }
  },
  created() {
    // 从URL参数获取courseId
    this.courseId = this.$route.query.courseId;
    
    if (this.courseId) {
      // 调用API获取课程内容
      this.fetchCourseContent();
    } else {
      this.$message.warning('未指定课程ID，将显示示例数据');
      // 使用示例数据（保留原有的示例数据作为fallback）
      const initialChapters = [
        {
          id: 'chap1',
          title: '第一章 传统匠艺的智慧',
          sections: [
            { 
              id: 'sec1.1', 
              title: '1. 探索古代工匠的奥秘', 
              progress: 100, 
              videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4', 
              videoProgress: 100, 
              displayName: '第一章 第一讲 探索古代工匠的奥秘',
              resources: [
                {
                  chapterResourceId: '1001',
                  resourceTitle: '古代工匠的奥秘',
                  resourceContent: '本节探讨古代工匠精神及其对现代制造的影响。',
                  resourceType: '1',
                  resourceSort: 1
                },
                {
                  chapterResourceId: '1002',
                  resourceTitle: '工匠视频',
                  resourceContent: 'https://www.w3schools.com/html/mov_bbb.mp4',
                  resourceType: '7',
                  resourceSort: 2
                }
              ]
            },
            { 
              id: 'sec1.2', 
              title: '2. 材料的演进与选择', 
              progress: 50, 
              videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4', 
              videoProgress: 50, 
              displayName: '第一章 第二讲 材料的演进与选择',
              resources: [
                {
                  chapterResourceId: '1003',
                  resourceTitle: '材料演进史',
                  resourceContent: '讨论不同历史时期制造材料的革新。',
                  resourceType: '1',
                  resourceSort: 1
                },
                {
                  chapterResourceId: '1004',
                  resourceTitle: '材料视频',
                  resourceContent: 'https://www.w3schools.com/html/mov_bbb.mp4',
                  resourceType: '7',
                  resourceSort: 2
                },
                {
                  chapterResourceId: '1005',
                  resourceTitle: '材料选择练习',
                  resourceContent: '完成关于材料选择的作业练习。',
                  resourceType: '5',
                  resourceSort: 3
                }
              ]
            },
          ]
        },
        {
          id: 'chap2',
          title: '第二章 工业革命的工艺革新',
          sections: [
            { 
              id: 'sec2.1', 
              title: '1. 手工锻造与精密铸造的千年传承', 
              progress: 20, 
              videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4', 
              videoProgress: 20, 
              displayName: '第二章 第一讲 手工锻造与精密铸造的千年传承',
              resources: [
                {
                  chapterResourceId: '2001',
                  resourceTitle: '锻造与铸造',
                  resourceContent: '深入了解手工锻造和精密铸造技术的发展历程。',
                  resourceType: '1',
                  resourceSort: 1
                },
                {
                  chapterResourceId: '2002',
                  resourceTitle: '锻造演示视频',
                  resourceContent: 'https://www.w3schools.com/html/mov_bbb.mp4',
                  resourceType: '7',
                  resourceSort: 2
                },
                {
                  chapterResourceId: '2003',
                  resourceTitle: '虚拟仿真：铸造工艺',
                  resourceContent: 'https://simulation.example.com/casting',
                  resourceType: '3',
                  resourceSort: 3
                },
                {
                  chapterResourceId: '2004',
                  resourceTitle: '锻造工艺资料',
                  resourceContent: 'https://example.com/download/forge-materials.zip',
                  resourceType: '6',
                  resourceSort: 4
                }
              ]
            }
          ]
        }
      ];

      // 使用示例数据
      this.course = {
        id: 1,
        name: '《机械制造工艺：从匠艺到智造》',
        description: '本课程以机械制造工艺的演变为核心，从传统工匠技艺讲起，逐步深入现代智能制造技术。课程将系统介绍铸造、锻造、切削、焊接等基础工艺...',
        teachers: ['王美艳', '李小刚'],
        openDate: '2025-03-12',
        studentCount: 21322,
        chapters: initialChapters
      };
      
      // 默认打开第一章
      this.activeChapterNames = ['chap1'];
      
      // 默认选中第一章第一节
      this.currentSection = initialChapters[0].sections[0];
      this.selectSection(this.currentSection);
    }
  }
};
</script>

<style scoped>
.page-background {
  background-color: #f0f2f5;
  min-height: 100vh;
  padding: 20px;
}

.app-container {
  max-width: 1400px;
  min-height: 1077px;
  margin: 0 auto;
  border-radius: 10px;
  padding: 20px;
  box-sizing: border-box;
  width: 100%;
}

.filters {
  margin-bottom: 10px;
  margin-top: 20px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  box-sizing: border-box;
}

.breadcrumb_text {
  font-size: 14px;
  font-weight: 400;
  line-height: 40px;
}

.btn {
  width: 120px;
  height: 40px;
  border-radius: 4px;
}

.learn-open-class-container {
  padding: 0;
  min-height: calc(100vh - 100px);
}

.breadcrumb-nav {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.main-content-area {
  /* Styles for the row containing left and right columns */
}

.left-sidebar .el-card, .right-content .el-card {
  border: 1px solid #e6e6e6; /* Unified border for cards */
  /* border-radius: 4px; */ /* Replaced by specific radius below */
}

.left-sidebar {
  /* background-color: #fff; */
  /* padding: 15px; */
  /* border-radius: 4px; */
  /* width: 424px; */ /* Will be applied to cards directly */
}

.course-info-preview {
  width: 100%;
  min-height: 216px;
  background: #FFFFFF;
  border-radius: 10px;
  margin-bottom: 10px; /* Restored for spacing with .course-catalog */
  padding: 20px;
  box-sizing: border-box; /* Ensure padding is included in width/height */
}

.course-info-preview .course-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-top: 0;
  margin-bottom: 10px;
}

.course-info-preview .description {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 15px;
  /* For multi-line ellipsis: */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.course-info-preview .meta {
  font-size: 14px;
  color: #585E76;
  margin-bottom: 2px; /* Preserving user's update */
  margin-top: 2px;    /* Preserving user's update */
}

/* New styles for meta and button layout */
.meta-actions-row {
  display: flex;
  justify-content: space-between;
  align-items: center; /* Vertically center items in the row */
  margin-top: 15px; /* Spacing from the description above */
}

.meta-info {
  /* Container for meta text elements */
}
/* End of new styles */

.exit-course-btn {
  /* width: 100%; Removed */
  /* margin-top: 15px; Removed */
  margin-left: 15px; /* Added for spacing from meta info */
  flex-shrink: 0; /* Prevent button from shrinking */
}

.course-catalog {
  width: 100%;
  height: 916px; /* Consider potential overflow issues */
  background: #FFFFFF;
  border-radius: 10px;
  padding: 0px; /* Remove padding if el-collapse handles it */
  box-sizing: border-box;
  overflow-y: auto; /* Add scroll if content exceeds height */
  width: 100% !important; /* Ensure cards take full width of their column */
}
.course-catalog >>> .el-collapse-item__header {
  font-size: 16px;
  font-weight: 500;
  padding: 0 20px; /* Add padding to header */
  background-color: #fafafa;
  border-bottom: 1px solid #EBEEF5;
}
.course-catalog >>> .el-collapse-item__header.is-active {
    color: #409EFF;
    /* border-left: 3px solid #409EFF; */
}

.course-catalog >>> .el-collapse-item__wrap {
  border-bottom: none;
}
.course-catalog >>> .el-collapse-item__content {
  padding:0; /* Remove default padding */
}


.section-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.section-item {
  display: flex;
  flex-direction: column; /* Changed from flex to allow multi-line */
  align-items: flex-start; /* Align items to the start */
  padding: 12px 20px 12px 30px; /* Indent section items */
  font-size: 14px;
  color: #303133;
  cursor: pointer;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.3s;
}

.section-item:last-child {
  border-bottom: none;
}

.section-item:hover {
  background-color: #ecf5ff;
}

.section-item.active-section {
  background-color: #d9ecff;
  color: #409EFF;
  font-weight: 500;
}

.section-title-wrapper {
  width: 100%; /* Ensure it takes full width if needed for alignment or truncation */
}

.right-content {
  /* background-color: #fff; */
  /* padding: 20px; */
  /* border-radius: 4px; */
  /* width: 951px; */ /* Width will be applied to inner cards */
}

.current-section-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 15px 0;
  padding: 10px 20px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
  /* Applying overall right content page style to its children cards instead */
  width: 100% !important; /* Ensure cards take full width of their column */
}

/* Responsive adjustments if needed */
@media (max-width: 992px) {
  .el-col.left-sidebar,
  .el-col.right-content {
    width: 100% !important; /* Make them full width on smaller screens */
    margin-bottom: 20px;
  }
  .course-info-preview,
  .course-catalog,
  .resource-container,
  .current-section-title {
    width: 100% !important; /* Ensure cards take full width of their column */
  }
  .video-wrapper {
    height: auto; /* Allow video height to adjust on smaller screens */
    max-height: 506px; /* But not exceed the original max height */
  }
  .main-video-player {
    max-height: unset; /* remove fixed height to allow aspect ratio scaling */
  }
}
</style>


