<template>
  <div class="index_classroom">
    <div class="index_classroom_operate">
      <div>
        <span class="classroom_operate_num">小组名称：50人</span>
        <span class="classroom_operate_num">人员个数：50人</span>
      </div>
      <el-input v-model="input2" style="width: 240px; margin-right: 10px" placeholder="请输入学号或姓名" :suffix-icon="Search" />
    </div>
    <el-table ref="tableRef" :data="tableData" class="index_classroom_table">
      <el-table-column prop="xzmc" align="center" label="学号" />
      <el-table-column prop="rygs" align="center" label="姓名" />
      <el-table-column prop="zz" align="center" label="所属班级" />
    </el-table>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
const tableRef = ref(null)
const ids = ref([])

const { proxy } = getCurrentInstance()
const statusList = ref([
  {
    value: '1',
    label: '未审批'
  },
  {
    value: '2',
    label: '已审批'
  },
])
const tableData = ref([
  {
    xzmc: '阿宾教学组',
    rygs: '50',
    zz: '阿宾',
    cjsj: '2021-01-01 12:00:00'
  }
])
const popupGroupAdd = ref(null)
// 新建
const addGroup = (id) => {
  popupGroupAdd.value.open(id)
}
const popupGroupAuto = ref(null)
// 自动
const autoGroup = () => {
  popupGroupAuto.value.open()
}
// 查看人员 
const popupGroupPeople = ref(null)
const viewPeople = () => {
  popupGroupPeople.value.open()
}
// 导出 
const exportGroup = () => {
  console.log('导出')
}
// 批量删除
const delBatch = () => {
  if (ids.value && ids.value.length == 0) {
    ElMessage({
      type: 'info',
      message: '请勾选需要删除的学生'
    })
  } else {
    ElMessageBox.confirm('确定删除已选学生吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '取消操作'
        })
      })
  }
}
// 审核 
const delGroup = (id) => {
  ElMessageBox.confirm('确定删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消操作'
      })
    })
}
</script>

<style scoped lang="scss">
.index_classroom {
  margin-top: 8px;

  .index_classroom_operate {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .classroom_operate_num {
      font-size: 16px;
      color: #333333;
      margin: 0 20px 0 10px;
    }
  }

  .index_classroom_table {
    width: 100%;
    margin-top: 20px;
  }
}
</style>
