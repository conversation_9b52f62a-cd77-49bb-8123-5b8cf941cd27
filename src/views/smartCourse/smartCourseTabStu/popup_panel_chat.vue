<template>
  <el-dialog v-model="dialogVisible" title="互动讨论" width="1300" :before-close="handleClose" :close-on-click-modal="false"
    :close-on-press-escape="false" :style="{ 'marginTop': (isFullscreen ? '0 !important' : '1vh !important') }"
    :fullscreen="isFullscreen">
    <div class="chat_full_screen_div" @click="isFullscreen = !isFullscreen">
      <el-icon class="chat_full_screen">
        <FullScreen />
      </el-icon>
    </div>
    <div class="common-layout">
      <el-container>
        <el-aside class="chat_aside">
          <div class="chat_tab">
            <div v-for="item in tabList" :key="item.value" @click="clickTab(item.value)"
              :class="[item.value == tabPosition ? 'active' : '']">{{ item.label }}</div>
          </div>
          <!-- 主题 -->
          <div class="chat_tab_zt" v-if="tabPosition == '1'">
            <h3>{{ ztRes.bt }}</h3>
            <div>参与人数{{ ztRes.rs }}人</div>
            <p>{{ ztRes.nr }}</p>
          </div>
          <!-- 答疑收藏 -->
          <div v-if="tabPosition == '2'" class="chat_tab_ydsc">
            <div class="dysc_time" @click="tabTime = !tabTime">
              <span>时间排序</span>
              <el-icon v-if="tabTime">
                <ArrowDown />
              </el-icon>
              <el-icon v-else>
                <ArrowUp />
              </el-icon>
            </div>
            <div class="dysc_list">
              <div class="dysc_item" v-for="item in dyscRes" :key="item.id">
                <img :src="item.tx" alt="">
                <span>{{ item.mz }}</span>
                <span class="dysc_item_time">{{ item.sj }}</span>
                <p>{{ item.wt }}</p>
                <p>{{ item.jd }}</p>
              </div>
            </div>
          </div>
          <!-- 成员 -->
          <div v-if="tabPosition == '3'" class="chat_tab_cy">
            <h3>成员名单</h3>
            <div>
              <div class="cy_item" v-for="item in cyRes" :key="item.id">
                <img :src="item.tx" alt="">
                <div class="cy_item_info">
                  <div>{{ item.mz }}</div>
                  <div>{{ item.xy }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-aside>
        <el-container>
          <el-header class="chat_header">
            <h2>机械设计二班讨论组</h2>
          </el-header>
          <el-main>
            <div class="chat_record" ref="chatRecordRef">
              <div v-for="(item, index) in chatList" :key="item.id">
                <p v-if="item.type == '1'" class="record_join"><span>{{ item.xm }}</span>加入了互动讨论</p>
                <div class="record_otherpeople" v-if="item.type == '2'">
                  <img class="otherpeople_tx" :src="item.tx" alt="">
                  <span>{{ item.xm }}</span>
                  <p @contextmenu.prevent="openMenu($event, item, index)">
                    <i v-if="item.wt && item.wtlx == '1'">{{ item.wt }}</i>
                    <el-image v-if="item.wtlx == '2'" style="width: 50px;" :src="item.wt" :preview-src-list="[item.wt]"
                      show-progress fit="contain" />
                    <video v-if="item.wtlx == '3'" @click="playVideo(item.wt)" :src="item.wt"></video>
                    <el-divider v-if="item.wt" />
                    <i v-if="item.fslx == '1'">{{
                      item.fsnr }}</i>
                    <el-image v-if="item.fslx == '2'" style="width: 50px;" :src="item.fsnr"
                      :preview-src-list="[item.fsnr]" show-progress fit="contain" />
                    <video v-if="item.fslx == '3'" @click="playVideo(item.fsnr)" :src="item.fsnr"></video>
                  </p>
                  <img v-if="item.sfdz" class="otherpeople_like" :src="chatLike" @click="likeStep(item.id)" alt="">
                  <img v-if="!item.sfdz" class="otherpeople_like" :src="chatStep" @click="likeStep(item.id)" alt="">
                </div>
                <div class="record_i" v-if="item.type == '3'">
                  <img class="record_i_img"
                    src="https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg" alt="">
                  <p @contextmenu.prevent="openMenu($event, item, index)">
                    <i v-if="item.wt && item.wtlx == '1'">{{ item.wt }}</i>
                    <el-image v-if="item.wtlx == '2'" style="width: 50px;" :src="item.wt" :preview-src-list="[item.wt]"
                      show-progress fit="contain" />
                    <video v-if="item.wtlx == '3'" @click="playVideo(item.wt)" :src="item.wt"></video>
                    <el-divider v-if="item.wt" />
                    <i v-if="item.fslx == '1' && !item.wt">{{ item.fsnr }}</i>
                    <i v-if="item.fslx == '1' && item.wt">{{ item.fsnr }}</i>
                    <el-image v-if="item.fslx == '2'" style="width: 50px;" :src="item.fsnr"
                      :preview-src-list="[item.fsnr]" show-progress fit="contain" />
                    <video v-if="item.fslx == '3'" @click="playVideo(item.fsnr)" :src="item.fsnr"></video>
                  </p>
                </div>
              </div>
            </div>
          </el-main>
          <el-footer>
            <!-- 输入框 -->
            <div class="chat_input">
              <div class="chat_question" v-if="answerNow">
                <div class="question_content">
                  <div class="question_content_text" v-if="chatQuestion.fslx == '1'">
                    <span>{{ chatQuestion.fsnr }}</span>
                  </div>
                  <div v-else class="question_content_other">
                    <el-image v-if="chatQuestion.fslx == '2'" style="height: 23px;" :src="chatQuestion.fsnr"
                      fit="contain" />
                    <video v-if="chatQuestion.fslx == '3'" style="height: 23px;" :src="chatQuestion.fsnr"></video>
                  </div>
                  <el-icon class="close_icon" @click="clearQuestion">
                    <CircleClose />
                  </el-icon>
                </div>
              </div>
              <div class="chat_input_tool">
                <!-- <img :src="chatImg" alt=""> -->
                <el-upload v-model:file-list="fileList" class="upload-demo" :action="uploadUrl" :headers="headers"
                  :before-upload="beforeAvatarUpload" :on-success="handleSuccess" :on-remove="handleRemove"
                  :auto-upload="true">
                  <template #trigger>
                    <img :src="chatImg" alt="">
                    <img :src="chatVideo" alt="">
                  </template>
                </el-upload>
              </div>
              <img class="chat_input_send" @click="send" :src="chatSending" alt="">

              <el-input class="chat_input_text" v-model="fontsend" placeholder="请输入内容" type="textarea" />
            </div>
          </el-footer>
        </el-container>
      </el-container>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog v-model="dialogVideo" title="视频播放" width="800">
    <video style="width: 100%;" controls>
      <source :src="videoUrl" type="video/mp4">
    </video>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVideo = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 自定义右键菜单 -->
  <div v-if="showMenu" class="chat_right_menu" :style="{ top: menuY + 'px', left: menuX + 'px' }">
    <el-button type="primary" @click="answer">解答</el-button>
    <el-button type="primary" @click="copyChat">复制</el-button>
    <el-button type="primary" @click="delChat">删除</el-button>
  </div>
</template>

<script setup>
import { nextTick, ref } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import chatLike from '@/assets/images/smartCourse/smartCourseTab/chat_like.png'
import chatStep from '@/assets/images/smartCourse/smartCourseTab/chat_step.png'
import chatImg from '@/assets/images/smartCourse/smartCourseTab/chat_img.png'
import chatVideo from '@/assets/images/smartCourse/smartCourseTab/chat_video.png'
import chatStop from '@/assets/images/smartCourse/smartCourseTab/chat_stop.png'
import chatSending from '@/assets/images/smartCourse/smartCourseTab/chat_sending.png'
import { getToken } from '@/utils/auth'

const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/file/upload') // 上传的图片服务器地址
const headers = ref({
  Authorization: 'Bearer ' + getToken()
})

const dialogVisible = ref(false)
const dialogVideo = ref(false)
const formRef = ref(null)
const isFullscreen = ref(false)
const tabPosition = ref('1')
const tabTime = ref(true)
const like = ref(false)
const fontsend = ref()
const videoUrl = ref()
const fileList = ref([])
const tabList = ref([
  {
    label: '主题',
    value: '1'
  },
  {
    label: '答疑收藏',
    value: '2'
  },
  {
    label: '成员(55)',
    value: '3'
  }
])
const ztRes = ref({
  bt: '沃达丰萨克利夫爱发牢骚了地方藕去皮我骄傲附件撒开绿灯飞机',
  rs: '10',
  nr: '沃达丰萨克利夫爱发牢骚了地方藕去皮我骄傲附件撒开绿灯飞机沃达丰萨克利夫爱发牢骚了地方藕去皮我骄傲附件撒开绿灯飞机沃达丰萨克利夫爱发牢骚了地方藕去皮我骄傲附件撒开绿灯飞机'
})
const dyscRes = ref([
  {
    id: '1',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    mz: '阿宾',
    sj: '2025-05-06 10:10:55',
    wt: '请问地球为什么是元的',
    jd: '因为我说是就是',
  },
  {
    id: '2',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    mz: '阿宾',
    sj: '2025-05-06 10:10:55',
    wt: '请问地球为什么是元的',
    jd: '因为我说是就是',
  },
])
const cyRes = ref([
  {
    id: '1',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    mz: '年阿宾',
    xy: '城市交通管理关系学院',
    zt: '1'
  },
  {
    id: '2',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    mz: '少阿宾',
    xy: '城市交通管理关系学院',
    zt: '2'
  },
])
const chatList = ref([
  // type类型：1-加入 2-别人发言 3-自己发言
  // 发送类型：1-文字 2-图片 3-视频
  {
    id: '1',
    type: '1',
    xm: '学姐'
  },
  {
    id: '2',
    type: '2',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    xm: '阿宾',
    fslx: '1', // 类型1-文字
    fsnr: '大家好，我是新同学，我叫钰慧，是阿宾的女朋友',
    wt: '',
    wtlx: '',
    sfdz: false,
  },
  {
    id: '3',
    type: '2',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    xm: '阿宾',
    fslx: '2', // 类型2-图片
    fsnr: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    sfdz: false
  },
  {
    id: '4',
    type: '2',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    xm: '阿宾',
    fslx: '3', // 类型3-视频
    fsnr: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1753174310340.mp4',
    sfdz: true
  },
  {
    id: '22',
    type: '2',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    xm: '阿宾',
    fslx: '1', // 类型1-文字
    fsnr: '大家好，我是新同学，我叫钰慧，是阿宾的女朋友',
    wt: '欢迎大家来参加舞会',
    wtlx: '1',
    sfdz: false,
  },
  {
    id: '222',
    type: '2',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    xm: '阿宾',
    fslx: '1', // 类型1-文字
    fsnr: '大家好，我是新同学，我叫钰慧，是阿宾的女朋友',
    wt: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    wtlx: '2',
    sfdz: false,
  },
  {
    id: '33',
    type: '2',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    xm: '阿宾',
    fslx: '1',
    fsnr: '欢迎大家来参加舞会',
    wt: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    wtlx: '2',
    sfdz: false
  },
  {
    id: '44',
    type: '2',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    xm: '阿宾',
    fslx: '1',
    fsnr: '欢迎大家来参加舞会',
    wt: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1753174310340.mp4',
    wtlx: '3',
    sfdz: true
  },
  {
    id: '5',
    type: '3',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    xm: '阿宾',
    fslx: '1', // 类型1-文字
    fsnr: '啊大大阿松大阿松大'
  },
  {
    id: '6',
    type: '3',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    xm: '阿宾',
    fslx: '2', // 类型2-图片 
    fsnr: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    id: '7',
    type: '3',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    xm: '阿宾',
    fslx: '3', // 类型3-视频 
    fsnr: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1753174310340.mp4'
  },
  {
    id: '8',
    type: '1',
    xm: '房东太太'
  },
  {
    id: '9',
    type: '3',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    xm: '阿宾',
    fslx: '1', // 类型1-文字 
    wt: '学姐呢',
    wtlx: '1',
    fsnr: '我是解答，老师喜欢帮助同学们哦'
  },
  {
    id: '10',
    type: '3',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    xm: '阿宾',
    fslx: '1',
    wt: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    wtlx: '2',
    fsnr: '我是解答，老师喜欢帮助同学们哦'
  },
  {
    id: '11',
    type: '3',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    xm: '阿宾',
    fslx: '1',
    wt: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1753174310340.mp4',
    wtlx: '3',
    fsnr: '我是解答，老师喜欢帮助同学们哦'
  },
])

// 开启弹窗
const open = () => {
  dialogVisible.value = true
  if (formRef.value) formRef.value.resetFields()
  nextTick(() => {
    scrollToBottom()
    document.addEventListener('click', handleClickOutside)
    document.addEventListener('keydown', handleKeydown)
    // document.addEventListener('scroll', closeMenu) // 和滚动事件有点冲突，暂时先不用了
  })
}
// 点击tab
const clickTab = (value) => {
  tabPosition.value = value
}
// 喜不喜欢
const likeStep = (id) => {
  for (let i = 0; i < chatList.value.length; i++) {
    if (chatList.value[i].type == '2' && chatList.value[i].id === id) {
      chatList.value[i].sfdz = !chatList.value[i].sfdz
    }
  }
}
// 保持新消息再底部
const chatRecordRef = ref(null)
const scrollToBottom = () => {
  if (chatRecordRef.value) {
    nextTick(() => {
      chatRecordRef.value.scrollTop = chatRecordRef.value.scrollHeight
    })
  }
}
// 发送
const send = () => {
  if (!fontsend.value) return
  const params = {
    type: '3',
    tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    xm: '阿宾',
    fslx: '1', // 类型1-文字
    fsnr: fontsend.value,
    wt: answerNow.value ? chatQuestion.value.fsnr : '',
    wtlx: chatQuestion.value?.fslx
  }
  chatList.value.push(params)
  fontsend.value = ''
  answerNow.value = false
  scrollToBottom()
}
const playVideo = (url) => {
  videoUrl.value = url
  dialogVideo.value = true
}
// 上传
const beforeAvatarUpload = rawFile => {
  // if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/jpg' && rawFile.type !== 'image/png' && rawFile.type !== 'application/pdf') {
  //   ElMessage.error('上传失败！请上传 jpg, jpeg, png, pdf格式文件！')
  //   return false
  // } else if (rawFile.size / 1024 / 1024 > 60) {
  //   ElMessage.error('上传失败！文件大小不能超过 60MB!')
  //   return false
  // }
  return true
}
// 上传成功
const handleSuccess = rawFile => {
  if (rawFile.data.url.endsWith('.jpg') || rawFile.data.url.endsWith('.png') || rawFile.data.url.endsWith('.jpeg')) {
    const params = {
      type: '3',
      tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
      xm: '阿宾',
      fslx: '2', // 类型2-图片 
      fsnr: rawFile.data.url
    }
    chatList.value.push(params)
  }
  if (rawFile.data.url.endsWith('.mp4')) {
    const params = {
      type: '3',
      tx: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
      xm: '阿宾',
      fslx: '3', // 类型3-视频 
      fsnr: rawFile.data.url
    }
    chatList.value.push(params)
  }
  scrollToBottom()
}
// 解除禁言
const liftTalk = (id) => {
  ElMessageBox.confirm('是否解除该学生禁言?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage({
      type: 'success',
      message: '解除成功'
    })
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '取消操作'
    })
  })
}
// 自定义的右键菜单
const showMenu = ref(false)
const menuX = ref()
const menuY = ref()
const chatItem = ref()
const chatIndex = ref()
const chatQuestion = ref()
const openMenu = (e, item, i) => {
  showMenu.value = true
  chatItem.value = item
  chatIndex.value = i
  menuX.value = e.clientX
  menuY.value = e.clientY
  // 防止菜单超出窗口
  // setTimeout(() => {
  //   const menu = document.querySelector('.chat_right_menu')
  //   if (!menu) return

  //   const rect = menu.getBoundingClientRect()
  //   const windowWidth = window.innerWidth
  //   const windowHeight = window.innerHeight

  //   if (rect.right > windowWidth) {
  //     menuX.value = windowWidth - rect.width - 10
  //   }
  //   if (rect.bottom > windowHeight) {
  //     menuY.value = windowHeight - rect.height - 10
  //   }
  // }, 0)
}
// 关闭菜单
const closeMenu = () => {
  showMenu.value = false
}

// 点击外部关闭菜单
const handleClickOutside = (e) => {
  if (showMenu.value && !e.target.closest('.chat_right_menu')) {
    closeMenu()
  }
}

// esc关闭菜单
const handleKeydown = (e) => {
  if (e.key === 'Escape' && showMenu.value) {
    closeMenu()
  }
}
// 功能项
// 解答
const answerNow = ref(false)
const answer = () => {
  answerNow.value = true
  showMenu.value = false
  chatQuestion.value = JSON.parse(JSON.stringify(chatItem.value))
}
// 删除回答
const clearQuestion = () => {
  answerNow.value = false
}
// 复制
const copyChat = () => {
  navigator.clipboard.writeText(chatItem.value.fsnr)
    .then(() => {
      ElMessage({
        type: 'success',
        message: '复制成功'
      })
    })
    .catch(err => {
      console.error('请检查是否为安全链接https', err);
    });
  showMenu.value = false
}
// 删除
const delChat = () => {
  chatList.value.splice(chatIndex.value, 1)
  showMenu.value = false
}

// 监听动作
// onMounted(() => {
//   document.addEventListener('click', handleClickOutside)
//   document.addEventListener('keydown', handleKeydown)
//   document.addEventListener('scroll', closeMenu) // 和滚动事件有点冲突，暂时先不用了
// })
// 销毁监听
onBeforeUnmount(() => {
  distroyDialog()
})
// xiaohuo
const distroyDialog = () => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleKeydown)
  // document.removeEventListener('scroll', closeMenu)
}
const handleClose = () => {
  dialogVisible.value = false
  distroyDialog()
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.common-layout {
  min-height: 200px;
}

.chat_aside {
  width: 400px;
  background-color: #fff;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 0;
}

.chat_header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  text-align: center;
}

.chat_full_screen_div {
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: var(--el-message-close-size, 16px);
  height: 48px;
  outline: none;
  padding: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 48px;
  position: absolute;
  top: 1px;
  right: 43px;
  cursor: pointer;
  line-height: 48px;
  text-align: center;
}

.chat_full_screen {
  width: 1em;
  height: 1em;
}

.chat_full_screen :hover {
  color: #409EFF;
}

.chat_tab {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;

  .active {
    color: #fff;
    background: linear-gradient(90deg, #226BFF 0%, #0448D2 100%), #FF9500;
  }

  div {
    width: 90px;
    height: 40px;
    background: #F2F5FA;
    border-radius: 8px;
    font-size: 16px;
    color: #333333;
    line-height: 40px;
    text-align: center;
    cursor: pointer;
  }
}

// 主题
.chat_tab_zt {
  div {
    // color: #000;
    // font-weight: bold;
    text-align: right;
  }
}

// 答疑收藏
.chat_tab_ydsc {
  .dysc_time {
    cursor: pointer;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin: 10px 0;
  }

  .dysc_list {
    padding: 0 0 0 30px;

    .dysc_item:first-of-type {
      margin-top: 30px;
    }

    .dysc_item {
      width: 100%;
      border-radius: 12px;
      background: #F1F1F1;
      position: relative;
      margin-top: 40px;

      img {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        vertical-align: top;
        position: absolute;
        top: -19px;
        left: -47px;
      }

      span {
        vertical-align: top;
        color: #333;
        font-size: 15px;
      }

      span:first-of-type {
        position: absolute;
        top: -30px;
        left: 0;
      }

      span:last-of-type {
        position: absolute;
        top: -30px;
        right: 0;
      }

      .dysc_item_time {
        text-align: right;
      }


      p {
        margin: 0;
        padding: 5px 10px;
        color: #333333;
      }

      p:first-of-type {
        border-bottom: 1px solid #E6EBF5;
      }
    }
  }
}

// 成员列表
.cy_item:last-of-type {
  border-bottom: 0;
}

.cy_item {
  display: flex;
  padding-bottom: 10px;
  margin-top: 10px;
  border-bottom: 1px solid #E6EBF5;
  position: relative;

  img {
    width: 40px;
    height: 40px;
    border-radius: 8px;
  }

  .cy_item_info {
    margin-left: 5px;

    div {
      line-height: 1.3;
    }

    div:first-of-type {
      color: #333;
    }

    div:last-of-type {
      color: #585E76;
      font-size: 14px;
    }
  }

  .cy_icon {
    position: absolute;
    top: 9px;
    right: 15px;
    color: red;
    font-size: 18px;
    cursor: pointer;
  }
}

// 聊天记录
.chat_record {
  height: 450px;
  overflow-y: auto;
  padding: 20px;

  .record_join {
    text-align: center;
    width: 100%;
    padding: 20px 0;
    margin: 0;

    span {
      color: #226BFF;
    }
  }

  .record_otherpeople {
    position: relative;
    padding-left: 45px;
    width: 100%;
    display: flex;
    margin-top: 20px;

    .otherpeople_tx {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      position: absolute;
      top: -23px;
      left: 0;
    }

    .otherpeople_like {
      width: 15px;
      object-fit: contain;
      margin: 0 0 22px 7px;
      cursor: pointer;
      // position: absolute;
      // top: 13px;
      // left: calc(70% + 20px);
    }

    span {
      font-size: 15px;
      color: #333;
      position: absolute;
      top: -25px;
      left: 45px;
    }

    p {
      max-width: 70%;
      background: #F1F1F1;
      border-radius: 12px;
      padding: 8px 13px;
      color: #000;
      font-size: 17px;
      margin-top: 0;
      cursor: pointer;

      i {
        font-style: normal;
      }

      img {
        width: 50px;
        object-fit: contain;
        cursor: pointer;
      }

      video {
        width: 50px;
        cursor: pointer;
      }
    }
  }

  .record_i {
    position: relative;
    padding-left: 45px;
    width: 100%;
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    padding-right: 45px;

    .record_i_img {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      position: absolute;
      top: -2px;
      right: -1px;
    }

    p {
      max-width: 70%;
      background: #226BFF;
      border-radius: 12px;
      padding: 8px 13px;
      color: #fff;
      font-size: 17px;
      margin-top: 0;
      cursor: pointer;

      i {
        font-style: normal;
      }

      img {
        width: 50px;
        object-fit: contain;
        cursor: pointer;
      }

      video {
        width: 50px;
        cursor: pointer;
      }
    }
  }
}

// 输入框
.chat_input {
  position: relative;

  // width: 100%;
  // height: 100px;
  // background: #FFFFFF;
  // border-radius: 12px;
  // border: 2px solid #E6EBF5;
  .chat_input_text {
    height: 80px !important;
  }

  .chat_input_send {
    position: absolute;
    right: 20px;
    bottom: 10px;
    z-index: 99;
    cursor: pointer;
  }

  .chat_question {
    width: 100%;
    height: 35px;
    position: absolute;
    left: 0;
    top: -40px;

    .question_content {
      display: flex;
      // line-height: 35px;
      font-size: 16px;

      .question_content_text {
        padding: 6px 5px;
        margin-bottom: 5px;
        background-color: #f5f7fa;
        border-radius: 8px;
      }

      .question_content_other {
        background-color: #f5f7fa;
        padding: 5px;
        border-radius: 8px;
      }

      // 引用，解答
      .close_icon {
        cursor: pointer;
        color: #c0c4cc;
        margin-top: 9px;
        margin-left: 5px;
        font-size: 18px;
      }

      .close_icon:hover {
        color: #909399;
      }
    }
  }
}

.chat_input_tool {
  width: 100%;
  height: 35px;
  background: #E6EBF5;
  border-radius: 8px 8px 0px 0px;
  display: flex;
  align-items: center;

  img {
    cursor: pointer;
    margin-left: 10px;
  }

  img:first-of-type {
    margin-left: 15px;
  }
}

.el-footer {
  height: 115px !important;
}

::v-deep .el-main {
  height: 450px !important;
  padding: 0 !important;
}

::v-deep .el-textarea__inner {
  height: 80px !important;
  border-radius: 0 0 8px 8px !important;
}

::v-deep .el-upload-list {
  display: none !important;
}

::v-deep .el-upload {
  height: 35px !important;
}

::v-deep .el-button {
  margin: 5px 5px 0 5px !important;
}

::v-deep .el-button:last-of-type {
  margin-bottom: 5px !important;
}

::v-deep .el-divider--horizontal {
  margin: 5px 0 !important;
}

// 自定义菜单
.chat_right_menu {
  position: fixed;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 999999;
  animation: fadeIn 0.15s ease;
  display: flex;
  flex-direction: column;
}
</style>
