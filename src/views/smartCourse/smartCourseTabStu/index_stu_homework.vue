<template>
  <div>
    <div class="stu_manage_container">
      <div v-for="item in stuLabel" :key="item.value" @click="handleClick(item.value)"
        :class="item.value === active ? 'active' : ''">{{ item.label }}</div>
    </div>
    <div class="stu_homework_main">
      <el-row :gutter="20">
        <el-col :span="12" v-for="item in res" :key="item.id">
          <el-card class="content">
            <div class="content_header">
              <div class="header_left">
                <div v-show="item.zt === '1'">进行中</div>
                <div v-show ="item.zt === '2'">已结束</div>
                <div>{{ item.zymc }}</div>
                <div v-if="item.zylx === '1'">小组作业</div>
              </div>
              <div class="header_right" v-if="item.zdzt === '1'" style="color: red;">待评分</div>
              <div class="header_right" v-if="item.zdzt === '2'" style="color: #999;">已完成</div>
            </div>
            <div class="content_main">
              <div>开始时间：{{ item.kssj }}</div>
              <div>参与小组：{{ item.cyxz }}</div>
              <div>作答时间：{{item.zdkssj}}-{{item.zdjssj}}</div>
              <div>互评时间：{{item.hpkssj}}-{{item.hpjssj}}</div>
            </div>
            <el-divider />
            <div class="content_btn">
              <el-button v-if="item.zt === '1'" type="primary" @click="goQuestion(1)">参与</el-button>
              <el-button v-else type="primary" @click="goQuestion(2)">查看</el-button>
              <div>距离互评结束还有<span>{{ item.jssj }}</span> </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
  <PopupHomeworkQuestion ref="popupHomeworkQuestion" />
  <PopupHomeworkAttachment ref="popupHomeworkAttachment" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import PopupHomeworkQuestion from './popup_homework_question.vue'
import PopupHomeworkAttachment from './popup_homework_attachment.vue'
const { proxy } = getCurrentInstance()
const stuLabel = ref([
  {
    label: '全部',
    value: '1'
  },
  {
    label: '未评分',
    value: '2'
  },
  {
    label: '已评分',
    value: '3'
  },
])
const res = ref([
  {
    id:'1',
    zt:'1',
    zymc:'作业名称啊啊啊',
    zylx:'1',
    zdzt:'1',
    kssj:'2022.1.20',
    cyxz:'探花小队',
    zdkssj:'2025-05-26 10:00:01',
    zdjssj:'2025-05-26 10:00:01',
    hpkssj:'2025-05-26 10:00:01',
    hpjssj: '2025-05-26 10:00:01',
    jssj:'2天10小时'
  },
  {
    id:'2',
    zt:'2',
    zymc:'作业名称啊啊啊',
    zylx:'1',
    zdzt:'2',
    kssj:'2022.1.20',
    cyxz:'探花小队',
    zdkssj:'2025-05-26 10:00:01',
    zdjssj:'2025-05-26 10:00:01',
    hpkssj:'2025-05-26 10:00:01',
    hpjssj: '2025-05-26 10:00:01',
    jssj:'2天10小时'
  },
])
const active = ref('1')
const handleClick = (value) => {
  active.value = value
}
// goQuestion
const popupHomeworkQuestion = ref(null)
const popupHomeworkAttachment = ref(null)
const goQuestion = (type) => {
  if (type === 1) {
    popupHomeworkQuestion.value.open()
  } else {
    popupHomeworkAttachment.value.open()
  }
}
</script>

<style scoped lang='scss'>
.stu_manage_container {
  display: flex;
  margin: 10px 0 20px 0;

  .active {
    background-color: rgba(85, 91, 116, 1) !important;
    color: #fff !important;
  }

  div {
    width: 98px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 4px;
    border: 1px solid rgb(126, 136, 165);
    // margin: 0.2vw 0 0 0.83vw;
    cursor: pointer;
    overflow-wrap: break-word;
    font-size: 14px;
    font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
    font-weight: normal;
    white-space: nowrap;
    margin-right: 14px;
  }
}

.stu_homework_main {
  .content {
    .content_header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header_left {
        display: flex;
        margin-top: 10px;
        div {
          margin-right: 8px;
        }

        div:nth-child(1) {
          width: 62px;
          height: 22px;
          background: #52C41A;
          border-radius: 20px;
          font-size: 14px;
          color: #FFFFFF;
          text-align: center;
          line-height: 24px;
        }

        div:nth-child(2) {
          width: 62px;
          height: 22px;
          background: #999999;;
          border-radius: 20px;
          font-size: 14px;
          color: #fff;
          text-align: center;
          line-height: 24px;
        }

        div:nth-child(3) {
          font-weight: bold;
          font-size: 18px;
        }

        div:nth-child(4) {
          font-size: 15px;
          color: #999;
          margin-top: 5px;
        }
      }
      .header_right {
        margin-top: 10px;
      }
    }

    .content_main {
      div {
        font-size: 15px;
        color: #999;
        margin-top: 10px;
      }
    }
    .content_btn {
      display: flex;
      justify-content: space-between;
      align-items: center;
      div {
        span {
          color: red;
        }
      }
    }
  }
}
</style>