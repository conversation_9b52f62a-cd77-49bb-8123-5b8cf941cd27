<template>
  <div class="stu_manage_stu_box">
    <el-row>
      <el-col :span="8" v-for="item in res" :key="item.id">
        <el-card class="content">
          <div class="content_title">
            <h4>{{ item.hdmc }}</h4>
            <p v-if="item.zt == '1'" class="content_jk">已结课</p>
            <p v-if="item.zt == '2'" class="content_sk">上课中</p>
          </div>
          <div class="content_sksj">上课时间：{{ item.sksj }}</div>
          <el-divider style="margin: 15px 0;" />
          <div class="content_btn">
            <el-button type="primary" v-if="item.zt == '1'" @click="gotoLearn(1)">查看</el-button>
            <el-button type="primary" v-if="item.zt == '2'" @click="gotoLearn(2)">上课</el-button>
            <div class="content_sksj">结课时间：{{ item.jksj }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance()
const res = ref([
  {
    id: '1',
    hdmc: '教学活动1',
    sksj: '2025-03-14周六 12:00',
    jksj: '2025-03-14 12:00',
    zt: '1',
  },
  {
    id: '2',
    hdmc: '教学活动2',
    sksj: '2025-03-14周六 12:00',
    jksj: '2025-03-14 12:00',
    zt: '2',
  },
  {
    id: '3',
    hdmc: '教学活动2',
    sksj: '2025-03-14周六 12:00',
    jksj: '2025-03-14 12:00',
    zt: '2',
  },
])
const gotoLearn = (type) => {
  proxy.$router.push('smart-course-details-stu');
}
</script>

<style scoped lang='scss'>
.stu_manage_stu_box {
  .content {
    width: 345px;
    // height: 150px;
    background: #FFFFFF;
    // border-radius: 12px 12px 12px 12px;
    // border: 2px solid #E6EBF5;
    // padding: 10px;

    .content_title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h4 {
        margin: 8px 0;
      }

      .content_jk {
        margin: 8px 0;
        width: 62px;
        height: 22px;
        background: #999999;
        border-radius: 20px;
        font-size: 14px;
        color: #FFFFFF;
        text-align: center;
        line-height: 24px;
      }

      .content_sk {
        margin: 8px 0;
        width: 62px;
        height: 22px;
        background: #52C41A;
        border-radius: 20px;
        font-size: 14px;
        color: #FFFFFF;
        text-align: center;
        line-height: 24px;
      }
    }

    .content_sksj {
      font-size: 15px;
      color: #585E76;
    }

    .content_btn {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>