<template>
  <div>
    <div>
      <div class="textbook_list" v-for="(item, index) in missionList" :key="indexKey" @click="goDetails(item.id)">
        <div class="textbook_list_left">
          <div class="textbook_list_left_top">
            <div>{{ item.missionTitle }}</div>
            <div>2025-01-20</div>
          </div>
          <div class="textbook_list_left_bottom">
            <el-text class="textbook_list_left_bottom_top" size="large" line-clamp="1" truncated>
              手动阀手动阀手动阀手手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀手动阀动阀手动阀手动阀手动阀手动阀手动阀
            </el-text>
          </div>
        </div>
        <!-- <div>
          <el-button @click="chat(2, item.id)">查看</el-button>
        </div> -->
      </div>
    </div>
  </div>
  <PopupNoticeDetails ref="popupNoticeDetails" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { delMission, getMission, listMission } from "@/api/edu/mission.js";
import PopupNoticeDetails from './popup_notice_details.vue'

const props = defineProps({
  /*  course: {
      type: Object,
      default: {},
    }*/
  classDetail: {
    type: Object,
    default: {
    },
  },
  /*测试用*/
  course: {
    type: Object,
    default: {
      /*      "createBy": null,
            "createTime": null,
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "courseId": "11",
            "courseName": "网络安全互动课堂用",
            "courseCode": "SEC1012",
            "courseType": 1,
            "categoryId": "101",
            "planId": "210",
            "bookId": "310",
            "description": "了解网络安全基础知识",
            "coverImageUrl": "https://example.com/images/sec101.jpg",
            "classroomQuestionWeight": 25,
            "homeworkWeight": 35,
            "examWeight": 40,
            "hour": 48,
            "status": 1,
            "userId": "1854076781086117972",
            "createdBy": null,
            "updatedBy": null,
            "delFlag": null,
            "classTotal": 3,
            "realName": "13840917725",
            "searchVar": null*/
      /*"createBy": null,
      "createTime": null,
      "updateBy": null,
      "updateTime": null,
      "remark": null,
      "courseId": "1943215209544761346",
      "courseName": "第六个教务课程",
      "courseCode": "999",
      "courseType": 1,
      "categoryId": "1912780772558868481",
      "planId": null,
      "bookId": "1893128341502074882",
      "description": "99",
      "coverImageUrl": "https://dutp-test.oss-cn-beijing.aliyuncs.com/1752133581962.png",
      "classroomQuestionWeight": null,
      "homeworkWeight": null,
      "examWeight": null,
      "hour": 1,
      "status": 0,
      "userId": "1854076781086117925",
      "createdBy": null,
      "updatedBy": null,
      "delFlag": null,
      "classTotal": 0,
      "realName": "学生(雪)1",
      "searchVar": null*/
    },
  },
});
const { proxy } = getCurrentInstance()
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    // classId: props.classDetail.classId
    classId: 25,
  },
});
const { queryParams } = toRefs(data)

// 状态
const statusList = ref([
  {
    label: '未开始',
    value: '1'
  },
  {
    label: '进行中',
    value: '2'
  },
  {
    label: '已结束',
    value: '3'
  }
])
const indexKey = ref(0)
const missionList = ref([
  // {
  //   id: '1',
  //   bt: '数字教材学习',
  //   zt: '1',
  //   wcrs: '10',
  //   zrs: '55',
  //   kssj: '2025-06-03 12:12:03',
  //   jssj: '2025-06-03 12:12:03'
  // },
  // {
  //   id: '2',
  //   bt: '数字教材学习',
  //   zt: '2',
  //   wcrs: '10',
  //   zrs: '55',
  //   kssj: '2025-06-03 12:12:03',
  //   jssj: '2025-06-03 12:12:03'
  // },
  // {
  //   id: '3',
  //   bt: '数字教材学习',
  //   zt: '3',
  //   wcrs: '10',
  //   zrs: '55',
  //   kssj: '2025-06-03 12:12:03',
  //   jssj: '2025-06-03 12:12:03'
  // }
])
const total = ref(0)
const missionId = ref(null)

onMounted(() => {
  console.log(props.course)
  console.log(props.classDetail)
  getList()
})

/** 查询列表 */
const getList = async () => {
  queryParams.pageNum = 1
  let response = await listMission(queryParams.value)
  missionList.value = response.rows;
  total.value = response.total;
}

// const editEndTime = (row) => {
//   console.log(row)
//   popupTextbookStart.value.open()
// }


const refreshList = () => {
  console.log("刷新")
  proxy.resetForm('queryRef')
  indexKey.value++
  handleQuery()
}

const resetQuery = () => {
  indexKey.value = 0
  proxy.resetForm('queryRef')
  handleQuery();
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}
// 聊天框 
const popupNoticeDetails = ref(null)
const goDetails = (id) => {
  popupNoticeDetails.value.open(id)
}


</script>

<style scoped lang="scss">
.textbook_set {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  margin-bottom: 10px;
}

.textbook_list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-image: url('../../../assets/images/smartCourse/smartCourseDetails/purple_bg.png');
  background-size: 100% 100px;
  background-repeat: no-repeat;
  width: 100%;
  height: 100px;
  margin-top: 15px;
  padding: 0 25px;
  cursor: pointer;
  .textbook_list_left_top {
    display: flex;
    justify-content: space-between;

    div:last-of-type {
      font-size: 15px;
      color: #585E76;
    }
  }

  .textbook_list_left_bottom {
    font-size: 16px;
    color: #585e76;
    margin-top: 10px;

    span {
      // margin-right: 20px;
    }
    .textbook_list_left_bottom_top {
      width: 1010px;
    }
  }
}

.textbook_list_left_top div {
  font-size: 20px;
  color: #333333;
}

.wks {
  border: 1px solid rgb(238, 161, 49) !important;
  color: rgb(238, 161, 49) !important;
}

.jxz {
  border: 1px solid rgb(82, 196, 26) !important;
  color: rgb(82, 196, 26) !important;
}
</style>
