<template>
  <div>
    <component :is="componentPage" @bookDetails="bookDetails" @goBack="goBack"></component>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import TextbookIndex from './textbook_index.vue'
import TextbookDetails from './textbook_details.vue'
const { proxy } = getCurrentInstance()
const componentPage = ref(TextbookIndex)
const bookDetails = (id) => {
  componentPage.value = TextbookDetails
}
const goBack = (id) => {
  componentPage.value = TextbookIndex
}
</script>

<style scoped lang='scss'>
</style>