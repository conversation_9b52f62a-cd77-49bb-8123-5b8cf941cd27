<template>
  <div>
    <el-card>
      <div style="text-align: right;">
        <el-button type="primary" plain @click="goBack">返回</el-button>
      </div>
      <el-tree :data="data" :props="defaultProps" default-expand-all @node-click="handleNodeClick" />
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance()
const emits = defineEmits(['goBack'])
const data = ref(
  [
    {
      label: '第一章：功法成长计划开启',
      children: [
        {
          label: '1. 功法道心圆满，开始顿悟',
          children: [
            {
              label: '1.1 稳住别浪——稳不住了！',
            },
          ],
        },
      ],
    },
    {
      label: '第二章：教化？强制洗脑',
      children: [
        {
          label: '1. 功法新词条：正义之盾',
          children: [
            {
              label: '1.1 炼气四层，新术法',
            },
          ],
        },
        {
          label: '2. 不屠龙你学什么斩龙剑？',
          children: [
            {
              label: '2.1 今日，斩龙',
            },
          ],
        },
      ],
    },
    {
      label: '第三章：功法与术法的协力技',
      children: [
        {
          label: '1.法力提纯，面见执法堂',
          children: [
            {
              label: '1.1斩龙剑大成，剑罡化形',
            },
          ],
        },
        {
          label: '2对阴傀宗的讨伐行动',
          children: [
            {
              label: '2.1出发，飞舟论道',
            },
          ],
        },
      ],
    },
  ]
)
const goBack = () => {
  emits('goBack')
}
</script>

<style scoped lang='scss'></style>