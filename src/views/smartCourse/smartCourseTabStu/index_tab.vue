<template>
  <div class="class_tab_module">
    <el-tabs v-model="activeName" stretch="false" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane v-for="(item, index) in tabList" :key="index" :name="item.name">
        <template #label>
          <span class="custom-tabs-label">
            <img v-show="activeName == item.name" :src="item.srcBlue" />
            <img v-show="activeName != item.name" :src="item.src" />
            <span>{{ item.label }}</span>
          </span>
        </template>
        <component :is="item.component" :course="course" :classDetail="classDetail"></component>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
// 统一导入所有图片资源
import classTabKt from '@/assets/images/smartCourse/smartCourseTab/class_tab_kt.png'
import classTabXsgl from '@/assets/images/smartCourse/smartCourseTab/class_tab_xsgl.png'
import classTabXxxz from '@/assets/images/smartCourse/smartCourseTab/class_tab_xxxz.png'
import classTabHdtl from '@/assets/images/smartCourse/smartCourseTab/class_tab_hdtl.png'
import classTabZttl from '@/assets/images/smartCourse/smartCourseTab/class_tab_zttl.png'
import classTabJcxx from '@/assets/images/smartCourse/smartCourseTab/class_tab_jcxx.png'
import classTabKs from '@/assets/images/smartCourse/smartCourseTab/class_tab_ks.png'
import classTabCj from '@/assets/images/smartCourse/smartCourseTab/class_tab_cj.png'
import classTabTjfx from '@/assets/images/smartCourse/smartCourseTab/class_tab_tjfx.png'
// 明亮的
import classTabKtBlue from '@/assets/images/smartCourse/smartCourseTab/class_tab_kt_blue.png'
import classTabXsglBlue from '@/assets/images/smartCourse/smartCourseTab/class_tab_xsgl_blue.png'
import classTabXxxzBlue from '@/assets/images/smartCourse/smartCourseTab/class_tab_xxxz_blue.png'
import classTabHdtlBlue from '@/assets/images/smartCourse/smartCourseTab/class_tab_hdtl_blue.png'
import classTabZttlBlue from '@/assets/images/smartCourse/smartCourseTab/class_tab_zttl_blue.png'
import classTabJcxxBlue from '@/assets/images/smartCourse/smartCourseTab/class_tab_jcxx_blue.png'
import classTabKsBlue from '@/assets/images/smartCourse/smartCourseTab/class_tab_ks_blue.png'
import classTabCjBlue from '@/assets/images/smartCourse/smartCourseTab/class_tab_cj_blue.png'
import classTabTjfxBlue from '@/assets/images/smartCourse/smartCourseTab/class_tab_tjfx_blue.png'
// tab内容
// 课堂
import IndexClassroom from './index_classroom.vue'
// 代评作业
import IndexStuHomework from './index_stu_homework.vue'
// 学习小组
import IndexLearnGroup from './index_learn_group.vue'
// 互动讨论
import IndexPanelDiscussion from './index_panel_discussion.vue'
// 主题讨论
import IndexTopicDiscussion from './index_topic_discussion.vue'
// 教材学习 
import IndexBook from './index_book.vue'
// 考试
import IndexNotice from './index_notice.vue'
// 成绩
import IndexScore from './index_score.vue'

/*选中的课程和班级*/
const props = defineProps({
  course: {
    type: Object,
    default: {},
  },
  classDetail: {
    type: Object,
    default: {},
  }
});

onMounted(() => {
  console.log(props.classDetail)
  console.log(props.course)
})

// tab数据
const tabList = ref([
  {
    name: 'kt',
    label: '课堂教学',
    src: classTabKt,
    srcBlue: classTabKtBlue,
    component: IndexClassroom
  },
  {
    name: 'xsgl',
    label: '待评作业',
    src: classTabXsgl,
    srcBlue: classTabXsglBlue,
    component: IndexStuHomework
  },
  {
    name: 'jcxx',
    label: '教材学习',
    src: classTabJcxx,
    srcBlue: classTabJcxxBlue,
    component: IndexBook
  },
  {
    name: 'hdtl',
    label: '互动讨论',
    src: classTabHdtl,
    srcBlue: classTabHdtlBlue,
    component: IndexPanelDiscussion
  },
  {
    name: 'zttl',
    label: '主题讨论',
    src: classTabZttl,
    srcBlue: classTabZttlBlue,
    component: IndexTopicDiscussion
  },
  {
    name: 'bjgg',
    label: '班级公告',
    src: classTabKs,
    srcBlue: classTabKsBlue,
    component: IndexNotice
  },
  {
    name: 'cj',
    label: '我的成绩',
    src: classTabCj,
    srcBlue: classTabCjBlue,
    component: IndexScore
  },
  {
    name: 'xxxz',
    label: '我的小组',
    src: classTabXxxz,
    srcBlue: classTabXxxzBlue,
    component: IndexLearnGroup
  },
  {
    name: 'tjfx',
    label: '统计分析',
    src: classTabTjfx,
    srcBlue: classTabTjfxBlue
  }
])
const activeName = ref('kt')
</script>
<style scoped lang="scss">
.class_tab_module {
  background: #fff;
  // position: absolute;
  // top: 18vw;
  // left: 13.5vw;
  // width: 72.1vw;
  padding: 20px;
  border-radius: 8px;
  min-height: 300px;
  margin-bottom: 30px;

  .custom-tabs-label {
    display: inline-flex;
    /* 或 inline-block */
    align-items: center;

    /* 垂直居中 */
    img {
      margin-right: 2px;
    }
  }
}
</style>
