<template>
  <div class="index_classroom">
    <div>
      <h3>学生成绩</h3>
      <p class="index_score_name">
        <span>学号：{{ res.xh }}</span>
        <span>姓名：{{ res.xm }}</span>
      </p>
    </div>
    <el-table :data="tableData" ref="table" class="index_classroom_table">
      <el-table-column align="center" prop="kttwcj" label="课件成绩（15%）" />
      <el-table-column align="center" prop="zycj" label="课堂提问成绩（15%）" />
      <el-table-column align="center" prop="kscj" label="作业成绩（30%）" />
      <el-table-column align="center" prop="pscj" label="考试成绩（50%）" />
      <el-table-column align="center" prop="zcj" label="总计" />
    </el-table>
    <h4>评语：</h4>
    <p class="index_score_comment">{{ res.py }}</p>
  </div>
  <PopupScoreRemark ref="popupScoreRemark" />
  <PopupScoreEdit ref="popupScoreEdit" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance()
const table = ref();
const res = ref({
  xh:'212343434',
  xm: '阿松大',
  py:'士大夫随风倒手动阀手动士大夫随风倒手动阀手动阀手动阀手动阀风格化风格化风格和风格和风格和风格和士大夫随风倒手动阀手动阀手动阀手动阀风格化风格化风格和风格和风格和风格和士大夫随风倒手动阀手动阀手动阀手动阀风格化风格化风格和风格和风格和风格和士大夫随风倒手动阀手动阀手动阀手动阀风格化风格化风格和风格和风格和风格和阀手动阀手动阀风格化风格化风格和风格和风格和风格和'
})
const tableData = ref([
  {
    id: '1',
    xh: '1111111',
    xm: '陈泽',
    jcxxcj: '20',
    cscj: '20',
    kttwcj: '20',
    zycj: '20',
    kscj: '20',
    pscj: '20',
    zcj: '100'
  }
])

</script>

<style scoped lang="scss">
.index_classroom {
  margin-top: 8px;

  .index_classroom_table {
    width: 100%;
    margin-top: 20px;
  }

  .index_score_set {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    margin-bottom: 10px;
  }
}
.index_score_name {
  color: #585E76;
  span {
    margin-right: 30px;
  }
}
.index_score_comment {
  padding: 0 20px;
}
</style>
