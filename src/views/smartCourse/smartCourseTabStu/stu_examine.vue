<template>
  <div class="stu_zy_page_index">
    <el-card class="stu_card_item" @click="lookNow(item.url)" v-for="item in res" :key="item.id">
      <img :src="item.img" alt="">
      <div>
        <p>{{ item.mc }}</p>
        <p>发布时间：{{ item.sj }}</p>
      </div>
    </el-card>
  </div>
  <el-dialog v-model="dialogVisible" title="Tips" width="1000" :before-close="handleClose" style="margin-top: 0 !important">
    <iframe :src="urlConf" width="100%" height="600px" frameborder="0" title="预览"></iframe>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance()
const dialogVisible = ref(false)
const res = ref([
  {
    id: '1',
    img: 'https://pic.rmb.bdstatic.com/bjh/news/660b630f0942d7d0ace1ab4166aafbc41169.jpeg',
    url: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1753853747275.doc',
    mc: '课件名称0001',
    sj: '2020-03-02 10:24:05',
  },
  {
    id: '2',
    img: 'https://pic.rmb.bdstatic.com/bjh/news/660b630f0942d7d0ace1ab4166aafbc41169.jpeg',
    url: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1753853747275.doc',
    mc: '课件名称0002',
    sj: '2020-03-02 10:24:05',
  },
  {
    id: '3',
    img: 'https://pic.rmb.bdstatic.com/bjh/news/660b630f0942d7d0ace1ab4166aafbc41169.jpeg',
    url: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1753853716899.ppt',
    mc: '课件名称0003',
    sj: '2020-03-02 10:24:05',
  }
])
const urlConf = ref()
const lookNow = (url) => {
  dialogVisible.value = true
  const wr = 'https://view.officeapps.live.com/op/view.aspx?src='
  urlConf.value = wr + url
}
</script>

<style scoped lang='scss'>
.stu_zy_page_index {
  display: flex;
  justify-content: space-between;

  .stu_card_item {
    width: 340px;
    cursor: pointer;

    div {
      margin-left: 10px;
    }

    img {
      width: 50px;
      height: 50px;
    }

    p:last-of-type {
      color: #999;
      font-size: 14px;
    }

    p {
      margin: 0;
    }
  }
}

::v-deep .el-card__body {
  display: flex !important;
  align-items: center !important;
}
</style>