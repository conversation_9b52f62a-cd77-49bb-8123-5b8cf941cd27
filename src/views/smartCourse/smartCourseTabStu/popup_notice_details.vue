<template>
  <el-dialog v-model="dialogVisible" title="公告详情" width="900" :before-close="handleClose"
    style="margin-top: 10vh !important">
  <h3>{{ res.title }}</h3>
  <p>{{ res.startDate }}</p>
  <p v-html="res.content"></p>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
const res = ref({
  title: "标题标题标题标题",
  content: "内容内容内容内容内内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容容内容内容内容内容内容内容",
  startDate: "2025-08-01"
})
const dialogVisible = ref(false)
// 开启弹窗
const open = () => {
  dialogVisible.value = true
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
h3 {
  text-align: center;
}
p:first-of-type {
  text-align: right;
  color: #585E76;
}
</style>
