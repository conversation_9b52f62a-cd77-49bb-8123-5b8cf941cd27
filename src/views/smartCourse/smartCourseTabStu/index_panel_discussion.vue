<template>
  <div>
    <div class="textbook_set">
      <div>
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px">
          <el-form-item label="活动名称：" prop="missionTitle" label-width="100">
            <el-input v-model="queryParams.missionTitle" placeholder="请输入活动名称" />
          </el-form-item>
          <el-form-item label="状态：" prop="status">
            <el-select v-model="queryParams.status" placeholder="状态" style="width: 200px">
              <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div>
      <div class="textbook_list" v-for="(item, index) in missionList" :key="indexKey">
        <div class="textbook_list_left">
          <div class="textbook_list_left_top">
            <div>{{ item.missionTitle }}</div>
            <div class="wks" v-if="item.status == '1'">未开始</div>
            <div class="jxz" v-if="item.status == '2'">进行中</div>
            <div v-if="item.status == '3'">已结束</div>
          </div>
          <div class="textbook_list_left_bottom">
            <span>参与人数：{{ item.finishCount }}</span>
            <span v-if="item.status != '1'">开始时间：{{ item.kssj }}</span>
            <span v-if="item.status != '1'">结束时间：{{ item.jssj }}</span>
          </div>
        </div>
        <div>
          <el-button v-if="item.status == '1'" @click="chat(1,item.id)">参与讨论</el-button>
          <el-button v-if="item.status != '1'" @click="chat(2,item.id)">查看</el-button>
        </div>
      </div>
    </div>
  </div>
    <PopupPanelChat ref="popupPanelChat" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { delMission, getMission, listMission } from "@/api/edu/mission.js";
import PopupPanelChat from './popup_panel_chat.vue'

const props = defineProps({
  /*  course: {
      type: Object,
      default: {},
    }*/
  classDetail: {
    type: Object,
    default: {
    },
  },
  /*测试用*/
  course: {
    type: Object,
    default: {
/*      "createBy": null,
      "createTime": null,
      "updateBy": null,
      "updateTime": null,
      "remark": null,
      "courseId": "11",
      "courseName": "网络安全互动课堂用",
      "courseCode": "SEC1012",
      "courseType": 1,
      "categoryId": "101",
      "planId": "210",
      "bookId": "310",
      "description": "了解网络安全基础知识",
      "coverImageUrl": "https://example.com/images/sec101.jpg",
      "classroomQuestionWeight": 25,
      "homeworkWeight": 35,
      "examWeight": 40,
      "hour": 48,
      "status": 1,
      "userId": "1854076781086117972",
      "createdBy": null,
      "updatedBy": null,
      "delFlag": null,
      "classTotal": 3,
      "realName": "13840917725",
      "searchVar": null*/
      /*"createBy": null,
      "createTime": null,
      "updateBy": null,
      "updateTime": null,
      "remark": null,
      "courseId": "1943215209544761346",
      "courseName": "第六个教务课程",
      "courseCode": "999",
      "courseType": 1,
      "categoryId": "1912780772558868481",
      "planId": null,
      "bookId": "1893128341502074882",
      "description": "99",
      "coverImageUrl": "https://dutp-test.oss-cn-beijing.aliyuncs.com/1752133581962.png",
      "classroomQuestionWeight": null,
      "homeworkWeight": null,
      "examWeight": null,
      "hour": 1,
      "status": 0,
      "userId": "1854076781086117925",
      "createdBy": null,
      "updatedBy": null,
      "delFlag": null,
      "classTotal": 0,
      "realName": "学生(雪)1",
      "searchVar": null*/
    },
  },
});
const { proxy } = getCurrentInstance()
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    // classId: props.classDetail.classId
    classId: 25,
  },
});
const { queryParams } = toRefs(data)

// 状态
const statusList = ref([
  {
    label: '未开始',
    value: '1'
  },
  {
    label: '进行中',
    value: '2'
  },
  {
    label: '已结束',
    value: '3'
  }
])
const indexKey = ref(0)
const missionList = ref([
  // {
  //   id: '1',
  //   bt: '数字教材学习',
  //   zt: '1',
  //   wcrs: '10',
  //   zrs: '55',
  //   kssj: '2025-06-03 12:12:03',
  //   jssj: '2025-06-03 12:12:03'
  // },
  // {
  //   id: '2',
  //   bt: '数字教材学习',
  //   zt: '2',
  //   wcrs: '10',
  //   zrs: '55',
  //   kssj: '2025-06-03 12:12:03',
  //   jssj: '2025-06-03 12:12:03'
  // },
  // {
  //   id: '3',
  //   bt: '数字教材学习',
  //   zt: '3',
  //   wcrs: '10',
  //   zrs: '55',
  //   kssj: '2025-06-03 12:12:03',
  //   jssj: '2025-06-03 12:12:03'
  // }
])
const total = ref(0)
const missionId = ref(null)

onMounted(() => {
  console.log(props.course)
  console.log(props.classDetail)
  getList()
})

/** 查询列表 */
const getList = async () => {
  queryParams.pageNum = 1
  let response = await listMission(queryParams.value)
  missionList.value = response.rows;
  total.value = response.total;
}

// const editEndTime = (row) => {
//   console.log(row)
//   popupTextbookStart.value.open()
// }


const refreshList = () => {
  console.log("刷新")
  proxy.resetForm('queryRef')
  indexKey.value++
  handleQuery()
}

const resetQuery = () => {
  indexKey.value = 0
  proxy.resetForm('queryRef')
  handleQuery();
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}
// 聊天框 
const popupPanelChat = ref(null)
const chat = (id) => {
  popupPanelChat.value.open(id)
}


</script>

<style scoped lang="scss">
.textbook_set {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  margin-bottom: 10px;
}
.textbook_list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-image: url('../../../assets/images/smartCourse/smartCourseDetails/blue_bg.png');
  background-size: 100% 100px;
  background-repeat: no-repeat;
  width: 100%;
  height: 100px;
  margin-top: 15px;
  padding: 0 25px;
  .textbook_list_left_top {
    display: flex;
  }
  .textbook_list_left_bottom {
    font-size: 16px;
    color: #585e76;
    margin-top: 10px;
    span {
      margin-right: 20px;
    }
  }
}
.textbook_list_left_top div:first-child {
  font-size: 20px;
  color: #333333;
  margin-right: 10px;
}
.textbook_list_left_top div:last-child {
  width: 84px;
  height: 26px;
  border-radius: 20px 20px 20px 20px;
  border: 1px solid #969aaa;
  font-size: 14px;
  color: #969aaa;
  text-align: center;
  line-height: 26px;
}
.wks {
  border: 1px solid rgb(238, 161, 49) !important;
  color: rgb(238, 161, 49) !important;
}
.jxz {
  border: 1px solid rgb(82, 196, 26)!important;
  color: rgb(82, 196, 26) !important;
}
</style>
