<template>
  <div class="stu_smart_page_index">
    <div class="smart_course_tab_stu">
      <div class="stu_title">
        <el-breadcrumb :separator-icon="ArrowRight">
          <el-breadcrumb-item :to="{ path: '/' }">班课列表</el-breadcrumb-item>
          <el-breadcrumb-item>班课名称</el-breadcrumb-item>
        </el-breadcrumb>
        <el-button type="primary" plain @click="goBack">返回</el-button>
      </div>
      <div class="stu_main">
        <div class="main_info">
          <div class="main_info_left">
            <h3>班级名称班级名称班级名称</h3>
            <p>课程名称课程名称课程名称</p>
            <p>
              <span>学生人数：50人</span>
              <span>授课教师：张老师</span>
            </p>
          </div>
          <div class="main_info_right">
            <img :src="stuBgKctp" alt="">
            <img :src="stuBgWttp" alt="">
            <img :src="stuBgNltp" alt="">
          </div>
        </div>
        <el-divider />
        <div class="main_bg">
          <div class="bg_gl">关联教材</div>
          <div class="bg_book">
            <img src="https://edu-image.nosdn.127.net/6b72637b-9edf-4d2b-9e50-a73d5735c8d2" alt="" />
            <div class="book_info">
              <h4>新一代技术本</h4>
              <p>主 编：王宏观、李糖</p>
              <p>辅助教材：9本</p>
              <div class="info_jd">
                <div>阅读进度：40%</div>
                <div>
                  <el-progress :percentage="50" />
                </div>
              </div>
            </div>
            <div>
              <el-icon color="#999">
                <ArrowRightBold />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
      <div class="stu_tab">
        <IndexTab />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import { ArrowRight } from '@element-plus/icons-vue'
const { proxy } = getCurrentInstance()
import IndexTab from './index_tab.vue'
import stuBgKctp from '@/assets/images/smartCourse/smartCourseTabStu/stu_bg_kctp.png'
import stuBgWttp from '@/assets/images/smartCourse/smartCourseTabStu/stu_bg_wttp.png'
import stuBgNltp from '@/assets/images/smartCourse/smartCourseTabStu/stu_bg_nltp.png'
const goBack = () => {
  proxy.$router.push({ path: '/smart-course-list' })
}
</script>

<style scoped lang='scss'>
.stu_smart_page_index {
  background: #F2F5FA;
  width: 100%;
}

.smart_course_tab_stu {
  width: 1100px;
  margin: auto;

  .stu_title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
  }

  .stu_main {
    width: 100%;
    background: #fff;
    border-radius: 8px;

    .main_info {
      width: 100%;
      display: flex;

      .main_info_left {
        width: 400px;
        height: 114px;
        padding-left: 20px;

        h3 {
          margin-bottom: 0;
        }

        p {
          font-size: 16px;
          color: #585E76;
        }

        p:first-of-type {
          margin-top: 5px;
          margin-bottom: 0;
        }

        p:last-child {
          span:first-child {
            margin-right: 40px;
          }
        }
      }

      .main_info_right {
        width: 700px;
        height: 114px;
        background-image: url('../../../assets/images/smartCourse/smartCourseTabStu/stu_bg.png');
        // background-size: contain;
        background-size: 100% 100%;
        /* 保持图片完整显示 */
        background-position: right top;
        /* 顶部居中对齐 */
        background-repeat: no-repeat;

        img {
          width: 100px;
          object-fit: contain;
          margin-left: 8px;
          margin-top: 22px;
          cursor: pointer;
        }

        img:first-child {
          margin-left: 115px;
        }
      }
    }

    .main_bg {
      // width: 700px;
      padding-left: 20px;

      .bg_gl {
        font-size: 12px;
        color: #333333;
        margin-top: 10px;
      }

      .bg_book {
        display: flex;
        align-items: center;
        padding-bottom: 30px;
        cursor: pointer;

        img {
          width: 72px;
          height: 98px;
          margin-top: 3px;
        }

        .book_info {
          width: 500px;
          margin-left: 10px;

          h4 {
            margin: 5px 0;
            font-size: 17px;
            color: #333333;
          }

          p {
            margin: 5px 0;
            font-size: 15px;
            color: #969AAA;
          }

          .info_jd {
            display: flex;
            align-items: center;

            div:first-of-type {
              color: #969AAA;
            }

            div:last-of-type {
              width: 300px;
              margin-left: 5px;
            }
          }
        }

        div:last-of-type {}
      }
    }
  }

  .stu_tab {
    margin-top: 20px;
  }
}

::v-deep .el-progress__text {
  display: none;
}
</style>