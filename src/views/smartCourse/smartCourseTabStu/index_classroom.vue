<template>
  <div>
    <div class="stu_manage_classroom_header">
      <div class="stu_manage_container">
        <div v-for="item in stuLabel" :key="item.value" @click="handleClick(item.value)"
          :class="item.value === active ? 'active' : ''">{{ item.label }}</div>
      </div>
      <el-date-picker v-model="value2" type="date" placeholder="请选择日期" :disabled-date="disabledDate"
        :shortcuts="shortcuts" :size="size" />
    </div>
    <StuManage v-if="active === '1'" />
    <StuExamine v-if="active === '2'" />
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import StuManage from './stu_manage.vue'
import StuExamine from './stu_examine.vue'

const { proxy } = getCurrentInstance()
const stuLabel = ref([
  {
    label: '课堂活动',
    value: '1'
  },
  {
    label: '内容资源',
    value: '2'
  }
])
const active = ref('1')
const handleClick = (value) => {
  active.value = value
}
</script>

<style scoped lang='scss'>
.stu_manage_classroom_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stu_manage_container {
  display: flex;
  margin: 10px 0 20px 0;

  .active {
    background-color: rgba(85, 91, 116, 1) !important;
    color: #fff !important;
  }

  div {
    width: 98px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 4px;
    border: 1px solid rgb(126, 136, 165);
    // margin: 0.2vw 0 0 0.83vw;
    cursor: pointer;
    overflow-wrap: break-word;
    font-size: 14px;
    font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
    font-weight: normal;
    white-space: nowrap;
    margin-right: 14px;
  }
}
</style>