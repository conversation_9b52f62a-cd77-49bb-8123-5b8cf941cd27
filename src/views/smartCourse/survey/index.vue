<template>
  <div class="survey-page-container">
    <div class="filters">
      <div class="filter-content">
        <el-breadcrumb :separator-icon="ArrowRight" class="breadcrumb_text">
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item>课堂学习</el-breadcrumb-item>
          <el-breadcrumb-item>问卷调查</el-breadcrumb-item>
        </el-breadcrumb>
        <el-button class="btn" type="primary" @click="goBack">返回</el-button>
      </div>
    </div>

    <div class="content-container">
      <el-card class="content-wrapper-card">
        <div class="header">
          <div 
            v-if="survey.status !== null && survey.status !== undefined"
            class="status-tag" 
            :class="{ ongoing: survey.status == 0, completed: survey.status == 1 }">
            {{ survey.status == 0 ? '进行中' : '已完成' }}
          </div>
          <h1 class="survey-title">{{ survey.surveyName }}</h1>
        </div>
        <div v-if="!isSubmitted" class="content-wrapper">
          <SurveyQuestion 
            :questions="questions" 
            v-model="studentAnswers"
            :read-only="isReadOnly"
          />
        </div>
        <div v-else class="submission-success">
          <el-result
            icon="success"
            title="提交成功"
            sub-title="感谢您的参与，您的回答已成功提交。"
          >
          </el-result>
        </div>
      </el-card>
    </div>

    <div class="page-footer">
      <el-button v-if="!isReadOnly && !isSubmitted" type="primary" size="large" @click="submitSurvey" :disabled="isSubmitDisabled">提交</el-button>
      <el-button v-else type="primary" size="large" @click="goBack">返回列表</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ArrowRight } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import SurveyQuestion from './components/SurveyQuestion.vue';
import { getSurvey, getSurveyDetails } from '@/api/edu/survey';
import { addAnswer } from '@/api/edu/surveyAnswer';
import useUserStore from '@/store/modules/user';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

const surveyId = computed(() => route.params.surveyId);

const survey = reactive({
  surveyName: '加载中...',
  surveyTopic: '',
  status: 0, // 0-进行中，1-已完成 (默认为进行中)
  courseId: null,
});
const questions = ref([]);
const studentAnswers = reactive({});
const isSubmitted = ref(false);
const isReadOnly = ref(false);

const isSubmitDisabled = computed(() => {
  if (questions.value.length === 0) {
    return true;
  }
  const answeredCount = Object.keys(studentAnswers).length;
  return answeredCount < questions.value.length;
});

const fetchSurveyData = async () => {
  if (!surveyId.value) return;
  try {
    // 使用 Promise.all 并行获取问卷基本信息和问题详情
    const [surveyRes, questionsRes] = await Promise.all([
      getSurvey(surveyId.value),
      getSurveyDetails(surveyId.value)
    ]);

    // 处理问卷基本信息
    if (surveyRes.data) {
      survey.surveyName = surveyRes.data.surveyName;
      survey.surveyTopic = surveyRes.data.surveyTopic;
      // survey.status = surveyRes.data.status; // 状态由后续的答案检查来决定
      survey.courseId = surveyRes.data.courseId;
    } else {
      ElMessage.error('加载问卷信息失败。');
    }

    // 处理问题列表及用户答案
    if (questionsRes.data) {
      questions.value = questionsRes.data;
      let hasExistingAnswers = false;

      // 预先填充已有的答案
      questions.value.forEach(q => {
        if (q.userAnswer && q.userAnswer.answerValue) {
          hasExistingAnswers = true;
          // optionType 1 代表多选题，后端返回的是逗号分隔的字符串，需要转换为数组
          if (q.optionType === 1 && typeof q.userAnswer.answerValue === 'string') {
            studentAnswers[q.questionId] = q.userAnswer.answerValue.split(',');
          } else {
            studentAnswers[q.questionId] = q.userAnswer.answerValue;
          }
        }
      });

      if (hasExistingAnswers) {
        isReadOnly.value = true;
        survey.status = 1; // 已完成
      } else {
        survey.status = 0; // 进行中
      }

    } else {
      // 即使没有问题，也可能是一个空的问卷，不报错
      questions.value = [];
      survey.status = 0; // 进行中
    }
  } catch (error) {
    console.error('获取问卷数据失败:', error);
    ElMessage.error('加载问卷数据失败，请稍后重试。');
  }
};

onMounted(fetchSurveyData);

const submitSurvey = () => {
  const answeredCount = Object.keys(studentAnswers).length;
  const totalQuestions = questions.value.length;

  let confirmMessage = '问卷已完成，确定要提交吗?';
  if (answeredCount < totalQuestions) {
    confirmMessage = `您还有 ${totalQuestions - answeredCount} 道题未作答，确定要提交吗?`;
  }

  ElMessageBox.confirm(confirmMessage, '提交确认', {
    confirmButtonText: '确定提交',
    cancelButtonText: '继续作答',
    type: 'warning',
  }).then(async () => {
    try {
      const answerItems = Object.entries(studentAnswers)
        .map(([questionId, answerValue]) => {
          const question = questions.value.find(q => String(q.questionId) === String(questionId));
          if (!question) return null;

          let formattedValue = answerValue;
          if (Array.isArray(answerValue)) {
            formattedValue = answerValue.join(',');
          }

          if (formattedValue === null || formattedValue === undefined || formattedValue === '') {
            return null;
          }

          return {
            surveyId: surveyId.value,
            courseId: survey.courseId,
            questionId: questionId,
            questionType: question.optionType,
            answerValue: formattedValue,
          };
        })
        .filter(Boolean); // 过滤掉无效答案

      if (answerItems.length === 0) {
        ElMessage.info('您尚未有效回答任何问题，无法提交。');
        return;
      }

      const payload = {
        surveyId: surveyId.value,
        userId: userStore.id,
        courseId: survey.courseId,
        answerItems: answerItems, // 将所有答案条目作为数组提交
      };
      
      await addAnswer(payload);
      ElMessage.success('问卷提交成功！');
      isSubmitted.value = true;
    } catch (error) {
      console.error('问卷提交失败:', error);
      ElMessage.error('提交失败，请稍后再试。');
    }
  }).catch(() => {
    ElMessage.info('已取消提交');
  });
};

const goBack = () => {
  router.back();
};
</script>

<style lang="scss" scoped>
.survey-page-container {
  background-color: #f0f2f5;
  min-height: calc(100vh - 90px);
  padding-bottom: 20px;
}

.filters {
  width: 70%;
  margin: 0 auto;
  padding: 20px 0;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.breadcrumb_text {
  font-size: 14px;
}

.content-container {
  width: 70%;
  margin: 0 auto;
}

.content-wrapper-card {
  width: 100%;
  .header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 20px;
    .survey-title {
      font-size: 24px;
      font-weight: bold;
      color: #333;
    }
    .status-tag {
      color: white;
      padding: 4px 12px;
      border-radius: 15px;
      font-size: 14px;
      margin-right: 16px;
      &.ongoing {
        background-color: #67c23a; /* 绿色 */
      }
      &.completed {
        background-color: #1890ff; /* 蓝色 */
      }
    }
  }
}

.content-wrapper {
  padding: 0 40px;
}

.submission-success {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.page-footer {
  padding: 20px 0;
  text-align: center;
  background-color: #f0f2f5;
}
</style>
