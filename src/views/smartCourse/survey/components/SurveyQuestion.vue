<template>
  <div class="survey-questions">
    <div v-for="(question, index) in questions" :key="question.questionId" class="question-item">
      <div class="question-title">
        <span class="question-index">{{ index + 1 }}.</span>
        <span class="question-type-tag">{{ question.optionType === 0 ? '单选' : '多选' }}</span>
        <span class="question-content">{{ question.questionContent }}</span>
      </div>

      <div class="question-options">
        <!-- 单选题 -->
        <el-radio-group
          v-if="question.optionType === 0"
          v-model="localAnswers[question.questionId]"
          @change="updateAnswer"
          class="option-group"
          :disabled="readOnly"
        >
          <el-radio
            v-for="option in question.options"
            :key="option.optionId"
            :label="option.optionId"
            class="option-radio"
          >
            {{ option.questionContent }}
            <span v-if="readOnly" class="selection-rate"> ({{ option.selectionRate }}%)</span>
          </el-radio>
        </el-radio-group>
        <!-- 多选题 -->
        <el-checkbox-group
          v-if="question.optionType === 1"
          v-model="localAnswers[question.questionId]"
          @change="updateAnswer"
          class="option-group"
          :disabled="readOnly"
        >
          <el-checkbox
            v-for="option in question.options"
            :key="option.optionId"
            :label="option.optionId"
            class="option-checkbox"
          >
            {{ option.questionContent }}
            <span v-if="readOnly" class="selection-rate"> ({{ option.selectionRate }}%)</span>
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, reactive, watch } from 'vue';

const props = defineProps({
  questions: {
    type: Array,
    required: true,
    default: () => []
  },
  modelValue: { // For v-model
    type: Object,
    default: () => ({})
  },
  readOnly: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue']);

const localAnswers = reactive({});

// Sync localAnswers with the modelValue prop from parent
watch(() => props.modelValue, (newAnswers) => {
  Object.keys(localAnswers).forEach(key => delete localAnswers[key]);
  Object.assign(localAnswers, newAnswers);
}, { deep: true, immediate: true });

// Initialize structure for questions not in modelValue (for new surveys)
watch(() => props.questions, (newQuestions) => {
  if (props.readOnly) return; // No need to initialize for readonly view
  newQuestions.forEach(q => {
    if (!Object.prototype.hasOwnProperty.call(localAnswers, q.questionId)) {
      localAnswers[q.questionId] = q.optionType === 1 ? [] : null;
    }
  });
}, { deep: true, immediate: true });


const updateAnswer = () => {
  const answerPayload = { ...localAnswers };
  
  Object.keys(answerPayload).forEach(key => {
    const val = answerPayload[key];
    if (val === null || val === undefined || (Array.isArray(val) && val.length === 0)) {
      delete answerPayload[key];
    }
  });

  emit('update:modelValue', answerPayload);
};
</script>

<style lang="scss" scoped>
.survey-questions {
  .question-item {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #fafafa;
  }

  .question-title {
    font-size: 16px;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    .question-index {
      font-weight: bold;
      margin-right: 8px;
    }

    .question-type-tag {
      font-size: 12px;
      background-color: #ecf5ff;
      color: #409eff;
      padding: 2px 8px;
      border-radius: 4px;
      margin-right: 12px;
      flex-shrink: 0;
    }
  }

  .question-options {
    margin-left: 25px;
  }

  .option-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .option-radio, .option-checkbox {
    // 允许选项内容换行
    height: auto;
    white-space: normal;
    align-items: flex-start;
    :deep(.el-radio__label), :deep(.el-checkbox__label) {
        line-height: 1.5;
    }
  }
}

.selection-rate {
  color: #909399;
  font-size: 13px;
  margin-left: 8px;
}
</style> 