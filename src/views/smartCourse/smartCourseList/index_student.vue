<template>
  <div class="course-section">
    <div class="section_1 flex-row">
      <div class="group_3 flex-col">
        <div class="section_2 flex-col">
          <div class="box_4 flex-row">
            <div class="section_3 flex-col"></div>
          </div>
          <div class="text-wrapper_1 flex-row">
            <span class="text_8">{{ userInfo?.realName }}</span>
          </div>
          <div class="text-wrapper_1 flex-row">
            <span class="text_8">{{ userInfo?.schoolName }}</span>
          </div>
          <div class="text-wrapper_2 flex-row">
            <!-- <span class="text_9">当前身份：{{ userInfo?.userType == '2' ? '教师' : '学生' }}</span> -->
            <span class="text_9">当前身份：学生</span>
            <!-- <el-button class="switch-btn">切换</el-button> -->
            <el-dropdown trigger="click" @command="handleCommand">
              <el-button class="switch-btn" type="primary"> 切换 </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="2">教师</el-dropdown-item>
                  <el-dropdown-item command="1">学生</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="image-wrapper_1 flex-col">
            <el-avatar class="image_1" :src="userInfo?.avatar" />
          </div>
        </div>
        <div class="section_4 flex-col">
          <div
            class="section_5 flex-row"
            :style="{
              'background-image': menuIndex == 1 ? 'linear-gradient(90deg,rgba(34, 107, 255, 1) 0,rgba(4, 72, 210, 1) 100%)' : 'none'
            }"
            @click="toMyClass"
          >
            <div class="image-text_1 flex-row justify-between">
              <img class="thumbnail_1" referrerpolicy="no-referrer" :src="menuIndex == 1 ? myClassW : myClassB" />
              <span
                class="text-group_1"
                :style="{
                  color: menuIndex == 1 ? 'rgba(255, 255, 255, 1)' : 'rgba(51, 51, 51, 1)'
                }"
                >我的班课</span
              >
            </div>
          </div>
          <div
            class="section_6 flex-row"
            :style="{
              'background-image': menuIndex == 2 ? 'linear-gradient(90deg,rgba(34, 107, 255, 1) 0,rgba(4, 72, 210, 1) 100%)' : 'none'
            }"
            @click="toClasswork"
          >
            <div class="image-text_1 flex-row justify-between">
              <img class="thumbnail_1" referrerpolicy="no-referrer" :src="menuIndex == 2 ? classworkW : classworkB" />
              <span
                class="text-group_1"
                :style="{
                  color: menuIndex == 2 ? 'rgba(255, 255, 255, 1)' : 'rgba(51, 51, 51, 1)'
                }"
                >课堂作业</span
              >
            </div>
          </div>
          <div
            class="section_6 flex-row"
            :style="{
              'background-image': menuIndex == 3 ? 'linear-gradient(90deg,rgba(34, 107, 255, 1) 0,rgba(4, 72, 210, 1) 100%)' : 'none'
            }"
            @click="toCourseExam"
          >
            <div class="image-text_1 flex-row justify-between">
              <img class="thumbnail_1" referrerpolicy="no-referrer" :src="menuIndex == 3 ? courseExamW : courseExamB" />
              <span
                class="text-group_1"
                :style="{
                  color: menuIndex == 3 ? 'rgba(255, 255, 255, 1)' : 'rgba(51, 51, 51, 1)'
                }"
                >课程考试</span
              >
            </div>
          </div>
          <div
            class="section_6 flex-row"
            :style="{
              'background-image': menuIndex == 4 ? 'linear-gradient(90deg,rgba(34, 107, 255, 1) 0,rgba(4, 72, 210, 1) 100%)' : 'none'
            }"
            @click="toQuestionnaire"
          >
            <div class="image-text_1 flex-row justify-between">
              <img class="thumbnail_1" referrerpolicy="no-referrer" :src="menuIndex == 4 ? questionnaireW : questionnaireB" />
              <span
                class="text-group_1"
                :style="{
                  color: menuIndex == 4 ? 'rgba(255, 255, 255, 1)' : 'rgba(51, 51, 51, 1)'
                }"
                >调查问卷</span
              >
            </div>
          </div>
          <!-- <div
            class="section_6 flex-row"
            :style="{
              'background-image':
                menuIndex == 5
                  ? 'linear-gradient(90deg,rgba(34, 107, 255, 1) 0,rgba(4, 72, 210, 1) 100%)'
                  : 'none',
            }"
            @click="toDataAnalysis"
          >
            <div class="image-text_1 flex-row justify-between">
              <img
                class="thumbnail_1"
                referrerpolicy="no-referrer"
                :src="menuIndex == 5 ? dataAnalysisW : dataAnalysisB"
              />
              <span
                class="text-group_1"
                :style="{
                  color:
                    menuIndex == 5
                      ? 'rgba(255, 255, 255, 1)'
                      : 'rgba(51, 51, 51, 1)',
                }"
                >数据分析</span
              >
            </div>
          </div> -->
        </div>
      </div>
      <div class="group_4 flex-col">
        <div class="group_5 flex-row justify-between">
          <div class="section_7 flex-row">
            <img class="image_2" referrerpolicy="no-referrer" :src="classImg" />
            <div class="text-wrapper_3 flex-col justify-between">
              <span class="text_10">{{ learnInformationStatistics.classCount }}</span>
              <span class="text_11">班级</span>
            </div>
            <img class="image_3" referrerpolicy="no-referrer" :src="academicClass" />
            <div class="text-wrapper_4 flex-col justify-between">
              <span class="text_12">{{ learnInformationStatistics.eduClassCount }}</span>
              <span class="text_13">教务班</span>
            </div>
            <img class="image_4" referrerpolicy="no-referrer" :src="autonomousClass" />
            <div class="text-wrapper_5 flex-col justify-between">
              <span class="text_14">{{ learnInformationStatistics.autonomousClassCount }}</span>
              <span class="text_15">自主班</span>
            </div>
            <img class="image_5" referrerpolicy="no-referrer" :src="examination" />
            <div class="text-wrapper_6 flex-col justify-between">
              <span class="text_16">{{ learnInformationStatistics.testCount }}</span>
              <span class="text_17">考试</span>
            </div>
            <img class="image_6" referrerpolicy="no-referrer" :src="schoolAssignment" />
            <div class="text-wrapper_7 flex-col justify-between">
              <span class="text_18">{{ learnInformationStatistics.homeworkCount }}</span>
              <span class="text_19">作业</span>
            </div>
          </div>
        </div>
        <div class="group_6 flex-row" v-if="isMyClass">
          <div class="group_10 flex-row">
            <el-input class="floating-input" placeholder="搜索课程名称或教师名称" v-model="searchVar">
              <template #suffix>
                <img class="floating-img" @click="selOpenCourse" :src="openCourseListSelect" />
              </template>
            </el-input>
          </div>
        </div>
        <div class="group_6 flex-row" v-if="isClasswork">
          <div class="group_10 flex-row">
            <el-input class="floating-input" placeholder="请输入作业名称" v-model="searchVar">
              <template #suffix>
                <img class="floating-img" @click="handleClickHomeworkOrTest" :src="openCourseListSelect" />
              </template>
            </el-input>
          </div>
        </div>
        <div class="group_6 flex-row" v-if="isCourseExam">
          <div class="group_10 flex-row">
            <el-input class="floating-input" placeholder="请输入考试名称" v-model="searchVar">
              <template #suffix>
                <img class="floating-img" @click="handleClickHomeworkOrTest" :src="openCourseListSelect" />
              </template>
            </el-input>
          </div>
        </div>
        <div class="group_6 flex-row" v-if="isQuestionnaire">
          <div class="group_10 flex-row">
            <el-input class="floating-input" placeholder="请输入调查问卷名称" v-model="searchVar">
              <template #suffix>
                <img class="floating-img" @click="selCourseSurvey" :src="openCourseListSelect" />
              </template>
            </el-input>
          </div>
        </div>
        <div v-if="isMyClass">
          <div class="group_12 flex-col">
            <div class="group_13 flex-row">
              <div
                class="text-wrapper_9 flex-col"
                :style="{
                  'background-color': courseStatus == null ? 'rgba(85, 91, 116, 1)' : 'rgba(255, 255, 255, 1)'
                }"
                @click="getInfoLearnClass(null)"
              >
                <span
                  class="text_26"
                  :style="{
                    color: courseStatus == null ? 'rgba(255, 255, 255, 1)' : 'rgba(51, 51, 51, 1)'
                  }"
                  >全部课程</span
                >
              </div>
              <div
                class="text-wrapper_10 flex-col"
                :style="{
                  'background-color': courseStatus == '1' ? 'rgba(85, 91, 116, 1)' : 'rgba(255, 255, 255, 1)'
                }"
                @click="getInfoLearnClass(1)"
              >
                <span
                  class="text_27"
                  :style="{
                    color: courseStatus == '1' ? 'rgba(255, 255, 255, 1)' : 'rgba(51, 51, 51, 1)'
                  }"
                  >未开始</span
                >
              </div>
              <div
                class="text-wrapper_11 flex-col"
                :style="{
                  'background-color': courseStatus == '2' ? 'rgba(85, 91, 116, 1)' : 'rgba(255, 255, 255, 1)'
                }"
                @click="getInfoLearnClass(2)"
              >
                <span
                  class="text_28"
                  :style="{
                    color: courseStatus == '2' ? 'rgba(255, 255, 255, 1)' : 'rgba(51, 51, 51, 1)'
                  }"
                  >进行中</span
                >
              </div>
              <div
                class="text-wrapper_12 flex-col"
                :style="{
                  'background-color': courseStatus == '3' ? 'rgba(85, 91, 116, 1)' : 'rgba(255, 255, 255, 1)'
                }"
                @click="getInfoLearnClass(3)"
              >
                <span
                  class="text_29"
                  :style="{
                    color: courseStatus == '3' ? 'rgba(255, 255, 255, 1)' : 'rgba(51, 51, 51, 1)'
                  }"
                  >已结束</span
                >
              </div>
              <div class="text-wrapper_23 flex-col">
                <el-button class="study-btn" @click="dialogVisible = true">加入班级</el-button>
              </div>
            </div>
            <div class="group_14">
              <div class="block_1 flex-col" v-for="item in learnClass.rows" :key="item.classId" @click="toCourseTabStu(item.classId)">
                <div class="image-text_6 flex-col justify-between">
                  <div
                    class="box_7 flex-row"
                    :style="{
                      background: 'classBg'
                    }"
                  >
                    <img
                      class="label_4"
                      referrerpolicy="no-referrer"
                      :src="item.courseType == '1' ? academicAffairsClass : selfDirectedClass"
                    />
                  </div>
                  <div class="box_8 flex-row justify-between">
                    <span class="text-group_6">{{ item.className }}</span>
                    <div
                      class="text-wrapper_15 flex-col"
                      :style="{
                        border:
                          item.status == '1'
                            ? '1px solid rgba(238, 161, 49, 1)'
                            : item.status == '2'
                              ? '1px solid rgba(82, 196, 26, 1)'
                              : '1px solid rgba(150, 154, 170, 1)'
                      }"
                    >
                      <span
                        class="text_32"
                        :style="{
                          color:
                            item.status == '1'
                              ? 'rgba(238, 161, 49, 1)'
                              : item.status == '2'
                                ? 'rgba(82, 196, 26, 1)'
                                : 'rgba(150, 154, 170, 1)'
                        }"
                        >{{ item.status == '1' ? '未开课' : item.status == '2' ? '进行中' : '已结束' }}</span
                      >
                    </div>
                  </div>
                </div>
                <div class="text-wrapper_16 flex-row justify-between">
                  <span class="text_33">{{ item.courseName }}</span>
                </div>
                <div class="box_9 flex-row">
                  <div class="image-text_7 flex-row justify-between">
                    <img class="thumbnail_6" referrerpolicy="no-referrer" :src="star" />
                    <span class="text-group_7">授课老师：{{ item.realName }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="isClasswork">
          <div class="group_12 flex-col">
            <div class="group_13 flex-row">
              <div
                class="text-wrapper_9 flex-col"
                :style="{
                  'background-color': submissionUnit == null ? 'rgba(85, 91, 116, 1)' : 'rgba(255, 255, 255, 1)'
                }"
                @click="changeHomeworkOrTest(null)"
              >
                <span
                  class="text_26"
                  :style="{
                    color: submissionUnit == null ? 'rgba(255, 255, 255, 1)' : 'rgba(51, 51, 51, 1)',
                    margin: '0.52vw 0 0 2vw'
                  }"
                  >全部</span
                >
              </div>
              <div
                class="text-wrapper_10 flex-col"
                :style="{
                  'background-color': submissionUnit == '0' ? 'rgba(85, 91, 116, 1)' : 'rgba(255, 255, 255, 1)'
                }"
                @click="changeHomeworkOrTest('0')"
              >
                <span
                  class="text_27"
                  :style="{
                    color: submissionUnit == '0' ? 'rgba(255, 255, 255, 1)' : 'rgba(51, 51, 51, 1)',
                    margin: '0.5vw 0px 0px 1.2vw'
                  }"
                  >个人作业</span
                >
              </div>
              <div
                class="text-wrapper_11 flex-col"
                :style="{
                  'background-color': submissionUnit == '1' ? 'rgba(85, 91, 116, 1)' : 'rgba(255, 255, 255, 1)'
                }"
                @click="changeHomeworkOrTest('1')"
              >
                <span
                  class="text_28"
                  :style="{
                    color: submissionUnit == '1' ? 'rgba(255, 255, 255, 1)' : 'rgba(51, 51, 51, 1)',
                    margin: '0.5vw 0px 0px 1.2vw'
                  }"
                  >小组作业</span
                >
              </div>
              <div class="text-wrapper_13 flex-col">
                <el-select v-model="progressStatus" placeholder="请选择" style="width: 7vw" @change="handleClickHomeworkOrTest()">
                  <el-option v-for="item in workOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </div>
              <div class="text-wrapper_14 flex-col">
                <el-radio-group v-model="answerStatus" @change="handleClickHomeworkOrTest">
                  <el-radio :value="null"><span class="text_radio">全部</span></el-radio>
                  <el-radio :value="1"><span class="text_radio">已作答</span></el-radio>
                  <el-radio :value="0"><span class="text_radio">未作答</span></el-radio>
                </el-radio-group>
              </div>
              <div class="text-wrapper_15 flex-col">
                <el-button class="study-btn">错题集</el-button>
              </div>
            </div>
            <div class="group_15">
              <div class="box_22 flex-col" v-for="item in homeworkOrTestList">
                <div class="group_18 flex-row">
                  <div
                    class="text-wrapper_27 flex-col"
                    :style="{
                      'background-color': item.progressStatus == '1' ? '#52C41A' : '#969AAA'
                    }"
                  >
                    <span class="text_51">{{ item.progressStatus == '1' ? '进行中' : '已结束' }}</span>
                  </div>
                  <span class="text_52"
                    >{{ item.name
                    }}<span class="text_54">{{
                      item.contentType == '0' ? '题库作业' : item.contentType == '1' ? '附件作业' : '登分作业'
                    }}</span></span
                  >
                  <span
                    class="text_64"
                    :style="{
                      color:
                        item.answerStatus === '0'
                          ? '#FF4D4F'
                          : item.answerStatus === '1'
                            ? '#999999'
                            : item.answerStatus === '2'
                              ? '#FF4D4F'
                              : '#999999'
                    }"
                  >
                    {{
                      item.answerStatus === '0'
                        ? '未作答'
                        : item.answerStatus === '1'
                          ? '已作答'
                          : item.answerStatus === '2'
                            ? '待评分'
                            : '已完成'
                    }}</span
                  >
                </div>
                <span class="text_55">所属课程：{{ item.courseName }}</span>
                <span class="text_55">所属班级：{{ item.className }}</span>
                <span class="text_55">创建教师：{{ item.realName }}</span>
                <span class="text_55">开始时间：{{ item.startTime }}</span>
                <span class="text_55">结束时间：{{ item.endTime }}</span>
                <div class="group_19 flex-col"></div>
                <div class="text-wrapper_28 flex-col" @click="toAnswer(item.answerStatus)">
                  <span class="text_59">
                    {{ item.answerStatus === '0' ? '参与' : '查看' }}
                  </span>
                </div>
                <div class="flex-row" style="justify-content: center; margin-top: -1.4vw; margin-left: 5vw">
                  <!-- 在指定位置替换为以下代码 -->
                  <div class="countdown" v-if="item.answerStatus == '2'">
                    距离互评结束还有：
                    <span v-if="item?.remainingTime.days > 0">{{ item?.remainingTime.days }}天</span>
                    <span>{{ item?.remainingTime.hours }}小时</span>
                    <span>{{ item?.remainingTime.minutes }}分钟</span>
                    <span>{{ item?.remainingTime.seconds }}秒</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="isCourseExam">
          <div class="group_12 flex-col">
            <div class="group_13 flex-row">
              <!-- <div
                class="text-wrapper_9 flex-col"
                :style="{
                  'background-color':
                    courseStatus == null
                      ? 'rgba(85, 91, 116, 1)'
                      : 'rgba(255, 255, 255, 1)',
                }"
                @click="getInfoLearnClass(null)"
              >
                <span
                  class="text_26"
                  :style="{
                    color:
                      courseStatus == null
                        ? 'rgba(255, 255, 255, 1)'
                        : 'rgba(51, 51, 51, 1)',
                    margin: '0.52vw 0 0 2vw',
                  }"
                  >全部</span
                >
              </div>
              <div
                class="text-wrapper_10 flex-col"
                :style="{
                  'background-color':
                    courseStatus == '1'
                      ? 'rgba(85, 91, 116, 1)'
                      : 'rgba(255, 255, 255, 1)',
                }"
                @click="getInfoLearnClass(1)"
              >
                <span
                  class="text_27"
                  :style="{
                    color:
                      courseStatus == '1'
                        ? 'rgba(255, 255, 255, 1)'
                        : 'rgba(51, 51, 51, 1)',
                    margin: '0.5vw 0px 0px 1.2vw',
                  }"
                  >个人作业</span
                >
              </div>
              <div
                class="text-wrapper_11 flex-col"
                :style="{
                  'background-color':
                    courseStatus == '2'
                      ? 'rgba(85, 91, 116, 1)'
                      : 'rgba(255, 255, 255, 1)',
                }"
                @click="getInfoLearnClass(2)"
              >
                <span
                  class="text_28"
                  :style="{
                    color:
                      courseStatus == '2'
                        ? 'rgba(255, 255, 255, 1)'
                        : 'rgba(51, 51, 51, 1)',
                    margin: '0.5vw 0px 0px 1.2vw',
                  }"
                  >小组作业</span
                >
              </div> -->
              <div class="text-wrapper_13 flex-col">
                <el-select v-model="progressStatus" placeholder="请选择" style="width: 7vw" @change="handleClickHomeworkOrTest()">
                  <el-option v-for="item in workOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </div>
              <div class="text-wrapper_18 flex-col">
                <el-radio-group v-model="answerStatus" @change="handleClickHomeworkOrTest">
                  <el-radio :value="null"><span class="text_radio">全部</span></el-radio>
                  <el-radio :value="1"><span class="text_radio">已作答</span></el-radio>
                  <el-radio :value="0"><span class="text_radio">未作答</span></el-radio>
                </el-radio-group>
              </div>
              <div class="text-wrapper_17 flex-col">
                <el-button class="study-btn">错题集</el-button>
              </div>
            </div>
            <div class="group_15">
              <div class="box_22 flex-col" v-for="item in homeworkOrTestList">
                <div class="group_18 flex-row">
                  <div
                    class="text-wrapper_27 flex-col"
                    :style="{
                      'background-color': item.progressStatus == '1' ? '#52C41A' : '#969AAA'
                    }"
                  >
                    <span class="text_51">{{ item.progressStatus == '1' ? '进行中' : '已结束' }}</span>
                  </div>
                  <span class="text_52"
                    >{{ item.name
                    }}<span class="text_54">{{
                      item.contentType == '0' ? '题库作业' : item.contentType == '1' ? '附件作业' : '登分作业'
                    }}</span></span
                  >
                  <span
                    class="text_64"
                    :style="{
                      color:
                        item.answerStatus === '0'
                          ? '#FF4D4F'
                          : item.answerStatus === '1'
                            ? '#999999'
                            : item.answerStatus === '2'
                              ? '#FF4D4F'
                              : '#999999'
                    }"
                  >
                    {{
                      item.answerStatus === '0'
                        ? '未作答'
                        : item.answerStatus === '1'
                          ? '已作答'
                          : item.answerStatus === '2'
                            ? '待评分'
                            : '已完成'
                    }}</span
                  >
                </div>
                <span class="text_55">所属课程：{{ item.courseName }}</span>
                <span class="text_55">所属班级：{{ item.className }}</span>
                <span class="text_55">创建教师：{{ item.realName }}</span>
                <span class="text_55">开始时间：{{ item.startTime }}</span>
                <span class="text_55">结束时间：{{ item.endTime }}</span>
                <div class="group_19 flex-col"></div>
                <div class="text-wrapper_28 flex-col" @click="toAnswer(item.answerStatus)">
                  <span class="text_59">
                    {{ item.answerStatus === '0' ? '参与' : '查看' }}
                  </span>
                </div>
                <div class="flex-row" style="justify-content: center; margin-top: -1.4vw; margin-left: 5vw">
                  <!-- 在指定位置替换为以下代码 -->
                  <div class="countdown" v-if="item.answerStatus == '2'">
                    距离互评结束还有：
                    <span v-if="item?.remainingTime.days > 0">{{ item?.remainingTime.days }}天</span>
                    <span>{{ item?.remainingTime.hours }}小时</span>
                    <span>{{ item?.remainingTime.minutes }}分钟</span>
                    <span>{{ item?.remainingTime.seconds }}秒</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="isQuestionnaire">
          <div class="group_12 flex-col">
            <div class="group_13 flex-row">
              <div class="text-wrapper_16 flex-col">
                <el-select v-model="progressStatus" placeholder="请选择" style="width: 7vw" @change="courseSurveyChange">
                  <el-option v-for="item in workOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </div>
            </div>
            <div class="group_15">
              <div class="box_23 flex-col" v-for="item in courseSurveyList">
                <div class="group_19 flex-row">
                  <span class="text_52">{{ item.surveyName }}</span>
                  <span
                    class="text_54"
                    :style="{
                      color: item.answerStatus == '0' ? 'rgba(238, 161, 49, 1)' : 'rgba(0, 181, 255, 1)'
                    }"
                    >{{ item.answerStatus == '0' ? '未参与' : '已参与' }}</span
                  >
                </div>
                <span class="text_55">调查主题：{{ item.surveyTopic }}</span>
                <span class="text_56">发布教师：{{ item.realName }}</span>
                <span class="text_56">发布时间：{{ item.startTime }}</span>
                <div
                  class="text-wrapper_29 flex-col"
                  :style="{
                    'background-image':
                      item.answerStatus == '0'
                        ? 'linear-gradient(90deg,rgba(238, 161, 49, 1) 0,rgba(238, 161, 49, 1) 100%)'
                        : 'linear-gradient(90deg,rgba(34, 107, 255, 1) 0,rgba(4, 72, 210, 1) 100%)'
                  }"
                >
                  <span class="text_59">{{ item.answerStatus == '0' ? '参与' : '查看' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="isMyClass">
        <div :class="{ hidden: hidden }" class="pagination-container">
          <el-pagination
            background="true"
            v-model:current-page="currentPage4"
            v-model:page-size="pageSize4"
            :page-sizes="[100, 200, 300, 400]"
            :size="size"
            :disabled="disabled"
            :background="background"
            layout="total, prev, pager, next, jumper"
            :total="learnClass.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
  <!-- 弹窗部分 -->
  <el-dialog v-model="dialogVisible" title="加入班级" width="20%">
    <el-input v-model="classCode" placeholder="请输入班课码"></el-input>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmClassCode">确认</el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 确认加入弹窗 -->
  <el-dialog v-model="joinDialogVisible" title="确认加入" width="30%">
    <div>
      <p>课程名称：{{ joinForm.courseName }}</p>
      <p>班级名称：{{ joinForm.className }}</p>
      <p>授课教师：{{ joinForm.teacherName }}</p>
      <p>备注：{{ joinForm.description }}</p>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="joinDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmJoin">加入该班级</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import openCourseListSelect from '@/assets/images/openCourse/course-sel.png'
import { onMounted, reactive, ref, onBeforeUnmount,getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import academicClass from '@/assets/images/smartCourse/academic-class.png'
import autonomousClass from '@/assets/images/smartCourse/autonomous-class.png'
import classImg from '@/assets/images/smartCourse/class.png'
import classworkB from '@/assets/images/smartCourse/classwork-b.png'
import classworkW from '@/assets/images/smartCourse/classwork-w.png'
import courseExamB from '@/assets/images/smartCourse/course-exam-b.png'
import courseExamW from '@/assets/images/smartCourse/course-exam-w.png'
import dataAnalysisB from '@/assets/images/smartCourse/data-analysis-b.png'
import dataAnalysisW from '@/assets/images/smartCourse/data-analysis-w.png'
import examination from '@/assets/images/smartCourse/examination.png'
import myClassB from '@/assets/images/smartCourse/my-class-b.png'
import myClassW from '@/assets/images/smartCourse/my-class-w.png'
import questionnaireB from '@/assets/images/smartCourse/questionnaire-b.png'
import questionnaireW from '@/assets/images/smartCourse/questionnaire-w.png'
import schoolAssignment from '@/assets/images/smartCourse/school-assignment.png'
import academicAffairsClass from '@/assets/images/smartCourse/academic-affairs-class.png'
import selfDirectedClass from '@/assets/images/smartCourse/self-directed-class.png'
import star from '@/assets/images/openCourse/star.png'
import useUserStore from '@/store/modules/user'
import { getLearnInformationStatistics, getLearnClass, addMember } from '@/api/edu/member'
import { getInfo } from '@/api/edu/class'
import { getCourseSurvey } from '@/api/edu/survey'
import { getHomeworkOrTestList } from '@/api/edu/test'
const { proxy } = getCurrentInstance()

// 计算剩余时间的方法
const calculateRemainingTime = endTime => {
  // 格式转换
  const normalizedEndTime = endTime.replace(' ', 'T')
  console.log(normalizedEndTime)
  const now = new Date()
  const end = new Date(normalizedEndTime)
  const diff = end - now

  if (diff <= 0) {
    return { days: 0, hours: 0, minutes: 0, seconds: 0 }
  }

  return {
    days: Math.floor(diff / (1000 * 60 * 60 * 24)),
    hours: Math.floor((diff / (1000 * 60 * 60)) % 24),
    minutes: Math.floor((diff / (1000 * 60)) % 60),
    seconds: Math.floor((diff / 1000) % 60)
  }
}

// 使用 storeToRefs 保持响应式特性
const userStore = useUserStore()
const userInfo = ref(null) // 关键修改点
const searchVar = ref(null)
const currentPage4 = ref(1)
const pageSize4 = ref(9)
const courseStatus = ref(null)
const menuIndex = ref(1)
const progressStatus = ref(null)
const workOptions = ref([
  { label: '全部', value: null },
  { label: '进行中', value: 1 },
  { label: '已结束', value: 2 }
])
const answerStatus = ref(null)
const learnInformationStatistics = ref({
  courseCount: 0,
  learnCount: 0,
  learnTime: 0,
  learnProgress: 0
})
const courseSurveyList = ref([])
const learnClass = ref([])
const queryParams = ref({
  pageNum: 1,
  pageSize: 9,
  status: null,
  realName: null,
  courseName: null
})
// 弹窗显示状态
const dialogVisible = ref(false)
const joinDialogVisible = ref(false)
const joinForm = ref({
  courseName: null,
  className: null,
  teacherName: null,
  description: null
})
// 表单数据
const classCode = ref('')
// 显示标识符
const isMyClass = ref(true)
const isClasswork = ref(false)
const isCourseExam = ref(false)
const isQuestionnaire = ref(false)
const isDataAnalysis = ref(false)
const homeworkOrTestList = ref(null)
const submissionUnit = ref(null)
const assignmentType = ref(null)
// 我的班课
const toMyClass = () => {
  searchVar.value = null
  menuIndex.value = 1
  isMyClass.value = true
  isClasswork.value = false
  isCourseExam.value = false
  isQuestionnaire.value = false
  isDataAnalysis.value = false
  queryParams.value.realName = searchVar.value
  queryParams.value.courseName = searchVar.value
  getInfoLearnClass()
}
// 课堂作业
const toClasswork = () => {
  searchVar.value = null
  menuIndex.value = 2
  isMyClass.value = false
  isClasswork.value = true
  isCourseExam.value = false
  isQuestionnaire.value = false
  isDataAnalysis.value = false
  assignmentType.value = '0'
  getHomeworkOrTestInfo()
}
// 课程考试
const toCourseExam = () => {
  searchVar.value = null
  menuIndex.value = 3
  isMyClass.value = false
  isClasswork.value = false
  isCourseExam.value = true
  isQuestionnaire.value = false
  isDataAnalysis.value = false
  assignmentType.value = '1'
  getHomeworkOrTestInfo()
}
// 调查问卷
const toQuestionnaire = () => {
  searchVar.value = null
  menuIndex.value = 4
  isMyClass.value = false
  isClasswork.value = false
  isCourseExam.value = false
  isQuestionnaire.value = true
  isDataAnalysis.value = false
  getCourseSurveyList()
}
// 数据分析
const toDataAnalysis = () => {
  menuIndex.value = 5
  isMyClass.value = false
  isClasswork.value = false
  isCourseExam.value = false
  isQuestionnaire.value = false
  isDataAnalysis.value = true
}
// 确认按钮点击事件
const confirmClassCode = () => {
  if (classCode.value) {
    joinDialogVisible.value = true
    const parm = {
      classCode: classCode.value
    }
    getInfo(parm).then(res => {
      if (res.code == 200) {
        joinForm.value = {
          courseName: res.data.courseName,
          className: res.data.className,
          teacherName: res.data.realName,
          description: res.data.description
        }
      } else {
        ElMessage.error(res.msg)
      }
    })
  } else {
    // 提示用户输入班课码
    ElMessage.warning('请输入班课码！')
  }
}
const confirmJoin = () => {
  addMember({ classCode: classCode.value }).then(res => {
    if (res.code == 200) {
      ElMessage.success('加入成功')
      // 关闭弹窗
      dialogVisible.value = false
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const selOpenCourse = () => {
  queryParams.value.realName = searchVar.value
  queryParams.value.courseName = searchVar.value
  getInfoLearnClass(null)
}
const selCourseSurvey = () => {
  getCourseSurveyList()
}
const courseSurveyChange = () => {
  getCourseSurveyList()
}
const handleStuClick = () => {}
const getInfoLearnClass = index => {
  courseStatus.value = index
  queryParams.value.status = index
  getLearnClass(queryParams.value).then(res => {
    if (res.code == 200) {
      learnClass.value = res
    }
  })
}
const changeHomeworkOrTest = index => {
  submissionUnit.value = index
  getHomeworkOrTestInfo()
}
const handleClickHomeworkOrTest = () => {
  getHomeworkOrTestInfo()
}
const getCourseSurveyList = () => {
  getCourseSurvey({
    progressStatus: progressStatus.value,
    surveyName: searchVar.value
  }).then(res => {
    if (res.code == 200) {
      courseSurveyList.value = res.data
    }
  })
}
const getHomeworkOrTestInfo = () => {
  const parm = {
    answerStatus: answerStatus.value,
    assignmentName: searchVar.value,
    submissionUnit: submissionUnit.value,
    progressStatus: progressStatus.value,
    name: searchVar.value ? searchVar.value : null,
    assignmentType: assignmentType.value
  }
  getHomeworkOrTestList(parm).then(res => {
    if (res.code == 200) {
      homeworkOrTestList.value = res.data.map(item => ({
        ...item,
        remainingTime: calculateRemainingTime(item.gradingEndTime),
        countdownInterval: null
      }))

      // 启动独立倒计时
      startCountdownForItems(homeworkOrTestList.value)
    }
  })
  // getHomeworkOrTestInfo
  const startCountdownForItems = items => {
    items.forEach(item => {
      // 清除旧定时器（防止重复启动）
      if (item.countdownInterval) clearInterval(item.countdownInterval)

      const interval = setInterval(() => {
        item.remainingTime = calculateRemainingTime(item.gradingEndTime)

        // 清除已完成的倒计时
        if (Object.values(item.remainingTime).every(time => time === 0)) {
          clearInterval(interval)
        }
      }, 1000)

      // 存储interval用于后续清理
      item.countdownInterval = interval
    })
  }
}
const toAnswer = value => {
  // router.push({
  //   path: "/openCourse/openCourseAnswer",
  //   query: {
  //     assignmentId: assignmentId.value,
  //     answerStatus: answerStatus.value,
  //   },
  // });
}
onMounted(async () => {
  const res = await userStore.getInfo()
  const resLearnInformation = await getLearnInformationStatistics()
  await getInfoLearnClass()
  if (resLearnInformation.code == 200) {
    // learnInformationStatistics.value = resLearnInformation.data
  }
  await getCourseSurveyList()
  await getHomeworkOrTestInfo()
  userInfo.value = res.user
})
onBeforeUnmount(() => {
  if (homeworkOrTestList.value) {
    homeworkOrTestList.value.forEach(item => {
      if (item.countdownInterval) {
        clearInterval(item.countdownInterval)
      }
    })
  }
})
// 切换角色
const emits = defineEmits(['changeRole'])

const handleCommand = command => {
  emits('changeRole', command)
}
// 跳转courseTabStu
const toCourseTabStu = classId => {
  proxy.$router.push('/smart-course-tab-stu');
}
</script>

<style lang="scss" scoped>
.filters {
  margin-top: 20px;
  margin-bottom: 20px;
}
.course-section {
  background-color: #f1f5fb;
}
.section_1 {
  width: 86.46vw;
  height: 56.25vw;
  margin: 1.04vw 0 0 13.54vw;
  position: relative; /* 作为绝对定位元素的参考点 */
  display: flex;
  flex-direction: row;
  overflow: hidden; /* 防止内容溢出 */
  .group_3 {
    width: 13.96vw;
    height: 56.25vw;
    .section_2 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 8px;
      // height: 17.1vw;
      width: 13.96vw;
      position: relative;
      .box_4 {
        width: 1.52vw;
        height: 1.05vw;
        margin: 7.76vw 0 0 10.88vw;
        .section_3 {
          background-color: rgba(255, 255, 255, 1);
          width: 1.52vw;
          height: 1.05vw;
        }
      }
      .text-wrapper_1 {
        width: 6.52vw;
        height: 0.94vw;
        margin: 0.83vw 0 0 3.69vw;
        .text_8 {
          width: 6.52vw;
          height: 0.94vw;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 0.93vw;
          font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 0.94vw;
        }
      }
      .text-wrapper_2 {
        width: 9.58vw;
        height: 0.94vw;
        margin: 0.62vw 0 2.25vw 3.69vw;
        .text_9 {
          margin: 0.62vw 0 1.35vw -1.69vw;
          width: 5.58vw;
          height: 0.94vw;
          overflow-wrap: break-word;
          color: rgba(88, 94, 118, 1);
          font-size: 0.93vw;
          font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.94vw;
        }
      }
      .image-wrapper_1 {
        background-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 0, rgba(233, 171, 35, 0.34) 100%);
        border-radius: 8px 8px 0px 0px;
        height: 8.03vw;
        width: 13.96vw;
        position: absolute;
        left: 0;
        top: 0.68vw;
        .image_1 {
          width: 7.09vw;
          height: 7.09vw;
          margin: 0.36vw 0 0 3.43vw;
        }
      }
    }
    .section_4 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 8px;
      width: 13.96vw;
      height: 41.59vw;
      margin-top: 1.05vw;
      cursor: pointer;
      .section_5 {
        background-image: linear-gradient(90deg, rgba(34, 107, 255, 1) 0, rgba(4, 72, 210, 1) 100%);
        border-radius: 8px;
        width: 13.96vw;
        height: 2.92vw;
        .image-text_1 {
          width: 4.64vw;
          height: 1.15vw;
          margin: 0.88vw 0 0 4.68vw;
          .thumbnail_1 {
            width: 1.05vw;
            height: 1.05vw;
            margin-top: 0.06vw;
          }
          .text-group_1 {
            width: 3.29vw;
            height: 1.15vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 1.15vw;
          }
        }
      }
      .section_6 {
        background-image: none;
        border-radius: 8px;
        width: 13.96vw;
        height: 2.92vw;
        .image-text_1 {
          width: 4.64vw;
          height: 1.15vw;
          margin: 0.88vw 0 0 4.68vw;
          .thumbnail_1 {
            width: 1.05vw;
            height: 1.05vw;
            margin-top: 0.06vw;
          }
          .text-group_1 {
            width: 3.29vw;
            height: 1.15vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 1.15vw;
          }
        }
      }
    }
    .section_6 {
      background-color: rgba(255, 255, 255, 1);
      width: 13.96vw;
      height: 27.09vw;
      margin-top: 0.06vw;
    }
  }
  .group_4 {
    width: 58.08vw;
    height: 56.25vw;
    margin-left: 0.89vw;
    .group_5 {
      width: 58.08vw;
      height: 4.9vw;
      .section_7 {
        justify-content: center;
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        width: 58.04vw;
        height: 4.9vw;
        .image_2 {
          width: 3.13vw;
          height: 3.13vw;
          margin: 0.88vw 0 0 1.35vw;
        }
        .text-wrapper_3 {
          width: 2.92vw;
          height: 2.14vw;
          margin: 1.4vw 0 0 0.72vw;
          .text_10 {
            width: 2.71vw;
            height: 1.1vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 1.14vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 1.1vw;
          }
          .text_11 {
            width: 2.92vw;
            height: 0.73vw;
            overflow-wrap: break-word;
            color: rgba(150, 154, 170, 1);
            font-size: 0.72vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: center;
            white-space: nowrap;
            line-height: 0.73vw;
            margin-top: 0.32vw;
          }
        }
        .image_3 {
          width: 3.13vw;
          height: 3.13vw;
          margin: 0.88vw 0 0 3.85vw;
        }
        .text-wrapper_4 {
          width: 2.92vw;
          height: 2.14vw;
          margin: 1.4vw 0 0 0.72vw;
          .text_12 {
            width: 2.71vw;
            height: 1.1vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 1.14vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 1.1vw;
          }
          .text_13 {
            width: 2.92vw;
            height: 0.73vw;
            overflow-wrap: break-word;
            color: rgba(150, 154, 170, 1);
            font-size: 0.72vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: center;
            white-space: nowrap;
            line-height: 0.73vw;
            margin-top: 0.32vw;
          }
        }
        .image_4 {
          width: 3.13vw;
          height: 3.13vw;
          margin: 0.88vw 0 0 3.85vw;
        }
        .text-wrapper_5 {
          width: 2.92vw;
          height: 2.14vw;
          margin: 1.4vw 0 0 0.72vw;
          .text_14 {
            width: 2.71vw;
            height: 1.1vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 1.14vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 1.1vw;
          }
          .text_15 {
            width: 2.92vw;
            height: 0.73vw;
            overflow-wrap: break-word;
            color: rgba(150, 154, 170, 1);
            font-size: 0.72vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: center;
            white-space: nowrap;
            line-height: 0.73vw;
            margin-top: 0.32vw;
          }
        }
        .image_5 {
          width: 3.13vw;
          height: 3.13vw;
          margin: 0.88vw 0 0 3.85vw;
        }
        .text-wrapper_6 {
          width: 2.92vw;
          height: 2.14vw;
          margin: 1.4vw 0 0 0.72vw;
          .text_16 {
            width: 2.71vw;
            height: 1.1vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 1.14vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 1.1vw;
          }
          .text_17 {
            width: 2.92vw;
            height: 0.73vw;
            overflow-wrap: break-word;
            color: rgba(150, 154, 170, 1);
            font-size: 0.72vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: center;
            white-space: nowrap;
            line-height: 0.73vw;
            margin-top: 0.32vw;
          }
        }
        .image_6 {
          width: 3.13vw;
          height: 3.13vw;
          margin: 0.88vw 0 0 3.85vw;
        }
        .text-wrapper_7 {
          width: 2.92vw;
          height: 2.14vw;
          margin: 1.4vw 1.4vw 0 0.72vw;
          .text_18 {
            width: 2.71vw;
            height: 1.1vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 1.14vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 1.1vw;
          }
          .text_19 {
            width: 2.92vw;
            height: 0.73vw;
            overflow-wrap: break-word;
            color: rgba(150, 154, 170, 1);
            font-size: 0.72vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: center;
            white-space: nowrap;
            line-height: 0.73vw;
            margin-top: 0.32vw;
          }
        }
      }
      .section_8 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        width: 5.53vw;
        height: 4.9vw;
        .section_9 {
          background-color: rgba(242, 245, 250, 1);
          border-radius: 50%;
          position: relative;
          width: 2.19vw;
          height: 2.19vw;
          margin: 0.72vw 0 0 1.61vw;
          .label_3 {
            width: 1.25vw;
            height: 1.25vw;
            margin: 0.57vw 0 0 0.46vw;
          }
          .text-wrapper_8 {
            background-color: rgba(255, 77, 79, 1);
            border-radius: 50%;
            height: 0.79vw;
            width: 0.79vw;
            position: absolute;
            left: 1.36vw;
            top: 0;
            .text_20 {
              width: 0.27vw;
              height: 0.47vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.41vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.42vw;
              margin: 0.15vw 0 0 0.26vw;
            }
          }
        }
        .text_21 {
          width: 2.19vw;
          height: 0.73vw;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 0.72vw;
          font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.73vw;
          margin: 0.57vw 0 0.67vw 1.61vw;
        }
      }
    }
    .group_6 {
      width: 58.08vw;
      height: 2.09vw;
      margin-top: 1.05vw;
      .group_7 {
        width: 4.95vw;
        height: 1.52vw;
        margin-top: 0.32vw;
        .text_22 {
          width: 4.95vw;
          height: 1.25vw;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 1.25vw;
          font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 1.25vw;
        }
        .box_5 {
          background-image: linear-gradient(153deg, rgba(199, 199, 199, 1) 0, rgba(105, 105, 105, 1) 100%);
          border-radius: 2px;
          width: 4.95vw;
          height: 0.11vw;
          margin-top: 0.16vw;
        }
      }
      .group_8 {
        width: 4.95vw;
        height: 1.52vw;
        margin: 0.31vw 0 0 2.29vw;
        .text_23 {
          width: 4.95vw;
          height: 1.25vw;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 1.25vw;
          font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 1.25vw;
        }
        .group_9 {
          border-radius: 2px;
          width: 4.95vw;
          height: 0.11vw;
          margin-top: 0.16vw;
        }
      }
      .group_10 {
        background-color: rgba(255, 255, 255, 0.28);
        border-radius: 2px;
        width: 12.4vw;
        height: 2.09vw;
        border: 1px solid rgba(150, 154, 170, 1);
        margin-left: 45.83vw;
        .text_24 {
          width: 5.79vw;
          height: 0.73vw;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 0.72vw;
          font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.73vw;
          margin: 0.67vw 0 0 0.93vw;
        }
        .box_6 {
          background-color: rgba(150, 154, 170, 1);
          border-radius: 2px;
          width: 1.1vw;
          height: 1.15vw;
          margin: 0.46vw 0.83vw 0 3.75vw;
        }
      }
      .group_11 {
        border-radius: 2px;
        width: 15.63vw;
        height: 2.09vw;
        border: 1px solid rgba(150, 154, 170, 1);
        margin-left: 1.05vw;
        .text_25 {
          width: 3.29vw;
          height: 1.25vw;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 0.83vw;
          font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 1.25vw;
          margin: 0.41vw 0 0 0.62vw;
        }
        .image_7 {
          width: 0.89vw;
          height: 1.67vw;
          margin: 0.2vw 0.57vw 0 10.26vw;
        }
      }
    }
    .group_12 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 8px;
      width: 58.08vw;
      height: 47.3vw;
      margin-top: 0.94vw;
      .group_13 {
        width: 55.47vw;
        height: 2.09vw;
        margin: 0.88vw 0 0 1.35vw;
        cursor: pointer;
        .text-wrapper_9 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 4px;
          height: 1.88vw;
          border: 1px solid rgba(126, 136, 165, 1);
          margin-top: 0.21vw;
          width: 5.84vw;
          .text_26 {
            width: 3.29vw;
            height: 0.84vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.84vw;
            margin: 0.52vw 0 0 1.3vw;
          }
        }
        .text-wrapper_10 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 4px;
          height: 1.88vw;
          border: 1px solid rgba(126, 136, 165, 1);
          width: 5.84vw;
          margin: 0.2vw 0 0 0.83vw;
          .text_27 {
            width: 2.5vw;
            height: 0.84vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.84vw;
            margin: 0.52vw 0 0 1.66vw;
          }
        }
        .text-wrapper_11 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 4px;
          height: 1.88vw;
          border: 1px solid rgba(126, 136, 165, 1);
          width: 5.84vw;
          margin: 0.2vw 0 0 0.83vw;
          .text_28 {
            width: 2.5vw;
            height: 0.84vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.84vw;
            margin: 0.52vw 0 0 1.66vw;
          }
        }
        .text-wrapper_12 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 4px;
          height: 1.88vw;
          border: 1px solid rgba(126, 136, 165, 1);
          width: 5.84vw;
          margin: 0.2vw 0 0 0.83vw;
          .text_29 {
            width: 2.5vw;
            height: 0.84vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.84vw;
            margin: 0.52vw 0 0 1.66vw;
          }
        }
        .text-wrapper_13 {
          margin-left: 3vw;
          margin-top: 0.2vw;
          :deep(.el-select__wrapper) {
            font-size: 0.8vw !important;
            height: 2vw !important;
          }
        }
        .text-wrapper_23 {
          margin-left: 3vw;
          margin-top: 0.2vw;
          margin-left: 21vw;
          :deep(.el-select__wrapper) {
            font-size: 0.8vw !important;
            height: 2vw !important;
          }
        }
        .text-wrapper_14 {
          display: flex;
          flex-direction: row;
          gap: 10px;
          margin-left: 1vw;
          height: 2.09vw;
          width: 17vw;
          .text_radio {
            width: 2.09vw;
            height: 2.09vw;
            font-weight: normal;
            font-size: 0.8vw;
            color: #000000;
            line-height: 2.09vw;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
        .text-wrapper_15 {
          height: 2.09vw;
          width: 5.79vw;
          .text_30 {
            width: 2.09vw;
            height: 2.09vw;
            background: linear-gradient(90deg, #226bff 0%, #0448d2 100%), #226bfe, #f8f7f4;
            border-radius: 20px 20px 20px 20px;
            font-family:
              Alibaba PuHuiTi 2,
              Alibaba PuHuiTi 20;
            font-weight: normal;
            font-size: 2.09vw;
            color: #ffffff;
            line-height: 2.09vw;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
        .text-wrapper_16 {
          height: 2.09vw;
          margin-left: 45.86vw;
          width: 5.79vw;
        }
        .text-wrapper_17 {
          height: 2.09vw;
          margin-left: 15.86vw;
          width: 5.79vw;
          .text_30 {
            width: 2.09vw;
            height: 2.09vw;
            background: linear-gradient(90deg, #226bff 0%, #0448d2 100%), #226bfe, #f8f7f4;
            border-radius: 20px 20px 20px 20px;
            font-family:
              Alibaba PuHuiTi 2,
              Alibaba PuHuiTi 20;
            font-weight: normal;
            font-size: 2.09vw;
            color: #ffffff;
            line-height: 2.09vw;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
        .text-wrapper_18 {
          display: flex;
          flex-direction: row;
          gap: 10px;
          margin-left: 1vw;
          height: 2.09vw;
          width: 20.3vw;
          .text_radio {
            width: 2.09vw;
            height: 2.09vw;
            font-weight: normal;
            font-size: 0.8vw;
            color: #000000;
            line-height: 2.09vw;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
      }
      .group_14 {
        display: flex;
        flex-wrap: wrap;
        width: 51.46vw;
        height: 11.67vw;
        margin: 1.19vw 0 0 3.07vw;
        .block_1 {
          margin: 0.3vw;
          background-color: rgba(255, 255, 255, 1);
          border-radius: 14px;
          width: 16.46vw;
          height: 11.67vw;
          border: 2px solid rgba(230, 235, 245, 1);
          cursor: pointer;
          .image-text_6 {
            width: 15.63vw;
            height: 6.1vw;
            margin: 0.41vw 0 0 0.41vw;
            .box_7 {
              width: 15.63vw;
              height: 4.22vw;
              background: url('../../../assets/images/smartCourse/classBg.png') no-repeat;
              background-size: cover;
              background-position: center;
              .text-wrapper_14 {
                border-radius: 8px;
                height: 1.36vw;
                width: 3.75vw;
                margin: 1.45vw 0 0 0.52vw;
                .text_31 {
                  width: 2.92vw;
                  height: 0.84vw;
                  overflow-wrap: break-word;
                  color: rgba(80, 44, 19, 1);
                  font-size: 0.72vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.84vw;
                  margin: 0.26vw 0 0 0.41vw;
                }
              }
              .label_4 {
                width: 25%;
                height: 40%;
                margin: 1.35vw 0.67vw 0 6vw;
              }
            }
            .box_8 {
              width: 14.07vw;
              height: 1.36vw;
              margin: 0.52vw 0 0 0.78vw;
              .text-group_6 {
                width: 8.29vw;
                height: 0.94vw;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.93vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.94vw;
                margin-top: 0.21vw;
              }
              .text-wrapper_15 {
                border-radius: 20px;
                height: 1.36vw;
                border: 1px solid rgba(82, 196, 26, 1);
                width: 4.38vw;
                .text_32 {
                  width: 2.19vw;
                  height: 0.73vw;
                  overflow-wrap: break-word;
                  color: rgba(82, 196, 26, 1);
                  font-size: 0.72vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.73vw;
                  margin: 0.31vw 0 0 1.09vw;
                }
              }
            }
          }
          .text-wrapper_16 {
            width: 10.06vw;
            height: 0.84vw;
            margin: 0.83vw 0 0 1.19vw;
            .text_33 {
              width: 3.75vw;
              height: 0.84vw;
              overflow-wrap: break-word;
              color: rgba(150, 154, 170, 1);
              font-size: 0.83vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.84vw;
            }
            .text_34 {
              width: 4.95vw;
              height: 0.84vw;
              overflow-wrap: break-word;
              color: rgba(34, 107, 255, 1);
              font-size: 0.83vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.84vw;
            }
          }
          .box_9 {
            background-color: rgba(242, 245, 250, 1);
            border-radius: 8px;
            width: 11.46vw;
            height: 1.67vw;
            margin: 0.83vw 0 0.98vw 1.19vw;
            .image-text_7 {
              width: 10.42vw;
              height: 0.84vw;
              margin: 0.41vw 0 0 0.52vw;
              .thumbnail_6 {
                width: 0.84vw;
                height: 0.84vw;
                margin-top: -0.1vw;
              }
              .text-group_7 {
                width: 9.28vw;
                height: 0.84vw;
                overflow-wrap: break-word;
                color: rgba(88, 94, 118, 1);
                font-size: 0.83vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.84vw;
              }
            }
          }
        }
      }
      .group_15 {
        display: flex;
        flex-wrap: wrap;
        width: 54.46vw;
        margin: 1.19vw 0 0 3.07vw;
        overflow: auto;
        .box_22 {
          margin-left: 0.5vw;
          margin-bottom: 0.5vw;
          background-color: rgba(255, 255, 255, 1);
          border-radius: 12px;
          width: 25.21vw;
          height: 13.44vw;
          border: 2px solid rgba(230, 235, 245, 1);
          .group_18 {
            width: 23.55vw;
            height: 1.25vw;
            margin: 0.83vw 0 0 0.83vw;
            .text-wrapper_27 {
              background-color: #52c41a;
              border-radius: 20px;
              height: 1.15vw;
              margin-top: 0.06vw;
              width: 3.23vw;
              .text_51 {
                width: 2.7vw;
                height: 0.94vw;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.94vw;
                margin: 0.1vw 0 0 0.52vw;
              }
            }
            .text_52 {
              width: 20.58vw;
              height: 1.25vw;
              overflow-wrap: break-word;
              color: rgba(37, 40, 45, 1);
              font-size: 0.93vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 1.25vw;
              margin-left: 0.63vw;
            }
            .text_53 {
              width: 2.92vw;
              height: 0.73vw;
              overflow-wrap: break-word;
              color: rgba(88, 94, 118, 1);
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 5.21vw;
              margin: 0.26vw 0 0 0.31vw;
            }
            .text_54 {
              height: 0.84vw;
              overflow-wrap: break-word;
              color: #585e76;
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.84vw;
              margin: 0.2vw 0 0 0.5vw;
            }
            .text_64 {
              width: 3.5vw;
              height: 0.84vw;
              overflow-wrap: break-word;
              color: #ff4d4f;
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.84vw;
              margin: 0.2vw 0 0 0;
            }
          }
          .text_55 {
            width: 23.55vw;
            height: 0.84vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            margin: 0.62vw 0 0 0.83vw;
          }
          .text_56 {
            width: 23.55vw;
            height: 0.84vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            margin: 0.52vw 0 0 0.83vw;
          }
          .text_57 {
            width: 23.55vw;
            height: 0.84vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            margin: 0.52vw 0 0 0.83vw;
          }
          .text_58 {
            width: 23.55vw;
            height: 0.84vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            margin: 0.52vw 0 0 0.83vw;
          }
          .group_19 {
            background-color: rgba(224, 228, 238, 1);
            width: 23.55vw;
            height: 0.06vw;
            margin-top: 0.83vw;
            margin-left: 0.83vw;
          }
          .text-wrapper_28 {
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0px 1px 2px 0px rgba(9, 25, 72, 0.06);
            background-image: linear-gradient(90deg, rgba(34, 107, 255, 1) 0, rgba(4, 72, 210, 1) 100%);
            border-radius: 8px;
            height: 1.67vw;
            width: 5.53vw;
            margin: 0.83vw 0 0 0.83vw;
            .text_59 {
              width: 1.46vw;
              height: 2vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: center;
              white-space: nowrap;
              line-height: 0.84vw;
              margin: 0.41vw 0 0 2.03vw;
            }
          }
          .text-wrapper_28:active {
            transform: scale(0.98);
            opacity: 0.9;
          }
        }
        .box_23 {
          margin-left: 0.5vw;
          margin-bottom: 0.5vw;
          background-color: rgba(255, 255, 255, 1);
          border-radius: 12px;
          width: 25.21vw;
          height: 8.44vw;
          border: 2px solid rgba(230, 235, 245, 1);
          .group_19 {
            width: 23.55vw;
            height: 0vw;
            margin: 0.83vw 0 0 0.83vw;
            background-color: rgba(255, 255, 255, 0) !important;
            .text-wrapper_27 {
              background-color: rgba(0, 181, 255, 1);
              border-radius: 20px;
              height: 1.15vw;
              margin-top: 0.06vw;
              width: 3.23vw;
              .text_51 {
                width: 2.19vw;
                height: 0.94vw;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.94vw;
                margin: 0.1vw 0 0 0.52vw;
              }
            }
            .text_52 {
              width: 5.58vw;
              height: 1.25vw;
              overflow-wrap: break-word;
              color: rgba(37, 40, 45, 1);
              font-size: 1vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 1.25vw;
              margin-left: 0vw;
            }
            .text_53 {
              width: 2.92vw;
              height: 0.73vw;
              overflow-wrap: break-word;
              color: rgba(88, 94, 118, 1);
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 5.21vw;
              margin: 0.26vw 0 0 0.31vw;
            }
            .text_54 {
              width: 7.3vw;
              height: 0.84vw;
              overflow-wrap: break-word;
              color: rgba(0, 181, 255, 1);
              font-size: 0.8vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.84vw;
              margin: 0vw 0 0 14.59vw;
            }
          }
          .text_55 {
            width: 23.55vw;
            height: 0.84vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 5.21vw;
            margin: 0.62vw 0 0 0.83vw;
          }
          .text_56 {
            width: 23.55vw;
            height: 0.84vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 5.21vw;
            margin: 0.52vw 0 0 0.83vw;
          }
          .text_57 {
            width: 23.55vw;
            height: 0.84vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 5.21vw;
            margin: 0.52vw 0 0 0.83vw;
          }
          .text_58 {
            width: 23.55vw;
            height: 0.84vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 5.21vw;
            margin: 0.52vw 0 0 0.83vw;
          }
          .text-wrapper_29 {
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0px 1px 2px 0px rgba(9, 25, 72, 0.06);
            background-image: linear-gradient(90deg, rgba(34, 107, 255, 1) 0, rgba(4, 72, 210, 1) 100%);
            border-radius: 8px;
            height: 1.67vw;
            width: 5.53vw;
            margin: 0.83vw 0 1.09vw 18.83vw;
            .text_59 {
              width: 1.46vw;
              height: 2vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: center;
              white-space: nowrap;
              line-height: 0.84vw;
              margin: 0.41vw 0 0 2.03vw;
            }
          }
          .text-wrapper_29:active {
            transform: scale(0.98);
            opacity: 0.9;
          }
        }
      }
    }
  }
}
:deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.28);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #969aaa;
}
:deep(.floating-input .el-input__wrapper.is-focus) {
  background: #f1f5fb !important;
  border-radius: 0px 0px 0px 0px !important;
  border: 1px solid #969aaa !important;
}
:deep(.el-input__inner) {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.8vw;
  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
  font-weight: normal;
  white-space: nowrap;
}
:deep(.el-input__suffix) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.2vw;
  height: 1.2vw;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
.floating-img {
  cursor: pointer;
}
.pagination-container {
  background: #fff;
  position: absolute;
  right: 0;
  z-index: 10;
  width: 40%;
  bottom: 4%;
  left: 25%;
  z-index: 10;
  text-align: center;
}

.pagination-container.hidden {
  display: none;
}

/* 设置分页容器字体 */
.el-pagination {
  font-family: 'Arial', sans-serif !important;
  font-size: 0.8vw !important;
  color: #333 !important;
}

/* 设置页码数字字体 */
.el-pagination .el-pager li {
  width: 2.5vw !important; /* 设置按钮宽度 */
  height: 2.5vw !important; /* 设置按钮高度 */
  padding: 0 !important; /* 去除默认内边距 */
  margin: 0 0.2vw !important; /* 设置左右间距 */
  line-height: 2.5vw !important; /* 保持文字垂直居中 */
  text-align: center !important;
  font-size: 1.2vw !important;
  color: #333 !important;
  border-radius: 4px !important; /* 可选：设置圆角 */
  background-color: #f5f7fa !important; /* 可选：设置默认背景色 */
}

/* 设置当前页码按钮样式 */
.el-pagination .el-pager li.active {
  width: 2.5vw !important;
  height: 2.5vw !important;
  padding: 0 !important;
  margin: 0 0.2vw !important;
  line-height: 2.5vw !important;
  text-align: center !important;
  font-size: 0.8vw !important;
  color: #ffffff !important;
  background-color: #2196f3 !important;
  border-radius: 4px !important;
}

/* 设置"上一页"、"下一页"按钮样式 */
.el-pagination .btn-prev,
.el-pagination .btn-next {
  width: 2.5vw !important;
  height: 2.5vw !important;
  padding: 0 !important;
  margin: 0 0.2vw !important;
  line-height: 2.5vw !important;
  text-align: center !important;
  font-size: 0.8vw !important;
  color: #333 !important;
  background-color: #f5f7fa !important;
  border-radius: 4px !important;
}

/* 设置每页数量下拉框按钮样式 */
.el-pagination .el-select .el-input input {
  width: 3vw !important;
  height: 2.5vw !important;
  padding: 0 0.5vw !important;
  font-size: 0.8vw !important;
  color: #333 !important;
  background-color: #f5f7fa !important;
  border-radius: 4px !important;
}
.study-btn {
  width: 5.78vw;
  height: 2.08vw;
  background-image: linear-gradient(90deg, #226bff 0%, #0448d2 100%);
  background-color: #226bfe;
  border-radius: 20px 20px 20px 20px;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 0.83vw;
  color: #ffffff;
  line-height: 0.83vw;
  text-align: left;
  font-style: normal;
}
.switch-btn {
  width: 2.78vw;
  height: 1.58vw;
  margin-top: 0.3vw;
  margin-left: 2vw;
  background-color: #13a981;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 0.83vw;
  color: #ffffff;
  text-align: left;
  font-style: normal;
}
:deep(.text_30 > span) {
  color: #409eff;
}
:deep(.text_30 > span:hover) {
  color: rgb(159.5, 206.5, 255);
}
.countdown {
  font-weight: bold;
  font-size: 0.7vw;
  color: #ff4d4f;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}
</style>
<style src="@/assets/styles/common.css" />
