<template>
  <div>
    <div class="group_16 flex-col">
      <div class="group_17 flex-row">
        <!-- <div class="text-wrapper_9 flex-col">
                <span class="text_25">全部课程</span>
              </div> -->
        <div
          class="text-wrapper_10 flex-col"
          v-for="(item, index) in topTabContent"
          :key="index"
          @click="changeTopTabContent(index)"
          :class="{ topTabContengActiveClass: index === topTabContentActive }"
        >
          <span class="text_26">{{ item.label }}</span>
        </div>
        <!-- <div class="text-wrapper_11 flex-col">
                <span class="text_27">进行中</span>
              </div>
              <div class="text-wrapper_12 flex-col">
                <span class="text_28">已归档</span>
              </div> -->
        <!-- 移除新增班级按钮，班级应该在课程详情页面中创建 -->
      </div>
      
      <!-- 搜索栏 -->
      <div style="margin: 1vw 0; display: flex; justify-content: flex-start; align-items: center; padding: 0 0.89vw;">
        <el-input
          v-model="searchText"
          placeholder="请输入班级名称"
          clearable
          style="width: 20vw;"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 班级列表 -->
      <div class="group_18" v-loading="loading">
                 <div class="block_6 flex-col" style="height: 10.67vw" v-for="(item, index) in tableData" :key="item.classId" @click="goDetailsClass(item.classId)">
          <div class="image-text_6 flex-col justify-between">
            <div class="group_19 flex-row" 
                 :style="item.coverImageUrl ? 
                   `background-image: url(${item.coverImageUrl}); background-size: cover; background-position: center;` : 
                   'background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);'
                 "
            >
              <div class="text-wrapper_14 flex-col" style="width: 3vw">
                <span class="text_30" v-if="item.classType == 1">独立班</span>
                <span class="text_30" v-if="item.classType == 2">平行班</span>
              </div>
              <!-- <img
                      class="label_5"
                      referrerpolicy="no-referrer"
                      src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG15da9e34d2d3859ca3ce71b19f612959.png"
                    /> -->
              <!-- <el-button class="label_5" :icon="DeleteFilled" circle /> -->
              <div class="group_19_bottom">
                <el-dropdown placement="bottom" @command="handleCommand">
                  <el-icon color="#fff"><MoreFilled /></el-icon>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{action: 'view', id: item.classId}">查看</el-dropdown-item>
                      <el-dropdown-item :command="{action: 'copy', id: item.classId}" v-if="item.status != 3">复制</el-dropdown-item>
                      <el-dropdown-item :command="{action: 'delete', id: item.classId}" v-if="item.status != 2">删除</el-dropdown-item>
                      <el-dropdown-item :command="{action: 'archive', id: item.classId}" v-if="item.status == 2">归档</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
            <div class="group_20 flex-row justify-between">
              <span class="text-group_6">{{ item.className }}</span>
              <div class="text-wrapper_24 flex-col" v-if="item.status == 1">
                <span class="text_43">未开课</span>
              </div>
              <div class="text-wrapper_15 flex-col" v-if="item.status == 2">
                <span class="text_31">进行中</span>
              </div>
              <div class="text-wrapper_33 flex-col" v-if="item.status == 3">
                <span class="text_55">已归档</span>
              </div>
            </div>
          </div>
          <div class="block_7 flex-row">
            <div class="image-text_7 flex-row justify-between">
              <img class="thumbnail_6" referrerpolicy="no-referrer" src="../../../assets/images/smartCourse/smartCourseDetails/star.png" />
              <span class="text-group_7">{{ item.description || '授课老师：未知' }}</span>
            </div>
          </div>
        </div>
        
                 <!-- 空状态 -->
         <div v-if="!loading && tableData.length === 0" style="width: 100%; text-align: center; padding: 2vw 0;">
           <el-empty description="暂无班级数据" />
         </div>
      </div>
      
             <!-- 分页 -->
       <div style="display: flex; justify-content: center; margin-top: 1vw;" v-if="total > 0">
         <el-pagination
           background
           v-model:current-page="currentPage"
           v-model:page-size="pageSize"
           :page-sizes="[10, 20, 50, 100]"
           layout="total, sizes, prev, pager, next, jumper"
           :total="total"
           @size-change="handleSizeChange"
           @current-change="handleCurrentChange"
         />
       </div>
    </div>
  </div>
  <!-- PopupClassAdd组件已移除，班级创建在课程详情页面进行 -->
</template>

<script setup>
import { ref, inject, getCurrentInstance, onMounted } from 'vue'
import { Search, DeleteFilled, MoreFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listClass, delClass, updateClass, copyClass as copyClassApi } from '@/api/edu/moocSmartCourseClass'

const { proxy } = getCurrentInstance()
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)
const searchText = ref('')
const topTabContentActive = ref(0)

const topTabContent = ref([
  {
    label: '全部班级',
    value: ''
  },
  {
    label: '未开课',
    value: '1'
  },
  {
    label: '进行中',
    value: '2'
  },
  {
    label: '已归档',
    value: '3'
  }
])

const tableData = ref([])

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  className: '', // 班级名称
  status: '', // 状态筛选
  type: 'created' // created-我创建的, joined-我加入的
})

// 获取班级列表
const getList = async () => {
  loading.value = true
  try {
    queryParams.value.pageNum = currentPage.value
    queryParams.value.pageSize = pageSize.value
    queryParams.value.className = searchText.value
    queryParams.value.status = topTabContent.value[topTabContentActive.value].value
    
    const response = await listClass(queryParams.value)
    if (response.code === 200) {
      tableData.value = response.rows || []
      total.value = response.total || 0
      

    } else {
      ElMessage.error(response.msg || '获取班级列表失败')
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取班级列表失败:', error)
    ElMessage.error('获取班级列表失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 切换状态筛选
const changeTopTabContent = (i) => {
  topTabContentActive.value = i
  currentPage.value = 1
  getList()
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  getList()
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  getList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

// 已移除新增班级功能，班级创建在课程详情页面进行

// 下拉菜单操作处理
const handleCommand = (command) => {
  const { action, id } = command
  switch (action) {
    case 'view':
      viewClass(id)
      break
    case 'copy':
      copyClass(id)
      break
    case 'delete':
      deleteClass(id)
      break
    case 'archive':
      archiveClass(id)
      break
  }
}

// 进入班级详情
const goClassTab = inject('goClassTab');
const goDetailsClass = (id) => {
  const params = { id: id, flag: true }
  goClassTab(params)
}

// 查看班级 - 跳转到班级详情页面
const viewClass = (id) => {
  goDetailsClass(id)
}

// 复制班级
const copyClass = async (id) => {
  try {
    await ElMessageBox.confirm('复制班级时，其下的课堂、学生、学习小组及教学活动将一并复制。', '提示', {
      confirmButtonText: '确定复制',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await copyClassApi(id)
    if (response.code === 200) {
      ElMessage.success('复制成功')
      getList()
    } else {
      ElMessage.error(response.msg || '复制失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('复制班级失败:', error)
      ElMessage.error('复制失败')
    } else {
      ElMessage.info('取消操作')
    }
  }
}

// 删除班级
const deleteClass = async (id) => {
  try {
    await ElMessageBox.confirm('删除班级后班级内的所有信息也将全部删除且无法找回，是否确认删除班级', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await delClass(id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      getList()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除班级失败:', error)
      ElMessage.error('删除失败')
    } else {
      ElMessage.info('取消操作')
    }
  }
}

// 归档班级
const archiveClass = async (id) => {
  try {
    await ElMessageBox.confirm('结束班级课堂活动也同步结束，且活动结束后无法再次开启', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await updateClass({ classId: id, status: 3 })
    if (response.code === 200) {
      ElMessage.success('归档成功')
      getList()
    } else {
      ElMessage.error(response.msg || '归档失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('归档班级失败:', error)
      ElMessage.error('归档失败')
    } else {
      ElMessage.info('取消操作')
    }
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.group_19_bottom {
  cursor: pointer;
  margin-left: 9.5vw;
  margin-top: 0.5vw;
}
</style>
