<template>
  <el-dialog v-model="dialogVisible" title="成绩考核权重" width="1000" :before-close="handleClose" style="margin-top: 30vh !important">
    <div class="popup_score_edit_but">
      <el-button v-if="!ztForm" type="primary" plain @click="edit">编辑</el-button>
      <el-button v-if="ztForm" type="primary" @click="save">保存</el-button>
      <el-button v-if="ztForm" @click="canc">取消</el-button>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column :label="item.label" v-for="item in columnList" :key="item.prop">
        <template #default="scope">
          <template v-if="ztForm"> 
            <el-input 
              v-model.number="scope.row[item.prop]" 
              style="width: 60px" 
              type="number"
              :min="0"
              :max="100"
            />% 
          </template>
          <template v-else>{{ scope.row[item.prop] }}%</template>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 权重总和提示 -->
    <div v-if="ztForm" class="weight-total-tip">
      <span :class="{ 'weight-error': getTotalWeight() !== 100 }">
        当前权重总和：{{ getTotalWeight() }}%
        <span v-if="getTotalWeight() !== 100" class="error-text">（总和必须为100%）</span>
      </span>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
// @ts-ignore
import { updateCourse } from '@/api/edu/moocSmartCourse'

interface WeightData {
  classroomQuestionWeight: number
  homeworkWeight: number
  examWeight: number
  testWeight: number
  bookStudyWeight: number
  performanceWeight: number
}

const dialogVisible = ref(false)
const formRef = ref(null)
const ztForm = ref(false)
const courseId = ref<number | null>(null)
const originalData = ref<WeightData>({
  classroomQuestionWeight: 20,
  homeworkWeight: 20,
  examWeight: 20,
  testWeight: 15,
  bookStudyWeight: 15,
  performanceWeight: 10
})

const tableData = ref([
  {
    classroomQuestionWeight: 20,
    homeworkWeight: 20,
    examWeight: 20,
    testWeight: 15,
    bookStudyWeight: 15,
    performanceWeight: 10
  }
])
const columnList = [
  {
    label: '课堂提问成绩权重',
    prop: 'classroomQuestionWeight'
  },
  {
    label: '作业成绩权重',
    prop: 'homeworkWeight'
  },
  {
    label: '考试成绩权重',
    prop: 'examWeight'
  },
  {
    label: '测试成绩权重',
    prop: 'testWeight'
  },
  {
    label: '教材学习成绩权重',
    prop: 'bookStudyWeight'
  },
  {
    label: '平时成绩权重',
    prop: 'performanceWeight'
  }
]
// 开启弹窗
const open = (id: number, weightData?: Partial<WeightData>) => {
  courseId.value = id
  dialogVisible.value = true
  ztForm.value = false
  
  // 设置权重数据，如果没有传入则使用默认值
  const weights: WeightData = {
    classroomQuestionWeight: weightData?.classroomQuestionWeight || 20,
    homeworkWeight: weightData?.homeworkWeight || 20,
    examWeight: weightData?.examWeight || 20,
    testWeight: weightData?.testWeight || 15,
    bookStudyWeight: weightData?.bookStudyWeight || 15,
    performanceWeight: weightData?.performanceWeight || 10
  }
  
  tableData.value = [weights]
  originalData.value = { ...weights }
  
  if (formRef.value) formRef.value.resetFields()
}

// 编辑
const edit = () => {
  ztForm.value = true
}

// 计算权重总和
const getTotalWeight = () => {
  const row = tableData.value[0]
  const total = (Number(row.classroomQuestionWeight) || 0) + 
                (Number(row.homeworkWeight) || 0) + 
                (Number(row.examWeight) || 0) + 
                (Number(row.testWeight) || 0) + 
                (Number(row.bookStudyWeight) || 0) + 
                (Number(row.performanceWeight) || 0)
  return total
}

// 验证权重总和
const validateWeights = () => {
  return getTotalWeight() === 100
}

// 保存
const save = async () => {
  if (!courseId.value) {
    ElMessage.error('课程ID不能为空')
    return
  }
  
  if (!validateWeights()) {
    ElMessage.error('权重总和必须等于100%')
    return
  }
  
  try {
    const weightData = {
      courseId: courseId.value,
      ...tableData.value[0]
    }
    
    console.log('保存权重数据:', weightData) // 调试日志
    
    await updateCourse(weightData)
    ElMessage.success('权重设置保存成功')
    ztForm.value = false
    originalData.value = { ...tableData.value[0] }
  } catch (error) {
    ElMessage.error('保存失败，请重试')
    console.error('保存权重失败:', error)
  }
}

// 取消
const canc = () => {
  tableData.value = [{ ...originalData.value }]
  ztForm.value = false
}

// 关闭弹窗
const handleClose = () => {
  if (ztForm.value) {
    ElMessageBox.confirm('您有未保存的修改，确定要关闭吗？', '确认关闭', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      tableData.value = [{ ...originalData.value }]
      ztForm.value = false
      dialogVisible.value = false
    }).catch(() => {})
  } else {
    dialogVisible.value = false
  }
}

defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.popup_score_edit_but {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.weight-total-tip {
  margin-top: 15px;
  text-align: center;
  font-size: 14px;
  
  .weight-error {
    color: #f56c6c;
    font-weight: bold;
  }
  
  .error-text {
    font-size: 12px;
    margin-left: 5px;
  }
}
</style>
