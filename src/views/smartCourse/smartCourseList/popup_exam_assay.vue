<template>
  <el-dialog v-model="dialogVisible" title="试卷分析" width="680" :before-close="handleClose" style="margin-top: 10vh !important">
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
const dialogVisible = ref(false)
// 开启弹窗
const open = () => {
  dialogVisible.value = true
}
// 关闭弹窗
const confirmFrom = () => {
  dialogVisible.value = false
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped></style>
