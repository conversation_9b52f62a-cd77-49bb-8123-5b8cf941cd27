<template>
  <el-dialog v-model="dialogVisible" title="新增课程" width="600" :before-close="handleClose" style="margin-top: 8vh !important">
    <el-form ref="formRef" :model="form" label-width="auto" class="demo-ruleForm" :rules="rules">
      <el-form-item label="课程类别" prop="courseType">
        <el-radio-group v-model="form.courseType" @change="onCourseTypeChange">
          <el-radio value="1">教务课程</el-radio>
          <el-radio value="2">自主课程</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="课程名称" prop="courseName">
        <el-select v-if="form.courseType == '1'" v-model="form.courseName" placeholder="请选择课程" @change="onBookNameChange">
          <el-option v-for="item in courseNameList" :key="item.courseId" :label="item.courseName" :value="item.courseId" />
        </el-select>
        <el-input v-else show-word-limit maxlength="11" v-model="form.courseName" type="text" autocomplete="off" placeholder="请输入课程名称" />
      </el-form-item>
      <el-form-item label="课程编码" prop="courseCode">
        <el-input show-word-limit maxlength="50" v-model="form.courseCode" type="text" autocomplete="off" placeholder="请输入课程编码" />
      </el-form-item>
<!--      <el-form-item label="所属专业大类" prop="categoryId">-->
<!--        <el-input show-word-limit maxlength="50" v-model="form.categoryName" type="text" autocomplete="off" placeholder="请输入所属专业大类" />-->
<!--      </el-form-item>-->
      <el-form-item label="所属专业大类" prop="categoryId">
        <el-tree-select
          v-model="form.categoryId"
          :data="categoryList"
          :props="{ label: 'name', children: 'children' }"
          value-key="id"
          placeholder="请选择专业大类"
          check-strictly
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="课时"  prop="hour">
        <el-input show-word-limit maxlength="11" v-model="form.hour" type="text" autocomplete="off" placeholder="请输入该课程所学课时数" />
      </el-form-item>
      <el-form-item label="关联云教材" prop="bookId">
        <el-select v-model="form.bookId" placeholder="请选择关联云教材">
          <el-option v-for="item in bookList" :key="item.bookId" :label="item.bookName" :value="item.bookId" />
        </el-select>
      </el-form-item>
      <el-form-item label="课程描述" prop="description">
        <el-input v-model="form.description" :rows="3" type="textarea" show-word-limit="true" maxlength="200" placeholder="请输入考试要求" />
      </el-form-item>
      <el-form-item label="添加课程封面" prop="coverImageUrl">

        <el-upload
          class="single-upload"
          :action="uploadUrl"
          :headers="headers"
          :show-file-list="false"
          :before-upload="beforeAvatarUpload"
          :http-request="httpRequest"
          :on-success="handleSuccess"
          :auto-upload="true"
          :disabled="!!form.coverImageUrl"
        >
          <div v-if="form.coverImageUrl" class="upload-item">
            <img class="upload-thumbnail" :src="form.coverImageUrl" alt="" />
            <span class="upload-actions">
              <span class="upload-action" @click.stop="handlePictureCardPreview">
                <el-icon><zoom-in /></el-icon>
              </span>
              <span class="upload-action" @click.stop="handleDownload">
                <el-icon><Download /></el-icon>
              </span>
              <span class="upload-action" @click.stop="handleRemove">
                <el-icon><Delete /></el-icon>
              </span>
            </span>
          </div>
          <div v-else class="upload-placeholder">
            <el-icon class="upload-icon"><Plus /></el-icon>
            <div class="upload-text">点击上传</div>
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmFrom">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 图片预览对话框 -->
  <el-dialog v-model="dialogVisibleImg" title="图片预览">
    <img w-full :src="dialogImageUrl" alt="Preview Image" style="width: 100%;" />
  </el-dialog>

</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken } from '@/utils/auth'
import { OssService } from '@/utils/aliOss.js';
import { getCoursePlanList } from '@/api/edu/moocSmartCoursePlan'
import { selectAllBookList, selectSubjectList } from '@/api/edu/edubook'
import { saveSmartCourse } from "@/api/edu/smartCourse.js";

const emit = defineEmits(['refreshPage'])

const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/file/upload') // 上传的图片服务器地址
const headers = ref({
  Authorization: 'Bearer ' + getToken()
})
const dialogVisible = ref(false)
const dialogVisibleImg = ref(false)
const dialogImageUrl = ref('')
const formRef = ref(null)
const form = ref({
  courseType:'1',
  coverImageUrl: '',
  hour: '',
  categoryId: '',
})
const rules = ref({
  courseType: [{ required: true, message: '课程类别不能为空', trigger: 'change' }],
  courseName: [{ required: true, message: '课程名称不能为空', trigger: 'change' }],
  courseCode: [{ required: true, message: '课程编码不能为空', trigger: ["blur", "change"] }],
  categoryId: [{ required: true, message: '请选择所属专业大类', trigger: 'change' }],
  hour: [{ required: true, message: '课时不能为空', trigger: ["blur", "change"] }],
  bookId: [{ required: true, message: '请选择关联云教材', trigger: 'change' }],
  description: [{ required: true, message: '课程描述不能为空', trigger: ["blur", "change"] }],
  coverImageUrl: [{ required: true, message: '请上传封面图片', trigger: ["blur", "change"] }],
})
const courseNameList = ref([]);
const bookList = ref([]);
const categoryList = ref([]);

const onCourseTypeChange = (val) => {
  formRef.value.resetFields();
  form.value.courseType = val;
}
// 开启弹窗
const open = () => {
  dialogVisible.value = true
  // 清空校验
  if (formRef.value) formRef.value.resetFields()
}
// 上传
const beforeAvatarUpload = rawFile => {
  if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/jpg' && rawFile.type !== 'image/png' && rawFile.type !== 'application/pdf') {
    ElMessage.error('上传失败！请上传 jpg, jpeg, png, pdf格式文件！')
    return false
  } else if (rawFile.size / 1024 / 1024 > 60) {
    ElMessage.error('上传失败！文件大小不能超过 60MB!')
    return false
  }
  return true
}
const httpRequest = async (options) => {
  try {
    const result = await OssService(options.file);
    options.onSuccess(result.result, options.file);
  } catch (err) {
    options.onError(err);
  }
};
// 上传成功回调
const handleSuccess = (response, file) => {
  if (response.code === 200) {
    form.value.coverImageUrl = response.data.url
    ElMessage.success('上传成功')
  }
}

// 确定
const confirmFrom = () => {
  formRef.value.validate(valid => {
    if (valid) {
      saveSmartCourse(form.value).then(res => {
        if (res.code === 200) {
          ElMessage.success('新增成功')
          // 单个文件URL
          console.log('封面图片URL:', form.value.coverImageUrl)
          console.log('submit!')
          dialogVisible.value = false
          emit('refreshPage');
        }
      })

    } else {
      console.log('error submit!')
    }
  })
}
// 移除图片
const handleRemove = () => {
  form.value.coverImageUrl = ''
  ElMessage.success('图片已移除')
}

// 预览图片
const handlePictureCardPreview = () => {
  dialogImageUrl.value = form.value.coverImageUrl
  dialogVisibleImg.value = true
}

// 下载图片
const handleDownload = () => {
  if (form.value.coverImageUrl) {
    const link = document.createElement('a')
    link.href = form.value.coverImageUrl
    link.download = '课程封面图片'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}


const handleClose = () => {
  dialogVisible.value = false
}
// 查询教学计划接口
const getCoursePlan = () => {
  getCoursePlanList().then(res => {
    courseNameList.value = res.data;
    console.log(courseNameList.value);
  })
}
// 查询所属专业大类
const getCategoryList = () => {
  selectSubjectList({}).then(res => {
    categoryList.value = res.data;
  })
}


// 查询云教材接口
const getBookList = () => {
  selectAllBookList({}).then(res => {
    bookList.value = res.data;
  })
}

// 课程名称改变时带出信息
const onBookNameChange = (val) => {
  courseNameList.value.forEach(item => {
    if (item.courseId == val) {
      form.value.courseName = item.courseName;
      form.value.courseCode = item.courseCode;
      form.value.categoryId = item.categoryId;
      form.value.hour = item.hour;
      form.value.description = item.description;
      form.value.bookId = item.bookId;
      form.value.bookName = item.bookName;
      form.value.coverImageUrl = item.coverImageUrl;
    }
  })
}


function initList(){
  getCoursePlan();
  getBookList();
  getCategoryList();
}

onMounted(() => {
  initList();
})
defineExpose({
  open
})

</script>
<style lang="scss" scoped>
.single-upload {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    width: 148px;
    height: 148px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.el-upload:hover) {
    border-color: var(--el-color-primary);
  }

  :deep(.el-upload.is-disabled) {
    cursor: not-allowed;
    border-color: var(--el-border-color);
  }

  :deep(.el-upload.is-disabled:hover) {
    border-color: var(--el-border-color);
  }
}

.upload-item {
  position: relative;
  width: 146px;
  height: 146px;
  border-radius: 6px;
  overflow: hidden;

  .upload-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .upload-actions {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 0;
    font-size: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    transition: opacity 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;

    .upload-action {
      cursor: pointer;
      padding: 5px;
      border-radius: 4px;
      transition: background-color 0.3s;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }
  }

  &:hover .upload-actions {
    opacity: 1;
  }
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #8c939d;

  .upload-icon {
    font-size: 28px;
    margin-bottom: 8px;
  }

  .upload-text {
    font-size: 14px;
  }
}
</style>
