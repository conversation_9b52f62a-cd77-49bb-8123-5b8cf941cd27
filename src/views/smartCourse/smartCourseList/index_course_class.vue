<template>
  <div class="index_course_class">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="我创建的" name="1">
        <ClassCreated />
      </el-tab-pane>
      <el-tab-pane label="我加入的" name="2">
        <ClassJoined />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import ClassCreated from './class_created.vue'
import ClassJoined from './class_joined.vue'
const { proxy } = getCurrentInstance()
const activeName = ref('1')
const handleClick = (tab, event) => {
  console.log(tab.props.name)
}
</script>

<style lang="scss">
.index_course_class {
  background-color: rgb(255, 255, 255);
    border-radius: 8px;
    margin-top: 0.94vw;
  .el-tabs__header {
    margin: 10px 20px 0 20px;
  }
}
</style>
