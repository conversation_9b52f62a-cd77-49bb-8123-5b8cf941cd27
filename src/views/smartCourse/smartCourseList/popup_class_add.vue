<template>
  <el-dialog v-model="dialogVisible" title="新增班级" width="600" :before-close="handleClose" style="margin-top: 8vh !important">
    <el-form ref="formRef" :model="form" label-width="auto" class="demo-ruleForm" :rules="rules">
      <el-form-item label="班级名称" prop="ksmc">
        <el-input show-word-limit maxlength="50" v-model="form.ksmc" type="text" autocomplete="off" placeholder="请输入班级名称" />
      </el-form-item>
      <el-form-item label="班级类" prop="kslx">
        <el-radio-group v-model="form.kslx">
          <el-radio value="1">独立班级</el-radio>
          <el-radio value="2">平行班级</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="关联云教材">
        <el-select v-model="form.textbookId" placeholder="请选择关联云教材">
          <el-option 
            v-for="item in textbookOptions" 
            :key="item.bookId" 
            :label="item.bookName" 
            :value="item.bookId" 
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关联教学班">
        <el-select 
          v-model="form.classIds" 
          multiple 
          placeholder="请选择关联教学班" 
          collapse-tags 
          collapse-tags-tooltip
          :multiple-limit="10"
          style="width: 100%"
        >
          <el-option v-for="item in eduClassOptions" :key="item.eduClassId || item.id" :label="item.eduClassName || item.className || item.name" :value="item.eduClassId || item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="班级描述">
        <el-input v-model="form.nr" :rows="3" type="textarea" show-word-limit="true" maxlength="500" placeholder="请输入班级描述" />
      </el-form-item>
      <el-form-item label="添加班级封面" prop="fileList">
        <el-upload
          v-model:file-list="form.fileList"
          :action="uploadUrl"
          :headers="headers"
          :before-upload="beforeAvatarUpload"
          :on-success="handleSuccess"
          :on-remove="handleRemove"
          :on-exceed="handleExceed"
          :limit="1"
          list-type="picture-card"
          :auto-upload="true"
        >
          <el-icon><Plus /></el-icon>

          <template #file="{ file }">
            <div>
              <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                  <el-icon><zoom-in /></el-icon>
                </span>
                <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleDownload(file)">
                  <el-icon><Download /></el-icon>
                </span>
                <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                  <el-icon><Delete /></el-icon>
                </span>
              </span>
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmFrom">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog v-model="dialogVisibleImg">
    <img w-full :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken } from '@/utils/auth'
import { selectSubjectList, selectAllBookList } from '@/api/edu/edubook'
import { addClass, updateClass, getClass, addClassWithMembers } from '@/api/edu/smartCourseClass'
import { listEduClass } from '@/api/edu/eduClass'
import useUserStore from '@/store/modules/user'

// 定义事件
const emit = defineEmits(['success'])

const userStore = useUserStore()
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/file/upload') // 上传的图片服务器地址
const headers = ref({
  Authorization: 'Bearer ' + getToken()
})
const dialogImageUrl = ref('')
const disabled = ref(false)
const dialogVisible = ref(false)
const dialogVisibleImg = ref(false)
const formRef = ref(null)
const changeValList = ref([])
const changeValIds = ref([])
const tableRef = ref(null)
const form = ref({
  ksmc: '',
  kskc: '',
  ksbj: '',
  kslx: '1',
  kssj: '',
  jssj: '',
  textbookId: '',
  classIds: [],
  nr: '',
  fileList: [],
  coverImageUrl: null,
  courseId: null
})
const rules = ref({
  ksmc: [{ required: true, message: '班级名称不能为空', trigger: 'change' }],
  kslx: [{ required: true, message: '班级类不能为空', trigger: 'change' }]
})
const textbookOptions = ref([])
const subjectOptions = ref([])
const eduClassOptions = ref([])



// 获取教材列表
const getTextbooks = () => {
  selectAllBookList({}).then(res => {
    if (res.code === 200) {
      // 优先使用 res.data，这是教材API的正确数据字段
      const bookData = res.data || res.rows || []
      textbookOptions.value = bookData
    } else {
      console.error('获取教材列表失败:', res.msg)
      textbookOptions.value = []
    }
  }).catch(error => {
    console.error('教材列表接口调用失败:', error)
    textbookOptions.value = []
  })
}

// 获取专业列表
const getSubjects = () => {
  selectSubjectList({}).then(res => {
    subjectOptions.value = res.data
  })
}

// 获取教务班级列表
const getEduClasses = () => {
  listEduClass({}).then(res => {
    if (res.code === 200) {
      eduClassOptions.value = res.rows || []
    } else {
      console.error('获取教务班级列表失败:', res.msg)
      eduClassOptions.value = []
    }
  }).catch(error => {
    console.error('教务班级列表接口调用失败:', error)
    eduClassOptions.value = []
  })
}

onMounted(() => {
  getTextbooks()
  getSubjects()
  getEduClasses()
})

// 开启弹窗
const open = (data = {}) => {
  dialogVisible.value = true
  
  if (data.id) {
    getClass(data.id).then(res => {
      const classData = res.data
      // 处理单个classId转换为数组classIds
      if (classData.classId && !classData.classIds) {
        classData.classIds = classData.classId ? [classData.classId] : []
      }
      form.value = classData
    })
  } else {
    form.value = {
      ksmc: '',
      kskc: '',
      ksbj: '',
      kslx: '1',
      kssj: '',
      jssj: '',
      textbookId: '',
      classIds: [],
      nr: '',
      fileList: [],
      coverImageUrl: null,
      courseId: data.courseId
    }
  }
  // 清空校验
  if (formRef.value) formRef.value.resetFields()
}
// 上传
const beforeAvatarUpload = rawFile => {
  if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/jpg' && rawFile.type !== 'image/png' && rawFile.type !== 'application/pdf') {
    ElMessage.error('上传失败！请上传 jpg, jpeg, png, pdf格式文件！')
    return false
  } else if (rawFile.size / 1024 / 1024 > 60) {
    ElMessage.error('上传失败！文件大小不能超过 60MB!')
    return false
  }
  return true
}

// 上传成功回调  
const handleSuccess = (response, file) => {
  if (response.code === 200) {
    // 确保coverImageUrl是字符串类型
    const imageUrl = typeof response.data === 'string' ? response.data : (response.data?.url || response.data?.path || null)
    form.value.coverImageUrl = imageUrl
  }
}

// 文件个数超出限制
const handleExceed = () => {
  ElMessage.warning('班级封面只能上传一张图片！')
}

// 确定
const confirmFrom = () => {
  formRef.value.validate(valid => {
    if (valid) {
      if (form.value.id) {
        // 编辑模式，使用原有API
        updateClass(form.value).then(() => {
          ElMessage.success('修改成功')
          dialogVisible.value = false
          // 通知父组件刷新列表
          emit('success')
        })
      } else {
        // 新增模式，后端自动生成班级码
        
        // 确保coverImageUrl是字符串类型或null
        let coverImageUrl = null
        if (form.value.coverImageUrl) {
          if (typeof form.value.coverImageUrl === 'string') {
            coverImageUrl = form.value.coverImageUrl
          } else if (typeof form.value.coverImageUrl === 'object') {
            coverImageUrl = form.value.coverImageUrl.url || form.value.coverImageUrl.path || null
          }
        }
        
        const requestData = {
          classInfo: {
            courseId: form.value.courseId,
            className: form.value.ksmc,
            // classCode 由后端自动生成，不需要前端传递
            classType: parseInt(form.value.kslx),
            userId: userStore.id, // 获取当前登录用户ID
            bookId: form.value.textbookId || null,
            description: form.value.nr || null,
            coverImageUrl: coverImageUrl, // 确保是字符串或null
            status: 1, // 默认状态为未开课
            eduClassId: form.value.classIds && form.value.classIds.length > 0 ? form.value.classIds.join(',') : null // 关联教学班id，用逗号分割
          },
          eduClassIds: form.value.classIds || []
        }
        

        
        addClassWithMembers(requestData).then((response) => {
          // 如果后端返回了班级码，可以在成功提示中显示
          const successMsg = response.data && response.data.classCode 
            ? `新增成功，班级码：${response.data.classCode}` 
            : '新增成功！'
          ElMessage.success(successMsg)
          dialogVisible.value = false
          // 通知父组件刷新列表
          emit('success')
        }).catch(error => {
          console.error('新增失败:', error)
          ElMessage.error('新增失败: ' + (error.message || '未知错误'))
        })
      }
    } else {
      console.log('error submit!')
    }
  })
}
const handleRemove = file => {
  form.value.fileList = form.value.fileList.filter(item => item.uid !== file.uid)
  
  // 如果删除的是当前封面图片，清空coverImageUrl
  if (form.value.coverImageUrl && file.response) {
    const fileUrl = typeof file.response.data === 'string' ? file.response.data : (file.response.data?.url || file.response.data?.path)
    const currentUrl = typeof form.value.coverImageUrl === 'string' ? form.value.coverImageUrl : (form.value.coverImageUrl?.url || form.value.coverImageUrl?.path)
    
    if (fileUrl === currentUrl) {
      form.value.coverImageUrl = null
    }
  }
}

const handlePictureCardPreview = file => {
  dialogImageUrl.value = file.url
  dialogVisibleImg.value = true
}

const handleDownload = file => {
  // 下载文件处理
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped></style>
