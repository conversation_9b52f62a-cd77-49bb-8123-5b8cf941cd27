<template>
  <div class="block_4 flex-col">
    <div>
      <div class="group_16 flex-col">
        <div class="group_17 flex-row">
          <div
            class="text-wrapper_10 flex-col"
            v-for="(item, index) in smartCourseStatus"
            :key="index"
            @click="changeTopTabContent(index)"
            :class="{ topTabContengActiveClass: index === topTabContentActive }"
          >
            <span class="text_26">{{ item.label }}</span>
          </div>
        </div>
        <div style="padding: 0 20px">
          <el-table :data="tableData" class="wait_approval_table">
            <el-table-column prop="zymc" label="作业名称" fixed="left" width="100">
              <template #default="scope">
                <el-button type="primary" link @click="goDetails(scope.row)">
                  {{ scope.row.zymc }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column prop="lx" label="类型">
              <template #default="scope">
                <span v-if="scope.row.lx === '1'">题库作业</span>
                <span v-if="scope.row.lx === '2'">附件作业</span>
                <span v-if="scope.row.lx === '3'">登分作业</span>
              </template>
            </el-table-column>
            <el-table-column prop="sskc" label="所属课程" />
            <el-table-column prop="ssbj" label="所属班级" />
            <el-table-column prop="pffs" label="评分方式">
              <template #default="scope">
                <span v-if="scope.row.pffs === '1'">教师评分</span>
                <span v-if="scope.row.pffs === '2'">组间评分</span>
                <span v-if="scope.row.pffs === '3'">组内打分</span>
              </template>
            </el-table-column>
            <el-table-column prop="kssj" label="开始时间" width="100" />
            <el-table-column prop="jssj" label="结束时间" width="100" />
            <el-table-column prop="yp" label="已批" />
            <el-table-column prop="wp" label="未批" />
            <el-table-column prop="wj" label="未交" />
            <el-table-column prop="zt" label="状态">
              <template #default="scope">
                <span v-if="scope.row.zt === '1'">未开始</span>
                <span v-if="scope.row.zt === '2'">进行中</span>
                <span v-if="scope.row.zt === '3'">已结束</span>
              </template>
            </el-table-column>
            <el-table-column prop="pgzt" label="批改状态">
              <template #default="scope">
                <span v-if="scope.row.zt === '1'">待批改</span>
                <span v-if="scope.row.zt === '2'">已批改</span>
                <span v-if="scope.row.zt === '3'">无批改</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-container">
          <el-pagination
            background="true"
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[100, 200, 300, 400]"
            :size="size"
            layout="total, prev, pager, next, jumper"
            :total="10"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
  <PopupWaHomework ref="popupWaHomework" />
</template>
<script setup>
import { Search } from '@element-plus/icons-vue'
import { ref } from 'vue'
import PopupWaHomework from './popup_wa_homework.vue'
const emits = defineEmits(['detailsCourse'])

const topTabContentActive = ref(0)
const smartCourseStatus = ref([
  {
    label: '全部',
    value: '1'
  },
  {
    label: '待批改',
    value: '2'
  },
  {
    label: '已批改',
    value: '3'
  }
])
const tableData = ref([
  {
    zymc: '作业名称',
    lx: '1',
    sskc:'啊实打实的',
    ssbj:'古典风格',
    pffs:'1',
    kssj: '2021-09-09 12:00:00',
    jssj: '2021-09-09 12:00:00',
    yp: '10',
    wp: '10',
    wj: '10',
    zt: '1',
    pgzt: '1'
  },
  {
    zymc: '作业名称',
    lx: '2',
    sskc:'啊实打实的',
    ssbj:'古典风格',
    pffs:'1',
    kssj: '2021-09-09 12:00:00',
    jssj: '2021-09-09 12:00:00',
    yp: '10',
    wp: '10',
    wj: '10',
    zt: '1',
    pgzt: '1'
  },
  {
    zymc: '作业名称',
    lx: '3',
    sskc:'啊实打实的',
    ssbj:'古典风格',
    pffs:'1',
    kssj: '2021-09-09 12:00:00',
    jssj: '2021-09-09 12:00:00',
    yp: '10',
    wp: '10',
    wj: '10',
    zt: '1',
    pgzt: '1'
  },
])
const popupWaHomework = ref(null)

const goDetails = row => {
  popupWaHomework.value.open(row)
}
const changeTopTabContent = i => {
  topTabContentActive.value = i
}
const handleSizeChange = val => {
  console.log(`每页 ${val} 条`)
}
</script>
<style lang="scss" scoped>
.wait_approval_table {
  margin-top: 20px;
}
</style>
