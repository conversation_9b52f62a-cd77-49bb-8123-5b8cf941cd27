<template>
  <el-dialog v-model="dialogVisible" title="作业" width="1000" :before-close="handleClose"
    style="margin-top: 10vh !important">
    <div class="exam_manage_operate">
      <div>
        <el-input v-model="input2" style="width: 240px; margin-right: 10px" placeholder="请输入小组名称"
          :suffix-icon="Search" />
        <el-select v-model="value" placeholder="状态" style="width: 140px">
          <el-option v-for="item in ztList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div>
        <el-button type="primary" @click="addNotice">导出</el-button>
      </div>
    </div>
    <el-table ref="tableRef" :data="tableData" style="width: 100%;margin-top: 20px;">
      <el-table-column type="index" label="序号" align="center" width="50" />
      <el-table-column prop="xzmc" align="center" label="小组名称" />
      <el-table-column prop="xzcy" align="center" label="小组乘员" />
      <el-table-column prop="zt" align="center" label="状态">
        <template #default="scope">
          {{ ztList[scope.row.zt / 1 - 1].label }}
        </template>
      </el-table-column>
      <el-table-column prop="tjsj" align="center" label="提交时间" />
      <el-table-column prop="df" align="center" label="得分">
        <template #default="scope">
          {{ scope.row.df + '/' + scope.row.zf }}
        </template>
      </el-table-column>
      <el-table-column prop="dfr" align="center" label="打分人" />
      <el-table-column fixed="right" align="center" label="操作" min-width="100">
        <template #default="scope">
          <!-- 实装请注意，当作业类型为登分时，不能查看 -->
          <el-button type="primary" link @click="goEdit(scope.row.id)" v-if="rowParams.lx != '3'">查看</el-button>
          <el-button type="primary" link @click="goScore(scope.row.id)">打分</el-button>
          <el-button type="primary" link @click="withdraw(scope.row.id)">撤回</el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <PopupHomeworkQuestion ref="popupHomeworkQuestion" />
  <PopupHomeworkAtta ref="popupHomeworkAtta" />
  <PopupHomeworkScore ref="popupHomeworkScore" />
</template>

<script setup>
import { ref } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import PopupHomeworkQuestion from './popup_homework_question.vue'
import PopupHomeworkAtta from './popup_homework_atta.vue'
import PopupHomeworkScore from './popup_homework_score.vue'
const dialogVisible = ref(false)
const formRef = ref(null)
const form = ref({
  bt: '',
  lx: '1',
  fs: '1'
})
const tableData = ref([
  {
    xzmc: '123',
    xzcy: '123',
    zt: '1',
    tjsj: '2021-12-12 12:12:12',
    df: '20',
    zf: '100',
    dfr: '123',
  }
])
const ztList = ref([
  {
    value: '1',
    label: '待批改'
  },
  {
    value: '2',
    label: '已批改'
  },
  {
    value: '3',
    label: '未提交'
  }
])
const rowParams = ref()
// 开启弹窗
const open = (row) => {
  dialogVisible.value = true
  rowParams.value = row
  console.log(row)
  if (formRef.value) formRef.value.resetFields()
}
const popupHomeworkQuestion = ref(null)
const popupHomeworkAtta = ref(null)
const popupHomeworkScore = ref(null)

const goScore = (id) => {
  popupHomeworkScore.value.open(id)
}
const goEdit = (id) => {
  if (rowParams.value.lx == '1') {
    popupHomeworkQuestion.value.open(id)
  } else if (rowParams.value.lx == '2') {
    popupHomeworkAtta.value.open(id)
  }
}
// 撤回
const withdraw = (id) => {
  ElMessageBox.confirm('若确认撤回该该学生本次作业内容，则学生需要重新作答作业。您确认撤回该学生的作业吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      ElMessage({
        type: 'success',
        message: '撤回成功'
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消操作'
      })
    })
}
// 关闭弹窗
const confirmFrom = () => {
  formRef.value.validate(valid => {
    if (valid) {
      console.log('submit!')
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.exam_manage_operate {
  display: flex;
  justify-content: space-between;
}
</style>
