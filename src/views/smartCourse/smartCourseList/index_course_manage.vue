<template>
  <div class="block_4 flex-col">
    <div class="group_7 flex-row justify-between" v-loading="loading" element-loading-text="加载统计数据中...">
      <div class="group_8 flex-row">
        <img
          class="image_4"
          referrerpolicy="no-referrer"
          :src="numCourseKczs"
        />
        <div class="text-wrapper_3 flex-col justify-between">
          <span class="text_9">{{ statisticsData.totalCourses || 0 }}</span>
          <span class="text_10">课程总数</span>
        </div>
        <img
          class="image_5"
          referrerpolicy="no-referrer"
          :src="numCourseBjzs"
        />
        <div class="text-wrapper_4 flex-col justify-between">
          <span class="text_11">{{ statisticsData.totalClasses || 0 }}</span>
          <span class="text_12">班级总数</span>
        </div>
        <img
          class="image_6"
          referrerpolicy="no-referrer"
          :src="numCourseKtzs"
        />
        <div class="text-wrapper_5 flex-col justify-between">
          <span class="text_13">{{ statisticsData.totalLessons || 0 }}</span>
          <span class="text_14">课堂总数</span>
        </div>
        <img
          class="image_7"
          referrerpolicy="no-referrer"
          :src="numCourseKszs"
        />
        <div class="text-wrapper_6 flex-col justify-between">
          <span class="text_15">{{ statisticsData.totalExams || 0 }}</span>
          <span class="text_16">考试总数</span>
        </div>
        <img
          class="image_8"
          referrerpolicy="no-referrer"
          :src="numCourseZyzs"
        />
        <div class="text-wrapper_7 flex-col justify-between">
          <span class="text_17">{{ statisticsData.totalHomeworks || 0 }}</span>
          <span class="text_18">作业总数</span>
        </div>
      </div>
      <div class="group_9 flex-col">
        <div class="box_7 flex-col">
          <img
            class="label_4"
            referrerpolicy="no-referrer"
            :src="numCourseSh"
          />
          <div class="text-wrapper_8 flex-col">
            <span class="text_19">{{ statisticsData.pendingApprovals || 0 }}</span>
          </div>
        </div>
        <span class="text_20">待审核</span>
      </div>
    </div>
    <div class="group_10 flex-row">
      <div class="group_11 flex-col justify-between" v-for="(item, index) in topTab" :key="index" @click="changeTopTab(index)">
        <span class="text_21" :class="{ topTabActiveClass: index === topTabActive }">{{ item.label }}</span>
        <div v-if="index === topTabActive" class="group_12 flex-col"></div>
      </div>
<!--      <el-input v-if="topTabActive == 0" class="group_14 flex-row" placeholder="请输入课程名称" v-model="searchVar" :suffix-icon="Search" />-->
      <el-input
          v-if="topTabActive == 0"
          v-model="params.searchVar"
          class="group_14 flex-row"
          placeholder="请输入课程名称"
      >
        <template #append>
          <el-button :icon="Search" @click="handleSearch" />
        </template>
      </el-input>
      <el-input
          v-if="topTabActive == 1"
          v-model="params.searchVar"
          class="group_14 flex-row"
          placeholder="请输入班级名称"
      >
        <template #append>
          <el-button :icon="Search" @click="handleSearch" />
        </template>
      </el-input>
      <div class="group_15 flex-row">
        <el-select v-if="topTabActive == 0" v-model="params.courseType" placeholder="课程类别" @change="handleSearch">
          <el-option v-for="item in kclbList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-if="topTabActive == 1" v-model="value" placeholder="查看班级">
          <el-option v-for="item in bjztList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </div>
    <CourseManageCourse ref="courseManageCourse" @detailsCourse="detailsCourse" v-if="topTabActive == 0" />
    <IndexCourseClass ref="indexCourseClass" v-if="topTabActive == 1" @getClass="getClassDetail"/>
  </div>
</template>
<script setup>
// 获取图片资源
import numCourseKczs from '@/assets/images/smartCourse/num_course_kczs.png'
import numCourseBjzs from '@/assets/images/smartCourse/num_course_bjzs.png'
import numCourseKtzs from '@/assets/images/smartCourse/num_course_ktzs.png'
import numCourseKszs from '@/assets/images/smartCourse/num_course_kszs.png'
import numCourseZyzs from '@/assets/images/smartCourse/num_course_zyzs.png'
import numCourseSh from '@/assets/images/smartCourse/num_course_sh.png'

import { Search } from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import CourseManageCourse from './course_manage_course.vue'
import IndexCourseClass from './index_course_class.vue'
import { getDashboardStatistics } from '@/api/edu/smartCourse'
const emits = defineEmits(['detailsCourse'])

const topTabActive = ref(0)
const courseManageCourse = ref(null)
const indexCourseClass = ref(null)
const course = ref(null)

// 统计数据
const statisticsData = ref({
  totalCourses: 0,
  totalClasses: 0,
  totalLessons: 0,
  totalExams: 0,
  totalHomeworks: 0,
  pendingApprovals: 0
})
const loading = ref(false)
const topTab = ref([
  {
    label: '全部课程',
    value: '1'
  },
  {
    label: '全部班级',
    value: '2'
  }
])
const kclbList = ref([
  {
    label: '教务课程',
    value: '1'
  },
  {
    label: '自主课程',
    value: '2'
  }
])
const bjztList = ref([
  {
    label: '未开课',
    value: '1'
  },
  {
    label: '进行中',
    value: '2'
  },
  {
    label: '归档课',
    value: '3'
  },
])

const params = ref({
  searchVar: '',
  courseType: '',
})

const changeTopTab = i => {
  topTabActive.value = i
}
// 切换页面
const detailsCourse = (item) => {
  course.value = item
  emits('detailsCourse',course.value)
}

const getClassDetail = (item) => {
  console.log(item)
/*  emits('detailsCourse',course.value)*/
}

// 加载统计数据
const loadStatistics = async () => {
  loading.value = true
  try {
    const response = await getDashboardStatistics()
    if (response.code === 200 && response.data) {
      statisticsData.value = {
        totalCourses: response.data.totalCourses || 0,
        totalClasses: response.data.totalClasses || 0,
        totalLessons: response.data.totalLessons || 0,
        totalExams: response.data.totalExams || 0,
        totalHomeworks: response.data.totalHomeworks || 0,
        pendingApprovals: response.data.pendingApprovals || 0
      }
    } else {
      ElMessage.error(response.msg || '获取统计数据失败')
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  if (topTabActive.value == 0){
    courseManageCourse.value.handleSerch(params.value)
  }else if (topTabActive.value == 1){
    // indexCourseClass.value.getListData()
  }
}

// 手动刷新统计数据
const refreshStatistics = () => {
  loadStatistics()
}

// 组件挂载时加载统计数据
onMounted(() => {
  loadStatistics()
})

// 暴露方法给父组件调用
defineExpose({
  refreshStatistics
})

</script>
<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  background-color: var(--el-fill-color-blank);
}
</style>
