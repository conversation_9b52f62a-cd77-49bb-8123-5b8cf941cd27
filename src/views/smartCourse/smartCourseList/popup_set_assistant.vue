<template>
  <el-dialog v-model="dialogVisible" title="设置助教" width="700" :before-close="handleClose" style="margin-top: 10vh !important">
    <el-form ref="formRef" style="max-width: 650px" :model="form"  class="demo-ruleForm" label-width="120px" :rules="rules">
      <h3>设置助教</h3>
      <el-form-item label="助教人员" prop="zjry">
        <el-select v-model="form.zjry" placeholder="请选择助教人员">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <h3>权限配置</h3>
      <el-form-item label="班级学生:">
        <el-checkbox-group v-model="form.bjxs">
          <el-checkbox label="分组" value="1" />
          <el-checkbox label="移除" value="2" />
          <el-checkbox label="添加分组" value="3" />
          <el-checkbox label="一键分组" value="4" />
          <el-checkbox label="加入审核" value="5" />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="作业:">
        <el-checkbox-group v-model="form.zy">
          <el-checkbox label="发布" value="1" />
          <el-checkbox label="查看" value="2" />
          <el-checkbox label="导出成绩" value="3" />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="考试:">
        <el-checkbox-group v-model="form.ks">
          <el-checkbox label="发布" value="1" />
          <el-checkbox label="查看" value="2" />
          <el-checkbox label="导出成绩" value="3" />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="成绩:">
        <el-checkbox-group v-model="form.cj">
          <el-checkbox label="导出" value="1" />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="互动讨论:">
        <el-checkbox-group v-model="form.hdtl">
          <el-checkbox label="回复" value="1" />
          <el-checkbox label="删除" value="2" />
        </el-checkbox-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'

const dialogVisible = ref(false)
const formRef = ref(null)
const form = ref({
  bt: '',
  lx: '1',
  fs: '1'
})
const rules = ref({
  zjry: [{ required: true, message: '助教人员不能为空', trigger: 'change' }],
})
// 开启弹窗
const open = () => {
  dialogVisible.value = true
  if (formRef.value) formRef.value.resetFields()
}
// 关闭弹窗
const confirmFrom = () => {
  formRef.value.validate(valid => {
    if (valid) {
      console.log('submit!')
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss">
// .el-dialog.signin_popup_dialog :not(.is-fullscreen) {
//   margin-top: 30vh !important;
// }
</style>
