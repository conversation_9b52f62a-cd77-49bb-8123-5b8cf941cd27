<template>
  <div class="block_4 flex-col">
    <div class="exam_manage">
      <div class="exam_manage_operate">
        <div>
          <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px">
            <el-form-item label="考试名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入考试名称" />
            </el-form-item>
            <el-form-item label="状态：" prop="status">
              <el-select v-model="queryParams.status" placeholder="状态" style="width: 200px">
                <el-option v-for="item in ztList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-button type="primary" @click="addExam">新增考试</el-button>
      </div>
      <div class="exam_manage_table">
        <el-table :data="tableData" :loading="loading" style="width: 100%" :key="childKey">
          <el-table-column type="index" label="序号" align="center" width="50" />
          <el-table-column prop="name" align="center" label="考试名称">
            <template #default="scope">
              <el-tooltip class="item" effect="dark" :content="scope.row.name" placement="top">
                <span class="ellipsis">{{ getShortName(scope.row.name,6) }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="courseName" align="center" label="考试课程" >
            <template #default="scope">
              <el-tooltip class="item" effect="dark" :content="scope.row.courseName" placement="top">
                <span class="ellipsis">{{ getShortName(scope.row.courseName,6) }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="courseType" align="center" width="80" label="课程类别">
            <template #default="scope">
              <span v-if="scope.row.courseType == '1'">自主课程</span>
              <span v-if="scope.row.courseType == '2'">校内课程</span>
            </template>
          </el-table-column>
          <el-table-column prop="className" align="center" label="考试班级" >
            <template #default="scope">
              <el-tooltip class="item" effect="dark" :content="scope.row.className" placement="top">
                <span class="ellipsis">{{ getShortName(scope.row.className,6) }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="contentType" align="center" label="考试类型">
            <template #default="scope">
              <span v-if="scope.row.contentType == '0'">题库考试</span>
              <span v-if="scope.row.contentType == '1'">附件考试</span>
              <span v-if="scope.row.contentType == '2'">登分考试</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" align="center" width="70" label="状态">
            <template #default="scope">
              <span v-if="scope.row.status == '1'">未开始</span>
              <span v-if="scope.row.status == '2'">进行中</span>
              <span v-if="scope.row.status == '3'">已结束</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" align="center" label="创建时间" width="160"> </el-table-column>
          <el-table-column fixed="right" align="center" label="操作" min-width="130">
            <template #default="scope">
              <el-button v-if="scope.row.status == 1" type="primary" link @click="goStart(scope.row)">开始</el-button>
              <el-button v-if="scope.row.status == 1" type="primary" link @click="goEdit(scope.row.assignmentId, 1)">编辑</el-button>
              <el-button v-if="scope.row.status == 1" type="primary" link @click="goDel(scope.row.assignmentId)">删除</el-button>
              <el-button v-if="scope.row.status == 3" type="primary" link @click="goAssay(scope.row.id)">试卷分析</el-button>
              <el-button v-if="scope.row.status != 1" type="primary" link @click="goEdit(scope.row.assignmentId,2)">查看</el-button>
              <el-button v-if="scope.row.status == 2" type="primary" link @click="goStop(scope.row)">结束</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList"/>
      </div>
    </div>
  </div>
  <PopupExamAdd ref="popupExamAdd" @refushList="resetQuery" />
  <PopupExamAssay ref="popupExamAssay" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import PopupExamAdd from './popup_exam_add.vue'
import PopupExamAssay from './popup_exam_assay.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance()
import {
  delHomeworkOrTest,
  getHomeworkOrTestListByUserId,
  startOrStopTest
} from "@/api/edu/moocSmartCourseHomeworkOrTest.js";
const total = ref(0)
const loading = ref(false)
const ztList = ref([
  {
    label: '未开始',
    value: '1'
  },
  {
    label: '进行中',
    value: '2'
  },
  {
    label: '已结束',
    value: '3'
  }
])

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    assignmentType: 1,
  },
});
const { queryParams } = toRefs(data);


const tableData = ref([])
const popupExamAdd = ref(null)
const popupExamAssay = ref(null)
const childKey = ref(0)

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}
/** 开始考试 */
const goStart = (row) => {
  proxy.$modal
    .confirm("确定开始该考试？")
    .then(function () {
      row.status = 2
      return startOrStopTest(row)
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("考试已开始");
    })
    .catch(() => {});
}

/** 结束考试 */
const goStop = async (row) => {
  proxy.$modal
      .confirm("确定结束该考试？")
      .then(function () {
        row.status = 3
        return startOrStopTest(row)
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("考试已结束");
      })
      .catch(() => {});
}

/** 查询列表 */
const getList = async () => {
  loading.value = true;
  let response = await getHomeworkOrTestListByUserId(queryParams.value)
  tableData.value = response.rows;
  total.value = response.total;
  loading.value = false;
}

onMounted(() => {
  getList()
})

function getShortName(name,length) {
  if (!name) return '';
  return name.length > length ? name.substr(0, length) + '...' : name;
}

// 添加试卷
const addExam = () => {
  popupExamAdd.value.open()
}

const resetQuery = () => {
/*  proxy.$refs.queryRef.resetFields()
  dateRange.value = []*/
  childKey.value = 0
  proxy.resetForm('queryRef')
  handleQuery();
}

const refushList = () => {
  proxy.resetForm('queryRef')
  childKey.value++
  handleQuery();
}

// 编辑试卷
const goEdit = (id, type) => {
  if (type == 1) {
    // 编辑
    popupExamAdd.value.open(id,1)
  } else if (type == 2) {
    // 查看
    popupExamAdd.value.open(id, 2)
  }
}
// 删除试卷
const goDel = id => {
  proxy.$modal
      .confirm("确定删除该条数据？")
      .then(function () {
        return delExam(id)
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
      .catch(() => {});
}

const delExam = async (id) => {
  let response = await delHomeworkOrTest(id)
}

const goAssay = id => {
  popupExamAssay.value.open(id)
}
</script>

<style scoped lang="scss">
.exam_manage {
  background: #fff;
  padding: 20px;
  min-height: 56.25vw;
  border-radius: 8px;
  .exam_manage_operate {
    display: flex;
    justify-content: space-between;
  }
  .exam_manage_table {
    margin-top: 20px;
  }
}
</style>
