<template>
  <div>
    <div class="group_16 flex-col">
      <div class="group_17 flex-row" style="margin-left: 27.35vw;">
        <div class="text-wrapper_13 flex-col">
          <el-button class="text_29" type="primary" @click="addCourse">加入班级</el-button>
        </div>
      </div>
      
      <!-- 搜索栏 -->
      <div style="margin: 1vw 0; display: flex; justify-content: flex-start; align-items: center; padding: 0 0.89vw;">
        <el-input
          v-model="searchText"
          placeholder="请输入班级名称"
          clearable
          style="width: 20vw;"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 班级列表 -->
      <div class="group_18" v-loading="loading">
                 <div class="block_6 flex-col" style="height: 13.67vw;" v-for="(item, index) in tableData" :key="item.classId" @click="goDetailsClass(item.classId)">
          <div class="image-text_6 flex-col justify-between">
            <div class="group_19 flex-row">
              <div class="text-wrapper_14 flex-col" style="width: 3vw;">
                <span class="text_30" v-if="item.classType == 1">独立班</span>
                <span class="text_30" v-if="item.classType == 2">平行班</span>
              </div>
              <!-- <img
                      class="label_5"
                      referrerpolicy="no-referrer"
                      src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG15da9e34d2d3859ca3ce71b19f612959.png"
                    /> -->
              <!-- <el-button class="label_5" :icon="DeleteFilled" circle /> -->
              <!-- <div class="group_19_bottom">
                <el-dropdown placement="bottom">
                  <el-icon color="#fff"><MoreFilled /></el-icon>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>查看</el-dropdown-item>
                      <el-dropdown-item>复制</el-dropdown-item>
                      <el-dropdown-item>删除</el-dropdown-item>
                      <el-dropdown-item>归档</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div> -->
            </div>
            <div class="group_20 flex-row justify-between">
              <span class="text-group_6">{{ item.className }}</span>
              <div class="text-wrapper_24 flex-col" v-if="item.status == 1">
                <span class="text_43">未开课</span>
              </div>
              <div class="text-wrapper_15 flex-col" v-if="item.status == 2">
                <span class="text_31">进行中</span>
              </div>
              <div class="text-wrapper_33 flex-col" v-if="item.status == 3">
                <span class="text_55">已归档</span>
              </div>
            </div>
          </div>
          <div class="text-wrapper_16 flex-row justify-between">
            <span class="text_32">课程名称：{{ item.courseName || item.className }}</span>
          </div>
          <div class="text-wrapper_16 flex-row justify-between">
            <span class="text_32">课程类别：{{ item.courseType == 1 ? '教务课程' : '自主课程' }}</span>
          </div>
          <div class="block_7 flex-row">
            <div class="image-text_7 flex-row justify-between">
              <img class="thumbnail_6" referrerpolicy="no-referrer" src="../../../assets/images/smartCourse/smartCourseDetails/star.png" />
              <span class="text-group_7">{{ item.description || '授课老师：未知' }}</span>
            </div>
          </div>
        </div>
        
                 <!-- 空状态 -->
         <div v-if="!loading && tableData.length === 0" style="width: 100%; text-align: center; padding: 2vw 0;">
           <el-empty description="暂无班级数据" />
         </div>
      </div>
      
             <!-- 分页 -->
       <div style="display: flex; justify-content: center; margin-top: 1vw;" v-if="total > 0">
         <el-pagination
           background
           v-model:current-page="currentPage"
           v-model:page-size="pageSize"
           :page-sizes="[10, 20, 50, 100]"
           layout="total, sizes, prev, pager, next, jumper"
           :total="total"
           @size-change="handleSizeChange"
           @current-change="handleCurrentChange"
         />
       </div>
    </div>
  </div>
  <PopupClassJoin ref="popupClassJoin" @success="handleJoinSuccess" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import PopupClassJoin from './popup_class_join.vue'
import { Search, DeleteFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { listClass } from '@/api/edu/moocSmartCourseClass'

const { proxy } = getCurrentInstance()
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)
const searchText = ref('')
const topTabContentActive = ref(0)

const topTabContent = ref([
  {
    label: '全部班级',
    value: ''
  },
  {
    label: '未开课',
    value: '1'
  },
  {
    label: '进行中',
    value: '2'
  },
  {
    label: '已归档',
    value: '3'
  }
])

const tableData = ref([])

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  className: '', // 班级名称
  status: '', // 状态筛选
  type: 'joined' // created-我创建的, joined-我加入的
})

// 获取班级列表
const getList = async () => {
  loading.value = true
  try {
    queryParams.value.pageNum = currentPage.value
    queryParams.value.pageSize = pageSize.value
    queryParams.value.className = searchText.value
    
    const response = await listClass(queryParams.value)
    if (response.code === 200) {
      tableData.value = response.rows || []
      total.value = response.total || 0
    } else {
      ElMessage.error(response.msg || '获取班级列表失败')
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取班级列表失败:', error)
    ElMessage.error('获取班级列表失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  getList()
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  getList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

// 状态筛选 - 暂时保留，学生加入的班级可能也需要这个功能
const changeTopTabContent = (i) => {
  topTabContentActive.value = i
  queryParams.value.status = topTabContent.value[i].value
  currentPage.value = 1
  getList()
}

// 加入班级
const popupClassJoin = ref(null)
const addCourse = () => {
  popupClassJoin.value.open()
}

// 加入成功回调
const handleJoinSuccess = () => {
  getList() // 刷新列表
}

// 进入班级详情
const goDetailsClass = (id) => {
  // 学生可能需要不同的详情页面处理
  const params = { id: id, flag: true, type: 'joined' }
  // 这里可以根据需要调用父组件的方法或路由跳转
  console.log('进入班级详情:', params)
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.group_19_bottom {
  cursor: pointer;
  margin-left: 9.5vw;
  margin-top: 0.5vw;
}
</style>
