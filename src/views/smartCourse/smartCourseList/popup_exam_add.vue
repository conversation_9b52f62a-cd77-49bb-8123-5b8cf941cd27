<template>
  <el-dialog v-model="dialogVisible" :title="title" width="700" :before-close="handleClose" style="margin-top: 8vh !important">
    <el-form ref="formRef" :model="form" label-width="auto" class="demo-ruleForm" :rules="rules">
      <el-form-item label="考试名称" prop="name">
        <el-input :disabled="isEdit" show-word-limit maxlength="50" v-model="form.name" type="text" autocomplete="off" placeholder="请输入考试名称" />
      </el-form-item>
      <el-form-item label="考试课程" prop="courseId">
        <el-select :disabled="isEdit" v-model="form.courseId" placeholder="请选择考试课程" @change="selectClass">
          <el-option v-for="item in courseList" :key="item.courseId" :label="item.courseName" :value="item.courseId" />
        </el-select>
      </el-form-item>
      <el-form-item label="考试班级" prop="classId" @change="changeClass">
        <el-select :disabled="isEdit" v-model="form.classId" placeholder="请选择考试班级">
          <el-option v-for="item in courseClassList" :key="item.classId" :label="item.className" :value="item.classId" />
        </el-select>
      </el-form-item>
      <el-form-item label="考试类型" prop="contentType">
        <el-radio-group :disabled="isEdit" v-model="form.contentType">
          <el-radio value="0">题库考试</el-radio>
          <el-radio value="1">附件考试</el-radio>
          <el-radio value="2">登分考试</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="选择试卷" prop="paperDetail">
        <el-button :disabled="isEdit" type="primary" @click="openChange">添加试卷</el-button>
      </el-form-item>
      <div class="popup_show_tag" v-if="!isEdit">
<!--        <el-tag
          class="popup_show_tag_item"
          v-for="(tag, index) in changeValList"
          :key="tag.id"
          closable
          :disable-transitions="false"
          @close="handleCloseTag(index)"
        >
          {{ tag.paperTitle }}
        </el-tag>-->
        <el-tag class="popup_show_tag_item" v-if="paperDetail" closable :disable-transitions="false" @close="handleCloseTag(index)">
          <span>{{ paperDetail.paperTitle }}</span>
        </el-tag>
      </div>

      <div class="popup_show_tag" v-else>
        <el-tag class="popup_show_tag_item" v-if="paperDetail" :disable-transitions="false">
          <span>{{ paperDetail.paperTitle }}</span>
        </el-tag>
      </div>
      <el-form-item label="考试开始时间" prop="submitStartTime">
        <el-date-picker :disabled="isEdit"
          v-model="form.submitStartTime"
          type="datetime"
          placeholder="请选择考试开始时间"
          value-format="YYYY-MM-DD HH:mm"
          date-format="MMM DD, YYYY"
          time-format="HH:mm"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label="考试结束时间" prop="submitEndTime">
        <el-date-picker :disabled="isEdit"
          v-model="form.submitEndTime"
          type="datetime"
          placeholder="请选择考试结束时间"
          value-format="YYYY-MM-DD HH:mm"
          date-format="MMM DD, YYYY"
          time-format="HH:mm"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label="批改开始时间" prop="gradingStartTime">
        <el-date-picker :disabled="isEdit"
            v-model="form.gradingStartTime"
            type="datetime"
            placeholder="请选择批改开始时间"
            value-format="YYYY-MM-DD HH:mm"
            date-format="MMM DD, YYYY"
            time-format="HH:mm"
            :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label="批改结束时间" prop="gradingEndTime">
        <el-date-picker :disabled="isEdit"
            v-model="form.gradingEndTime"
            type="datetime"
            placeholder="请选择批改结束时间"
            value-format="YYYY-MM-DD HH:mm"
            date-format="MMM DD, YYYY"
            time-format="HH:mm"
            :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label="考试要求">
        <el-input :disabled="isEdit" v-model="form.assignmentRequirement" :rows="3" type="textarea" show-word-limit="true" maxlength="500" placeholder="请输入考试要求" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer" v-if="!isEdit">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmFrom">确定</el-button>
      </div>
      <div class="dialog-footer" v-else>
        <el-button type="primary" @click="handleClose">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <PopupExamChange @changeVal="changeVal" :paperDetail="paperDetail" ref="popupTestChange" />
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance();
import PopupExamChange from './popup_exam_change.vue'
import { getListByTeacherIdNoPage } from '@/api/edu/moocSmartCourse.js'
import {getListByTeacherIdAndCourseIdNoPage} from "@/api/edu/moocSmartCourseClass.js";
import {
  addHomeworkOrTest,
  getHomeworkOrTestWithPaper,
  updateHomeworkOrTest
} from "@/api/edu/moocSmartCourseHomeworkOrTest.js";
import {getTestListNoPage} from "@/api/edu/moocSmartCourseTestPaper.js";

const emits = defineEmits(['refushList'])
const dialogVisible = ref(false)
const formRef = ref(null)
const changeValList = ref([])
const changeValIds = ref([])
const courseList = ref([])
const courseClassList = ref([])
const tableRef = ref(null)
const title = ref(null)
const paperDetail = ref(null)
const assignmentId = ref(null)
const isEdit = ref(false)
const form = ref({
  contentType: '0',
  assignmentType:1
})
const rules = ref({
  name: [{ required: true, message: '考试名称不能为空', trigger: 'blur' }],
  courseId: [{ required: true, message: '考试课程不能为空', trigger: 'blur' }],
  classId: [{ required: true, message: '考试班级不能为空', trigger: 'blur' }],
  contentType: [{ required: true, message: '考试类型不能为空', trigger: 'blur' }],
  submitStartTime: [{ required: true, message: '考试开始时间不能为空', trigger: 'blur' }],
  submitEndTime: [{ required: true, message: '考试结束时间不能为空', trigger: 'blur' }],
  gradingEndTime: [{ required: true, message: '批改开始时间不能为空', trigger: 'blur' }],
  gradingStartTime: [{ required: true, message: '批改结束时间不能为空', trigger: 'blur' }],
})
// 开启弹窗
const open = (val,type) => {
  reset()
  getCourseList()
  changeValList.value = []
  changeValIds.value = []
  dialogVisible.value = true
  // 清空校验
  if (formRef.value) formRef.value.resetFields()
  // 清空表格选中
  if (tableRef.value) tableRef.value.clearSelection()
  if (val) {
    // 修改
    title.value = '修改考试'
    assignmentId.value = val
    getTestDetail()
    if (type == 2) {
      //查看
      isEdit.value = true
      title.value = '查看考试'
    }
  } else {
    title.value = '新增考试'
  }
}
/*获取考试*/
const getTestDetail = async () => {
  let response = await getHomeworkOrTestWithPaper(assignmentId.value)
  if (response.code === 200){
    form.value = response.data
    let paperRes = await getTestListNoPage()
    if (paperRes.code === 200){
      paperDetail.value = paperRes.data.find(item => item.paperId == form.value.testPaperId)
    }
  }
}

/*获取课程*/
const getCourseList = async () => {
  let response = await getListByTeacherIdNoPage()
  courseList.value = response.data
}

const disabledDate = (time) => {
  return time.getTime() < Date.now()
}

/*获取班级*/
const selectClass = async () => {
  form.value.classId = ''
  let param = {
    courseId: form.value.courseId
  }
  let response = await getListByTeacherIdAndCourseIdNoPage(param)
  courseClassList.value = response.data
}

// 表单重置
function reset() {
  form.value = {
    name: null,
    courseId: null,
    classId: null,
    contentType: '0',
    submitStartTime: null,
    submitEndTime: null,
    testPaperId: null,
    score: null,
    assignmentRequirement: null,
    gradingEndTime: null,
    gradingStartTime: null,
    assignmentType: 1
  };
  paperDetail.value = null
  proxy.resetForm("formRef");
  isEdit.value = false
}

/*选择班级*/
const changeClass = async () => {
  if (!form.value.courseId) {
    return ElMessage({
      type: 'error',
      message: '请先选择课程'
    })
  }
}

// 选择作业
const popupTestChange = ref(null)
const openChange = () => {
  popupTestChange.value.open()
}
// 作业返回id
const changeVal = val => {
/*  changeValList.value = changeValList.value.concat(val)
  changeValIds.value = changeValList.value.map(item => item.id)*/
  paperDetail.value = val
  form.value.testPaperId = val.paperId
  form.value.score = val.totalScore
}
// 确定
const confirmFrom = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      saveTest()
      emits("refushList")
    }
  })
}

const saveTest = async () => {
  if (paperDetail.value) {
    form.value.testPaperId = paperDetail.value.paperId
  }
  if (form.value.assignmentId) {
    // 修改
    updateHomeworkOrTest(form.value).then(response => {
      if (response.code === 200) {
        proxy.$modal.msgSuccess("修改成功");
        dialogVisible.value = false
      }
    });
  } else {
    // 新增
    addHomeworkOrTest(form.value).then(response => {
      if (response.code === 200) {
        proxy.$modal.msgSuccess("新增成功");
        dialogVisible.value = false
      }
    });
  }
}

const handleCloseTag = i => {
  paperDetail.value = null
}

const closeDialog = () => {
  dialogVisible.value = false
}

const handleClose = () => {
  dialogVisible.value = false
  reset()
}
defineExpose({
  open,closeDialog
})
</script>
<style lang="scss" scoped>
.popup_show_table {
  max-height: 300px;
  overflow-y: auto;
}
.popup_show_tag {
  margin: 0 20px 15px 75px;
  .popup_show_tag_item {
    margin: 3px;
  }
}
</style>
