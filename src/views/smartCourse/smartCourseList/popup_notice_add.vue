<template>
  <el-dialog v-model="dialogVisible" title="创建公告" width="700" :before-close="handleClose" style="margin-top: 8vh !important">
    <el-form ref="formRef" :model="form" label-width="auto" class="demo-ruleForm" :rules="rules">
      <el-form-item label="课程" prop="courseId">
        <el-select v-model="form.courseId" placeholder="请选择课程" @change="selectClass">
          <el-option v-for="item in courseList" :key="item.courseId" :label="item.courseName" :value="item.courseId" />
        </el-select>
      </el-form-item>
      <el-form-item label="班级" prop="classId" @change="changeClass">
        <el-select v-model="form.classId" placeholder="请选择班级">
          <el-option v-for="item in courseClassList" :key="item.classId" :label="item.className" :value="item.classId" />
        </el-select>
      </el-form-item>
      <el-form-item label="标题" prop="name">
        <el-input show-word-limit maxlength="60" v-model="form.name" type="text" autocomplete="off" placeholder="请输入标题" />
      </el-form-item>
      <el-form-item label="发布时间" prop="submitStartTime">
        <el-date-picker
          v-model="form.submitStartTime"
          type="datetime"
          placeholder="请选择发布时间"
          value-format="YYYY-MM-DD HH:mm"
          date-format="MMM DD, YYYY"
          time-format="HH:mm"
        />
      </el-form-item>
      <el-form-item label="内容" prop="assignmentRequirement">
        <editor v-model="form.assignmentRequirement" :min-height="80" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmFrom">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <PopupExamChange @changeVal="changeVal" ref="popupTestChange" />
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance();
import PopupExamChange from './popup_exam_change.vue'
import { getListByTeacherIdNoPage } from '@/api/edu/moocSmartCourse.js'
import {getListByTeacherIdAndCourseIdNoPage} from "@/api/edu/moocSmartCourseClass.js";
import {addHomeworkOrTest, updateHomeworkOrTest} from "@/api/edu/moocSmartCourseHomeworkOrTest.js";
import Editor from "@/components/Editor/index.vue";

const emits = defineEmits(['refushList'])
const dialogVisible = ref(false)
const formRef = ref(null)
const changeValList = ref([])
const changeValIds = ref([])
const courseList = ref([])
const courseClassList = ref([])
const tableRef = ref(null)
const paperDetail = ref(null)
const form = ref({
  contentType: 0,
  assignmentType:1
})
const rules = ref({
  name: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
  courseId: [{ required: true, message: '课程不能为空', trigger: 'blur' }],
  classId: [{ required: true, message: '班级不能为空', trigger: 'blur' }],
  submitStartTime: [{ required: true, message: '发布时间不能为空', trigger: 'blur' }],
  assignmentRequirement: [{ required: true, message: '内容不能为空', trigger: 'blur' }],
})
// 开启弹窗
const open = () => {
  reset()
  getCourseList()
  changeValList.value = []
  changeValIds.value = []
  dialogVisible.value = true
  // 清空校验
  if (formRef.value) formRef.value.resetFields()
  // 清空表格选中
  if (tableRef.value) tableRef.value.clearSelection()
}
/*获取课程*/
const getCourseList = async () => {
  let response = await getListByTeacherIdNoPage()
  courseList.value = response.data
}

/*获取班级*/
const selectClass = async () => {
  form.value.classId = ''
  let param = {
    courseId: form.value.courseId
  }
  let response = await getListByTeacherIdAndCourseIdNoPage(param)
  courseClassList.value = response.data
}

// 表单重置
function reset() {
  form.value = {
    name: null,
    courseId: null,
    classId: null,
    contentType: 0,
    submitStartTime: null,
    submitEndTime: null,
    testPaperId: null,
    score: null,
    assignmentRequirement: null,
    gradingEndTime: null,
    gradingStartTime: null,
    assignmentType: 1
  };
  proxy.resetForm("formRef");
}

/*选择班级*/
const changeClass = async () => {
  if (!form.value.courseId) {
    return ElMessage({
      type: 'error',
      message: '请先选择课程'
    })
  }
}

// 选择作业
const popupTestChange = ref(null)
const openChange = () => {
  popupTestChange.value.open()
}
// 作业返回id
const changeVal = val => {
/*  changeValList.value = changeValList.value.concat(val)
  changeValIds.value = changeValList.value.map(item => item.id)*/
  paperDetail.value = val
  form.value.testPaperId = val.paperId
  form.value.score = val.totalScore
}
// 确定
const confirmFrom = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      saveTest()
      dialogVisible.value = false
      emits("refushList")
    }
  })
}

const saveTest = async () => {
  form.value.testPaperId = paperDetail.value.paperId
  console.log(form.value)
  if (form.value.assignmentId) {
    // 修改
    updateHomeworkOrTest(form.value).then(response => {
      proxy.$modal.msgSuccess("修改成功");
    });
  } else {
    // 新增
    addHomeworkOrTest(form.value).then(response => {
      proxy.$modal.msgSuccess("新增成功");
    });
  }
}

const handleCloseTag = i => {
  paperDetail.value = null
}

const handleClose = () => {
  dialogVisible.value = false
  reset()
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.popup_show_table {
  max-height: 300px;
  overflow-y: auto;
}
.popup_show_tag {
  margin: 0 20px 15px 75px;
  .popup_show_tag_item {
    margin: 3px;
  }
}
</style>
