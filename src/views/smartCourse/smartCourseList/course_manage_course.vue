<template>
  <div>
    <div class="group_16 flex-col">
      <div class="group_17 flex-row">
        <div
            class="text-wrapper_10 flex-col"
            @click="changeTopTabContent(-1)"
            :class="{ topTabContengActiveClass: -1 === topTabContentActive }"
        >
          <span class="text_26">全部课程</span>
        </div>
        <div
            class="text-wrapper_10 flex-col"
            v-for="(item, index) in smartCourseStatus"
            :key="index"
            @click="changeTopTabContent(index)"
            :class="{ topTabContengActiveClass: index === topTabContentActive }"
        >
          <span class="text_26">{{ item.label }}</span>
        </div>
        <div class="text-wrapper_13 flex-col">
          <el-button class="text_29" type="primary" @click="addCourse">新增课程</el-button>
        </div>
      </div>
      <div class="group_18">
        <div class="block_6 flex-col" v-for="(item, index) in tableData" :key="index" @click="detailsCourse(item)">
          <div class="image-text_6 flex-col justify-between">
            <div class="group_19 flex-row" :style="{ background: 'url(' + item.coverImageUrl + ')'}" style=" background-size: cover; background-position: center; border-radius: 10px; background-repeat: no-repeat;" >
              <div class="text-wrapper_14 flex-col">
                <span class="text_30">{{ item.courseType == 1 ? '教务课程' : '自主课程' }}</span>
              </div>
              <el-button class="label_5" :icon="DeleteFilled" circle @click.stop="removeCourse(item.courseId)" />
            </div>
            <div class="group_20 flex-row justify-between">
              <span class="text-group_6">{{ item.courseName }}</span>
              <div class="text-wrapper_24 flex-col">
                <span class="text_43">{{ getOptionDesc(smartCourseStatus,item.status) }}</span>
              </div>
            </div>
          </div>
          <div class="text-wrapper_16 flex-row justify-between">
            <span class="text_32">共{{ item.classTotal }}个班级</span>
          </div>
          <div class="block_7 flex-row">
            <div class="image-text_7 flex-row justify-between">
              <img class="thumbnail_6" referrerpolicy="no-referrer" src="../../../assets/images/smartCourse/smartCourseDetails/star.png" />
              <span class="text-group_7">授课老师：{{ item.realName }}</span>
            </div>
          </div> 
        </div>
      </div>
      <div class="pagination-container">
        <el-pagination
          background="true"
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :size="size"
          layout="total, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
  <PopupCourseAdd ref="popupCourseAdd" @refreshPage="getListData" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import PopupCourseAdd from './popup_course_add.vue'
import { Search, DeleteFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {myListSmartCourse, removeSmartCourse} from "@/api/edu/smartCourse.js";
import { getOptionDesc,smartCourseStatus } from "@/utils/optionUtil";
import {delData} from "@/api/system/dict/data.js";
import useDictStore from "@/store/modules/dict.js";

const { proxy } = getCurrentInstance()
const total = ref(0)

const queryParams = ref({
  pageNum: 1,
  pageSize: 9,
  status:'',
})
const emits = defineEmits(['detailsCourse'])
const topTabContentActive = ref(-1)
const tableData = ref([])

const changeTopTabContent = i => {
  topTabContentActive.value = i;
  if (i === -1) {
    queryParams.value.status = '';
  } else {
    queryParams.value.status = i;
  }
  getListData();
}

function handleSizeChange(val) {
  queryParams.value.pageSize = val;
  getListData();
}

// 分页页码改变
const handleCurrentChange = (val) => {
  queryParams.value.pageNum = val
  getListData()
}

// 新增课程
const popupCourseAdd = ref(null)
const addCourse = () => {
  popupCourseAdd.value.open()
}
// 进入课程详情
const detailsCourse = (item) => {
  emits('detailsCourse',item)
}

const removeCourse = (courseId) => {
  proxy.$modal
      .confirm('是否确认删除？')
      .then(function () {
        return removeSmartCourse(courseId)
      })
      .then(() => {
        getListData();
        proxy.$modal.msgSuccess('删除成功')
      })
      .catch(() => {})
}

const getListData = () => {
  myListSmartCourse(queryParams.value).then(res => {
    console.log(res);
    if (res.code == 200) {
      tableData.value = res.rows;
      total.value = res.total;
    }
  })
}

const handleSerch = (params) => {
  queryParams.value =  { ...params };
  console.log(queryParams.value);
  getListData();
}

onMounted(() => {
  getListData();
})

defineExpose({
  handleSerch
})
</script>

<style scoped lang='scss'>
</style>
