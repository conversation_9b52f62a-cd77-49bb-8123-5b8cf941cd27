<template>
  <div class="block_4 flex-col">
    <div class="group_10 flex-row">
      <div class="group_111 flex-col justify-between" v-for="(item, index) in topTab" :key="index" @click="changeTopTab(index)">
        <span class="text_21" :class="{ topTabActiveClass: index === topTabActive }">{{ item.label }}</span>
        <div v-if="index === topTabActive" class="group_12 flex-col"></div>
      </div>
      <!--      <el-input v-if="topTabActive == 0" class="group_14 flex-row" placeholder="请输入课程名称" v-model="searchVar" :suffix-icon="Search" />-->
      <el-input v-if="topTabActive == 0" v-model="params.searchVar" class="group_14 flex-row" placeholder="请输入作业名称">
        <template #append>
          <el-button :icon="Search" @click="handleSearch" />
        </template>
      </el-input>
      <el-input v-if="topTabActive == 1" v-model="params.searchVar" class="group_14 flex-row" placeholder="请输入考试名称">
        <template #append>
          <el-button :icon="Search" @click="handleSearch" />
        </template>
      </el-input>
      <div class="group_15 flex-row">
        <el-select v-if="topTabActive == 0" v-model="params.courseType" placeholder="作业状态" @change="handleSearch">
          <el-option v-for="item in ztList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-if="topTabActive == 1" v-model="value" placeholder="考试状态">
          <el-option v-for="item in ztList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </div>
    <WaitApprovalHomework v-if="topTabActive == 0"/>
    <WaitApprovalExam v-if="topTabActive == 1"/>
  </div>
</template>
<script setup>
import { Search } from '@element-plus/icons-vue'
import { ref } from 'vue'
import WaitApprovalHomework from './wait_approval_homework.vue'
import WaitApprovalExam from './wait_approval_exam.vue'
const emits = defineEmits(['detailsCourse'])

const topTabActive = ref(0)
const courseManageCourse = ref(null)
const topTabContentActive = ref(0)

const topTab = ref([
  {
    label: '作业',
    value: '1'
  },
  {
    label: '考试',
    value: '2'
  }
])
const ztList = ref([
  {
    label: '未开始',
    value: '1'
  },
  {
    label: '进行中',
    value: '2'
  },
  {
    label: '已结束',
    value: '3'
  }
])
const params = ref({
  searchVar: '',
  courseType: ''
})

const changeTopTab = i => {
  topTabActive.value = i
}
// 切换页面
const detailsCourse = () => {
  emits('detailsCourse')
}
const changeTopTabContent = i => {
  topTabContentActive.value = i;
}

const handleSearch = () => {
  if (topTabActive.value == 0) {
    courseManageCourse.value.handleSerch(params.value)
  } else if (topTabActive.value == 1) {
    // indexCourseClass.value.getListData()
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  background-color: var(--el-fill-color-blank);
}
</style>
