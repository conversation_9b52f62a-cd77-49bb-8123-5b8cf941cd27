<template>
  <div class="page_details_course flex-col">
    <div class="section_2 flex-col">
      <div class="section_3 flex-row justify-between">
        <el-breadcrumb class="text_7" separator="/">
          <el-breadcrumb-item :to="{ path: '/' }">课程管理</el-breadcrumb-item>
          <el-breadcrumb-item>
            <a href="/">班级列表</a>
          </el-breadcrumb-item>
        </el-breadcrumb>
        <div class="text-wrapper_1">
          <!-- <span class="text_8">返回</span> -->
          <el-button type="primary" @click="setAssistant">设置助教</el-button>
          <el-button type="primary" plain @click="goBack">返回</el-button>
        </div>
      </div>
      <div class="section_4 flex-row justify-between">
        <div class="group_10 flex-col">
          <div class="box_3 flex-row justify-between">
            <span class="text_11">{{ courseParam.kcmc }}</span>
            <div class="text-wrapper_4_1 flex-col" v-if="courseParam.zt == 1">
              <span class="text_12_1">未开始</span>
            </div>
            <div class="text-wrapper_4 flex-col" v-if="courseParam.zt == 2">
              <span class="text_12">进行中</span>
            </div>
            <div class="text-wrapper_4_2 flex-col" v-if="courseParam.zt == 3">
              <span class="text_12_2">已归档</span>
            </div>
          </div>
          <div class="box_4 flex-row justify-between">
            <span class="text_13">{{ courseParam.kcjs }}</span>
            <div class="text-wrapper_5">
              <!-- <span class="text_14">成绩考核权重</span> -->
              <el-button type="primary" @click="attendClass">新建课堂</el-button>
              <el-button type="primary" plain @click="popupScoredit">归档</el-button>
            </div>
          </div>
          <div class="text-wrapper_6 flex-row justify-between">
            <span class="text_15">授课教师：{{ courseParam.jxtd }}</span>
            <span class="text_16">
              <span v-if="courseParam.kclx == 1">独立班</span>
              <span v-else>平行班</span>
            </span>
          </div>
          <div class="box_5 flex-row">
            <div class="image-text_1 flex-row justify-between">
              <img class="image-wrapper_3 flex-col" :src="courseParam.fmUrl" />
              <div class="text-group_1 flex-col justify-between">
                <span class="text_17">{{ courseParam.xjmc }}</span>
                <span class="text_18">主编：{{ courseParam.zb }}</span>
                <span class="text_19">阅读进度</span>
              </div>
            </div>
            <!-- <img
              class="image_4"
              referrerpolicy="no-referrer"
              src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGda477767c59717f1c8176ab5e6286510.png"
            /> -->
            <el-progress class="image_4" :percentage="courseParam.ydjd" />
            <el-icon class="thumbnail_1"><ArrowRightBold /></el-icon>
            <!-- <img
              class="thumbnail_1"
              referrerpolicy="no-referrer"
              src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG595db758a3fc651619acbfd9520c06cb.png"
            /> -->
          </div>
        </div>
      </div>
    </div>
  </div>
  <PopupSetAssistant ref="popupSetAssistantRef" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { Search, DeleteFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PopupSetAssistant from './popup_set_assistant.vue'
const { proxy } = getCurrentInstance();

const emits = defineEmits(['goBack'])
const topTabContent = ref([
  { label: '全部课程', value: '1' },
  { label: '未开始', value: '2' },
  { label: '进行中', value: '3' },
  { label: '已归档', value: '4' }
])
const tableData = ref([
  { id: '1', kclx: '1', bt: '终极一班', zt: '1', bjsl: '班级1、班级2', skjs: '李晓兰 张伟伟' },
  { id: '1', kclx: '1', bt: '终极一班', zt: '2', bjsl: '班级1、班级2', skjs: '李晓兰 张伟伟' },
  { id: '1', kclx: '2', bt: '终极一班', zt: '3', bjsl: '班级1、班级2', skjs: '李晓兰 张伟伟' },
  { id: '1', kclx: '2', bt: '终极一班', zt: '3', bjsl: '班级1、班级2', skjs: '李晓兰 张伟伟' },
  { id: '1', kclx: '2', bt: '终极一班', zt: '3', bjsl: '班级1、班级2', skjs: '李晓兰 张伟伟' }
])
const courseParam = ref({
  id: '1',
  kcmc: '1机械制造工艺：从匠艺到智造（教务课程）',
  zt: '3',
  kcjs: '本课程以机械制造工艺的演变为核心，从传统工匠技艺讲起，逐步深入现代智能制造技术。课程将系统介绍铸造、锻造、切削、焊接等基础工艺，并重点探讨数字化设计（CAD/CAM）、增材制造（3D打印）、工业机器人、智能工厂等前沿',
  jxtd: '李晓兰 张伟伟',
  fmUrl: 'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG87f2df0c45a327787b538d5a3e30dd13.png',
  xxrs: '2',
  xjmc: '新一代技术文本文本文本文本文本文本文本文本文本文本文本',
  zb: '某某某、某某某',
  ydjd: 48
})
// 设置助教
const popupSetAssistantRef = ref(null)
const setAssistant = () => {
  popupSetAssistantRef.value.open()
}
// 上课
const attendClass = () => { 
  // proxy.$router.push('/smart-course-details');
    ElMessageBox.confirm('您确定要新建一个课堂吗?', '新建课堂', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      ElMessage({
        type: 'success',
        message: '新建成功'
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消操作'
      })
    })
}
const goBack = () => {
  emits('goBack')
}
</script>

<style lang="scss" scoped>
.page_details_course {
  background-color: rgba(242, 245, 250, 1);
  position: relative;
  width: 58.08vw;
  // height: 58.08vw;
  overflow: hidden;
  .section_1 {
    box-shadow: 0px 4px 10px 0px rgba(134, 154, 189, 0.15);
    background-color: rgba(255, 255, 255, 1);
    height: 3.96vw;
    width: 100vw;
    .box_1 {
      position: relative;
      width: 58.08vw;
      height: 3.96vw;
      .label_1 {
        width: 1.46vw;
        height: 1.2vw;
        margin: 0.78vw 0 0 13.9vw;
      }
      .group_1 {
        width: 1.46vw;
        height: 1.83vw;
        margin: 0.78vw 0 0 0.05vw;
        .label_2 {
          width: 1.46vw;
          height: 1.2vw;
        }
        .group_2 {
          background-color: rgba(13, 94, 159, 1);
          width: 0.27vw;
          height: 0.11vw;
          margin: 0.52vw 0 0 0.78vw;
        }
      }
      .group_3 {
        background-color: rgba(13, 94, 159, 1);
        width: 0.37vw;
        height: 0.16vw;
        margin-top: 2.56vw;
      }
      .image-wrapper_1 {
        width: 4.74vw;
        height: 1.62vw;
        margin: 1.35vw 0 0 0.72vw;
        .image_1 {
          width: 4.74vw;
          height: 0.89vw;
        }
        .image_2 {
          width: 3.6vw;
          height: 0.58vw;
          margin: 0.15vw 0 0 0.05vw;
        }
      }
      .text_1 {
        width: 2.82vw;
        height: 0.94vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.93vw;
        font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.94vw;
        margin: 1.51vw 0 0 10.1vw;
      }
      .group_4 {
        width: 3.7vw;
        height: 1.46vw;
        margin: 1.51vw 0 0 3.07vw;
        .text_2 {
          width: 3.7vw;
          height: 0.94vw;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 0.93vw;
          font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.94vw;
        }
        .group_5 {
          background-color: rgba(51, 51, 51, 1);
          border-radius: 1px;
          width: 1.36vw;
          height: 0.11vw;
          margin: 0.41vw 0 0 1.14vw;
        }
      }
      .text_3 {
        width: 3.7vw;
        height: 0.94vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.93vw;
        font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.94vw;
        margin: 1.51vw 0 0 3.22vw;
      }
      .text_4 {
        width: 3.7vw;
        height: 0.94vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.93vw;
        font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.94vw;
        margin: 1.51vw 0 0 3.22vw;
      }
      .text_5 {
        width: 3.7vw;
        height: 0.94vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.93vw;
        font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.94vw;
        margin: 1.51vw 0 0 3.22vw;
      }
      .label_3 {
        width: 2.09vw;
        height: 2.09vw;
        margin: 0.98vw 0 0 17.96vw;
      }
      .text_6 {
        width: 2.82vw;
        height: 0.94vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.93vw;
        font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.94vw;
        margin: 1.56vw 13.54vw 0 0.41vw;
      }
      .group_6 {
        background-color: rgba(13, 94, 159, 1);
        position: absolute;
        left: 13.55vw;
        top: 2.56vw;
        width: 3.44vw;
        height: 0.68vw;
      }
      .group_7 {
        background-color: rgba(19, 169, 129, 1);
        position: absolute;
        left: 13.6vw;
        top: 1.83vw;
        width: 3.6vw;
        height: 0.53vw;
      }
    }
  }
  .section_2 {
    width: 58.08vw;
    height: 54.17vw;
    margin-bottom: 0.06vw;
    position: relative;
    .class_tab_module {
      // margin: 1vw 0 0 -20vw;
      position: absolute;
      top: 10vw;
      left: -10vw;
      z-index: 999;
    }
    .section_3 {
      width: 58.08vw;
      height: 2.09vw;
      // margin: 0 0 0 0.89vw;
      .text_7 {
        // width: 7.5vw;
        height: 0.84vw;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 0.83vw;
        font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.84vw;
        margin-top: 0.47vw;
        margin-left: 0.89vw;
      }
      .text-wrapper_1 {
        // border-radius: 4px;
        // height: 2.09vw;
        // border: 1px solid rgba(34, 107, 254, 1);
        // width: 5.73vw;
        .text_8 {
          width: 1.67vw;
          height: 0.84vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.83vw;
          font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.84vw;
          margin: 0.62vw 0 0 2.03vw;
        }
      }
    }
    .section_4 {
      width: 58.08vw;
      height: 13.49vw;
      margin: 0.5vw 0 0 0.89vw;
      .group_8 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        height: 13.49vw;
        width: 13.96vw;
        position: relative;
        .group_9 {
          width: 1.52vw;
          height: 0.99vw;
          margin: 6.56vw 0 0 10.88vw;
          .box_2 {
            background-color: rgba(255, 255, 255, 1);
            width: 1.52vw;
            height: 0.99vw;
          }
        }
        .text-wrapper_2 {
          width: 6.52vw;
          height: 0.94vw;
          margin: 2.08vw 0 0 3.69vw;
          .text_9 {
            width: 6.52vw;
            height: 0.94vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.93vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.94vw;
          }
        }
        .text-wrapper_3 {
          width: 5.58vw;
          height: 0.94vw;
          margin: 0.62vw 0 1.35vw 3.69vw;
          .text_10 {
            width: 5.58vw;
            height: 0.94vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 0.93vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.94vw;
          }
        }
        .image-wrapper_2 {
          background-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 0, rgba(233, 171, 35, 0.34) 100%);
          border-radius: 8px 8px 0px 0px;
          height: 8.03vw;
          width: 13.96vw;
          position: absolute;
          left: 0;
          top: 0.68vw;
          .image_3 {
            width: 7.09vw;
            height: 7.09vw;
            margin: 0.36vw 0 0 3.43vw;
          }
        }
      }
      .group_10 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        height: 13.49vw;
        width: 58.08vw;
        justify-content: flex-center;
        .box_3 {
          width: 26.2vw;
          height: 1.36vw;
          margin: 1.04vw 0 0 1.35vw;
          .text_11 {
            width: 20.73vw;
            height: 1.15vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 1.14vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 1.15vw;
            margin-top: 0.11vw;
          }
          .text-wrapper_4 {
            border-radius: 20px;
            height: 1.36vw;
            border: 1px solid rgba(107, 198, 148, 1);
            width: 4.38vw;
            .text_12 {
              width: 2.19vw;
              height: 0.73vw;
              overflow-wrap: break-word;
              color: rgba(107, 198, 148, 1);
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.73vw;
              margin: 0.31vw 0 0 1.09vw;
            }
          }
          .text-wrapper_4_1 {
            border-radius: 20px;
            height: 1.36vw;
            border: 1px solid rgba(238, 161, 49, 1);
            width: 4.38vw;
            .text_12_1 {
              width: 2.19vw;
              height: 0.73vw;
              overflow-wrap: break-word;
              color: rgba(238, 161, 49, 1);
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.73vw;
              margin: 0.31vw 0 0 1.09vw;
            }
          }
          .text-wrapper_4_2 {
            border-radius: 20px;
            height: 1.36vw;
            border: 1px solid rgba(150, 154, 170, 1);
            width: 4.38vw;
            .text_12_2 {
              width: 2.19vw;
              height: 0.73vw;
              overflow-wrap: break-word;
              color: rgba(150, 154, 170, 1);
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.73vw;
              margin: 0.31vw 0 0 1.09vw;
            }
          }
        }
        .box_4 {
          width: 55.47vw;
          height: 2.5vw;
          margin: 0.52vw 0 0 1.35vw;
          .text_13 {
            width: 44.43vw;
            height: 2.5vw;
            overflow-wrap: break-word;
            color: rgba(150, 154, 170, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            line-height: 1.25vw;
          }
          .text-wrapper_5 {
            border-radius: 20px;
            height: 2.09vw;
            margin-top: 0.32vw;
            // margin-right: 1vw;
            // width: 7.45vw;
            .text_14 {
              width: 4.95vw;
              height: 0.84vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.83vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.84vw;
              margin: 0.62vw 0 0 1.25vw;
            }
          }
        }
        .text-wrapper_6 {
          width: 18.39vw;
          height: 1.25vw;
          margin: 0.52vw 0 0 1.35vw;
          .text_15 {
            width: 9.28vw;
            height: 1.25vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 1.25vw;
          }
          .text_16 {
            width: 7.3vw;
            height: 1.25vw;
            overflow-wrap: break-word;
            color: #226BFF;
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 1.25vw;
          }
        }
        .box_5 {
          width: 34.74vw;
          height: 3.81vw;
          margin: 1.45vw 0 1.04vw 1.3vw;
          .image-text_1 {
            width: 29.33vw;
            height: 3.81vw;
            .image-wrapper_3 {
              height: 3.81vw;
              background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG87f2df0c45a327787b538d5a3e30dd13.png) 100% no-repeat;
              background-size: 100% 100%;
              width: 3.81vw;
              .label_4 {
                width: 2.19vw;
                height: 2.19vw;
                margin: 0.83vw 0 0 0.88vw;
              }
            }
            .text-group_1 {
              width: 24.95vw;
              height: 3.81vw;
              .text_17 {
                width: 24.95vw;
                height: 0.94vw;
                overflow-wrap: break-word;
                color: rgba(51, 51, 51, 1);
                font-size: 0.93vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.94vw;
              }
              .text_18 {
                width: 8.23vw;
                height: 1.25vw;
                overflow-wrap: break-word;
                color: rgba(150, 154, 170, 1);
                font-size: 0.83vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 1.25vw;
                margin-top: 0.42vw;
              }
              .text_19 {
                width: 4.59vw;
                height: 0.79vw;
                overflow-wrap: break-word;
                color: rgba(88, 94, 118, 1);
                font-size: 0.78vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.79vw;
                margin-top: 0.42vw;
              }
            }
          }
          .image_4 {
            width: 19.54vw;
            height: 0.16vw;
            margin: 3.33vw 0 0 -20.94vw;
          }
          .thumbnail_1 {
            // width: 0.58vw;
            // height: 0.94vw;
            font-size: 1vw;
            color: #999;
            margin: 1.35vw 0 0 5.26vw;
          }
        }
      }
    }
  }
}
</style>
