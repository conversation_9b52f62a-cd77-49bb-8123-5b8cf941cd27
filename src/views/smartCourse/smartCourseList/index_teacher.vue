<template>
  <div class="page_index_teacher flex-col">
    <div class="section_3 flex-col">
      <div class="group_6 flex-row">
        <div class="block_1 flex-col">
          <div class="box_3 flex-col">
            <div class="box_4 flex-row">
              <div class="block_2 flex-col"></div>
            </div>
            <div class="text-wrapper_1 flex-row">
              <span class="text_7">周润发</span>
            </div>
            <div class="text-wrapper_2 flex-row">
              <span class="text_8">大连理工大学</span>
            </div>
            <div class="text-wrapper_222 flex-row">
              <span class="text_99">当前身份：教师</span>
              <!--            <el-button class="switch-btn" @click="changeRole(userInfo?.userType)">切换</el-button>-->
              <el-dropdown trigger="click" @command="handleCommand">
                <el-button class="switch-btn" type="primary"> 切换 </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="2">教师</el-dropdown-item>
                    <el-dropdown-item command="1">学生</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <div class="image-wrapper_2 flex-col">
              <img
                class="image_3"
                referrerpolicy="no-referrer"
                src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG7a49cc1735018c670d766961aea4cb8b.png"
              />
            </div>
          </div>
          <div class="box_5 flex-col" v-if="!changeSmartCourseTabIndex">
            <div
              class="block_3 flex-row"
              v-for="(item, index) in leftTab"
              :key="index"
              @click="changeLeftTab(index)"
              :class="{ leftTabActiveClass: index === leftTabActive }"
            >
              <div class="image-text_1 flex-row justify-between">
                <img
                  class="thumbnail_1"
                  referrerpolicy="no-referrer"
                  :src="index === leftTabActive ? leftTabImgWhite[index] : leftTabImg[index]"
                />
                <span class="text-group_1">{{ item.label }}</span>
              </div>
            </div>
          </div>
          <div v-if="!changeSmartCourseTabIndex" class="box_6 flex-col"></div>
        </div>
        <!-- 课程管理 -->
        <IndexCourseManage @detailsCourse="detailsCourse" v-if="leftTabActive == 0 && !changeDetailsCourse && !changeSmartCourseTabIndex" />
        <!-- 课程详情 -->
        <DetailsCourse
          :courseId="course?.courseId"
          @goBack="goBack"
          @goClassTab="goClassTab"
          v-if="leftTabActive == 0 && changeDetailsCourse && !changeSmartCourseTabIndex"
        />
        <DetailsClass @goBack="goBackTab" v-if="leftTabActive == 0 && changeDetailsCourse && changeSmartCourseTabIndex" />
        <!-- tab  -->
        <SmartCourseTabIndex v-if="changeSmartCourseTabIndex" :course="course" :classDetail="classDetail"/>
        <!-- 考试管理 -->
        <ExamManage v-if="leftTabActive == 1" />
        <!-- 待批中心 -->
        <IndexWaitApproval v-if="leftTabActive == 2" />
        <!-- 公告管理 -->
        <NoticeManage v-if="leftTabActive == 3" />
        <!-- <div class="block_18 flex-col justify-between">
          <img
            class="image_10"
            referrerpolicy="no-referrer"
            src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG981eaf572e8160183f15b5e953d31eb6.png"
          />
          <div class="box_16 flex-col justify-between">
            <div class="box_17 flex-row">
              <div class="image-text_24 flex-col justify-between">
                <img
                  class="label_14"
                  referrerpolicy="no-referrer"
                  src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG7502e5256dc0e6b0c0af54936a12a7e1.png"
                />
                <span class="text-group_24">联系客服</span>
              </div>
            </div>
            <div class="box_18 flex-row">
              <div class="image-text_25 flex-col justify-between">
                <img
                  class="label_15"
                  referrerpolicy="no-referrer"
                  src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGe74281e78326f1a7daea906a8b56f867.png"
                />
                <span class="text-group_25">反馈意见</span>
              </div>
            </div>
            <div class="box_19 flex-row">
              <div class="image-text_26 flex-col justify-between">
                <img
                  class="label_16"
                  referrerpolicy="no-referrer"
                  src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG07174fb9e1e708f6722b24876bced8e5.png"
                />
                <span class="text-group_26">使用说明</span>
              </div>
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>
<script setup>
import blackKc from '@/assets/images/smartCourse/black_kc.png'
import blackKs from '@/assets/images/smartCourse/black_ks.png'
import blackTp from '@/assets/images/smartCourse/black_tp.png'
import blackGg from '@/assets/images/smartCourse/black_gg.png'
import blackSj from '@/assets/images/smartCourse/black_sj.png'
import whiteKc from '@/assets/images/smartCourse/white_kc.png'
import whiteKs from '@/assets/images/smartCourse/white_ks.png'
import whiteTp from '@/assets/images/smartCourse/white_tp.png'
import whiteGg from '@/assets/images/smartCourse/white_gg.png'
import whiteSj from '@/assets/images/smartCourse/white_sj.png'
import IndexCourseManage from './index_course_manage.vue'
import DetailsCourse from './details_course.vue'
import DetailsClass from './details_class.vue'
import ExamManage from './exam_manage.vue'
import IndexWaitApproval from './index_wait_approval.vue'
import NoticeManage from './notice_manage.vue'
import SmartCourseTabIndex from '../smartCourseTab/index.vue'
import { ref, provide } from 'vue'

const leftTab = ref([
  { label: '课程管理', value: '1' },
  { label: '考试管理', value: '2' },
  { label: '待批中心', value: '3' },
  { label: '公告管理', value: '4' },
  { label: '数据统计', value: '5' }
])
const leftTabImg = [blackKc, blackKs, blackTp, blackGg, blackSj]
const leftTabImgWhite = [whiteKc, whiteKs, whiteTp, whiteGg, whiteSj]
const leftTabActive = ref(0)
const changeDetailsCourse = ref(false)
const changeSmartCourseTabIndex = ref(false)
const flag = ref(false)
const course = ref(null) //选择的课程
const classDetail = ref(null) //选择的班级
const changeLeftTab = i => {
  leftTabActive.value = i
}
// 切换角色
const emits = defineEmits(['changeRole'])

const handleCommand = command => {
  emits('changeRole', command)
}
// 切换页面指令
const detailsCourse = (item) => {
  course.value = item
  changeDetailsCourse.value = true
}
// 返回
const goBack = () => {
  changeDetailsCourse.value = false
}
// tab返回
const goBackTab = () => {
  if (flag.value) {
    changeDetailsCourse.value = false
    changeSmartCourseTabIndex.value = false
  } else {
    changeDetailsCourse.value = true
    changeSmartCourseTabIndex.value = false
  }
}
// 去班级tab页
const goClassTab = param => {
  flag.value = param.flag
  console.log(param)
  classDetail.value = param
  changeDetailsCourse.value = true
  changeSmartCourseTabIndex.value = true
}
provide('goClassTab', goClassTab)
</script>
<style lang="scss">
.page_index_teacher {
  .leftTabActiveClass {
    background-image: linear-gradient(90deg, rgba(34, 107, 255, 1) 0, rgba(4, 72, 210, 1) 100%);
    color: #fff;
  }
  .topTabActiveClass {
    color: #000 !important;
  }
  .topTabContengActiveClass {
    background-color: rgba(85, 91, 116, 1) !important;
    color: #fff !important;
  }
  background-color: rgba(242, 245, 250, 1);
  position: relative;
  width: 100vw;
  height: 62.82vw;
  overflow: hidden;
  .pagination-container {
    margin-right: 3.5vw;
  }
  .section_3 {
    width: 100vw;
    height: 58.91vw;
    margin-bottom: 0.06vw;
    .group_6 {
      width: 86.46vw;
      height: 56.25vw;
      margin: 1.04vw 0 0 13.54vw;
      .block_1 {
        width: 13.96vw;
        height: 56.25vw;
        .box_3 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 8px;
          // height: 17.7vw;
          width: 13.96vw;
          position: relative;
          .box_4 {
            width: 1.52vw;
            height: 1.05vw;
            margin: 7.76vw 0 0 10.88vw;
            .block_2 {
              background-color: rgba(255, 255, 255, 1);
              width: 1.52vw;
              height: 1.05vw;
            }
          }
          .text-wrapper_1 {
            // width: 6.52vw;
            height: 0.94vw;
            margin: 0.83vw 0 0 0;
            justify-content: center;
            .text_7 {
              // width: 6.52vw;
              height: 0.94vw;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 0.93vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              // text-align: left;
              white-space: nowrap;
              line-height: 0.94vw;
            }
          }
          .text-wrapper_2 {
            // width: 5.58vw;
            height: 0.94vw;
            margin: 0.83vw 0 0 0;
            justify-content: center;
            .text_8 {
              // width: 5.58vw;
              height: 0.94vw;
              overflow-wrap: break-word;
              color: rgba(88, 94, 118, 1);
              font-size: 0.93vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              // text-align: left;
              white-space: nowrap;
              line-height: 0.94vw;
            }
          }
          .text-wrapper_222 {
            width: 9.58vw;
            height: 0.94vw;
            margin: 0.62vw 0 2.25vw 3.69vw;
            .text_99 {
              margin: 0.62vw 0 1.35vw -1.69vw;
              width: 5.58vw;
              height: 0.94vw;
              overflow-wrap: break-word;
              color: rgba(88, 94, 118, 1);
              font-size: 0.93vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.94vw;
            }
          }
          .switch-btn {
            width: 2.78vw;
            height: 1.58vw;
            margin-top: 0.3vw;
            margin-left: 2vw;
            background-color: #13a981;
            font-family:
              Alibaba PuHuiTi 2,
              Alibaba PuHuiTi 20;
            font-weight: normal;
            font-size: 0.83vw;
            color: #ffffff;
            text-align: left;
            font-style: normal;
          }
          .image-wrapper_2 {
            background-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 0, rgba(233, 171, 35, 0.34) 100%);
            border-radius: 8px 8px 0px 0px;
            height: 8.03vw;
            width: 13.96vw;
            position: absolute;
            left: 0;
            top: 0.68vw;
            .image_3 {
              width: 7.09vw;
              height: 7.09vw;
              margin: 0.36vw 0 0 3.43vw;
            }
          }
        }
        .box_5 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 8px 8px 0 0;
          width: 13.96vw;
          height: 14.59vw;
          margin-top: 1.05vw;
          .block_3 {
            // background-image: linear-gradient(90deg, rgba(34, 107, 255, 1) 0, rgba(4, 72, 210, 1) 100%);
            cursor: pointer;
            border-radius: 8px;
            width: 13.96vw;
            height: 2.92vw;
            .image-text_1 {
              width: 4.64vw;
              height: 1.15vw;
              margin: 0.88vw 0 0 4.68vw;
              .thumbnail_1 {
                width: 1.05vw;
                height: 1.05vw;
                margin-top: 0.06vw;
              }
              .text-group_1 {
                width: 3.29vw;
                height: 1.15vw;
                overflow-wrap: break-word;
                // color: rgba(255, 255, 255, 1);
                font-size: 0.83vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 1.15vw;
              }
            }
          }
          .image-text_2 {
            width: 4.64vw;
            height: 1.15vw;
            margin: 0.88vw 0 0 4.68vw;
            .thumbnail_2 {
              width: 1.05vw;
              height: 1.05vw;
              margin-top: 0.06vw;
            }
            .text-group_2 {
              width: 3.29vw;
              height: 1.15vw;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 0.83vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 1.15vw;
            }
          }
          .image-text_3 {
            width: 4.64vw;
            height: 1.15vw;
            margin: 1.77vw 0 0 4.68vw;
            .thumbnail_3 {
              width: 1.05vw;
              height: 1.05vw;
              margin-top: 0.06vw;
            }
            .text-group_3 {
              width: 3.29vw;
              height: 1.15vw;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 0.83vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 1.15vw;
            }
          }
          .image-text_4 {
            width: 4.64vw;
            height: 1.15vw;
            margin: 1.77vw 0 0 4.68vw;
            .thumbnail_4 {
              width: 1.05vw;
              height: 1.05vw;
              margin-top: 0.06vw;
            }
            .text-group_4 {
              width: 3.29vw;
              height: 1.15vw;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 0.83vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 1.15vw;
            }
          }
          .image-text_5 {
            width: 4.85vw;
            height: 1.15vw;
            margin: 1.77vw 0 0.88vw 4.58vw;
            .thumbnail_5 {
              width: 1.05vw;
              height: 1.05vw;
              margin-top: 0.06vw;
            }
            .text-group_5 {
              width: 3.29vw;
              height: 1.15vw;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 0.83vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 1.15vw;
            }
          }
        }
        .box_6 {
          background-color: rgba(255, 255, 255, 1);
          width: 13.96vw;
          height: 27.09vw;
          // margin-top: 0.06vw;
        }
      }
      .block_4 {
        width: 58.08vw;
        height: 56.25vw;
        margin-left: 0.89vw;
        .group_7 {
          width: 58.08vw;
          height: 4.9vw;
          .group_8 {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 8px;
            width: 52.04vw;
            height: 4.9vw;
            .image_4 {
              width: 3.13vw;
              height: 3.13vw;
              margin: 0.88vw 0 0 1.35vw;
            }
            .text-wrapper_3 {
              width: 2.92vw;
              height: 2.14vw;
              margin: 1.4vw 0 0 0.72vw;
              .text_9 {
                width: 2.71vw;
                height: 1.1vw;
                overflow-wrap: break-word;
                color: rgba(88, 94, 118, 1);
                font-size: 1.14vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 1.1vw;
              }
              .text_10 {
                width: 2.92vw;
                height: 0.73vw;
                overflow-wrap: break-word;
                color: rgba(150, 154, 170, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: center;
                white-space: nowrap;
                line-height: 0.73vw;
                margin-top: 0.32vw;
              }
            }
            .image_5 {
              width: 3.13vw;
              height: 3.13vw;
              margin: 0.88vw 0 0 3.85vw;
            }
            .text-wrapper_4 {
              width: 2.92vw;
              height: 2.14vw;
              margin: 1.4vw 0 0 0.72vw;
              .text_11 {
                width: 2.04vw;
                height: 1.1vw;
                overflow-wrap: break-word;
                color: rgba(88, 94, 118, 1);
                font-size: 1.14vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 1.1vw;
              }
              .text_12 {
                width: 2.92vw;
                height: 0.73vw;
                overflow-wrap: break-word;
                color: rgba(150, 154, 170, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: center;
                white-space: nowrap;
                line-height: 0.73vw;
                margin-top: 0.32vw;
              }
            }
            .image_6 {
              width: 3.13vw;
              height: 3.13vw;
              margin: 0.88vw 0 0 3.85vw;
            }
            .text-wrapper_5 {
              width: 2.92vw;
              height: 2.14vw;
              margin: 1.4vw 0 0 0.72vw;
              .text_13 {
                width: 2.71vw;
                height: 1.1vw;
                overflow-wrap: break-word;
                color: rgba(88, 94, 118, 1);
                font-size: 1.14vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 1.1vw;
              }
              .text_14 {
                width: 2.92vw;
                height: 0.73vw;
                overflow-wrap: break-word;
                color: rgba(150, 154, 170, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: center;
                white-space: nowrap;
                line-height: 0.73vw;
                margin-top: 0.32vw;
              }
            }
            .image_7 {
              width: 3.13vw;
              height: 3.13vw;
              margin: 0.88vw 0 0 3.85vw;
            }
            .text-wrapper_6 {
              width: 2.92vw;
              height: 2.14vw;
              margin: 1.4vw 0 0 0.72vw;
              .text_15 {
                width: 2.71vw;
                height: 1.1vw;
                overflow-wrap: break-word;
                color: rgba(88, 94, 118, 1);
                font-size: 1.14vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 1.1vw;
              }
              .text_16 {
                width: 2.92vw;
                height: 0.73vw;
                overflow-wrap: break-word;
                color: rgba(150, 154, 170, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: center;
                white-space: nowrap;
                line-height: 0.73vw;
                margin-top: 0.32vw;
              }
            }
            .image_8 {
              width: 3.13vw;
              height: 3.13vw;
              margin: 0.88vw 0 0 3.85vw;
            }
            .text-wrapper_7 {
              width: 2.92vw;
              height: 2.14vw;
              margin: 1.4vw 1.4vw 0 0.72vw;
              .text_17 {
                width: 2.71vw;
                height: 1.1vw;
                overflow-wrap: break-word;
                color: rgba(88, 94, 118, 1);
                font-size: 1.14vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 1.1vw;
              }
              .text_18 {
                width: 2.92vw;
                height: 0.73vw;
                overflow-wrap: break-word;
                color: rgba(150, 154, 170, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: center;
                white-space: nowrap;
                line-height: 0.73vw;
                margin-top: 0.32vw;
              }
            }
          }
          .group_9 {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 8px;
            width: 5.53vw;
            height: 4.9vw;
            .box_7 {
              background-color: rgba(242, 245, 250, 1);
              border-radius: 50%;
              position: relative;
              width: 2.19vw;
              height: 2.19vw;
              margin: 0.72vw 0 0 1.61vw;
              .label_4 {
                width: 1.25vw;
                height: 1.25vw;
                margin: 0.57vw 0 0 0.46vw;
              }
              .text-wrapper_8 {
                background-color: rgba(255, 77, 79, 1);
                border-radius: 50%;
                height: 0.79vw;
                width: 0.79vw;
                position: absolute;
                left: 1.36vw;
                top: 0;
                .text_19 {
                  width: 0.27vw;
                  height: 0.47vw;
                  overflow-wrap: break-word;
                  color: rgba(255, 255, 255, 1);
                  font-size: 0.41vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.42vw;
                  margin: 0.15vw 0 0 0.26vw;
                }
              }
            }
            .text_20 {
              width: 2.19vw;
              height: 0.73vw;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.73vw;
              margin: 0.57vw 0 0.67vw 1.61vw;
            }
          }
        }
        .group_10 {
          width: 58.08vw;
          height: 2.09vw;
          margin-top: 1.05vw;
          .group_11 {
            width: 4.95vw;
            height: 1.52vw;
            margin-top: 0.32vw;
            margin-right: 2vw;
            cursor: pointer;
            .text_21 {
              width: 4.95vw;
              height: 1.25vw;
              overflow-wrap: break-word;
              color: rgb(153, 153, 153);
              // color: rgba(51, 51, 51, 1);
              font-size: 1.25vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: center;
              white-space: nowrap;
              line-height: 1.25vw;
            }
            .group_12 {
              background-image: linear-gradient(153deg, rgba(199, 199, 199, 1) 0, rgba(105, 105, 105, 1) 100%);
              border-radius: 2px;
              width: 4.95vw;
              height: 0.11vw;
              margin-top: 0.16vw;
            }
          }
          .group_111:nth-child(2) {
            margin-right: 2vw;
          }
          .group_111 {
            width: 4.95vw;
            height: 1.52vw;
            margin-top: 0.32vw;
            cursor: pointer;
            .text_21 {
              width: 4.95vw;
              height: 1.25vw;
              overflow-wrap: break-word;
              color: rgb(153, 153, 153);
              // color: rgba(51, 51, 51, 1);
              font-size: 1.25vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: center;
              white-space: nowrap;
              line-height: 1.25vw;
            }
            .group_12 {
              background-image: linear-gradient(153deg, rgba(199, 199, 199, 1) 0, rgba(105, 105, 105, 1) 100%);
              border-radius: 2px;
              width: 2.95vw;
              height: 0.11vw;
              margin-top: 0.16vw;
              margin-left: 1vw;
            }
          }
          .group_13 {
            width: 4.95vw;
            height: 1.52vw;
            margin: 0.31vw 0 0 2.29vw;
            .text_22 {
              width: 4.95vw;
              height: 1.25vw;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 1.25vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: center;
              white-space: nowrap;
              line-height: 1.25vw;
            }
            .box_8 {
              border-radius: 2px;
              width: 4.95vw;
              height: 0.11vw;
              margin-top: 0.16vw;
              background-image: linear-gradient(153deg, rgb(199, 199, 199) 0, rgb(105, 105, 105) 100%);
            }
          }
          .group_14 {
            background-color: rgba(255, 255, 255, 0.28);
            border-radius: 2px;
            width: 12.4vw;
            height: 2.09vw;
            // border: 1px solid rgba(150, 154, 170, 1);
            margin-left: 16.83vw;
            .text_23 {
              width: 5.79vw;
              height: 0.73vw;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.73vw;
              margin: 0.67vw 0 0 0.93vw;
            }
            .block_5 {
              background-color: rgba(150, 154, 170, 1);
              border-radius: 2px;
              width: 1.1vw;
              height: 1.15vw;
              margin: 0.46vw 0.83vw 0 3.75vw;
            }
          }
          .group_15 {
            border-radius: 2px;
            width: 15.63vw;
            height: 2.09vw;
            // border: 1px solid rgba(150, 154, 170, 1);
            margin-left: 1.05vw;
            .text_24 {
              width: 3.29vw;
              height: 1.25vw;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 0.83vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 1.25vw;
              margin: 0.41vw 0 0 0.62vw;
            }
            .image_9 {
              width: 0.89vw;
              height: 1.67vw;
              margin: 0.2vw 0.57vw 0 10.26vw;
            }
          }
        }
        .group_16 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 8px;
          width: 58.08vw;
          height: 47.3vw;
          margin-top: 0.94vw;
          .group_17 {
            width: 55.47vw;
            height: 2.09vw;
            margin: 0.88vw 0 0 1.35vw;
            .text-wrapper_9 {
              background-color: rgba(85, 91, 116, 1);
              border-radius: 4px;
              height: 1.88vw;
              margin-top: 0.21vw;
              width: 5.84vw;
              .text_25 {
                width: 3.29vw;
                height: 0.84vw;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 0.83vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.84vw;
                margin: 0.52vw 0 0 1.3vw;
              }
            }
            .text-wrapper_10 {
              border-radius: 4px;
              height: 1.88vw;
              border: 1px solid rgba(126, 136, 165, 1);
              width: 5.84vw;
              margin: 0.2vw 0 0 0.83vw;
              cursor: pointer;
              text-align: center;
              line-height: 1.88vw;
              .text_26 {
                overflow-wrap: break-word;
                font-size: 0.83vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                white-space: nowrap;
              }
            }
            .text-wrapper_11 {
              background-color: rgba(255, 255, 255, 1);
              border-radius: 4px;
              height: 1.88vw;
              border: 1px solid rgba(126, 136, 165, 1);
              width: 5.84vw;
              margin: 0.2vw 0 0 0.83vw;
              .text_27 {
                width: 2.5vw;
                height: 0.84vw;
                overflow-wrap: break-word;
                color: rgba(51, 51, 51, 1);
                font-size: 0.83vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.84vw;
                margin: 0.52vw 0 0 1.66vw;
              }
            }
            .text-wrapper_12 {
              background-color: rgba(255, 255, 255, 1);
              border-radius: 4px;
              height: 1.88vw;
              border: 1px solid rgba(126, 136, 165, 1);
              width: 5.84vw;
              margin: 0.2vw 0 0 0.83vw;
              .text_28 {
                width: 2.5vw;
                height: 0.84vw;
                overflow-wrap: break-word;
                color: rgba(51, 51, 51, 1);
                font-size: 0.83vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.84vw;
                margin: 0.52vw 0 0 1.66vw;
              }
            }
            .text-wrapper_13 {
              border-radius: 20px;
              height: 2.09vw;
              margin-left: 23.86vw;
              width: 5.79vw;
              font-size: 20px;
              .text_29 {
                color: rgba(255, 255, 255, 1);
                font-size: 0.83vw;
              }
            }
          }
          .group_18 {
            width: 51.46vw;
            // height: 11.67vw;
            margin: 1.19vw 0 0 3.07vw;
            display: flex;
            flex-wrap: wrap;
            gap: 1vw;
            .block_6 {
              cursor: pointer;
              background-color: rgba(255, 255, 255, 1);
              border-radius: 14px;
              width: 16.46vw;
              height: 11.67vw;
              border: 2px solid rgba(230, 235, 245, 1);
              .image-text_6 {
                width: 15.63vw;
                height: 6.1vw;
                margin: 0.41vw 0 0 0.41vw;
                .group_19 {
                  width: 15.45vw;
                  height: 4.22vw;
                  background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6a4f63b34982242b7e0d4462f96ab730.png) 100% no-repeat;
                  background-size: 100% 100%;
                  .text-wrapper_14 {
                    border-radius: 8px;
                    height: 1.36vw;
                    width: 3.75vw;
                    margin: 1.45vw 0 0 0.52vw;
                    background: #ffbf9b;
                    .text_30 {
                      width: 2.92vw;
                      height: 0.84vw;
                      overflow-wrap: break-word;
                      color: rgba(80, 44, 19, 1);
                      font-size: 0.72vw;
                      font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                      font-weight: normal;
                      text-align: left;
                      white-space: nowrap;
                      line-height: 0.84vw;
                      margin: 0.26vw 0 0 0.41vw;
                    }
                  }
                  .label_5 {
                    width: 1.57vw;
                    height: 1.57vw;
                    margin: 1.35vw 0.67vw 0 9.11vw;
                  }
                }
                .group_20 {
                  width: 14.07vw;
                  height: 1.36vw;
                  margin: 0.52vw 0 0 0.78vw;
                  .text-group_6 {
                    width: 8.29vw;
                    height: 0.94vw;
                    overflow-wrap: break-word;
                    color: rgba(0, 0, 0, 1);
                    font-size: 0.93vw;
                    font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                    font-weight: normal;
                    text-align: left;
                    white-space: nowrap;
                    line-height: 0.94vw;
                    margin-top: 0.21vw;
                  }
                  .text-wrapper_15 {
                    border-radius: 20px;
                    height: 1.36vw;
                    border: 1px solid rgba(82, 196, 26, 1);
                    width: 4.38vw;
                    .text_31 {
                      width: 2.19vw;
                      height: 0.73vw;
                      overflow-wrap: break-word;
                      color: rgba(82, 196, 26, 1);
                      font-size: 0.72vw;
                      font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                      font-weight: normal;
                      text-align: left;
                      white-space: nowrap;
                      line-height: 0.73vw;
                      margin: 0.31vw 0 0 1.09vw;
                    }
                  }
                  .text-wrapper_24 {
                    border-radius: 20px;
                    height: 1.36vw;
                    border: 1px solid rgba(238, 161, 49, 1);
                    width: 4.38vw;
                    .text_43 {
                      width: 2.19vw;
                      height: 0.73vw;
                      overflow-wrap: break-word;
                      color: rgba(238, 161, 49, 1);
                      font-size: 0.72vw;
                      font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                      font-weight: normal;
                      text-align: left;
                      white-space: nowrap;
                      line-height: 0.73vw;
                      margin: 0.31vw 0 0 1.09vw;
                    }
                  }
                  .text-wrapper_33 {
                    border-radius: 20px;
                    height: 1.36vw;
                    border: 1px solid rgba(150, 154, 170, 1);
                    width: 4.38vw;
                    .text_55 {
                      width: 2.19vw;
                      height: 0.73vw;
                      overflow-wrap: break-word;
                      color: rgba(150, 154, 170, 1);
                      font-size: 0.72vw;
                      font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                      font-weight: normal;
                      text-align: left;
                      white-space: nowrap;
                      line-height: 0.73vw;
                      margin: 0.31vw 0 0 1.09vw;
                    }
                  }
                }
              }
              .text-wrapper_16 {
                width: 10.06vw;
                height: 0.84vw;
                margin: 0.83vw 0 0 1.19vw;
                .text_32 {
                  width: 3.75vw;
                  height: 0.84vw;
                  overflow-wrap: break-word;
                  color: rgba(150, 154, 170, 1);
                  font-size: 0.83vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.84vw;
                }
                .text_33 {
                  width: 4.95vw;
                  height: 0.84vw;
                  overflow-wrap: break-word;
                  color: rgba(34, 107, 255, 1);
                  font-size: 0.83vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.84vw;
                }
              }
              .block_7 {
                background-color: rgba(242, 245, 250, 1);
                border-radius: 8px;
                width: 11.46vw;
                height: 1.67vw;
                margin: 0.83vw 0 0.98vw 1.19vw;
                .image-text_7 {
                  width: 10.42vw;
                  height: 0.84vw;
                  margin: 0.41vw 0 0 0.52vw;
                  .thumbnail_6 {
                    width: 0.84vw;
                    height: 0.84vw;
                    margin-top: -0.05vw;
                  }
                  .text-group_7 {
                    width: 9.28vw;
                    height: 0.84vw;
                    overflow-wrap: break-word;
                    color: rgba(88, 94, 118, 1);
                    font-size: 0.83vw;
                    font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                    font-weight: normal;
                    text-align: left;
                    white-space: nowrap;
                    line-height: 0.84vw;
                  }
                }
              }
            }
            .block_8 {
              background-color: rgba(255, 255, 255, 1);
              border-radius: 14px;
              width: 16.46vw;
              height: 11.67vw;
              border: 2px solid rgba(230, 235, 245, 1);
              margin-left: 1.05vw;
              .image-text_8 {
                width: 15.63vw;
                height: 6.1vw;
                margin: 0.41vw 0 0 0.41vw;
                .block_9 {
                  width: 15.63vw;
                  height: 4.22vw;
                  background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6a4f63b34982242b7e0d4462f96ab730.png) 100% no-repeat;
                  background-size: 100% 100%;
                  .text-wrapper_17 {
                    border-radius: 8px;
                    height: 1.36vw;
                    width: 3.75vw;
                    margin: 1.45vw 0 0 0.52vw;
                    .text_34 {
                      width: 2.92vw;
                      height: 0.84vw;
                      overflow-wrap: break-word;
                      color: rgba(80, 44, 19, 1);
                      font-size: 0.72vw;
                      font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                      font-weight: normal;
                      text-align: left;
                      white-space: nowrap;
                      line-height: 0.84vw;
                      margin: 0.26vw 0 0 0.41vw;
                    }
                  }
                  .label_6 {
                    width: 1.57vw;
                    height: 1.57vw;
                    margin: 1.35vw 0.62vw 0 9.16vw;
                  }
                }
                .block_10 {
                  width: 13.75vw;
                  height: 1.36vw;
                  margin: 0.52vw 0 0 0.78vw;
                  .text-group_8 {
                    width: 7.97vw;
                    height: 0.94vw;
                    overflow-wrap: break-word;
                    color: rgba(0, 0, 0, 1);
                    font-size: 0.93vw;
                    font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                    font-weight: normal;
                    text-align: left;
                    white-space: nowrap;
                    line-height: 0.94vw;
                    margin-top: 0.21vw;
                  }
                  .text-wrapper_18 {
                    border-radius: 20px;
                    height: 1.36vw;
                    border: 1px solid rgba(82, 196, 26, 1);
                    width: 4.38vw;
                    .text_35 {
                      width: 2.19vw;
                      height: 0.73vw;
                      overflow-wrap: break-word;
                      color: rgba(82, 196, 26, 1);
                      font-size: 0.72vw;
                      font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                      font-weight: normal;
                      text-align: left;
                      white-space: nowrap;
                      line-height: 0.73vw;
                      margin: 0.31vw 0 0 1.09vw;
                    }
                  }
                }
              }
              .text-wrapper_19 {
                width: 10.06vw;
                height: 0.84vw;
                margin: 0.83vw 0 0 1.19vw;
                .text_36 {
                  width: 3.75vw;
                  height: 0.84vw;
                  overflow-wrap: break-word;
                  color: rgba(150, 154, 170, 1);
                  font-size: 0.83vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.84vw;
                }
                .text_37 {
                  width: 4.95vw;
                  height: 0.84vw;
                  overflow-wrap: break-word;
                  color: rgba(34, 107, 255, 1);
                  font-size: 0.83vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.84vw;
                }
              }
              .block_11 {
                background-color: rgba(242, 245, 250, 1);
                border-radius: 8px;
                width: 11.46vw;
                height: 1.67vw;
                margin: 0.83vw 0 0.98vw 1.19vw;
                .image-text_9 {
                  width: 10.42vw;
                  height: 0.84vw;
                  margin: 0.41vw 0 0 0.52vw;
                  .thumbnail_7 {
                    width: 0.84vw;
                    height: 0.84vw;
                  }
                  .text-group_9 {
                    width: 9.28vw;
                    height: 0.84vw;
                    overflow-wrap: break-word;
                    color: rgba(88, 94, 118, 1);
                    font-size: 0.83vw;
                    font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                    font-weight: normal;
                    text-align: left;
                    white-space: nowrap;
                    line-height: 0.84vw;
                  }
                }
              }
            }
            .block_12 {
              background-color: rgba(255, 255, 255, 1);
              border-radius: 14px;
              width: 16.46vw;
              height: 11.67vw;
              border: 2px solid rgba(230, 235, 245, 1);
              margin-left: 1.05vw;
              .image-text_10 {
                width: 15.73vw;
                height: 6.1vw;
                margin: 0.41vw 0 0 0.41vw;
                .block_13 {
                  width: 15.63vw;
                  height: 4.22vw;
                  background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6a4f63b34982242b7e0d4462f96ab730.png) 100% no-repeat;
                  background-size: 100% 100%;
                  .text-wrapper_20 {
                    border-radius: 8px;
                    height: 1.36vw;
                    width: 3.75vw;
                    margin: 1.45vw 0 0 0.52vw;
                    .text_38 {
                      width: 2.92vw;
                      height: 0.84vw;
                      overflow-wrap: break-word;
                      color: rgba(80, 44, 19, 1);
                      font-size: 0.72vw;
                      font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                      font-weight: normal;
                      text-align: left;
                      white-space: nowrap;
                      line-height: 0.84vw;
                      margin: 0.26vw 0 0 0.41vw;
                    }
                  }
                  .label_7 {
                    width: 1.57vw;
                    height: 1.57vw;
                    margin: 1.35vw 0.67vw 0 9.11vw;
                  }
                }
                .block_14 {
                  width: 14.95vw;
                  height: 1.36vw;
                  margin: 0.52vw 0 0 0.78vw;
                  .text-group_10 {
                    width: 9.17vw;
                    height: 0.94vw;
                    overflow-wrap: break-word;
                    color: rgba(0, 0, 0, 1);
                    font-size: 0.93vw;
                    font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                    font-weight: normal;
                    text-align: left;
                    white-space: nowrap;
                    line-height: 0.94vw;
                    margin-top: 0.21vw;
                  }
                  .text-wrapper_21 {
                    border-radius: 20px;
                    height: 1.36vw;
                    border: 1px solid rgba(82, 196, 26, 1);
                    width: 4.38vw;
                    .text_39 {
                      width: 2.19vw;
                      height: 0.73vw;
                      overflow-wrap: break-word;
                      color: rgba(82, 196, 26, 1);
                      font-size: 0.72vw;
                      font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                      font-weight: normal;
                      text-align: left;
                      white-space: nowrap;
                      line-height: 0.73vw;
                      margin: 0.31vw 0 0 1.09vw;
                    }
                  }
                }
              }
              .text-wrapper_22 {
                width: 10.06vw;
                height: 0.84vw;
                margin: 0.83vw 0 0 1.19vw;
                .text_40 {
                  width: 3.75vw;
                  height: 0.84vw;
                  overflow-wrap: break-word;
                  color: rgba(150, 154, 170, 1);
                  font-size: 0.83vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.84vw;
                }
                .text_41 {
                  width: 4.95vw;
                  height: 0.84vw;
                  overflow-wrap: break-word;
                  color: rgba(34, 107, 255, 1);
                  font-size: 0.83vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.84vw;
                }
              }
              .box_9 {
                background-color: rgba(242, 245, 250, 1);
                border-radius: 8px;
                width: 11.46vw;
                height: 1.67vw;
                margin: 0.83vw 0 0.98vw 1.19vw;
                .image-text_11 {
                  width: 10.42vw;
                  height: 0.84vw;
                  margin: 0.41vw 0 0 0.52vw;
                  .thumbnail_8 {
                    width: 0.84vw;
                    height: 0.84vw;
                  }
                  .text-group_11 {
                    width: 9.28vw;
                    height: 0.84vw;
                    overflow-wrap: break-word;
                    color: rgba(88, 94, 118, 1);
                    font-size: 0.83vw;
                    font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                    font-weight: normal;
                    text-align: left;
                    white-space: nowrap;
                    line-height: 0.84vw;
                  }
                }
              }
            }
          }
          .group_32 {
            width: 31.93vw;
            height: 1.67vw;
            margin: 2.23vw 0 2.13vw 11.45vw;
            .image-wrapper_3 {
              background-color: rgba(255, 255, 255, 1);
              border-radius: 2px;
              height: 1.67vw;
              border: 1px solid rgba(217, 217, 217, 1);
              width: 1.67vw;
              .thumbnail_15 {
                width: 0.63vw;
                height: 0.63vw;
                margin: 0.52vw 0 0 0.52vw;
              }
            }
            .text-wrapper_41 {
              background-color: rgba(255, 255, 255, 1);
              border-radius: 2px;
              height: 1.67vw;
              border: 1px solid rgba(217, 217, 217, 1);
              margin-left: 0.42vw;
              width: 1.67vw;
              .text_66 {
                width: 0.94vw;
                height: 1.57vw;
                overflow-wrap: break-word;
                color: rgba(51, 51, 51, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: center;
                line-height: 1.15vw;
                margin: 0.05vw 0 0 0.36vw;
              }
            }
            .text_67 {
              width: 0.99vw;
              height: 1.67vw;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 0.25);
              font-size: 0.72vw;
              letter-spacing: 2px;
              font-family: Arial-Regular;
              font-weight: normal;
              text-align: center;
              line-height: 1.67vw;
              margin-left: 0.79vw;
            }
            .text-wrapper_42 {
              background-color: rgba(255, 255, 255, 1);
              border-radius: 2px;
              height: 1.67vw;
              border: 1px solid rgba(217, 217, 217, 1);
              margin-left: 0.73vw;
              width: 1.67vw;
              .text_68 {
                width: 0.94vw;
                height: 1.57vw;
                overflow-wrap: break-word;
                color: rgba(51, 51, 51, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: center;
                line-height: 1.15vw;
                margin: 0.05vw 0 0 0.36vw;
              }
            }
            .text-wrapper_43 {
              background-color: rgba(255, 255, 255, 1);
              border-radius: 2px;
              height: 1.67vw;
              border: 1px solid rgba(217, 217, 217, 1);
              margin-left: 0.42vw;
              width: 1.67vw;
              .text_69 {
                width: 0.94vw;
                height: 1.57vw;
                overflow-wrap: break-word;
                color: rgba(51, 51, 51, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: center;
                line-height: 1.15vw;
                margin: 0.05vw 0 0 0.36vw;
              }
            }
            .text-wrapper_44 {
              background-color: rgba(255, 255, 255, 1);
              border-radius: 2px;
              height: 1.67vw;
              border: 1px solid rgba(34, 107, 255, 1);
              margin-left: 0.42vw;
              width: 1.67vw;
              .text_70 {
                width: 0.94vw;
                height: 1.57vw;
                overflow-wrap: break-word;
                color: rgba(34, 107, 255, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: center;
                line-height: 1.15vw;
                margin: 0.05vw 0 0 0.36vw;
              }
            }
            .text-wrapper_45 {
              background-color: rgba(255, 255, 255, 1);
              border-radius: 2px;
              height: 1.67vw;
              border: 1px solid rgba(217, 217, 217, 1);
              margin-left: 0.42vw;
              width: 1.67vw;
              .text_71 {
                width: 0.94vw;
                height: 1.57vw;
                overflow-wrap: break-word;
                color: rgba(51, 51, 51, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: center;
                line-height: 1.15vw;
                margin: 0.05vw 0 0 0.36vw;
              }
            }
            .text-wrapper_46 {
              background-color: rgba(255, 255, 255, 1);
              border-radius: 2px;
              height: 1.67vw;
              border: 1px solid rgba(217, 217, 217, 1);
              margin-left: 0.42vw;
              width: 1.67vw;
              .text_72 {
                width: 0.94vw;
                height: 1.57vw;
                overflow-wrap: break-word;
                color: rgba(51, 51, 51, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: center;
                line-height: 1.15vw;
                margin: 0.05vw 0 0 0.36vw;
              }
            }
            .text_73 {
              width: 0.99vw;
              height: 1.67vw;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 0.25);
              font-size: 0.72vw;
              letter-spacing: 2px;
              font-family: Arial-Regular;
              font-weight: normal;
              text-align: center;
              line-height: 1.67vw;
              margin-left: 0.79vw;
            }
            .text-wrapper_47 {
              background-color: rgba(255, 255, 255, 1);
              border-radius: 2px;
              height: 1.67vw;
              border: 1px solid rgba(217, 217, 217, 1);
              margin-left: 0.73vw;
              width: 1.67vw;
              .text_74 {
                width: 0.94vw;
                height: 1.57vw;
                overflow-wrap: break-word;
                color: rgba(51, 51, 51, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: center;
                line-height: 1.15vw;
                margin: 0.05vw 0 0 0.36vw;
              }
            }
            .image-wrapper_4 {
              background-color: rgba(255, 255, 255, 1);
              border-radius: 2px;
              height: 1.67vw;
              border: 1px solid rgba(217, 217, 217, 1);
              margin-left: 0.42vw;
              width: 1.67vw;
              .thumbnail_16 {
                width: 0.63vw;
                height: 0.63vw;
                margin: 0.52vw 0 0 0.52vw;
              }
            }
            .text_75 {
              width: 0.73vw;
              height: 1.15vw;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: center;
              white-space: nowrap;
              line-height: 1.15vw;
              margin: 0.26vw 0 0 1.56vw;
            }
            .text-wrapper_48 {
              background-color: rgba(255, 255, 255, 1);
              border-radius: 2px;
              height: 1.67vw;
              border: 1px solid rgba(217, 217, 217, 1);
              margin-left: 0.53vw;
              width: 2.14vw;
              .text_76 {
                width: 0.89vw;
                height: 1.15vw;
                overflow-wrap: break-word;
                color: rgba(51, 51, 51, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 1.15vw;
                margin: 0.26vw 0 0 0.62vw;
              }
            }
            .text_77 {
              width: 0.73vw;
              height: 1.15vw;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: center;
              white-space: nowrap;
              line-height: 1.15vw;
              margin: 0.26vw 0 0 0.52vw;
            }
            .text-wrapper_49 {
              background-color: rgba(34, 107, 255, 1);
              border-radius: 2px;
              height: 1.67vw;
              margin-left: 0.53vw;
              width: 2.71vw;
              .text_78 {
                width: 1.46vw;
                height: 1.15vw;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 0.85);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 1.15vw;
                margin: 0.26vw 0 0 0.62vw;
              }
            }
          }
        }
      }
      .block_18 {
        width: 5.68vw;
        height: 15.21vw;
        margin: 20.46vw 0 0 7.86vw;
        .image_10 {
          width: 5.68vw;
          height: 4.28vw;
        }
        .box_16 {
          box-shadow: 0px 4px 10px 0px rgba(134, 154, 189, 0.15);
          width: 3.65vw;
          height: 10.94vw;
          margin-left: 0.99vw;
          .box_17 {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 8px 8px 0px 0px;
            width: 3.65vw;
            height: 3.65vw;
            border: 1px solid rgba(230, 235, 245, 1);
            .image-text_24 {
              width: 2.92vw;
              height: 2.45vw;
              margin: 0.62vw 0 0 0.36vw;
              .label_14 {
                width: 1.46vw;
                height: 1.46vw;
                margin-left: 0.73vw;
              }
              .text-group_24 {
                width: 2.92vw;
                height: 0.73vw;
                overflow-wrap: break-word;
                color: rgba(150, 154, 170, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.73vw;
                margin-top: 0.27vw;
              }
            }
          }
          .box_18 {
            background-color: rgba(255, 255, 255, 1);
            width: 3.65vw;
            height: 3.65vw;
            border: 1px solid rgba(230, 235, 245, 1);
            .image-text_25 {
              width: 2.92vw;
              height: 2.45vw;
              margin: 0.62vw 0 0 0.36vw;
              .label_15 {
                width: 1.57vw;
                height: 1.46vw;
                margin-left: 0.68vw;
              }
              .text-group_25 {
                width: 2.92vw;
                height: 0.73vw;
                overflow-wrap: break-word;
                color: rgba(150, 154, 170, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.73vw;
                margin-top: 0.27vw;
              }
            }
          }
          .box_19 {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 0px 0px 8px 8px;
            width: 3.65vw;
            height: 3.65vw;
            border: 1px solid rgba(230, 235, 245, 1);
            .image-text_26 {
              width: 2.92vw;
              height: 2.45vw;
              margin: 0.62vw 0 0 0.36vw;
              .label_16 {
                width: 1.46vw;
                height: 1.46vw;
                margin-left: 0.73vw;
              }
              .text-group_26 {
                width: 2.92vw;
                height: 0.73vw;
                overflow-wrap: break-word;
                color: rgba(150, 154, 170, 1);
                font-size: 0.72vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.73vw;
                margin-top: 0.27vw;
              }
            }
          }
        }
      }
    }
  }
}
</style>
