<template>
  <div class="page_details_course flex-col">
    <div class="section_2 flex-col">
      <!-- <div class="section_3 flex-row justify-between">
        <span class="text_7">课程管理&nbsp;&gt;&nbsp;班级列表</span>
        <div class="text-wrapper_1 flex-col">
          <span class="text_8">返回</span>
        </div>
      </div> -->
      <div class="section_3 flex-row justify-between">
        <el-breadcrumb class="text_7" separator="/">
          <el-breadcrumb-item :to="{ path: '/' }">课程管理</el-breadcrumb-item>
          <el-breadcrumb-item>
            <a href="/">班级列表</a>
          </el-breadcrumb-item>
        </el-breadcrumb>
        <div class="text-wrapper_1 flex-col">
          <!-- <span class="text_8">返回</span> -->
          <el-button type="primary" plain @click="goBack">返回</el-button>
        </div>
      </div>
      <div class="section_4 flex-row justify-between">
        <div class="group_10 flex-col">
          <div class="box_3 flex-row justify-between">
            <span class="text_11">{{ courseParam.courseName }}</span>
            <div class="text-wrapper_4_1 flex-col" v-if="courseParam.status == 1">
              <span class="text_12_1">未开始</span>
            </div>
            <div class="text-wrapper_4 flex-col" v-if="courseParam.status == 2">
              <span class="text_12">进行中</span>
            </div>
            <div class="text-wrapper_4_2 flex-col" v-if="courseParam.status == 3">
              <span class="text_12_2">已归档</span>
            </div>
          </div>
          <div class="box_4 flex-row justify-between">
            <span class="text_13">{{ courseParam.description }}</span>
            <div class="box_4_bg justify-between">
              <img :src="stuBgKctp" alt="">
              <img :src="stuBgWttp" alt="">
              <img :src="stuBgNltp" alt="">
            </div>
            <div class="text-wrapper_5 flex-col">
              <!-- <span class="text_14">成绩考核权重</span> -->
              <el-button type="primary" @click="popupScoredit">成绩<br />权重</el-button>
            </div>
          </div>
          <div class="text-wrapper_6 flex-row justify-between">
            <span class="text_15">授课教师：{{ courseParam.realName || '未设置' }}</span>
            <span class="text_16">课程代码：{{ courseParam.courseCode }}</span>
          </div>
          <div class="box_5 flex-row">
            <div class="image-text_1 flex-row justify-between">
              <img
                class="image-wrapper_3 flex-col"
                :src="courseParam.coverImageUrl"
              />
              <div class="text-group_1 flex-col justify-between">
                <span class="text_17">{{ courseParam.courseName }}</span>
                <span class="text_18">课程类型：{{ courseParam.courseType == 1 ? '教务课程' : '自主课程' }}</span>
                <span class="text_19">课时：{{ courseParam.hour }}小时</span>
              </div>
            </div>
            <!-- <img
              class="image_4"
              referrerpolicy="no-referrer"
              src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGda477767c59717f1c8176ab5e6286510.png"
            /> -->
            <el-progress class="image_4" :percentage="courseParam.ydjd" />
            <el-icon class="thumbnail_1">
              <ArrowRightBold />
            </el-icon>
            <!-- <img
              class="thumbnail_1"
              referrerpolicy="no-referrer"
              src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG595db758a3fc651619acbfd9520c06cb.png"
            /> -->
          </div>
        </div>
      </div>
    </div>
    <div class="section_5 flex-col">
      <div class="group_16 flex-col">
        <div class="group_17 flex-row">
          <!-- <div class="text-wrapper_9 flex-col">
                <span class="text_25">全部课程</span>
              </div> -->
          <div
            class="text-wrapper_10 flex-col"
            v-for="(item, index) in topTabContent"
            :key="index"
            @click="changeTopTabContent(index)"
            :class="{ topTabContengActiveClass: index === topTabContentActive }"
          >
            <span class="text_26">{{ item.label }}</span>
          </div>
          <!-- <div class="text-wrapper_11 flex-col">
                <span class="text_27">进行中</span>
              </div>
              <div class="text-wrapper_12 flex-col">
                <span class="text_28">已归档</span>
              </div> -->
          <div class="text-wrapper_13 flex-col">
            <!-- <span class="text_29">新增课程</span> -->
            <el-button class="text_29" type="primary" @click="handleAddClass">新增班级</el-button>
          </div>
        </div>
        <div class="group_18">
          <div class="block_6 flex-col" v-for="(item, index) in tableData" :key="index" @click="goClassTab(item)">
            <div class="image-text_6 flex-col justify-between">
              <div class="group_19 flex-row"
                   :style="item.coverImageUrl ?
                     `background: url(${item.coverImageUrl}) center/cover !important;` :
                     'background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;'
                   "
              >
                <div class="text-wrapper_14 flex-col" style="width: 3vw">
                  <span class="text_30" v-if="item.classType == '1' || item.kclx == '1'">独立班</span>
                  <span class="text_30" v-if="item.classType == '2' || item.kclx == '2'">平行班</span>
                </div>


                <!-- <img
                      class="label_5"
                      referrerpolicy="no-referrer"
                      src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG15da9e34d2d3859ca3ce71b19f612959.png"
                    /> -->
                <!-- <el-button class="label_5" :icon="DeleteFilled" circle /> -->
                <div class="group_19_bottom">
                  <el-dropdown placement="bottom">
                    <el-icon color="#fff">
                      <MoreFilled />
                    </el-icon>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="viewClass(item.classId || item.id)">查看</el-dropdown-item>
                        <el-dropdown-item @click="copyClass(item.classId || item.id)" v-if="item.status != 3">复制</el-dropdown-item>
                        <el-dropdown-item @click="deleteClass(item.classId || item.id)" v-if="item.status != 2">删除</el-dropdown-item>
                        <el-dropdown-item @click="archiveClass(item.classId || item.id)" v-if="item.status == 2">归档</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
              <div class="group_20 flex-row justify-between">
                <span class="text-group_6">{{ item.className || item.name || item.bt }}</span>
                <div class="text-wrapper_24 flex-col" v-if="item.status == 1">
                  <span class="text_43">未开始</span>
                </div>
                <div class="text-wrapper_15 flex-col" v-if="item.status == 2">
                  <span class="text_31">进行中</span>
                </div>
                <div class="text-wrapper_33 flex-col" v-if="item.status == 3">
                  <span class="text_55">已归档</span>
                </div>
              </div>
            </div>
            <div class="text-wrapper_16 flex-row justify-between">
              <span class="text_32">关联教学班：{{ item.classCount || item.bjsl || 0 }}</span>
            </div>
            <div class="block_7 flex-row">
              <div class="image-text_7 flex-row justify-between">
                <img
                  class="thumbnail_6"
                  referrerpolicy="no-referrer"
                  src="../../../assets/images/smartCourse/smartCourseDetails/star.png"
                />
                <span class="text-group_7">授课老师：{{ item.teacherName || item.realName || item.skjs || '未设置' }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="pagination-container">
          <el-pagination
            background="true"
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[100, 200, 300, 400]"
            :size="size"
            layout="total, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
  <PopupClassAdd ref="popupClassAdd" @success="getClassList" />
  <PopupScore ref="popupScore" />
</template>
<script setup>
import { ref, reactive, getCurrentInstance, onMounted, watch } from 'vue'
import PopupClassAdd from './popup_class_add.vue'
import PopupScore from './popup_score.vue'
import { Search, DeleteFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import stuBgKctp from '@/assets/images/smartCourse/smartCourseTabStu/stu_bg_kctp.png'
import stuBgWttp from '@/assets/images/smartCourse/smartCourseTabStu/stu_bg_wttp.png'
import stuBgNltp from '@/assets/images/smartCourse/smartCourseTabStu/stu_bg_nltp.png'
import { getCourse } from '@/api/edu/smartCourse'
import { listClass, getClass, delClass, addClass, updateClass } from '@/api/edu/smartCourseClass'
const { proxy } = getCurrentInstance()
const emits = defineEmits(['goBack', 'goClassTab'])

const props = defineProps({
  courseId: {
    type: String,
    default: ''
  }
})

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const size = ref('default')
const topTabContentActive = ref(0)
const topTabContent = ref([
  { label: '全部课程', value: '' },
  { label: '未开始', value: '1' },
  { label: '进行中', value: '2' },
  { label: '已归档', value: '3' }
])
const tableData = ref([])
const courseParam = ref({})

watch(
  () => props.courseId,
  newVal => {
    if (newVal) {
      getCourseDetails()
      getClassList()
    }
  }
)

onMounted(() => {
  if (props.courseId) {
    getCourseDetails()
    getClassList()
  }
})

// 获取课程详情
const getCourseDetails = () => {
  getCourse(props.courseId).then(res => {
    if (res.code === 200) {
      courseParam.value = res.data
    } else {
      ElMessage.error(res.msg || '获取课程详情失败')
    }
  }).catch(error => {
    console.error('获取课程详情失败:', error)
    ElMessage.error('获取课程详情失败')
  })
}

// 获取班级列表
const getClassList = () => {
  const params = {
    courseId: props.courseId,
    status: topTabContent.value[topTabContentActive.value].value,
    pageNum: currentPage.value,
    pageSize: pageSize.value
  }
  listClass(params).then(res => {
    if (res.code === 200) {
      tableData.value = res.rows || []
      total.value = res.total || 0
    } else {
      ElMessage.error(res.msg || '获取班级列表失败')
      tableData.value = []
      total.value = 0
    }
  }).catch(error => {
    console.error('获取班级列表失败:', error)
    ElMessage.error('获取班级列表失败')
    tableData.value = []
    total.value = 0
  })
}

const changeTopTabContent = i => {
  topTabContentActive.value = i
  currentPage.value = 1 // 切换状态时重置页码
  getClassList()
}

// 分页大小改变
const handleSizeChange = (val) => {
  pageSize.value = val
  getClassList()
}

// 当前页改变
const handleCurrentChange = (val) => {
  currentPage.value = val
  getClassList()
}
// 新增班级
const popupClassAdd = ref(null)
const handleAddClass = () => {
  popupClassAdd.value.open({ courseId: props.courseId })
}
// 查看班级
const viewClass = id => {
  popupClassAdd.value.open({ id })
}
// 前往班级
const goClassTab = item => {
  const params = { id: item.id, flag: false }
  console.log(item)
  // emits('goClassTab', params)
  emits('goClassTab', item)
}
// 成绩考核权重
const popupScore = ref(null)

const popupScoredit = () => {
  const weightData = {
    classroomQuestionWeight: courseParam.value.classroomQuestionWeight,
    homeworkWeight: courseParam.value.homeworkWeight,
    examWeight: courseParam.value.examWeight,
    testWeight: courseParam.value.testWeight,
    bookStudyWeight: courseParam.value.bookStudyWeight,
    performanceWeight: courseParam.value.performanceWeight
  }
  popupScore.value.open(props.courseId, weightData)
}
// 复制班级
const copyClass = id => {
  ElMessageBox.confirm('复制班级时，其下的课堂、学生、学习小组及教学活动将一并复制。', '提示', {
    confirmButtonText: '确定复制',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    getClass(id).then(res => {
      const classData = res.data
      classData.id = null
      addClass(classData).then(() => {
        ElMessage({ type: 'success', message: '复制成功' })
        getClassList()
      })
    })
  })
}
// 删除班级
const deleteClass = id => {
  ElMessageBox.confirm('删除班级后班级内的所有信息也将全部删除且无法找回，是否确认删除班级', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      delClass(id).then(() => {
        ElMessage({ type: 'success', message: '删除成功' })
        getClassList()
      })
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '取消操作' })
    })
}
// 归档
const archiveClass = id => {
  ElMessageBox.confirm('结束班级课堂活动也同步结束，且活动结束后无法再次开启', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      updateClass({ id, status: '3' }).then(() => {
        ElMessage({ type: 'success', message: '归档成功' })
        getClassList()
      })
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '取消操作' })
    })
}
// 返回
const goBack = () => {
  emits('goBack')
}
</script>

<style lang="scss" scoped>
.page_details_course {
  background-color: rgba(242, 245, 250, 1);
  position: relative;
  width: 58.08vw;
  // height: 58.08vw;
  overflow: hidden;

  .section_1 {
    box-shadow: 0px 4px 10px 0px rgba(134, 154, 189, 0.15);
    background-color: rgba(255, 255, 255, 1);
    height: 3.96vw;
    width: 100vw;

    .box_1 {
      position: relative;
      width: 58.08vw;
      height: 3.96vw;

      .label_1 {
        width: 1.46vw;
        height: 1.2vw;
        margin: 0.78vw 0 0 13.9vw;
      }

      .group_1 {
        width: 1.46vw;
        height: 1.83vw;
        margin: 0.78vw 0 0 0.05vw;

        .label_2 {
          width: 1.46vw;
          height: 1.2vw;
        }

        .group_2 {
          background-color: rgba(13, 94, 159, 1);
          width: 0.27vw;
          height: 0.11vw;
          margin: 0.52vw 0 0 0.78vw;
        }
      }

      .group_3 {
        background-color: rgba(13, 94, 159, 1);
        width: 0.37vw;
        height: 0.16vw;
        margin-top: 2.56vw;
      }

      .image-wrapper_1 {
        width: 4.74vw;
        height: 1.62vw;
        margin: 1.35vw 0 0 0.72vw;

        .image_1 {
          width: 4.74vw;
          height: 0.89vw;
        }

        .image_2 {
          width: 3.6vw;
          height: 0.58vw;
          margin: 0.15vw 0 0 0.05vw;
        }
      }

      .text_1 {
        width: 2.82vw;
        height: 0.94vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.93vw;
        font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.94vw;
        margin: 1.51vw 0 0 10.1vw;
      }

      .group_4 {
        width: 3.7vw;
        height: 1.46vw;
        margin: 1.51vw 0 0 3.07vw;

        .text_2 {
          width: 3.7vw;
          height: 0.94vw;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 0.93vw;
          font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.94vw;
        }

        .group_5 {
          background-color: rgba(51, 51, 51, 1);
          border-radius: 1px;
          width: 1.36vw;
          height: 0.11vw;
          margin: 0.41vw 0 0 1.14vw;
        }
      }

      .text_3 {
        width: 3.7vw;
        height: 0.94vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.93vw;
        font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.94vw;
        margin: 1.51vw 0 0 3.22vw;
      }

      .text_4 {
        width: 3.7vw;
        height: 0.94vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.93vw;
        font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.94vw;
        margin: 1.51vw 0 0 3.22vw;
      }

      .text_5 {
        width: 3.7vw;
        height: 0.94vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.93vw;
        font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.94vw;
        margin: 1.51vw 0 0 3.22vw;
      }

      .label_3 {
        width: 2.09vw;
        height: 2.09vw;
        margin: 0.98vw 0 0 17.96vw;
      }

      .text_6 {
        width: 2.82vw;
        height: 0.94vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.93vw;
        font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.94vw;
        margin: 1.56vw 13.54vw 0 0.41vw;
      }

      .group_6 {
        background-color: rgba(13, 94, 159, 1);
        position: absolute;
        left: 13.55vw;
        top: 2.56vw;
        width: 3.44vw;
        height: 0.68vw;
      }

      .group_7 {
        background-color: rgba(19, 169, 129, 1);
        position: absolute;
        left: 13.6vw;
        top: 1.83vw;
        width: 3.6vw;
        height: 0.53vw;
      }
    }
  }

  .section_2 {
    width: 58.08vw;
    height: 54.17vw;
    margin-bottom: 0.06vw;

    .section_3 {
      width: 58.08vw;
      height: 2.09vw;

      // margin: 0 0 0 0.89vw;
      .text_7 {
        // width: 7.5vw;
        height: 0.84vw;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 0.83vw;
        font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.84vw;
        margin-top: 0.47vw;
        margin-left: 0.89vw;
      }

      .text-wrapper_1 {

        // border-radius: 4px;
        // height: 2.09vw;
        // border: 1px solid rgba(34, 107, 254, 1);
        // width: 5.73vw;
        .text_8 {
          width: 1.67vw;
          height: 0.84vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.83vw;
          font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.84vw;
          margin: 0.62vw 0 0 2.03vw;
        }
      }
    }

    .section_4 {
      width: 58.08vw;
      height: 13.49vw;
      margin: 0.5vw 0 0 0.89vw;

      .group_8 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        height: 13.49vw;
        width: 13.96vw;
        position: relative;

        .group_9 {
          width: 1.52vw;
          height: 0.99vw;
          margin: 6.56vw 0 0 10.88vw;

          .box_2 {
            background-color: rgba(255, 255, 255, 1);
            width: 1.52vw;
            height: 0.99vw;
          }
        }

        .text-wrapper_2 {
          width: 6.52vw;
          height: 0.94vw;
          margin: 2.08vw 0 0 3.69vw;

          .text_9 {
            width: 6.52vw;
            height: 0.94vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.93vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.94vw;
          }
        }

        .text-wrapper_3 {
          width: 5.58vw;
          height: 0.94vw;
          margin: 0.62vw 0 1.35vw 3.69vw;

          .text_10 {
            width: 5.58vw;
            height: 0.94vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 0.93vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.94vw;
          }
        }

        .image-wrapper_2 {
          background-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 0, rgba(233, 171, 35, 0.34) 100%);
          border-radius: 8px 8px 0px 0px;
          height: 8.03vw;
          width: 13.96vw;
          position: absolute;
          left: 0;
          top: 0.68vw;

          .image_3 {
            width: 7.09vw;
            height: 7.09vw;
            margin: 0.36vw 0 0 3.43vw;
          }
        }
      }

      .group_10 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        height: 13.49vw;
        width: 58.08vw;
        justify-content: flex-center;

        .box_3 {
          width: 26.2vw;
          height: 1.36vw;
          margin: 1.04vw 0 0 1.35vw;

          .text_11 {
            width: 20.73vw;
            height: 1.15vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 1.14vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 1.15vw;
            margin-top: 0.11vw;
          }

          .text-wrapper_4 {
            border-radius: 20px;
            height: 1.36vw;
            border: 1px solid rgba(107, 198, 148, 1);
            width: 4.38vw;

            .text_12 {
              width: 2.19vw;
              height: 0.73vw;
              overflow-wrap: break-word;
              color: rgba(107, 198, 148, 1);
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.73vw;
              margin: 0.31vw 0 0 1.09vw;
            }
          }

          .text-wrapper_4_1 {
            border-radius: 20px;
            height: 1.36vw;
            border: 1px solid rgba(238, 161, 49, 1);
            width: 4.38vw;

            .text_12_1 {
              width: 2.19vw;
              height: 0.73vw;
              overflow-wrap: break-word;
              color: rgba(238, 161, 49, 1);
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.73vw;
              margin: 0.31vw 0 0 1.09vw;
            }
          }

          .text-wrapper_4_2 {
            border-radius: 20px;
            height: 1.36vw;
            border: 1px solid rgba(150, 154, 170, 1);
            width: 4.38vw;

            .text_12_2 {
              width: 2.19vw;
              height: 0.73vw;
              overflow-wrap: break-word;
              color: rgba(150, 154, 170, 1);
              font-size: 0.72vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.73vw;
              margin: 0.31vw 0 0 1.09vw;
            }
          }
        }

        .box_4 {
          width: 55.47vw;
          height: 2.5vw;
          margin: 0.52vw 0 0 1.35vw;

          .text_13 {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 27vw;
            height: 2.5vw;
            overflow-wrap: break-word;
            color: rgba(150, 154, 170, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            line-height: 1.25vw;
          }

          .box_4_bg {
            width: 24vw;
            height: 6vw;
            margin-top: -2vw;
            background-image: url('../../../assets/images/smartCourse/smartCourseTabStu/tea_bg.png');
            // background-size: contain;
            background-size: 100% 100%;
            /* 保持图片完整显示 */
            background-position: right top;
            /* 顶部居中对齐 */
            background-repeat: no-repeat;
            padding: 0 10px;
            img {
              width: 7vw;
              object-fit: contain;
              cursor: pointer;
            }
          }

          .text-wrapper_5 {
            border-radius: 20px;
            height: 2.09vw;
            // margin-top: 0.32vw;
            margin-right: 1vw;
            width: 3vw;

            .text_14 {
              width: 4.95vw;
              height: 0.84vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.83vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.84vw;
              margin: 0.62vw 0 0 1.25vw;
            }

          }
        }

        .text-wrapper_6 {
          width: 18.39vw;
          height: 1.25vw;
          margin: 0.52vw 0 0 1.35vw;

          .text_15 {
            width: 9.28vw;
            height: 1.25vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 1.25vw;
          }

          .text_16 {
            width: 7.3vw;
            height: 1.25vw;
            overflow-wrap: break-word;
            color: rgba(88, 94, 118, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 1.25vw;
          }
        }

        .box_5 {
          width: 34.74vw;
          height: 3.81vw;
          margin: 1.45vw 0 1.04vw 1.3vw;

          .image-text_1 {
            width: 29.33vw;
            height: 3.81vw;

            .image-wrapper_3 {
              height: 3.81vw;
              background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG87f2df0c45a327787b538d5a3e30dd13.png) 100% no-repeat;
              background-size: 100% 100%;
              width: 3.81vw;

              .label_4 {
                width: 2.19vw;
                height: 2.19vw;
                margin: 0.83vw 0 0 0.88vw;
              }
            }

            .text-group_1 {
              width: 24.95vw;
              height: 3.81vw;

              .text_17 {
                width: 24.95vw;
                height: 0.94vw;
                overflow-wrap: break-word;
                color: rgba(51, 51, 51, 1);
                font-size: 0.93vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.94vw;
              }

              .text_18 {
                width: 8.23vw;
                height: 1.25vw;
                overflow-wrap: break-word;
                color: rgba(150, 154, 170, 1);
                font-size: 0.83vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 1.25vw;
                margin-top: 0.42vw;
              }

              .text_19 {
                width: 4.59vw;
                height: 0.79vw;
                overflow-wrap: break-word;
                color: rgba(88, 94, 118, 1);
                font-size: 0.78vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.79vw;
                margin-top: 0.42vw;
              }
            }
          }

          .image_4 {
            width: 19.54vw;
            height: 0.16vw;
            margin: 3.33vw 0 0 -19.5vw;
          }

          .thumbnail_1 {
            // width: 0.58vw;
            // height: 0.94vw;
            font-size: 1vw;
            color: #999;
            margin: 1.35vw 0 0 5.26vw;
          }
        }
      }
    }
  }

  .section_5 {
    .group_16 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 8px;
      width: 58.08vw;
      height: 47.3vw;
      margin-top: 0.94vw;
      margin-left: 0.89vw;

      .group_17 {
        width: 55.47vw;
        height: 2.09vw;
        margin: 0.88vw 0 0 1.35vw;

        .text-wrapper_9 {
          background-color: rgba(85, 91, 116, 1);
          border-radius: 4px;
          height: 1.88vw;
          margin-top: 0.21vw;
          width: 5.84vw;

          .text_25 {
            width: 3.29vw;
            height: 0.84vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.84vw;
            margin: 0.52vw 0 0 1.3vw;
          }
        }

        .text-wrapper_10 {
          border-radius: 4px;
          height: 1.88vw;
          border: 1px solid rgba(126, 136, 165, 1);
          width: 5.84vw;
          margin: 0.2vw 0 0 0.83vw;
          cursor: pointer;
          text-align: center;
          line-height: 1.88vw;

          .text_26 {
            overflow-wrap: break-word;
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            white-space: nowrap;
          }
        }

        .text-wrapper_11 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 4px;
          height: 1.88vw;
          border: 1px solid rgba(126, 136, 165, 1);
          width: 5.84vw;
          margin: 0.2vw 0 0 0.83vw;

          .text_27 {
            width: 2.5vw;
            height: 0.84vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.84vw;
            margin: 0.52vw 0 0 1.66vw;
          }
        }

        .text-wrapper_12 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 4px;
          height: 1.88vw;
          border: 1px solid rgba(126, 136, 165, 1);
          width: 5.84vw;
          margin: 0.2vw 0 0 0.83vw;

          .text_28 {
            width: 2.5vw;
            height: 0.84vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.83vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.84vw;
            margin: 0.52vw 0 0 1.66vw;
          }
        }

        .text-wrapper_13 {
          border-radius: 20px;
          height: 2.09vw;
          margin-left: 23.86vw;
          width: 5.79vw;
          font-size: 20px;

          .text_29 {
            color: rgba(255, 255, 255, 1);
            font-size: 0.83vw;
          }
        }
      }

      .group_18 {
        width: 51.46vw;
        // height: 11.67vw;
        margin: 1.19vw 0 0 3.07vw;
        display: flex;
        flex-wrap: wrap;
        gap: 1vw;

        .block_6 {
          cursor: pointer;
          background-color: rgba(255, 255, 255, 1);
          border-radius: 14px;
          width: 16.46vw;
          height: 11.67vw;
          border: 2px solid rgba(230, 235, 245, 1);

          .image-text_6 {
            width: 15.63vw;
            margin: 0.41vw 0 0 0.41vw;

            .group_19 {
              width: 15.45vw;
              height: 6.8vw;
              background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6a4f63b34982242b7e0d4462f96ab730.png) center no-repeat;
              background-size: cover;
              .text-wrapper_14 {
                border-radius: 8px;
                height: 1.36vw;
                width: 3.75vw;
                margin: 1.45vw 0 0 0.52vw;
                background: #ffbf9b;

                .text_30 {
                  width: 2.92vw;
                  height: 0.84vw;
                  overflow-wrap: break-word;
                  color: rgba(80, 44, 19, 1);
                  font-size: 0.72vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.84vw;
                  margin: 0.26vw 0 0 0.41vw;
                }
              }

              .label_5 {
                width: 1.57vw;
                height: 1.57vw;
                margin: 1.35vw 0.67vw 0 9.11vw;
              }

              .group_19_bottom {
                cursor: pointer;
                margin-left: 9.5vw;
                margin-top: 0.5vw;
              }
            }

            .group_20 {
              width: 14.07vw;
              height: 1.36vw;
              margin: 0.52vw 0 0 0.78vw;

              .text-group_6 {
                width: 8.29vw;
                height: 0.94vw;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.93vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.94vw;
                margin-top: 0.21vw;
              }

              .text-wrapper_15 {
                border-radius: 20px;
                height: 1.36vw;
                border: 1px solid rgba(82, 196, 26, 1);
                width: 4.38vw;

                .text_31 {
                  width: 2.19vw;
                  height: 0.73vw;
                  overflow-wrap: break-word;
                  color: rgba(82, 196, 26, 1);
                  font-size: 0.72vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.73vw;
                  margin: 0.31vw 0 0 1.09vw;
                }
              }

              .text-wrapper_24 {
                border-radius: 20px;
                height: 1.36vw;
                border: 1px solid rgba(238, 161, 49, 1);
                width: 4.38vw;

                .text_43 {
                  width: 2.19vw;
                  height: 0.73vw;
                  overflow-wrap: break-word;
                  color: rgba(238, 161, 49, 1);
                  font-size: 0.72vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.73vw;
                  margin: 0.31vw 0 0 1.09vw;
                }
              }

              .text-wrapper_33 {
                border-radius: 20px;
                height: 1.36vw;
                border: 1px solid rgba(150, 154, 170, 1);
                width: 4.38vw;

                .text_55 {
                  width: 2.19vw;
                  height: 0.73vw;
                  overflow-wrap: break-word;
                  color: rgba(150, 154, 170, 1);
                  font-size: 0.72vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.73vw;
                  margin: 0.31vw 0 0 1.09vw;
                }
              }
            }
          }

          .text-wrapper_16 {
            width: 10.06vw;
            height: 0.84vw;
            margin: 0.83vw 0 0 1.19vw;

            .text_32 {
              width: 3.75vw;
              height: 0.84vw;
              overflow-wrap: break-word;
              color: rgba(150, 154, 170, 1);
              font-size: 0.83vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.84vw;
            }

            .text_33 {
              width: 4.95vw;
              height: 0.84vw;
              overflow-wrap: break-word;
              color: rgba(34, 107, 255, 1);
              font-size: 0.83vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.84vw;
            }
          }

          .block_7 {
            background-color: rgba(242, 245, 250, 1);
            border-radius: 8px;
            width: 11.46vw;
            height: 1.67vw;
            margin: 0.83vw 0 0.98vw 1.19vw;

            .image-text_7 {
              width: 10.42vw;
              height: 0.84vw;
              margin: 0.41vw 0 0 0.52vw;

              .thumbnail_6 {
                width: 0.84vw;
                height: 0.84vw;
                margin-top: -0.05vw;
              }

              .text-group_7 {
                width: 9.28vw;
                height: 0.84vw;
                overflow-wrap: break-word;
                color: rgba(88, 94, 118, 1);
                font-size: 0.83vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.84vw;
              }
            }
          }
        }

        .block_8 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 14px;
          width: 16.46vw;
          height: 11.67vw;
          border: 2px solid rgba(230, 235, 245, 1);
          margin-left: 1.05vw;

          .image-text_8 {
            width: 15.63vw;
            height: 6.1vw;
            margin: 0.41vw 0 0 0.41vw;

            .block_9 {
              width: 15.63vw;
              height: 4.22vw;
              background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6a4f63b34982242b7e0d4462f96ab730.png) center no-repeat;
              background-size: cover;
              .text-wrapper_17 {
                border-radius: 8px;
                height: 1.36vw;
                width: 3.75vw;
                margin: 1.45vw 0 0 0.52vw;

                .text_34 {
                  width: 2.92vw;
                  height: 0.84vw;
                  overflow-wrap: break-word;
                  color: rgba(80, 44, 19, 1);
                  font-size: 0.72vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.84vw;
                  margin: 0.26vw 0 0 0.41vw;
                }
              }

              .label_6 {
                width: 1.57vw;
                height: 1.57vw;
                margin: 1.35vw 0.62vw 0 9.16vw;
              }
            }

            .block_10 {
              width: 13.75vw;
              height: 1.36vw;
              margin: 0.52vw 0 0 0.78vw;

              .text-group_8 {
                width: 7.97vw;
                height: 0.94vw;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.93vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.94vw;
                margin-top: 0.21vw;
              }

              .text-wrapper_18 {
                border-radius: 20px;
                height: 1.36vw;
                border: 1px solid rgba(82, 196, 26, 1);
                width: 4.38vw;

                .text_35 {
                  width: 2.19vw;
                  height: 0.73vw;
                  overflow-wrap: break-word;
                  color: rgba(82, 196, 26, 1);
                  font-size: 0.72vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.73vw;
                  margin: 0.31vw 0 0 1.09vw;
                }
              }
            }
          }

          .text-wrapper_19 {
            width: 10.06vw;
            height: 0.84vw;
            margin: 0.83vw 0 0 1.19vw;

            .text_36 {
              width: 3.75vw;
              height: 0.84vw;
              overflow-wrap: break-word;
              color: rgba(150, 154, 170, 1);
              font-size: 0.83vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.84vw;
            }

            .text_37 {
              width: 4.95vw;
              height: 0.84vw;
              overflow-wrap: break-word;
              color: rgba(34, 107, 255, 1);
              font-size: 0.83vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.84vw;
            }
          }

          .block_11 {
            background-color: rgba(242, 245, 250, 1);
            border-radius: 8px;
            width: 11.46vw;
            height: 1.67vw;
            margin: 0.83vw 0 0.98vw 1.19vw;

            .image-text_9 {
              width: 10.42vw;
              height: 0.84vw;
              margin: 0.41vw 0 0 0.52vw;

              .thumbnail_7 {
                width: 0.84vw;
                height: 0.84vw;
              }

              .text-group_9 {
                width: 9.28vw;
                height: 0.84vw;
                overflow-wrap: break-word;
                color: rgba(88, 94, 118, 1);
                font-size: 0.83vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.84vw;
              }
            }
          }
        }

        .block_12 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 14px;
          width: 16.46vw;
          height: 11.67vw;
          border: 2px solid rgba(230, 235, 245, 1);
          margin-left: 1.05vw;

          .image-text_10 {
            width: 15.73vw;
            height: 6.1vw;
            margin: 0.41vw 0 0 0.41vw;

            .block_13 {
              width: 15.63vw;
              height: 4.22vw;
              background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6a4f63b34982242b7e0d4462f96ab730.png) center no-repeat;
              background-size: cover;
              .text-wrapper_20 {
                border-radius: 8px;
                height: 1.36vw;
                width: 3.75vw;
                margin: 1.45vw 0 0 0.52vw;

                .text_38 {
                  width: 2.92vw;
                  height: 0.84vw;
                  overflow-wrap: break-word;
                  color: rgba(80, 44, 19, 1);
                  font-size: 0.72vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.84vw;
                  margin: 0.26vw 0 0 0.41vw;
                }
              }

              .label_7 {
                width: 1.57vw;
                height: 1.57vw;
                margin: 1.35vw 0.67vw 0 9.11vw;
              }
            }

            .block_14 {
              width: 14.95vw;
              height: 1.36vw;
              margin: 0.52vw 0 0 0.78vw;

              .text-group_10 {
                width: 9.17vw;
                height: 0.94vw;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.93vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.94vw;
                margin-top: 0.21vw;
              }

              .text-wrapper_21 {
                border-radius: 20px;
                height: 1.36vw;
                border: 1px solid rgba(82, 196, 26, 1);
                width: 4.38vw;

                .text_39 {
                  width: 2.19vw;
                  height: 0.73vw;
                  overflow-wrap: break-word;
                  color: rgba(82, 196, 26, 1);
                  font-size: 0.72vw;
                  font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                  font-weight: normal;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.73vw;
                  margin: 0.31vw 0 0 1.09vw;
                }
              }
            }
          }

          .text-wrapper_22 {
            width: 10.06vw;
            height: 0.84vw;
            margin: 0.83vw 0 0 1.19vw;

            .text_40 {
              width: 3.75vw;
              height: 0.84vw;
              overflow-wrap: break-word;
              color: rgba(150, 154, 170, 1);
              font-size: 0.83vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.84vw;
            }

            .text_41 {
              width: 4.95vw;
              height: 0.84vw;
              overflow-wrap: break-word;
              color: rgba(34, 107, 255, 1);
              font-size: 0.83vw;
              font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.84vw;
            }
          }

          .box_9 {
            background-color: rgba(242, 245, 250, 1);
            border-radius: 8px;
            width: 11.46vw;
            height: 1.67vw;
            margin: 0.83vw 0 0.98vw 1.19vw;

            .image-text_11 {
              width: 10.42vw;
              height: 0.84vw;
              margin: 0.41vw 0 0 0.52vw;

              .thumbnail_8 {
                width: 0.84vw;
                height: 0.84vw;
              }

              .text-group_11 {
                width: 9.28vw;
                height: 0.84vw;
                overflow-wrap: break-word;
                color: rgba(88, 94, 118, 1);
                font-size: 0.83vw;
                font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.84vw;
              }
            }
          }
        }
      }

      .group_32 {
        width: 31.93vw;
        height: 1.67vw;
        margin: 2.23vw 0 2.13vw 11.45vw;

        .image-wrapper_3 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 2px;
          height: 1.67vw;
          border: 1px solid rgba(217, 217, 217, 1);
          width: 1.67vw;

          .thumbnail_15 {
            width: 0.63vw;
            height: 0.63vw;
            margin: 0.52vw 0 0 0.52vw;
          }
        }

        .text-wrapper_41 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 2px;
          height: 1.67vw;
          border: 1px solid rgba(217, 217, 217, 1);
          margin-left: 0.42vw;
          width: 1.67vw;

          .text_66 {
            width: 0.94vw;
            height: 1.57vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.72vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: center;
            line-height: 1.15vw;
            margin: 0.05vw 0 0 0.36vw;
          }
        }

        .text_67 {
          width: 0.99vw;
          height: 1.67vw;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 0.25);
          font-size: 0.72vw;
          letter-spacing: 2px;
          font-family: Arial-Regular;
          font-weight: normal;
          text-align: center;
          line-height: 1.67vw;
          margin-left: 0.79vw;
        }

        .text-wrapper_42 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 2px;
          height: 1.67vw;
          border: 1px solid rgba(217, 217, 217, 1);
          margin-left: 0.73vw;
          width: 1.67vw;

          .text_68 {
            width: 0.94vw;
            height: 1.57vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.72vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: center;
            line-height: 1.15vw;
            margin: 0.05vw 0 0 0.36vw;
          }
        }

        .text-wrapper_43 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 2px;
          height: 1.67vw;
          border: 1px solid rgba(217, 217, 217, 1);
          margin-left: 0.42vw;
          width: 1.67vw;

          .text_69 {
            width: 0.94vw;
            height: 1.57vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.72vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: center;
            line-height: 1.15vw;
            margin: 0.05vw 0 0 0.36vw;
          }
        }

        .text-wrapper_44 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 2px;
          height: 1.67vw;
          border: 1px solid rgba(34, 107, 255, 1);
          margin-left: 0.42vw;
          width: 1.67vw;

          .text_70 {
            width: 0.94vw;
            height: 1.57vw;
            overflow-wrap: break-word;
            color: rgba(34, 107, 255, 1);
            font-size: 0.72vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: center;
            line-height: 1.15vw;
            margin: 0.05vw 0 0 0.36vw;
          }
        }

        .text-wrapper_45 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 2px;
          height: 1.67vw;
          border: 1px solid rgba(217, 217, 217, 1);
          margin-left: 0.42vw;
          width: 1.67vw;

          .text_71 {
            width: 0.94vw;
            height: 1.57vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.72vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: center;
            line-height: 1.15vw;
            margin: 0.05vw 0 0 0.36vw;
          }
        }

        .text-wrapper_46 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 2px;
          height: 1.67vw;
          border: 1px solid rgba(217, 217, 217, 1);
          margin-left: 0.42vw;
          width: 1.67vw;

          .text_72 {
            width: 0.94vw;
            height: 1.57vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.72vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: center;
            line-height: 1.15vw;
            margin: 0.05vw 0 0 0.36vw;
          }
        }

        .text_73 {
          width: 0.99vw;
          height: 1.67vw;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 0.25);
          font-size: 0.72vw;
          letter-spacing: 2px;
          font-family: Arial-Regular;
          font-weight: normal;
          text-align: center;
          line-height: 1.67vw;
          margin-left: 0.79vw;
        }

        .text-wrapper_47 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 2px;
          height: 1.67vw;
          border: 1px solid rgba(217, 217, 217, 1);
          margin-left: 0.73vw;
          width: 1.67vw;

          .text_74 {
            width: 0.94vw;
            height: 1.57vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.72vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: center;
            line-height: 1.15vw;
            margin: 0.05vw 0 0 0.36vw;
          }
        }

        .image-wrapper_4 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 2px;
          height: 1.67vw;
          border: 1px solid rgba(217, 217, 217, 1);
          margin-left: 0.42vw;
          width: 1.67vw;

          .thumbnail_16 {
            width: 0.63vw;
            height: 0.63vw;
            margin: 0.52vw 0 0 0.52vw;
          }
        }

        .text_75 {
          width: 0.73vw;
          height: 1.15vw;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 0.72vw;
          font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 1.15vw;
          margin: 0.26vw 0 0 1.56vw;
        }

        .text-wrapper_48 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 2px;
          height: 1.67vw;
          border: 1px solid rgba(217, 217, 217, 1);
          margin-left: 0.53vw;
          width: 2.14vw;

          .text_76 {
            width: 0.89vw;
            height: 1.15vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.72vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 1.15vw;
            margin: 0.26vw 0 0 0.62vw;
          }
        }

        .text_77 {
          width: 0.73vw;
          height: 1.15vw;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 0.72vw;
          font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 1.15vw;
          margin: 0.26vw 0 0 0.52vw;
        }

        .text-wrapper_49 {
          background-color: rgba(34, 107, 255, 1);
          border-radius: 2px;
          height: 1.67vw;
          margin-left: 0.53vw;
          width: 2.71vw;

          .text_78 {
            width: 1.46vw;
            height: 1.15vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 0.85);
            font-size: 0.72vw;
            font-family: Helvetica, 'Microsoft YaHei', Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 1.15vw;
            margin: 0.26vw 0 0 0.62vw;
          }
        }
      }
    }
  }
}

// 覆盖默认的背景图片，使用动态封面
.group_19 {
  // 清除固定背景，让内联样式生效
  background-image: none !important;
  position: relative;
}
</style>
