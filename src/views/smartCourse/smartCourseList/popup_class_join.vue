<template>
  <el-dialog v-model="dialogVisible" title="加入班级" width="500" :before-close="handleClose" style="margin-top: 30vh !important">
    <el-form ref="formRef" style="max-width: 600px" :model="form" label-width="auto" class="demo-ruleForm" :rules="rules">
      <el-form-item label="班级码" prop="classCode">
        <el-input 
          v-model="form.classCode" 
          type="text" 
          autocomplete="off" 
          placeholder="请输入6位班级码" 
          maxlength="10"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom" :loading="loading"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { joinClass } from '@/api/edu/moocSmartCourseClass'

const dialogVisible = ref(false)
const formRef = ref(null)
const loading = ref(false)

const form = ref({
  classCode: '' // 班级码
})

const rules = ref({
  classCode: [
    { required: true, message: '班级码不能为空', trigger: 'blur' },
  ]
})

// 定义事件
const emits = defineEmits(['success'])

// 开启弹窗
const open = () => {
  dialogVisible.value = true
  form.value.classCode = ''
  if (formRef.value) formRef.value.resetFields()
}

// 确认加入
const confirmFrom = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const response = await joinClass(form.value)
        if (response.code === 200) {
          ElMessage.success('加入班级成功')
          dialogVisible.value = false
          emits('success')
        } else {
          ElMessage.error(response.msg || '加入班级失败')
        }
      } catch (error) {
        console.error('加入班级失败:', error)
        ElMessage.error('加入班级失败')
      } finally {
        loading.value = false
      }
    } else {
      console.log('表单验证失败!')
    }
  })
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

defineExpose({
  open
})
</script>

<style lang="scss">
// .el-dialog.signin_popup_dialog :not(.is-fullscreen) {
//   margin-top: 30vh !important;
// }
</style>
