<template>
  <el-dialog v-model="dialogVisible" title="考试详情" width="850" :before-close="handleClose" style="margin-top: 5vh !important">
    <el-container class="popup_show_details">
      <el-main>
        <!-- <div class="show_details_top">
          <span>学号：{{ form.xh }}</span>
          <span>姓名：{{ form.xm }}</span>
          <span
            >分数：<i>{{ form.fs }}分</i></span
          >
        </div> -->
        <div class="show_details_mid">
          <div>学生写作内容</div>
          <el-input readonly v-model="form.xsxzrn" :rows="15" type="textarea" />
          <div>学生考试附件</div>
          <div>
            <img @click="downloadUrl(1)" :src="attaDownload" alt="下载附件" />
          </div>
        </div>
      </el-main>
      <el-aside class="show_details_aside">
        <div class="details_aside_title">考试内容</div>
        <div class="details_aside_cont">
          <div>考试名称：{{ form.zymc }}</div>
          <div>
            考试类型：
            <span v-if="form.zylx == '1'">题库考试</span>
            <span v-if="form.zylx == '2'">附件考试</span>
          </div>
          <div>
            评分方式：
            <span v-if="form.pffs == '1'">组内评分</span>
            <span v-if="form.pffs == '2'">组间评分</span>
            <span v-if="form.pffs == '3'">老师评分</span>
          </div>
          <div>考试分值：{{ form.zyfz }}</div>
          <div>提交时间：{{ form.tjsj }}</div>
          <div>考试要求：</div>
          <el-input readonly v-model="form.zyyq" :rows="4" type="textarea" />
          <div>
            <img @click="downloadUrl(2)" :src="attaDownload" alt="下载附件" />
            <img @click="downloadUrl(3)" :src="attaDownload" alt="下载附件" />
          </div>
          <div>参考答案：</div>
          <el-input readonly v-model="form.ckda" :rows="4" type="textarea" />
          <div>
            <img @click="downloadUrl(4)" :src="attaDownload" alt="下载附件" />
            <img @click="downloadUrl(5)" :src="attaDownload" alt="下载附件" />
          </div>
          <!-- <div>考试分数：</div>
          <el-input-number controls-position="right" placeholder="请输入考试分数" class="details_aside_input" v-model="form.zyfs" /> -->
          <!-- <el-button type="primary" size="small">确定</el-button> -->
        </div>
      </el-aside>
    </el-container>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import attaDownload from '@/assets/images/smartCourse/smartCourseDetails/atta_download.png'
const dialogVisible = ref(false)

const form = ref({
  xh: '10010',
  xm: '柳如烟',
  fs: 99,
  xsxzrn:
    '开学之前，他考虑到每天通车恐怕太过于辛苦，于是就在学校旁边租了间学生房，只在周末假日，才回家看看妈妈。他所租的是专门分租给学生的一层楼，在旧公寓六楼顶木板加盖的小违建，一共有六个房间，共享一套卫浴设备和一小间厨房，外头屋顶还留有一小片阳台可以晒衣服。阿宾搬进去的时候，还要五六天才开学，也不知道其它房间住的是什么人。',
  xszyfj: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1750656047737.pdf',
  zymc: '阿宾的一天',
  zylx: '2',
  pffs: '1',
  zyfz: 100,
  tjsj: '2025-01-01 08:11:07',
  zyyq: '要求标准规范，不少于一万字',
  zyyq1: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1750656047737.pdf',
  zyyq2: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1750656047737.pdf',
  ckda: '一万字的非常好',
  ckda1: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1750656047737.pdf',
  ckda2: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1750656047737.pdf',
  zyfs: 80
})
// 开启弹窗
const open = () => {
  dialogVisible.value = true
}
// 下载附件
const downloadUrl = n => {
  switch (true) {
    case n == 1:
      window.open(form.value.xszyfj)
      break
    case n == 2:
      window.open(form.value.zyyq1)
      break
    case n == 3:
      window.open(form.value.zyyq2)
      break
    case n == 4:
      window.open(form.value.ckda1)
      break
    case n == 5:
      window.open(form.value.ckda2)
      break
  }
}
// 关闭弹窗
const confirmFrom = () => {
  if (!form.value.zyfs && form.value.zyfs != 0) {
    return ElMessage({
      type: 'info',
      message: '请输入考试分数'
    })
  }
  dialogVisible.value = false
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.popup_show_details {
  overflow-y: auto;
  max-height: 500px;
}
.show_details_top {
  margin-bottom: 20px;
  font-size: 17px;
  color: rgba(0, 0, 0, 0.85);
  span {
    margin-right: 25px;
  }
  i {
    font-style: normal;
    color: #ff4d4f;
  }
}
.show_details_mid {
  div {
    font-size: 17px;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 10px;
  }
  img {
    width: 35px;
    cursor: pointer;
  }
}
.show_details_aside {
  width: 250px;
  .details_aside_title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 17px;
  }
  .details_aside_cont {
    font-size: 15px;
    color: rgba(0, 0, 0, 0.65);
    img {
      width: 35px;
      margin-right: 10px;
      margin-top: 10px;
      cursor: pointer;
    }
    .details_aside_input {
      width: 100%;
      // margin-right: 10px;
    }
  }
}
</style>
