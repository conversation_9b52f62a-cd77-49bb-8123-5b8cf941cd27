<template>
  <el-dialog v-model="dialogVisible" title="打分" width="300" :before-close="handleClose" style="margin-top: 30vh !important">
    <el-form ref="formRef" :model="form" label-width="auto" class="homework_score_ruleForm" :rules="rules">
      <el-form-item label="分数" prop="score">
        <el-input-number style="width: 100%;" v-model="form.score" :min="0" :max="100" label="分数" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'

const dialogVisible = ref(false)
const formRef = ref(null)
const form = ref({
  score: 0
})
const rules = ref({
  score: [{ required: true, message: '分数不能为空', trigger: 'change' }],
})
// 开启弹窗
const open = () => {
  dialogVisible.value = true
  if (formRef.value) formRef.value.resetFields()
}
// 关闭弹窗
const confirmFrom = () => {
  formRef.value.validate(valid => {
    if (valid) {
      console.log('submit!')
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss">
.homework_score_ruleForm {
  margin: 20px 0;
}
</style>
