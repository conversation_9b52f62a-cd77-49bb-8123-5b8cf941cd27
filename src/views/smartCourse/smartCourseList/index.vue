<template>
  <component :is="type == '2' ? IndexTeacher : IndexStudent" @changeRole="changeRole"></component>
</template>
<script>
// 教师权限
import IndexTeacher from './index_teacher.vue'
// 学生权限
import IndexStudent from './index_student.vue'
</script>
<script setup>
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import { ArrowDown, Search, ArrowUp } from '@element-plus/icons-vue'
import { badgeProps } from 'element-plus'
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()

const type = ref()
// 切换角色
function changeRole(n) {
  type.value = n
}
onMounted(async () => {
  let indexCom = {}
  indexCom = await userStore.getInfo()
  type.value = indexCom.user.userType
})
</script>
