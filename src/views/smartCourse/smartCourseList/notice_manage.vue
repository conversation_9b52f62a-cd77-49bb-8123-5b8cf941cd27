<template>
  <div class="block_4 flex-col">
    <div class="exam_manage">
      <div class="exam_manage_operate">
        <div>
          <el-input v-model="input2" style="width: 240px; margin-right: 10px" placeholder="请输入公告标题" :suffix-icon="Search" />
        </div>
        <div>
          <el-button type="danger" plain @click="delBatch">批量删除</el-button>
          <el-button type="primary" @click="addNotice">创建公告</el-button>
        </div>
      </div>
      <div class="exam_manage_table">
        <el-table ref="tableRef" :data="tableData" :loading="loading" style="width: 100%" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" label="序号" align="center" width="50" />
          <el-table-column prop="ggbt" align="center" label="公告标题" />
          <el-table-column prop="fskc" align="center" label="发送课程" />
          <el-table-column prop="bj" align="center" label="班级" />
          <el-table-column prop="fbsj" align="center" label="发布时间" width="160"> </el-table-column>
          <el-table-column fixed="right" align="center" label="操作" min-width="100">
            <template #default="scope">
              <el-button type="primary" link @click="goEdit(scope.row.id, 1)">编辑</el-button>
              <el-button type="primary" link @click="goEdit(scope.row.id, 2)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-container">
        <el-pagination
          background="true"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[100, 200, 300, 400]"
          :size="size"
          layout="total, prev, pager, next, jumper"
          :total="10"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
  <PopupNoticeAdd ref="popupNoticeAdd" @refushList="refushList" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import PopupNoticeAdd from './popup_notice_add.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance()
import { getListByTeacherIdNoPage } from '@/api/edu/moocSmartCourse.js'
import { getHomeworkOrTestListByUserId } from '@/api/edu/moocSmartCourseHomeworkOrTest.js'
/*const currentPage = ref(1)
const pageSize = ref(10)*/
const total = ref(0)
const courseList = ref([])
const loading = ref(false)
const tableRef = ref(null)
const ids = ref([])
const ztList = ref([
  {
    label: '未开始',
    value: '1'
  },
  {
    label: '进行中',
    value: '2'
  },
  {
    label: '已结束',
    value: '3'
  }
])

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    assignmentType: 1
  }
})
const { queryParams } = toRefs(data)

const tableData = ref([
  {
    ggbt: '考试名称',
    fskc: '我是课程',
    bj: '考试班级',
    fbsj: '2025-01-01 12:00:00'
  }
])
const popupNoticeAdd = ref(null)

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}
// 勾选
const handleSelectionChange = val => {
  // 勾选id集合
  ids.value = val.map(item => item.id)
}
// 批量删除
const delBatch = () => {
  if (ids.value && ids.value.length == 0) {
    ElMessage({
      type: 'info',
      message: '请勾选需要删除的公告'
    })
  } else {
    ElMessageBox.confirm('确定删除已选公告吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '取消操作'
        })
      })
  }
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  let response = await getHomeworkOrTestListByUserId(queryParams.value)
  // tableData.value = response.rows;
  total.value = response.total
  loading.value = false
}

onMounted(() => {
  getList()
  ids.value = []
  // 清空表格选中
  if (tableRef.value) tableRef.value.clearSelection()
})

const handleSizeChange = val => {
  console.log(`每页 ${val} 条`)
}
// 添加试卷
const addNotice = () => {
  popupNoticeAdd.value.open()
}

const refushList = () => {
  getList()
}

// 编辑试卷
const goEdit = (id, type) => {
  // 编辑
  if (type == 1) {
    popupNoticeAdd.value.open(1)
    // 查看
  } else {
    popupNoticeAdd.value.open(2)
  }
}
</script>

<style scoped lang="scss">
.exam_manage {
  background: #fff;
  padding: 20px;
  min-height: 56.25vw;
  border-radius: 8px;
  .exam_manage_operate {
    display: flex;
    justify-content: space-between;
  }
  .exam_manage_table {
    margin-top: 20px;
  }
}
</style>
