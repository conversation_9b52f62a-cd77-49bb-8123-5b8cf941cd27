<template>
  <div>
    <div class="filters">
      <div class="filter-content">
        <el-breadcrumb :separator-icon="ArrowRight" class="breadcrumb_text">
          <el-breadcrumb-item>课程</el-breadcrumb-item>
          <el-breadcrumb-item>{{ courseName }}</el-breadcrumb-item>
          <el-breadcrumb-item>我的班课列表</el-breadcrumb-item>
          <el-breadcrumb-item>{{ className }}</el-breadcrumb-item>
          <el-breadcrumb-item>当前课堂</el-breadcrumb-item>
        </el-breadcrumb>
        <el-button class="btn" type="primary" @click="goBack">返回</el-button>
      </div>
    </div>
    <div class="evaluation-page-container">
      <el-card class="content-wrapper-card">
        <div class="content-wrapper">
          <h2 class="title">课堂评价</h2>
          <div v-if="!isSubmitted">
            <div class="rating-section">
              <span class="rating-label">为课堂评分</span>
              <el-rate
                v-model="form.rating"
                show-score
                text-color="#ff9900"
                score-template="{value} 分"
                size="large"
              />
            </div>
            <el-input
              v-model="form.comment"
              :rows="10"
              type="textarea"
              placeholder="请输入评价内容"
              show-word-limit
              maxlength="500"
            />
          </div>
          <div v-else class="submitted-view">
             <div class="rating-section">
              <span class="rating-label">我的评分</span>
              <el-rate
                v-model="submittedData.rating"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value} 分"
                size="large"
              />
            </div>
            <div class="comment-section">
              <p class="comment-label">评价内容</p>
              <div class="comment-content">{{ submittedData.comment || '未填写评价内容' }}</div>
            </div>
          </div>
        </div>
      </el-card>
      <div class="page-footer" v-if="!isSubmitted">
        <el-button size="large" @click="goBack">取消</el-button>
        <el-button type="primary" size="large" @click="submit" :disabled="form.rating === 0">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ArrowRight } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getMyLessonEvaluation, addLessonEvaluation } from '@/api/edu/lessonEvaluation';

const route = useRoute();
const router = useRouter();

// 假设 lessonId 从路由参数中获取
const lessonId = computed(() => route.params.lessonId);
// 假设课程和班级名称从查询参数中获取，用于面包屑导航
const courseName = computed(() => route.query.courseName || '课程名称');
const className = computed(() => route.query.className || '班课名称');

const form = ref({
  rating: 0,
  comment: '',
});

const isSubmitted = ref(false);
const submittedData = ref({
  rating: 0,
  comment: '',
});

// 检查用户是否已提交过评价
const checkSubmissionStatus = async () => {
  if (!lessonId.value) return;
  try {
    // 后端接口 /getMine 会在没有评价时因 list.get(0) 报错。
    // 在前端，我们会捕获这个错误，并将其视为"未提交"，这是为了兼容当前不规范的后端实现。
    // 理想情况下，后端在未找到时应返回 200 和 null/空对象，或 404。
    const res = await getMyLessonEvaluation({ lessonId: lessonId.value });
    // 后端直接返回评价对象，而不是包装在 data 属性中。
    // 因此我们直接检查 res 对象本身是否存在并且有关键字段（如 evaluationId）。
    if (res && res.evaluationId) {
      const myEvaluation = res;
      isSubmitted.value = true;
      submittedData.value.rating = myEvaluation.rating;
      submittedData.value.comment = myEvaluation.comment;
      form.value.rating = myEvaluation.rating;
      form.value.comment = myEvaluation.comment;
    } else {
      isSubmitted.value = false;
    }
  } catch (error) {
    // 捕获错误，并假设这是因为学生尚未提交评价。
    // 不向用户显示错误信息，仅在控制台记录。
    console.info('查询评价记录失败 (这可能是正常的，因为学生尚未提交评价):', error);
    isSubmitted.value = false; // 确保在出错时，页面处于可提交状态
  }
};

onMounted(() => {
  checkSubmissionStatus();
});

const submit = async () => {
  if (form.value.rating === 0) {
    ElMessage.warning('请先为课堂评分');
    return;
  }

  const payload = {
    lessonId: lessonId.value,
    rating: form.value.rating,
    comment: form.value.comment,
  };

  try {
    await addLessonEvaluation(payload);
    ElMessage.success('评价已提交！');
    // 提交后刷新状态
    await checkSubmissionStatus();
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error('评价提交失败，请稍后再试。');
  }
};

const goBack = () => {
  router.back();
};
</script>

<style lang="scss" scoped>
.filters {
  width: 70%;
  margin: -50px auto 20px auto;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  box-sizing: border-box;
  width: 95%;
  margin-left: 2.5%;
}

.breadcrumb_text {
  font-size: 14px;
  font-weight: 400;
  line-height: 40px;
}

.btn {
  height: 40px;
  width: 120px;
  border-radius: 4px;
}

.evaluation-page-container {
  padding: 0 20px 20px 20px;
  background-color: #f0f2f5;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 150px);
  align-items: center;
}

.content-wrapper-card {
  width: 70%;
  flex: 1;
  overflow: auto;
  :deep(.el-card__body) {
    height: 100%;
    padding: 30px;
    display: flex;
    flex-direction: column;
  }
}

.content-wrapper {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 4px;
  height: 100%;
}

.title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 30px;
}

.rating-section {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  gap: 15px;
}

.rating-label {
  font-size: 16px;
  color: #333;
}

.page-footer {
  padding: 20px 0;
  text-align: center;
  background-color: #f0f2f5;
  
  .el-button {
    margin: 0 15px;
    width: 120px;
  }
}

.submitted-view {
  .comment-section {
    margin-top: 20px;
  }
  .comment-label {
    font-size: 16px;
    color: #333;
    margin-bottom: 15px;
  }
  .comment-content {
    background-color: #f9f9f9;
    border: 1px solid #ebebeb;
    border-radius: 4px;
    padding: 15px;
    min-height: 150px;
    white-space: pre-wrap;
    font-size: 14px;
    color: #606266;
  }
}
</style>