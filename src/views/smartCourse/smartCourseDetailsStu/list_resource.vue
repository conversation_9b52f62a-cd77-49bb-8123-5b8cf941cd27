<template>
  <div class="stu_homework_main">
    <el-row :gutter="20">
      <el-col :span="8" class="main_col" v-for="item in data" :key="item.id">
        <el-card class="content">
          <div class="content_header">
            <div class="header_left">
              <div>{{ item.bt }}</div>
            </div>
          </div>
          <div class="content_main">
            <div>发送时间：{{ item.kssj }}</div>
          </div>
          <el-divider />
          <div class="content_btn">
            <el-button type="primary" @click="microsoft(item)">查看</el-button>
            <el-button v-if="item.type === '2'" type="primary" plain @click="downUrl(item.url)">下载</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
  <el-dialog v-model="dialogVisible" title="Tips" width="1000" :before-close="handleClose"
    style="margin-top: 0 !important">
    <iframe :src="urlConf" width="100%" height="600px" frameborder="0" title="预览"></iframe>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance()
const dialogVisible = ref(false)
const data = ref([
  {
    id: '1',
    type: '1', // 课件
    bt: '2022.02.10 10:00:01的课件',
    kssj: '2022.02.10 10:00:01',
  },
  {
    id: '2',
    type: '2', // 文件
    bt: '2022.02.10 10:00:01的文件',
    kssj: '2022.02.10 10:00:01',
    url: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1753853747275.doc',
  },
  {
    id: '2',
    type: '2', // 文件
    bt: '2022.02.10 10:00:01的文件',
    kssj: '2022.02.10 10:00:01',
    url: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1753853716899.ppt',
  }
])
const emits = defineEmits(['typeKey'])
const goQuestion = (num, item) => {
  emits('typeKey', { num: num, params: item, type: item.type })
}
// 预览
const urlConf = ref()
const microsoft = (item) => {
  if (item.type === '1') {
    console.log('跳转课件页面')
  } else {
    dialogVisible.value = true
    const wr = 'https://view.officeapps.live.com/op/view.aspx?src='
    urlConf.value = wr + item.url
  }
}
// 下载 
const downUrl = (url) => {
  window.open(url)
}
</script>

<style scoped lang='scss'>
.stu_homework_main {
  .main_col {
    margin-top: 20px;
  }

  .content {
    .content_header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header_left {
        display: flex;
        margin-top: 10px;

        div {
          font-weight: bold;
          font-size: 18px;
        }
      }

      .header_right {
        margin-top: 10px;
      }
    }

    .content_main {
      min-height: 50px;

      div {
        font-size: 15px;
        color: #999;
        margin-top: 10px;
      }
    }

    .content_btn {
      display: flex;
      align-items: center;

      div {
        span {
          color: red;
        }
      }
    }
  }
}
</style>