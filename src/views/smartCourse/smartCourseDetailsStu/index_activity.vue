<template>
  <div>
    <component :is="COMS[active]" @typeKey="typeKey"></component>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import ListActivity from './list_activity.vue'
import LessonEvaluation from '../lessonEvaluation/index.vue'
import IndexBrain from './index_brain.vue'
const COMS = ref([ListActivity,1,IndexBrain,3,4,5,6,7])
const active = ref(0)
const { proxy } = getCurrentInstance()
const typeKey = (receive) => {
  active.value = receive.type
}
</script>

<style scoped lang='scss'>
</style>