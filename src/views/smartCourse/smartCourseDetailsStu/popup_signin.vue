<template>
  <el-dialog
    v-model="dialogVisible"
    title="签到"
    width="500"
    :before-close="handleClose"
    style="margin-top: 20vh !important"
  >
    <el-form
      ref="formRef"
      style="max-width: 600px"
      :model="form"
      label-width="auto"
      class="demo-ruleForm"
      :rules="rules"
    >
      <el-form-item :label="form.attendanceType == '2' ? '手势签到' : '签到码'" prop="attendanceType">
        <!-- <el-radio-group v-model="form.attendanceType">
          <el-radio value="1">教师签到</el-radio>
          <el-radio value="2">手势签到</el-radio>
          <el-radio value="3">签到码</el-radio>
        </el-radio-group> -->
        <el-input
          v-if="form.attendanceType === '3'"
          v-model="form.signInCode"
          style="width: 240px"
          placeholder="请输入签到码"
        />
        <GesturePad
          v-if="form.attendanceType === '2'"
          mode="create"
          @finish="saveGesture"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import dayjs from 'dayjs';
import { nextTick } from 'vue';
// 手势签到
import GesturePad from "./index_gesture_pad.vue";
import { ElMessageBox } from "element-plus";
import { addAttendance } from "@/api/edu/attendance";
const pattern = ref("");
const props = defineProps({ form: { type: Object, default: {} }, hdData: { type: Object, default: [] }});
const form = ref({
  courseId: "",
  lessonId: "",
  classId: "",
  title: "",
  attendanceType: "2",
  endMethod: "1",
  durationMinutes: "",
  signInCode: "",
  gesturePattern: "",
});
const dialogVisible = ref(false);
const formRef = ref(null);
const rules = ref({
  title: [{ required: true, message: "签到标题不能为空", trigger: "change" }],
  attendanceType: [
    { required: true, message: "签到类型不能为空", trigger: "change" },
  ],
  endMethod: [
    { required: true, message: "结束方式不能为空", trigger: "change" },
  ],
});
// 开启弹窗
const open = () => {
  dialogVisible.value = true;
  form.value = {
    courseId: "",
    lessonId: "",
    classId: "",
    title: "",
    attendanceType: "2",
    endMethod: "1",
    durationMinutes: "",
    signInCode: "",
    gesturePattern: "",
  };
  if (formRef.value) formRef.value.resetFields();
};
// 关闭弹窗
const confirmFrom = () => {
  if (form.value.endMethod === "2" && form.value.durationMinutes === "") {
    ElMessageBox({
      title: "操作提示",
      message: "请输入结束时长",
      type: "warning",
    });
    return;
  }
  if (form.value.attendanceType === "2" &&  pattern.value === "") {
    ElMessageBox({
      title: "操作提示",
      message: "请绘制手势",
      type: "warning",
    });
    return;
  }
  if (form.value.attendanceType === "3" && form.value.signInCode === "") {
    ElMessageBox({
      title: "操作提示",
      message: "请输入签到码",
      type: "warning",
    });
    return;
  }
  // 纯数字校验
  else if (
    form.value.attendanceType === "3" &&
    !/^[0-9]*$/.test(form.value.signInCode)
  ) {
    ElMessageBox({
      title: "操作提示",
      message: "请输入纯数字的签到码",
      type: "warning",
    });
    return;
  }
  formRef.value.validate(async (valid) => {
    if (valid) {
        // 时间格式转换
      if (form.value.endMethod === '2') {
        form.value.durationMinutes = dayjs(form.value.durationMinutes).format('HH:mm:ss');
      }
      form.value.courseId = props.form.courseId;
      form.value.classId = props.form.classId;
      form.value.lessonId = props.form.lessonId;
      addAttendance(form.value).then((res) => {
        if (res.code == 200) {
          ElMessageBox({
            title: "操作提示",
            message: "添加活动成功",
            type: "success",
          });
        }
      });
      // 等待DOM更新后再关闭弹窗
      await nextTick();
      dialogVisible.value = false;
      console.log(props.hdData);  
      const hdData = props.hdData;
      const params = {
        zt: 1,
        id: 1,
        type: 1,
        bt: "学生签到1111",
        rs: 2,
        ts: 2,
        kssj: "2025-01-10",
        jssj: "2025-03-13",
      }
      hdData.push(params);
    } else {
      console.log("error submit!");
    }
  });
};
const handleClose = () => {
  dialogVisible.value = false;
};
defineExpose({ open });

async function saveGesture(p) {
  pattern.value = p;
  form.value.gesturePattern = pattern.value;
  // 提示手势保存成功
  ElMessageBox({ title: "操作提示", message: "手势保存成功", type: "success" });
}
</script>
<style lang="scss">
// .el-dialog.signin_popup_dialog :not(.is-fullscreen) {
//   margin-top: 30vh !important;
// }
</style>
