<template>
  <div>
    <component :is="COMS[active]" @typeKey="typeKey"></component>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import ListResource from './list_resource.vue'
const COMS = ref([ListResource])
const active = ref(0)
const { proxy } = getCurrentInstance()
const typeKey = (receive) => {
  active.value = receive.type
}
</script>

<style scoped lang='scss'>
</style>