<template>
  <el-card v-if="!showPage">
    <div class="back"><el-button @click="goback">返回</el-button>
    </div>
    <h3>{{ res.bt }}</h3>
    <p>
      {{ res.nr }}
    </p>
    <el-image v-for="(item, index) in res.img" :key="index" ref="imageRef" style="width: 150px; height: 150px"
      :src="item" show-progress :preview-src-list="[item]" fit="cover" />
    <div class="brain_down">
      <el-button type="primary" :icon="Download" plain>下载附件</el-button>
    </div>
    <el-divider />
    <el-form ref="formRef" :model="form" label-width="auto" label-position="top"
      class="demo-ruleForm" :rules="rules">
      <el-form-item label="内容" prop="nr">
        <el-input v-model="form.nr" :rows="5" type="textarea" show-word-limit="true" maxlength="300"
          placeholder="请输入内容" />
      </el-form-item>
      <el-form-item label="上传文件" prop="fileList">
        <el-upload v-model:file-list="form.fileList" class="upload-demo" :action="uploadUrl" :headers="headers"
          :before-upload="beforeAvatarUpload" :on-success="handleSuccess" :on-remove="handleRemove">
          <el-button type="primary" :icon="Upload">上传文件</el-button>
          <template #tip>
            <div class="el-upload__tip">请上传 jpg, jpeg, png, pdf文件, 大小在60M以内</div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <div style="text-align: center;">
      <el-button @click="goback">取消</el-button>
      <el-button type="primary" @click="confirmFrom">提交</el-button>
    </div>
  </el-card>
  <el-card class="brain_success" v-else>
    <div><el-icon size="30"	color="#67C23A">
        <CircleCheckFilled />
      </el-icon></div>
    <p>作答已提交</p>
    <p>3s后回到课堂活动页面</p>
    <div>
      <el-button @click="goback">返回</el-button>
      <el-button type="primary" @click="lookBrain">查看</el-button>
    </div>
  </el-card>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import { Download, Upload } from '@element-plus/icons-vue'
import { getToken } from '@/utils/auth'
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/file/upload') // 上传的图片服务器地址
const headers = ref({
  Authorization: 'Bearer ' + getToken()
})
const formRef = ref(null)
const showPage = ref(false)
const form = ref({
  nr: '',
  fileList: [],
})
// 上传
const beforeAvatarUpload = rawFile => {
  if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/jpg' && rawFile.type !== 'image/png' && rawFile.type !== 'application/pdf') {
    ElMessage.error('上传失败！请上传 jpg, jpeg, png, pdf格式文件！')
    return false
  } else if (rawFile.size / 1024 / 1024 > 60) {
    ElMessage.error('上传失败！文件大小不能超过 60MB!')
    return false
  }
  return true
}
const { proxy } = getCurrentInstance()
const rules = ref({
  bt: [{ required: true, message: '标题不能为空', trigger: 'change' }],
  nr: [{ required: true, message: '内容不能为空', trigger: 'change' }],
  fileList: [{ required: true, message: '请上传文件', trigger: 'change' }]
})
// 返回
const emits = defineEmits('typeKey')
const goback = () => {
  emits('typeKey',{type:0})
}
const fileList = ref([])
const lookBrain = () => {
  showPage.value = false
}
// 确定
const confirmFrom = () => {
  formRef.value.validate(valid => {
    if (valid) {
      console.log('submit!')
      showPage.value = true
      // 上传文件数组
      const list = fileList.value.map(item => item.response.data)
      console.log(list)
    } else {
      console.log('error submit!')
    }
  })
}
onMounted(() => {
  if (formRef.value) formRef.value.resetFields()
})
const res = ref({
  id: '1',
  bt: '《当「电子木鱼」敲出 Z 世代的精神按摩 —— 年轻人为何沉迷赛博祈福？》',
  nr: '从寺庙手串到电子木鱼 APP，当代年轻人正在用「赛博修行」重构精神寄托。当 00 后在地铁里敲着手机屏幕求「KPI暴涨」，当程序员用木鱼音效覆盖键盘敲击声解压，这种「电子禅意」背后藏着三重时代隐喻：对抗信息过载的仪式感：在碎片化时代，敲木鱼的机械动作成为对抗焦虑的「数字结界」，就像古人转动经筒，现代人用点击屏幕制造「专注锚点」。某木鱼APP 数据显示，用户日均敲击超 300 次，其中「防摸鱼」模式启动时，工作效率提升 27%。解构宗教符号的玩梗心态：年轻人把「开光」改成「程序员祝福代码永不报错」，将「功德」量化「今日防杠成功 +1」，用戏谑消解传统宗教的严肃感。这种「赛博朋克式修行」，本质是用互联网语言重构精神图腾。社交货币的新型载体：微信小程序里的「云敲木鱼」功能，允许用户给好友「远程敲钟祈福」，衍生出「帮我敲 108下求甲方改稿」的社交玩法。当祈福变成可分享的数字行为，精神需...',
  img: ['https://dutp-test.oss-cn-beijing.aliyuncs.com/1754127715643.png', 'https://img3.redocn.com/tupian/20130518/huangsebeijingxiadeqingsezhengfangxing_1706865_small.jpg', 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1754127715643.png', 'https://img3.redocn.com/tupian/20130518/huangsebeijingxiadeqingsezhengfangxing_1706865_small.jpg']
})
</script>

<style scoped lang="scss">
.back {
  text-align: right;
}
.brain_down {
  margin: 10px 0;
}
.brain_success {
  text-align: center;
  height: 500px;
  padding-top: 160px;
}
</style>
