<template>
  <div class="stu_homework_main">
    <el-row :gutter="20">
      <el-col :span="12" class="main_col" v-for="item in data" :key="item.id">
        <el-card class="content">
          <div class="content_header">
            <div class="header_left">
              <div v-show="item.zt === '1'">进行中</div>
              <div v-show="item.zt === '2'">已结束</div>
              <div>{{ item.bt }}</div>
              <div v-if="item.type === '5'">小组作业</div>
            </div>
            <div class="header_right" v-if="item.sfcy === '1'" style="color: green;">已参与</div>
            <div class="header_right" v-if="item.sfcy === '2'" style="color: #999;">未参与</div>
            <div class="header_right" v-if="item.sfcy === '3'" style="color: green;">已作答</div>
            <div class="header_right" v-if="item.sfcy === '4'" style="color: #999;">待作答</div>
            <div class="header_right" v-if="item.sfcy === '5'" style="color: #999;">待评分</div>
            <div class="header_right" v-if="item.sfcy === '6'" style="color: green;">已完成</div>
          </div>
          <div class="content_main">
            <div>开始时间：{{ item.kssj }}</div>
            <div v-if="item.type === '5'">结束时间：{{ item.kssj }}</div>
            <div>参与人数：{{ item.cyrs }}</div>
            <div v-if="item.type === '5'">作答时间：{{ item.zdkssj }}-{{ item.zdjssj }}</div>
            <div v-if="item.type === '5'">互评时间：{{ item.hpkssj }}-{{ item.hpjssj }}</div>
          </div>
          <el-divider />
          <div class="content_btn">
            <el-button v-if="item.zt === '1'" type="primary" @click="goQuestion(1,item)">参与</el-button>
            <el-button v-else type="primary" @click="goQuestion(2,item)">查看</el-button>
            <div v-if="item.type === '5'">距离互评结束还有<span>{{ item.jssj }}</span> </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance()
const data = ref([
  {
    id: '1',
    type: '1', // 投票问卷
    bt: '2022.02.10 10:00:01的投票问卷',
    kssj: '2022.02.10 10:00:01',
    cyrs: '10',
    sfcy: '1',
    zt: '1'
  },
  {
    id: '2',
    type: '2', // 头脑风暴
    bt: '2022.02.10 10:00:01的头脑风暴',
    kssj: '2022.02.10 10:00:01',
    cyrs: '10',
    sfcy: '1',
    zt: '1'
  },
  {
    id: '3',
    type: '3', // 拓展学习
    bt: '2022.02.10 10:00:01的拓展学习',
    kssj: '2022.02.10 10:00:01',
    cyrs: '10',
    sfcy: '1',
    zt: '1'
  },
  {
    id: '4',
    type: '4', // 提问
    bt: '2022.02.10 10:00:01的提问',
    kssj: '2022.02.10 10:00:01',
    cyrs: '10',
    sfcy: '1',
    zt: '1',
    fs: '10'
  },
  {
    id: '5',
    type: '5', // 作业 
    bt: '2022.02.10 10:00:01的作业',
    kssj: '2022.02.10 10:00:01',
    cyrs: '10',
    sfcy: '2',
    zt: '1',
    fs: ''
  },
  {
    id: '6',
    type: '6', // 提问 
    bt: '2022.02.10 10:00:01的提问',
    kssj: '2022.02.10 10:00:01',
    sfcy: '2',
    zt: '1',
    fs: ''
  },
  {
    id: '7',
    type: '6', // 提问 
    bt: '2022.02.10 10:00:01的提问',
    kssj: '2022.02.10 10:00:01',
    sfcy: '2',
    zt: '1',
    fs: ''
  },
  {
    id: '8',
    type: '7', // 课堂评价 
    bt: '课堂评价',
    kssj: '2022.02.10 10:00:01',
  }
])
const emits = defineEmits(['typeKey'])
const goQuestion = (num,item) => {
  emits('typeKey', {num: num, params: item,type:item.type})
}
</script>

<style scoped lang='scss'>
.stu_homework_main {
  .main_col {
    margin-top: 20px;
  }

  .content {
    .content_header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header_left {
        display: flex;
        margin-top: 10px;

        div {
          margin-right: 8px;
        }

        div:nth-child(1) {
          width: 62px;
          height: 22px;
          background: #52C41A;
          border-radius: 20px;
          font-size: 14px;
          color: #FFFFFF;
          text-align: center;
          line-height: 24px;
        }

        div:nth-child(2) {
          width: 62px;
          height: 22px;
          background: #999999;
          border-radius: 20px;
          font-size: 14px;
          color: #fff;
          text-align: center;
          line-height: 24px;
        }

        div:nth-child(3) {
          font-weight: bold;
          font-size: 18px;
        }

        div:nth-child(4) {
          font-size: 15px;
          color: #999;
          margin-top: 5px;
        }
      }

      .header_right {
        margin-top: 10px;
      }
    }

    .content_main {
      min-height: 140px;

      div {
        font-size: 15px;
        color: #999;
        margin-top: 10px;
      }
    }

    .content_btn {
      display: flex;
      justify-content: space-between;
      align-items: center;

      div {
        span {
          color: red;
        }
      }
    }
  }
}
</style>