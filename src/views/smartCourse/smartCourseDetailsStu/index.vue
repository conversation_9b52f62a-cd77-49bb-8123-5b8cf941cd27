<template>
  <div class="stu_learn_box">
    <div class="box">
      <div class="box_top">
        <el-breadcrumb :separator-icon="ArrowRight">
          <el-breadcrumb-item :to="{ path: '/' }">课程</el-breadcrumb-item>
          <el-breadcrumb-item>我的班课列表</el-breadcrumb-item>
          <el-breadcrumb-item>班课名称</el-breadcrumb-item>
          <el-breadcrumb-item>课堂学习</el-breadcrumb-item>
        </el-breadcrumb>
        <el-button type="primary" plain @click="goBack">返回</el-button>
      </div>
      <div class="box_mid">
        <div class="mid_time">
          <div>{{time.year}}年{{time.month}}月{{time.day}}日 {{howDay}}</div>
          <h2>{{ time.hours }}:{{ time.minutes }}:{{ time.seconds }}</h2>
          <div>你要悄悄拔尖，然后惊艳所有人</div>
        </div>
        <div class="mid_data">
          <div>学习在线时长：<i>在课程所有时间</i></div>
          <div>
            <span>作业总数：<i>3</i></span>
            <span>考试总数：<i>4</i></span>
            <span>得分：<i>100</i></span>
          </div>
        </div>
      </div>
      <div class="box_bot">
        <div class="bot_tools">
          <div class="bot_tools_left">
            <div v-for="item in tab" :key="item.value" :class="tabValue === item.value ? 'avtive' : ''"
              @click="tabChange(item.value)">{{ item.label }}</div>
          </div>
          <div class=" bot_tools_right">
            <el-input v-model="input2" style="width: 240px" placeholder="请输入搜索关键词" :suffix-icon="Search" />
            <el-select class="right_select" v-model="value" placeholder="类型" style="width: 140px">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-button class="right_btn" type="primary" @click="sign">签到（剩余200分钟）</el-button>
          </div>
        </div>
        <component :is="coms[tabValue - 1]" />
      </div>
    </div>
  </div>
  <PopupSignin ref="popupSignin" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted,onUnmounted } from 'vue';
import { ArrowRight, Search } from '@element-plus/icons-vue'
import IndexActivity from './index_activity.vue'
import IndexResource from './index_resource.vue'
import PopupSignin from './popup_signin.vue'
const coms = [IndexActivity, IndexResource]
const { proxy } = getCurrentInstance()
const tab = ref([
  {
    label: '课堂活动',
    value: '1'
  },
  {
    label: '内容资源',
    value: '2'
  }
])
// tab切换
const tabValue = ref('1')
const tabChange = (value) => {
  tabValue.value = value
}
// 签到 
const popupSignin = ref(null)
const sign = () => {
  popupSignin.value.open()
}
// 计算时间
const time = ref({
  year: '',
  month: '',
  day: '',
  hours: '',
  minutes: '',
  seconds: ''
})
const howDay =  computed(() => {
  const nowOne = new Date(time.value.year + '-' + time.value.month + '-' + time.value.day).getDay()
  let one = ''
  switch (nowOne) {
    case 1:
      one = '星期一'
      break;
    case 2:
      one = '星期二'
      break;
    case 3:
      one = '星期三'
      break;
    case 4:
      one = '星期四'
      break;
    case 5:
      one = '星期五'
      break;
    case 6:
      one = '星期六'
      break;
    case 0:
      one = '星期日'
      break;
    default:
      break;
  }
  return one
})
const calcTime = () => {
  const now = new Date();
  // 获取年、月、日、小时、分钟、秒
  // padStart确保是两位数
  time.value.year = now.getFullYear();
  time.value.month = String(now.getMonth() + 1).padStart(2, '0');
  time.value.day = String(now.getDate()).padStart(2, '0');
  time.value.hours = String(now.getHours()).padStart(2, '0');
  time.value.minutes = String(now.getMinutes()).padStart(2, '0');
  time.value.seconds = String(now.getSeconds()).padStart(2, '0');
}
const timer = ref(null)
onMounted(() => {
  calcTime()
    timer.value = setInterval(() => {
    calcTime()
  }, 1000)
})
onUnmounted(() => {
  if(timer.value) clearInterval(timer.value)
})

const goBack = () => {
  proxy.$router.push('smart-course-tab-stu')
}
</script>

<style scoped lang='scss'>
.stu_learn_box {
  background: #F2F5FA;

  .box {
    width: 1100px;
    margin: auto;

    .box_top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 13px 0;
    }

    .box_mid {
      width: 100%;
      height: 102px;
      border-radius: 10px;
      background-image: url('../../../assets/images/smartCourse/smartCourseDetailsStu/index_bg.png');
      background-size: 100% 100px;
      background-repeat: no-repeat;
      margin-bottom: 20px;
      display: flex;
      align-items: center;

      .mid_time {
        margin-left: 60px;
        text-align: center;

        div:first-of-type {
          font-size: 12px;
          color: #333333;
        }

        div:last-of-type {
          font-size: 12px;
          color: #666666;
        }

        h2 {
          margin: 5px 0;
          font-size: 33px;
          color: #000000;
        }
      }

      .mid_data {
        margin-left: 50px;
        font-size: 16px;
        color: #666666;
        font-weight: normal;

        div:first-of-type {
          margin-bottom: 15px;
        }

        i {
          font-style: normal;
          color: #333333;
        }

        span {
          margin-right: 30px;
        }
      }
    }

    .box_bot {
      background-color: #fff;
      padding: 20px;
      border-radius: 10px;
      margin-bottom: 20px;

      .bot_tools {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        .bot_tools_left {
          display: flex;
          font-size: 22px;
          color: #999999;

          div {
            cursor: pointer;
            padding-bottom: 5px;
          }

          div:first-of-type {
            margin-right: 30px;
          }

          .avtive {
            color: #333333;
            border-bottom: 2px solid #696969;
          }
        }

        .bot_tools_right {
          .right_btn {
            margin-left: 20px;
          }

          .right_select {
            margin-left: 20px;
          }
        }
      }
    }
  }
}
</style>