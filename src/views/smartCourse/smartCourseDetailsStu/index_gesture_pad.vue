<template>
  <canvas ref="canvasRef" width="300" height="300" style="border: 1px solid #ccc;" />
</template>

<script setup>
import { ref, onMounted, watch, defineEmits } from 'vue';

const props = defineProps({
  mode: { type: String, default: 'sign' }, // 'create' or 'sign'
});
const emit = defineEmits(['finish']);

const canvasRef = ref(null);
const ctx = ref(null);
const points = ref([]);
const selectedPoints = ref([]);
const radius = 20;
const offset = 50;
let isDrawing = false;

onMounted(() => {
  const canvas = canvasRef.value;
  ctx.value = canvas.getContext('2d');

  for (let row = 0; row < 3; row++) {
    for (let col = 0; col < 3; col++) {
      points.value.push({
        x: col * 100 + offset,
        y: row * 100 + offset,
        index: row * 3 + col + 1,
      });
    }
  }
  draw();
  bindEvents(canvas);
});

function draw() {
  ctx.value.clearRect(0, 0, 300, 300);
  points.value.forEach(p => {
    ctx.value.beginPath();
    ctx.value.arc(p.x, p.y, radius, 0, Math.PI * 2);
    ctx.value.strokeStyle = '#999';
    ctx.value.stroke();
  });

  if (selectedPoints.value.length > 1) {
    ctx.value.beginPath();
    ctx.value.moveTo(selectedPoints.value[0].x, selectedPoints.value[0].y);
    for (let i = 1; i < selectedPoints.value.length; i++) {
      ctx.value.lineTo(selectedPoints.value[i].x, selectedPoints.value[i].y);
    }
    ctx.value.strokeStyle = '#409EFF';
    ctx.value.lineWidth = 3;
    ctx.value.stroke();
  }
}

function bindEvents(canvas) {
  canvas.addEventListener('mousedown', start);
  canvas.addEventListener('mousemove', move);
  canvas.addEventListener('mouseup', end);
}

function getPos(e) {
  const rect = canvasRef.value.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;
  return { x, y };
}

function detectHit(pos) {
  return points.value.find(p => {
    return Math.hypot(p.x - pos.x, p.y - pos.y) < radius;
  });
}

function start(e) {
  e.preventDefault();
  selectedPoints.value.length = 0;
  isDrawing = true;
  const pos = getPos(e);
  const hit = detectHit(pos);
  if (hit && !selectedPoints.value.includes(hit)) {
    selectedPoints.value.push(hit);
  }
  draw();
}

function move(e) {
  if (!isDrawing) return;
  const pos = getPos(e);
  const hit = detectHit(pos);
  if (hit && !selectedPoints.value.includes(hit)) {
    selectedPoints.value.push(hit);
    draw();
  }
}

function end() {
  isDrawing = false;
  draw();
  const pattern = selectedPoints.value.map(p => p.index).join('-');
  emit('finish', pattern);
  setTimeout(() => {
    selectedPoints.value = [];
    draw();
  }, 500);
}
</script>
