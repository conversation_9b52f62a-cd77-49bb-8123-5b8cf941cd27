<template>
  <div>
    <div class="filters">
      <div class="filter-content">
        <el-breadcrumb :separator-icon="ArrowRight" class="breadcrumb_text">
          <el-breadcrumb-item>首页</el-breadcrumb-item>
          <el-breadcrumb-item>课程</el-breadcrumb-item>
          <el-breadcrumb-item>我的班课列表</el-breadcrumb-item>
          <el-breadcrumb-item>课程名称</el-breadcrumb-item>
          <el-breadcrumb-item>作业</el-breadcrumb-item>
        </el-breadcrumb>
        <el-button class="btn" type="primary" @click="goBack">返回</el-button>
      </div>
    </div>
    <div class="extension-page-container">
      <el-card class="content-wrapper-card">
        <div v-if="!isSubmitted" class="content-wrapper">
          <div class="submission-section">
            <h3>我的作答</h3>
            <el-input
              v-model="studentAnswerText"
              :rows="10"
              type="textarea"
              placeholder="请输入内容"
              show-word-limit
              maxlength="1000"
            />
            <el-upload
              v-model:file-list="fileList"
              class="upload-demo"
              :http-request="httpRequest"
              :on-success="handleSuccess"
              :on-remove="handleRemove"
              :on-error="handleError"
              multiple
              :show-file-list="false"
            >
              <el-button type="primary">上传附件</el-button>
            </el-upload>
            <div v-if="fileList.length > 0" class="attachments-list uploaded-list">
              <div v-for="file in fileList" :key="file.uid" class="attachment-item">
                <a :href="file.url" class="file-link" @click.prevent>
                  <img src="@/assets/icons/<EMAIL>" alt="file icon" class="file-icon" />
                  <span>{{ file.name }}</span>
                </a>
                <el-icon class="remove-icon" @click="removeFile(file)"><CloseBold /></el-icon>
              </div>
            </div>
          </div>
          <div class="details-section">
            <el-card class="box-card">
              <div class="text item">
                <p><strong>拓展内容</strong></p>
                <p>拓展要求:</p>
                <div class="requirement-content">{{ extensionContent.description }}</div>
                <div>
                  <p><strong>附件:</strong></p>
                  <div v-if="extensionContent.attachments.length > 0" class="attachments-list">
                    <div v-for="file in extensionContent.attachments" :key="file.fileId" class="attachment-item">
                       <a :href="file.fileUrl" @click.prevent="downloadFile(file)">
                        <img src="@/assets/icons/<EMAIL>" alt="file icon" class="file-icon" />
                        <span>{{ file.fileName }}</span>
                      </a>
                    </div>
                  </div>
                  <el-empty v-else description="暂无附件" :image-size="50" />
                </div>
              </div>
            </el-card>
          </div>
        </div>
        <div v-else class="content-wrapper">
           <div class="submission-section">
            <h3>我的提交</h3>
             <div class="description-content requirement-content" style="white-space: pre-wrap;">{{ submittedData.questionContent || '未填写内容' }}</div>
            <div class="attachments">
              <h3>提交的附件</h3>
              <div v-if="submittedData.attachments.length > 0" class="attachments-list">
                <div v-for="file in submittedData.attachments" :key="file.fileId" class="attachment-item">
                  <a :href="file.fileUrl" @click.prevent="downloadFile(file)">
                    <img src="@/assets/icons/<EMAIL>" alt="file icon" class="file-icon" />
                    <span>{{ file.fileName }}</span>
                  </a>
                </div>
              </div>
              <el-empty v-else description="未提交附件" :image-size="50" />
            </div>
          </div>
           <div class="details-section">
            <el-card class="box-card">
              <div class="text item">
                <p><strong>拓展内容</strong></p>
                <p>拓展要求:</p>
                <div class="requirement-content">{{ extensionContent.description }}</div>
                <div>
                  <p><strong>附件:</strong></p>
                  <div v-if="extensionContent.attachments.length > 0" class="attachments-list">
                    <div v-for="file in extensionContent.attachments" :key="file.fileId" class="attachment-item">
                       <a :href="file.fileUrl" @click.prevent="downloadFile(file)">
                        <img src="@/assets/icons/<EMAIL>" alt="file icon" class="file-icon" />
                        <span>{{ file.fileName }}</span>
                      </a>
                    </div>
                  </div>
                  <el-empty v-else description="暂无附件" :image-size="50" />
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </el-card>
      <div class="page-footer">
        <el-button v-if="!isSubmitted" type="primary" size="large" @click="submit" :disabled="isSubmitDisabled">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ArrowRight, UploadFilled, CloseBold } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getExtension } from '@/api/edu/extension';
import { getListByIds } from '@/api/edu/file';
import { addRecord, getMyRecord } from '@/api/edu/extensionRecord';
import { OssService } from '@/utils/aliOss.js';

const route = useRoute();
const router = useRouter();

const extensionId = computed(() => route.params.extensionId);
const fileList = ref([]);
const uploadedFilesInfo = ref([]);
const studentAnswerText = ref('');
const extensionContent = ref({
  title: '',
  description: '',
  attachments: [],
});
const isSubmitted = ref(false);
const submittedData = ref({
  questionContent: '',
  attachments: []
});

const uploadUrl = computed(() => import.meta.env.VITE_APP_BASE_API + '/edu/file/upload');

const isSubmitDisabled = computed(() => {
  return studentAnswerText.value.trim() === '' && uploadedFilesInfo.value.length === 0;
});

const fetchExtensionContent = async () => {
  if (extensionId.value) {
    try {
      const response = await getExtension(extensionId.value);
      const data = response.data;
      extensionContent.value.title = data.extensionName;
      extensionContent.value.description = data.extensionContent;

      const filePromises = [];
      if (data.attachedMaterialIds) {
        filePromises.push(getListByIds(data.attachedMaterialIds).then(res => {
          extensionContent.value.attachments = res.rows;
        }));
      }
      await Promise.all(filePromises);

    } catch (error) {
      ElMessage.error('获取拓展内容失败');
      console.error(error);
    }
  }
};

const checkSubmissionStatus = async () => {
  try {
    const res = await getMyRecord(extensionId.value);
    if (res.data) {
      const submission = res.data;
      isSubmitted.value = true;
      submittedData.value.questionContent = submission.questionContent;
      submittedData.value.attachments = submission.files || [];
    }
  } catch (error) {
    console.error('查询提交记录失败 (这可能是正常的，如果学生尚未提交):', error);
  }
};

onMounted(async () => {
  await fetchExtensionContent();
  await checkSubmissionStatus();
});

const httpRequest = async (options) => {
  try {
    const result = await OssService(options.file);
    options.onSuccess(result.result, options.file);
  } catch (err) {
    options.onError(err);
  }
};

const handleSuccess = (response, file) => {
  if (response.code === 200) {
    ElMessage.success('文件上传成功');
    uploadedFilesInfo.value.push({
      uid: file.uid,
      fileName: file.name,
      fileUrl: response.data.url,
      fileType: file.name.split('.').pop() || '',
      fileSize: file.size,
    });
  } else {
    ElMessage.error(response.msg || '文件上传失败');
    // Remove the failed file from the list
    fileList.value = fileList.value.filter(f => f.uid !== file.uid);
  }
};

const handleError = (err) => {
  ElMessage.error('文件上传失败，请检查网络或联系管理员');
  console.error("Upload error:", err);
};

const handleRemove = (file) => {
  ElMessage.info('文件已移除');
  uploadedFilesInfo.value = uploadedFilesInfo.value.filter(info => info.uid !== file.uid);
};

const removeFile = (fileToRemove) => {
  fileList.value = fileList.value.filter(file => file.uid !== fileToRemove.uid);
  uploadedFilesInfo.value = uploadedFilesInfo.value.filter(info => info.uid !== fileToRemove.uid);
};

const submit = () => {
  const filesToSubmit = uploadedFilesInfo.value.map(info => {
    const { uid, ...rest } = info;
    return rest;
  });

  if (isSubmitDisabled.value) {
    ElMessage.warning('请输入内容或上传作业文件');
    return;
  }

  const dataToSubmit = {
    extensionId: extensionId.value,
    questionContent: studentAnswerText.value,
    files: filesToSubmit,
  };

  addRecord(dataToSubmit).then(() => {
    ElMessage.success('作业已提交！');
    checkSubmissionStatus();
  }).catch(error => {
    console.error('提交失败:', error);
    ElMessage.error('作业提交失败，请稍后再试。');
  });
};

const goBack = () => {
  router.back();
};

const downloadFile = async (file) => {
  ElMessage.info('正在准备下载...');
  const fileUrl = file.url || file.fileUrl;
  if (!fileUrl) {
    ElMessage.error('文件地址不存在！');
    return;
  }
  try {
    const response = await fetch(fileUrl);
    if (!response.ok) {
      throw new Error('网络响应错误');
    }
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', file.fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('下载失败:', error);
    ElMessage.error('文件下载失败。将在新标签页中打开文件。');
    window.open(fileUrl, '_blank');
  }
};
</script>

<style lang="scss" scoped>
.filters {
  width: 70%;
  margin: -50px auto 20px auto;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  box-sizing: border-box;
  width: 95%;
  margin-left: 2.5%;
}

.breadcrumb_text {
  font-size: 14px;
  font-weight: 400;
  line-height: 40px;
}

.btn {
  height: 40px;
  width: 120px;
  border-radius: 4px;
}

.extension-page-container {
  padding: 0 20px 20px 20px;
  background-color: #f0f2f5;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 150px);
  align-items: center;
}

.content-wrapper-card {
  width: 70%;
  flex: 1;
  overflow: hidden;
  :deep(.el-card__body) {
    height: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;
  }
}

.content-wrapper {
  display: flex;
  gap: 20px;
  height: 100%;
  overflow: hidden;
  flex: 1;
}

.submission-section {
  flex: 1;
  background-color: #ffffff;
  border-radius: 4px;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 0;
}

.details-section {
  display: flex;
  flex-direction: column;
}

.box-card {
  width: 100%;
  max-width: 290px;
  background-color: #e6f7ff;
  flex: 1;
  overflow-y: auto;
  margin-left: auto;
}

.requirement-content {
  padding: 10px;
  background-color: #f9f9f9;
  border: 1px solid #ebebeb;
  border-radius: 4px;
  margin-top: 10px;
  margin-bottom: 20px;
  white-space: pre-wrap;
  min-height: 150px;
}

.page-footer {
  padding: 20px 0;
  text-align: center;
  background-color: #f0f2f5;
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding-left: 1em;
  margin-top: 10px;
}

.attachment-item {
  position: relative;
  a {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #606266;
    text-decoration: none;
    width: 80px;

    span {
      margin-top: 8px;
      font-size: 12px;
      text-align: center;
      word-break: break-all;
    }

    &:hover {
      color: #409eff;
    }
  }
}

.file-icon {
  width: 40px;
  height: 40px;
}

.remove-icon {
  position: absolute;
  top: -5px;
  right: -5px;
  cursor: pointer;
  color: #606266;
  background-color: #e4e7ed;
  border-radius: 50%;
  padding: 3px;
  font-size: 10px;
  &:hover {
    color: #ffffff;
    background-color: #f56c6c;
  }
}

.uploaded-list {
  margin-top: 10px;
}
</style> 