<template>
  <div v-if="!loading" class="content-wrapper">
    <div class="paper-preview-section">
      <!-- 问答题/测试 展示 -->
      <template v-if="paperData.contentType === '0'">
        <PaperResult :paper-id="paperId" :user-answers="studentAnswers" @loaded="handlePaperInfoLoaded" />
      </template>

      <!-- 附件作业 展示 -->
      <div v-else-if="paperData.contentType === '1'" class="attachment-assignment-wrapper">
        <div class="submitted-attachment-info">
          <div class="assignment-description">
            <h3>{{ paperData.title }}</h3>
            <div v-html="paperDetails.paperRequirement" class="requirement-content"></div>
          </div>
          <div class="submitted-files-list">
            <h4>您已提交的作业文件:</h4>
            <div class="attachments-list">
              <div v-for="file in submittedFiles" :key="file.fileId || file.fileUrl" class="attachment-item">
                <a href="#" @click.prevent="downloadFile(file)">
                  <img src="@/assets/icons/<EMAIL>" alt="file icon" class="file-icon" />
                  <span>{{ file.fileName }}</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <PaperDetails
      :paper-details="paperDetails"
      :download-file="downloadFile"
    />
  </div>
  <div v-else class="loading-placeholder">
    加载中...
  </div>
</template>

<script setup>
import { ref, computed, reactive, watch, defineProps, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import PaperResult from './PaperResult.vue';
import PaperDetails from './PaperDetails.vue';
import { getUserAnswer } from '@/api/edu/moocSmartCourseTestPaperAnswer';
import { getSubmissionByAssignmentId, getSubmission } from '@/api/edu/moocSmartCourseHomeworkSubmission';
import { getHomeworkOrTestWithPaper } from '@/api/edu/moocSmartCourseHomeworkOrTest';
import { getListByIds } from '@/api/edu/file';

const props = defineProps({
  paperId: {
    type: String,
    required: false
  },
  submissionId: {
    type: String,
    required: false
  }
});

const emit = defineEmits(['loaded']);

const loading = ref(true);
const studentAnswers = reactive({});
const submittedFiles = ref([]);

const paperData = reactive({
  title: '',
  requirement: '',
  type: 1, // 0 作业 1 测试
  score: '',
  submitTime: '',
  questionFiles: [],
  answerFiles: [],
  referenceAnswerContent: '',
  contentType: '0' // 0 作答题 1 附件
});

const isHomework = computed(() => paperData.type == 0);
const isTest = computed(() => paperData.type == 1);

const paperDetails = computed(() => ({
  isTest: isTest.value,
  isHomework: isHomework.value,
  paperRequirement: paperData.requirement,
  paperScore: paperData.score,
  paperSubmitTime: paperData.submitTime,
  questionFiles: paperData.questionFiles,
  referenceAnswerContent: paperData.referenceAnswerContent,
  answerFiles: paperData.answerFiles
}));

const initializeViewByPaperId = async (pId) => {
  loading.value = true;
  try {
    const paperInfoRes = await getHomeworkOrTestWithPaper(pId);
    const paperInfo = paperInfoRes.data;
    handlePaperInfoLoaded(paperInfo);

    if (paperInfo.contentType === '1' && paperInfo.assignmentType == 0) { // 附件作业
      const res = await getSubmissionByAssignmentId(pId);
      if (res.data) {
        submittedFiles.value = res.data.files || [];
      } else {
        ElMessage.warning('未找到附件提交记录。');
      }
    } else { // 问答题或测试
      const res = await getUserAnswer(pId);
      if (res.data && res.data.answerContent) {
        Object.assign(studentAnswers, JSON.parse(res.data.answerContent));
      } else {
         ElMessage.warning('未找到作答记录。');
      }
    }
  } catch (error) {
    console.error("提交内容查看器初始化失败:", error);
    ElMessage.error("加载提交内容失败，请稍后重试。");
  } finally {
    loading.value = false;
  }
};

const initializeViewBySubmissionId = async (sId) => {
  loading.value = true;
  try {
    // 假设 getSubmissionById 能返回完整的提交信息，包括关联的作业信息
    const submissionRes = await getSubmission(sId);
    const submissionData = submissionRes.data;

    if (!submissionData) {
      ElMessage.error("无法加载指定的提交记录。");
      loading.value = false;
      return;
    }
    
    // 用提交记录中的作业ID，获取作业的详细信息
    const paperInfoRes = await getHomeworkOrTestWithPaper(submissionData.assignmentId);
    const paperInfo = paperInfoRes.data;
    handlePaperInfoLoaded(paperInfo);

    // 根据内容类型填充提交详情
    if (paperData.contentType === '1') { // 附件作业
      submittedFiles.value = submissionData.files || [];
      if (submissionData.answerContent) {
        Object.assign(studentAnswers, JSON.parse(submissionData.answerContent));
      }
    } else { // 问答题
      if (submissionData.answerContent) {
        Object.assign(studentAnswers, JSON.parse(submissionData.answerContent));
      }
    }
    
    emit('loaded', { paper: paperInfo, submission: submissionData });

  } catch (error) {
    console.error("通过Submission ID初始化失败:", error);
    ElMessage.error("加载批改内容失败，请稍后重试。");
  } finally {
    loading.value = false;
  }
};

watch(
  () => props.paperId,
  (newPaperId) => {
    if (newPaperId) {
      initializeViewByPaperId(newPaperId);
    }
  },
  { immediate: true }
);

watch(
  () => props.submissionId,
  (newSubmissionId) => {
    if (newSubmissionId) {
      initializeViewBySubmissionId(newSubmissionId);
    }
  },
  { immediate: true }
);

const handlePaperInfoLoaded = (data) => {
  paperData.title = data.name;
  paperData.contentType = data.contentType;
  paperData.requirement = data.assignmentRequirement;
  paperData.type = data.assignmentType;
  paperData.score = data.score;
  paperData.submitTime = data.submitEndTime;
  paperData.referenceAnswerContent = data.referenceAnswerContent;

  const filePromises = [];
  if (data.attachedMaterialIds) {
    filePromises.push(getListByIds(data.attachedMaterialIds).then(res => { paperData.questionFiles = res.rows; }));
  }
  if (data.referenceAnswerMaterialIds) {
    filePromises.push(getListByIds(data.referenceAnswerMaterialIds).then(res => { paperData.answerFiles = res.rows; }));
  }

  Promise.all(filePromises).catch(error => {
    console.error('获取附件文件失败:', error);
    ElMessage.error('获取作业相关文件失败。');
  });
};

const downloadFile = async (file) => {
  ElMessage.info('正在准备下载...');
  const fileUrl = file.url || file.fileUrl;
  if (!fileUrl) {
    ElMessage.error('文件地址不存在！');
    return;
  }
  try {
    const response = await fetch(fileUrl);
    if (!response.ok) {
      throw new Error('网络响应错误');
    }
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', file.fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('下载失败:', error);
    ElMessage.error('文件下载失败。将在新标签页中打开文件。');
    window.open(fileUrl, '_blank');
  }
};
</script>

<style lang="scss" scoped>
.content-wrapper {
  display: flex;
  gap: 20px;
  height: 100%;
  overflow: hidden;
}

.loading-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 16px;
  color: #909399;
}

.paper-preview-section {
  flex: 3;
  background-color: #ffffff;
  border-radius: 4px;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.attachment-assignment-wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.submitted-attachment-info {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.assignment-description {
  flex-grow: 1;
  overflow-y: auto;
  padding-bottom: 20px;
}

.requirement-content {
  margin-top: 10px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
  min-height: 450px;
}

.submitted-files-list {
  flex-shrink: 0;
}

.file-icon {
  width: 40px;
  height: 40px;
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 10px;
}

.attachment-item {
  a {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #606266;
    text-decoration: none;
    width: 80px;

    span {
      margin-top: 8px;
      font-size: 12px;
      text-align: center;
      word-break: break-all;
    }

    &:hover {
      color: #409eff;
    }
  }
}
</style> 