<template>
  <div class="preview-container">
    <div class="paper-info">
      <h2 class="paper-title">{{ previewData.paperTitle }}</h2>
      <div class="paper-meta">
        <span>总分: {{ previewData.totalScore }}分</span>
        <el-divider direction="vertical" />
        <span>题目数量: {{ previewData.questionQuantity }}道</span>
      </div>
    </div>

    <el-divider />

    <!-- 题目列表 -->
    <div class="questions-list">
      <div v-for="(collection, collectionIndex) in previewData.groupedQuestions" 
            :key="collectionIndex" 
            class="question-collection">
        <div class="collection-header">
          <h3>{{ getQuestionTypeName(collection.questionType) }}
              （共{{ collection.questions.length }}题，共{{ collection.totalScore }}分）
          </h3>
        </div>
        <div v-for="(question, index) in collection.questions" 
              :key="question.questionId" 
              class="question-item">
          <div class="question-header">
            <span class="question-index">第{{ index+1 }}题</span>
            <span class="question-score">({{ question.score || 0 }}分)</span>
          </div>
          <QuestionAnswer :question="question" @answer-change="handleAnswerChange"/>
          <el-divider v-if="index !== collection.questions.length - 1" />
        </div>
        <el-divider v-if="collectionIndex !== Object.keys(previewData.groupedQuestions).length - 1" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, reactive } from 'vue';
import QuestionAnswer from '@/components/QuestionAnswer/index.vue';
import { getHomeworkOrTestWithPaper } from "@/api/edu/moocSmartCourseHomeworkOrTest";
import { questionTypeOptions } from '@/utils/optionUtil.js';

const questionTypeMap = questionTypeOptions.reduce((acc, item) => {
  acc[item.value] = item.label;
  return acc;
}, {});

const props = defineProps({
  paperId: {
    type: [String, Number],
    default: null
  }
});

const emit = defineEmits(['answer-update', 'loaded']);

const previewData = ref({
  paperTitle: '',
  totalScore: 0,
  questionQuantity: 0,
  groupedQuestions: []
});

const answers = reactive({});

const handleAnswerChange = (data) => {
  const { questionId, answer } = data;
  let questionData = null;

  for (const group of previewData.value.groupedQuestions) {
    const foundQuestion = group.questions.find(q => q.questionId === questionId);
    if (foundQuestion) {
      questionData = foundQuestion;
      break;
    }
  }
  
  answers[questionId] = {
    answer: answer,
    question: questionData
  };
  emit('answer-update', answers);
};

watch(() => props.paperId, (newVal) => {
  if (newVal) {
    loadPreviewData();
  }
}, { immediate: true });

const getQuestionTypeName = (type) => {
  return questionTypeMap[type] || '未知题型';
};

async function loadPreviewData() {
  try {
    const response = await getHomeworkOrTestWithPaper(props.paperId);
    const paperData = response.data;
    const questionsData = paperData.moocSmartCourseTestPaperQuestionCollectionList || [];
    const groupedQuestions = {};

    questionsData.forEach(collection => {
      collection.questionList.forEach(item => {
        const question = item.questionContent;
        const actualQuestionType = question.questionType;
        if (!groupedQuestions[actualQuestionType]) {
          groupedQuestions[actualQuestionType] = {
            questionType: actualQuestionType,
            questions: [],
            totalScore: 0,
            sort: collection.sort || 0
          };
        }
        
        const score = Number(item.questionScore || 0);
        groupedQuestions[actualQuestionType].questions.push({
          ...question,
          sort: item.sort,
          score: score
        });
        groupedQuestions[actualQuestionType].totalScore += score;
      });
    });

    const sortedGroupedQuestions = Object.values(groupedQuestions).sort((a, b) => (a.sort || 0) - (b.sort || 0));

    previewData.value = {
      paperTitle: paperData.paperTitle,
      totalScore: paperData.totalScore,
      questionQuantity: paperData.questionQuantity,
      groupedQuestions: sortedGroupedQuestions
    };
    emit('loaded', paperData);
  } catch (error) {
    console.error('获取试卷数据失败:', error);
    emit('load-error', '获取试卷数据失败');
  }
}
</script>

<style lang="scss" scoped>
.preview-container {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  width: 100%;
  display: flex;
  flex-direction: column;

  .paper-info {
    text-align: center;
    margin-bottom: 20px;

    .paper-title {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
      margin: 0 0 15px 0;
    }

    .paper-meta {
      color: #606266;
      font-size: 14px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .el-divider--vertical {
        display: none;
      }
    }
  }

  .questions-list {
    width: 100%;
    display: flex;
    flex-direction: column;
    
    .question-collection {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 24px;

      .collection-header {
        padding: 16px 0;
        margin-bottom: 20px;

        h3 {
          color: #333;
          font-size: 18px;
          font-weight: 600;
          text-align: left;
          margin: 0;
        }
      }

      .question-item {
        background-color: #fafafa;
        border-radius: 6px;
        padding: 16px;
        margin-bottom: 16px;

        .question-header {
          flex-direction: row;
          align-items: center;
          margin-bottom: 16px;
          border-bottom: 1px solid #e8e8e8;
          padding-bottom: 12px;

          .question-index {
            font-size: 16px;
            color: #1890ff;
            margin-right: 12px;
          }

          .question-score {
            color: #ff4d4f;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style> 