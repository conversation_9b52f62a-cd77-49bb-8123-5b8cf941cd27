<template>
  <div class="attachment-assignment-container">
    <h1 class="assignment-title">{{ title }}</h1>
    <div class="description-section">
      <h3>附件作业描述</h3>
      <el-input
        v-model="studentDescription"
        type="textarea"
        :rows="10"
        placeholder="请输入作业说明"
        class="description-content"
      />
    </div>
    <div class="upload-section">
      <h3>我的作业</h3>
      <el-upload
        v-model:file-list="fileList"
        class="upload-demo"
        multiple
        :http-request="httpRequest"
        :on-success="handleSuccess"
        :on-remove="handleRemove"
      >
        <el-button>上传附件</el-button>
      </el-upload>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { OssService } from '@/utils/aliOss.js';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  submission: {
    type: Object,
    required: false,
    default: () => ({})
  }
});

const emit = defineEmits(['files-updated', 'description-updated']);

const studentDescription = ref('');
const fileList = ref([]);
const submittedFiles = ref([]);

watch(() => props.submission, (newSubmission) => {
  if (newSubmission) {
    studentDescription.value = newSubmission.submitContent || '';
    if (newSubmission.files && newSubmission.files.length > 0) {
      submittedFiles.value = [...newSubmission.files];
      fileList.value = newSubmission.files.map(f => ({
        name: f.fileName,
        url: f.fileUrl,
        response: { data: { id: f.fileId } }
      }));
    } else {
      submittedFiles.value = [];
      fileList.value = [];
    }
  }
}, { immediate: true, deep: true });

watch(studentDescription, (newValue) => {
  emit('description-updated', newValue);
});

const emitFilesUpdate = () => {
  const filesToEmit = submittedFiles.value.map(f => ({
    fileName: f.fileName,
    fileUrl: f.fileUrl,
    fileType: f.fileType,
    fileSize: f.fileSize
  }));
  emit('files-updated', filesToEmit);
};

const httpRequest = async (options) => {
  try {
    const result = await OssService(options.file);
    options.onSuccess(result.result, options.file);
  } catch (err) {
    options.onError(err);
  }
};

const handleSuccess = (response, uploadFile) => {
  if (response.code === 200) {
    const newFile = {
      fileId: response.data.id,
      fileName: uploadFile.name,
      fileUrl: response.data.url,
      fileType: uploadFile.name.split('.').pop() || '',
      fileSize: uploadFile.size
    };
    submittedFiles.value.push(newFile);
    emitFilesUpdate();
  }
};

const handleRemove = (file) => {
  const fileIdToRemove = file.response?.data?.id;
  if (fileIdToRemove) {
    submittedFiles.value = submittedFiles.value.filter(f => f.fileId !== fileIdToRemove);
    emitFilesUpdate();
  }
};

</script>

<style lang="scss" scoped>
.assignment-title {
  text-align: left;
  margin-bottom: 25px;
  font-size: 24px;
}

.attachment-assignment-container {
  display: flex;
  flex-direction: column;
  min-height: 550px; /* As requested by user */
  gap: 20px;
  height: 100%;
}

.description-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;

  .description-content {
    flex: 1;
    :deep(textarea) {
      height: 100% !important;
      resize: none;
    }
  }
}

.upload-section {
  h3 {
    margin-bottom: 10px;
  }
}

h3 {
  margin-top: 0;
  margin-bottom: 10px;
}
</style> 