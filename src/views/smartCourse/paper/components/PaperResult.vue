<template>
  <div class="result-container">
    <div class="paper-info">
      <h2 class="paper-title">{{ previewData.paperTitle }}</h2>
      <div class="paper-meta">
        <span>总分: {{ previewData.totalScore }}分</span>
        <el-divider direction="vertical" />
        <span>题目数量: {{ previewData.questionQuantity }}道</span>
      </div>
    </div>
    <el-divider />
    <div class="questions-list">
      <div v-for="(collection, collectionIndex) in previewData.groupedQuestions" :key="collectionIndex" class="question-collection">
        <div class="collection-header">
          <h3>{{ getQuestionTypeName(collection.questionType) }}
              （共{{ collection.questions.length }}题，共{{ collection.totalScore }}分）
          </h3>
        </div>
        <div v-for="(question, index) in collection.questions" :key="question.questionId" class="question-item">
          <div class="question-header">
            <span class="question-index">第{{ index + 1 }}题</span>
            <span class="question-score">({{ question.score || 0 }}分)</span>
          </div>
          <QuestionResult :question="question" :user-answer="userAnswers[question.questionId]" />
          <el-divider v-if="index !== collection.questions.length - 1" />
        </div>
        <el-divider v-if="collectionIndex !== Object.keys(previewData.groupedQuestions).length - 1" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import QuestionResult from '@/components/QuestionResult/index.vue';
import { getHomeworkOrTestWithPaper } from "@/api/edu/moocSmartCourseHomeworkOrTest";
import { questionTypeOptions } from '@/utils/optionUtil.js';

const emit = defineEmits(['loaded']);

const questionTypeMap = questionTypeOptions.reduce((acc, item) => {
  acc[item.value] = item.label;
  return acc;
}, {});

const props = defineProps({
  paperId: {
    type: [String, Number],
    required: true
  },
  userAnswers: {
    type: Object,
    required: true
  }
});

const previewData = ref({
  paperTitle: '',
  totalScore: 0,
  questionQuantity: 0,
  groupedQuestions: []
});

watch(() => props.paperId, (newVal) => {
  if (newVal) {
    loadResultData();
  }
}, { immediate: true });

const getQuestionTypeName = (type) => {
  return questionTypeMap[type] || '未知题型';
};

async function loadResultData() {
  try {
    const response = await getHomeworkOrTestWithPaper(props.paperId);
    const paperData = response.data;
    const questionsData = paperData.moocSmartCourseTestPaperQuestionCollectionList || [];
    const groupedQuestions = {};

    questionsData.forEach(collection => {
      collection.questionList.forEach(item => {
        const question = item.questionContent;
        const actualQuestionType = question.questionType;
        if (!groupedQuestions[actualQuestionType]) {
          groupedQuestions[actualQuestionType] = {
            questionType: actualQuestionType,
            questions: [],
            totalScore: 0,
            sort: collection.sort || 0
          };
        }
        
        const score = Number(item.questionScore || 0);
        groupedQuestions[actualQuestionType].questions.push({
          ...question,
          sort: item.sort,
          score: score
        });
        groupedQuestions[actualQuestionType].totalScore += score;
      });
    });

    const sortedGroupedQuestions = Object.values(groupedQuestions).sort((a, b) => (a.sort || 0) - (b.sort || 0));

    previewData.value = {
      paperTitle: paperData.paperTitle,
      totalScore: paperData.totalScore,
      questionQuantity: paperData.questionQuantity,
      groupedQuestions: sortedGroupedQuestions
    };
    emit('loaded', paperData);
  } catch (error) {
    console.error('获取试卷数据失败:', error);
  }
}
</script>

<style lang="scss" scoped>
/* Styles are similar to PaperAnswer.vue */
.result-container {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.paper-info {
  text-align: center;
  margin-bottom: 20px;
}
.paper-title {
  font-size: 24px;
  font-weight: bold;
}
.paper-meta {
  color: #606266;
  font-size: 14px;
}
.questions-list {
  /* styles for questions list */
}
.question-collection {
  margin-bottom: 20px;
}
.collection-header h3 {
  font-size: 18px;
  font-weight: 600;
}
.question-item {
  margin-bottom: 15px;
}
.question-header {
  margin-bottom: 10px;
}
</style> 