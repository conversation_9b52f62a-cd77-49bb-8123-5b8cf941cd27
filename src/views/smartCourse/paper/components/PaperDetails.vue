<template>
  <div class="paper-details-section">
    <div class="right-panel">
      <el-card class="box-card">
        <div class="text item">
          <p v-if="paperDetails.isTest"><strong>测试要求:</strong></p>
          <p v-if="paperDetails.isHomework"><strong>作业要求:</strong></p>
          <p class="reference-answer">{{ paperDetails.paperRequirement }}</p>
          <p v-if="paperDetails.isHomework">作业分值:{{ paperDetails.paperScore }}</p>
          <p v-if="paperDetails.isHomework">提交时间:{{ paperDetails.paperSubmitTime }}</p>
          <div v-if="paperDetails.questionFiles.length > 0">
            <p><strong>题目附件:</strong></p>
            <div class="attachments-list">
              <div v-for="file in paperDetails.questionFiles" :key="file.fileId" class="attachment-item">
                <a :href="file.url" @click.prevent="downloadFile(file)">
                  <img src="@/assets/icons/<EMAIL>" alt="file icon" class="file-icon" />
                  <span>{{ file.fileName }}</span>
                </a>
              </div>
            </div>
          </div>
          <div v-if="paperDetails.referenceAnswerContent">
            <p><strong>参考答案:</strong></p>
            <div class="reference-answer">
              {{ paperDetails.referenceAnswerContent }}
            </div>
          </div>
          <div v-if="paperDetails.answerFiles.length > 0">
            <p><strong>参考答案附件:</strong></p>
            <div class="attachments-list">
              <div v-for="file in paperDetails.answerFiles" :key="file.fileId" class="attachment-item">
                <a :href="file.url" @click.prevent="downloadFile(file)">
                  <img src="@/assets/icons/<EMAIL>" alt="file icon" class="file-icon" />
                  <span>{{ file.fileName }}</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
defineProps({
  paperDetails: {
    type: Object,
    required: true
  },
  downloadFile: {
    type: Function,
    required: true
  }
});
</script>

<style lang="scss" scoped>
.paper-details-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.right-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.box-card {
  width: 100%;
  background-color: #e6f7ff;
}

.file-icon {
  width: 40px;
  height: 40px;
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding-left: 1em;
}

.attachment-item {
  a {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #606266;
    text-decoration: none;
    width: 80px;

    span {
      margin-top: 8px;
      font-size: 12px;
      text-align: center;
      word-break: break-all;
    }

    &:hover {
      color: #409eff;
    }
  }
}

.reference-answer {
  padding: 10px;
  background-color: #f9f9f9;
  border: 1px solid #ebebeb;
  border-radius: 4px;
  margin-top: 10px;
  white-space: pre-wrap;
}
</style> 