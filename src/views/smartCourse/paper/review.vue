<template>
  <div>
    <!-- 面包屑和返回按钮 -->
    <div class="filters">
      <div class="filter-content">
        <el-breadcrumb :separator-icon="ArrowRight" class="breadcrumb_text">
          <el-breadcrumb-item>首页</el-breadcrumb-item>
          <el-breadcrumb-item>课程</el-breadcrumb-item>
          <el-breadcrumb-item>作业批改</el-breadcrumb-item>
          <el-breadcrumb-item>
            {{ paperData.title || '批改详情' }}
          </el-breadcrumb-item>
        </el-breadcrumb>
        <el-button class="btn" type="primary" @click="goBack">返回</el-button>
      </div>
    </div>

    <div class="paper-page-container">
      <el-card class="review-wrapper-card">
 
        <div class="review-content-wrapper">
          <!-- 左侧：学生提交内容展示 -->
          <div class="paper-preview-section">
            <template v-if="paperData.contentType === '0'">
              <PaperResult :paper-id="paperData.assignmentId" :user-answers="studentAnswers" @loaded="handlePaperInfoLoaded" />
            </template>
            <div v-else-if="paperData.contentType === '1'" class="attachment-assignment-wrapper">

              <div class="submitted-attachment-info">
                <div class="assignment-description">
                  <h3>{{ paperData.title }}        <span v-if="countdown.timeLeft > 0" class="countdown-tag">
              距互评结束还有{{ countdown.days }}天{{ countdown.hours }}小时
            </span></h3>
                  
                  <div v-html="paperDetails.paperRequirement" class="requirement-content"></div>
                </div>
                <div class="submitted-files-list">
                  <h4>学生提交的作业文件:</h4>
                  <div class="attachments-list">
                    <div v-for="file in submittedFiles" :key="file.fileId || file.fileUrl" class="attachment-item">
                      <a href="#" @click.prevent="downloadFile(file)">
                        <img src="@/assets/icons/<EMAIL>" alt="file icon" class="file-icon" />
                        <span>{{ file.fileName }}</span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：详情与评分 -->
          <div class="details-and-grading-section">
            <PaperDetails
              :paper-details="paperDetails"
              :download-file="downloadFile"
            />
      
              <el-form :model="gradingForm" label-position="top">
                <el-form-item label="分数">
                  <div style="display: flex; align-items: center;">
                    <el-input-number v-model="gradingForm.score" :min="0" :max="paperData.score || 100" :disabled="isGraded" />
                    <el-button type="primary" @click="submitGrade" :disabled="isGraded || !submissionData" style="margin-left: 10px;">打分</el-button>
                  </div>
                </el-form-item>
              </el-form>
        
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onUnmounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ArrowRight } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { addMutualEvaluation, listMutualEvaluation } from '@/api/edu/mutualEvaluation';
import useUserStore from '@/store/modules/user';

import PaperResult from './components/PaperResult.vue';
import PaperDetails from './components/PaperDetails.vue';
import { getSubmission } from '@/api/edu/moocSmartCourseHomeworkSubmission';
import { getHomeworkOrTestWithPaper } from '@/api/edu/moocSmartCourseHomeworkOrTest';
import { getListByIds } from '@/api/edu/file';


const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

const submissionId = computed(() => route.params.submissionId); 
const submissionData = ref(null);
const loading = ref(true);
const studentAnswers = reactive({});
const submittedFiles = ref([]);
const isGraded = ref(false);

const paperData = reactive({
  assignmentId: null,
  title: '',
  requirement: '',
  type: 1,
  score: 100,
  submitTime: '',
  questionFiles: [],
  answerFiles: [],
  referenceAnswerContent: '',
  contentType: '0',
  gradingEndTime: '',
  testPaperId: null,
  courseId: null,
  classId: null,
  gradingMethod: null
});

const isHomework = computed(() => paperData.type == 0);
const isTest = computed(() => paperData.type == 1);

const paperDetails = computed(() => ({
  isTest: isTest.value,
  isHomework: isHomework.value,
  paperRequirement: paperData.requirement,
  paperScore: paperData.score,
  paperSubmitTime: paperData.submitTime,
  questionFiles: paperData.questionFiles,
  referenceAnswerContent: paperData.referenceAnswerContent,
  answerFiles: paperData.answerFiles
}));

const gradingForm = reactive({
  score: 0,
});

const countdown = reactive({
  timeLeft: 0,
  days: 0,
  hours: 0,
  timer: null
});

const initializeView = async (sId) => {
  loading.value = true;
  try {
    const submissionRes = await getSubmission(sId);
    submissionData.value = submissionRes.data;

    if (!submissionData.value) {
      ElMessage.error("无法加载指定的提交记录。");
      loading.value = false;
      return;
    }

    const evaluationQuery = {
      assignmentId: submissionData.value.assignmentId,
      fromEvaluationId: userStore.id,
      toEvaluationId: submissionData.value.studentId,
    };
    const evalRes = await listMutualEvaluation(evaluationQuery);
    if (evalRes.rows && evalRes.rows.length > 0) {
      gradingForm.score = evalRes.rows[0].score;
      isGraded.value = true;
    } else {
      gradingForm.score = 0;
      isGraded.value = false;
    }
    
    const paperInfoRes = await getHomeworkOrTestWithPaper(submissionData.value.assignmentId);
    const paperInfo = paperInfoRes.data;
    handlePaperInfoLoaded(paperInfo);
    
    if (paperInfo.contentType === '1') {
      submittedFiles.value = submissionData.value.files || [];
    } else {
      if (submissionData.value.answerContent) {
        Object.assign(studentAnswers, JSON.parse(submissionData.value.answerContent));
      }
    }
    
    if (paperInfo.gradingEndTime) {
      startCountdown(paperInfo.gradingEndTime);
    }

  } catch (error) {
    console.error("加载批改内容失败:", error);
    ElMessage.error("加载批改内容失败，请稍后重试。");
  } finally {
    loading.value = false;
  }
};

watch(
  submissionId,
  (newId) => {
    if (newId) {
      initializeView(newId);
    }
  },
  { immediate: true }
);

const handlePaperInfoLoaded = (data) => {
  paperData.assignmentId = data.assignmentId;
  paperData.title = data.name;
  paperData.contentType = data.contentType;
  paperData.requirement = data.assignmentRequirement;
  paperData.type = data.assignmentType;
  paperData.score = data.score;
  paperData.submitTime = data.submitEndTime;
  paperData.referenceAnswerContent = data.referenceAnswerContent;
  paperData.gradingEndTime = data.gradingEndTime;
  paperData.testPaperId = data.testPaperId;
  paperData.courseId = data.courseId;
  paperData.classId = data.classId;
  paperData.gradingMethod = data.gradingMethod;
  
  const filePromises = [];
  if (data.attachedMaterialIds) {
    filePromises.push(getListByIds(data.attachedMaterialIds).then(res => { paperData.questionFiles = res.rows; }));
  }
  if (data.referenceAnswerMaterialIds) {
    filePromises.push(getListByIds(data.referenceAnswerMaterialIds).then(res => { paperData.answerFiles = res.rows; }));
  }

  Promise.all(filePromises).catch(error => {
    console.error('获取附件文件失败:', error);
    ElMessage.error('获取作业相关文件失败。');
  });
};

const startCountdown = (endTime) => {
  if (!endTime) {
    return;
  }
  if (countdown.timer) clearInterval(countdown.timer);

  const end = new Date(endTime.replace(/-/g, '/')).getTime();

  if (isNaN(end)) {
    console.error('无效的截止日期格式:', endTime);
    return;
  }

  const update = () => {
    const now = new Date().getTime();
    const timeLeft = end - now;
    countdown.timeLeft = timeLeft;

    if (timeLeft <= 0) {
      countdown.days = 0;
      countdown.hours = 0;
      clearInterval(countdown.timer);
      return;
    }

    countdown.days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
    countdown.hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  };

  update();
  countdown.timer = setInterval(update, 1000 * 60 * 60); // Update every hour
};

onUnmounted(() => {
  if (countdown.timer) clearInterval(countdown.timer);
});

const goBack = () => {
  router.back();
};

const submitGrade = async () => {
  if (!submissionData.value || !paperData.assignmentId) {
    ElMessage.error('数据加载中，请稍候');
    return;
  }

  try {
    await ElMessageBox.confirm('确定打分吗？', '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    const payload = {
      assignmentId: paperData.assignmentId,
      testPaperId: paperData.testPaperId,
      courseId: paperData.courseId,
      classId: paperData.classId,
      gradingMethod: paperData.gradingMethod,
      fromEvaluationId: userStore.id,
      toEvaluationId: submissionData.value.studentId,
      score: gradingForm.score,
    };
    
    await addMutualEvaluation(payload);

    ElMessage.success('批改结果提交成功！');
    initializeView(submissionId.value);

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批改提交失败:', error);
      ElMessage.error('提交失败，请稍后重试。');
    } else {
      ElMessage.info('已取消提交');
    }
  }
};

const downloadFile = async (file) => {
  ElMessage.info('正在准备下载...');
  const fileUrl = file.url || file.fileUrl;
  if (!fileUrl) {
    ElMessage.error('文件地址不存在！');
    return;
  }
  try {
    const response = await fetch(fileUrl);
    if (!response.ok) {
      throw new Error('网络响应错误');
    }
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', file.fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('下载失败:', error);
    ElMessage.error('文件下载失败。将在新标签页中打开文件。');
    window.open(fileUrl, '_blank');
  }
};
</script>

<style lang="scss" scoped>
.filters {
  width: 90%;
  margin: -50px auto 20px auto;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  box-sizing: border-box;
  width: 95%;
  margin-left: 2.5%;
}

.countdown-tag {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fde2e2;
  border-radius: 4px;
  padding: 2px 8px;
  margin-left: 12px;
  font-size: 12px;
  font-weight: normal;
}

.breadcrumb_text {
  font-size: 14px;
  font-weight: 400;
  line-height: 40px;
}

.btn {
  height: 40px;
  width: 120px;
  border-radius: 4px;
}

.paper-page-container {
  padding: 0 20px 20px 20px;
  background-color: #f0f2f5;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 150px);
  align-items: center;
}

.review-wrapper-card {
  width: 90%;
  flex: 1;
  overflow: hidden;
  :deep(.el-card__body) {
    height: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;
  }
}

.review-content-wrapper {
  display: flex;
  gap: 20px;
  height: 100%;
  overflow: hidden;
  flex: 1;
}

.paper-preview-section {
  flex: 3;
  background-color: #ffffff;
  border-radius: 4px;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  border: 1px solid #ebeef5;
}

.details-and-grading-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.grading-card {
  flex-shrink: 0;
}

.card-header {
  font-weight: bold;
}

.attachment-assignment-wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.submitted-attachment-info {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.assignment-description {
  flex-grow: 1;
  overflow-y: auto;
  padding-bottom: 20px;
}

.requirement-content {
  margin-top: 10px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
  min-height: 450px;
}

.submitted-files-list {
  flex-shrink: 0;
}

.file-icon {
  width: 40px;
  height: 40px;
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 10px;
}

.attachment-item a {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #606266;
  text-decoration: none;
  width: 80px;
}

.attachment-item span {
  margin-top: 8px;
  font-size: 12px;
  text-align: center;
  word-break: break-all;
}

.attachment-item a:hover {
  color: #409eff;
}
</style> 