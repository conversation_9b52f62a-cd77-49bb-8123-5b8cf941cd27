<template>
  <div>
    <div class="filters">
      <div class="filter-content">
        <el-breadcrumb :separator-icon="ArrowRight" class="breadcrumb_text">
          <el-breadcrumb-item>首页</el-breadcrumb-item>
          <el-breadcrumb-item>课程</el-breadcrumb-item>
          <el-breadcrumb-item>我的班课列表</el-breadcrumb-item>
          <el-breadcrumb-item>{{ pageTitle }}</el-breadcrumb-item>
        </el-breadcrumb>
        <el-button class="btn" type="primary" @click="goBack">返回</el-button>
      </div>
    </div>
    <div class="paper-page-container">
      <el-card class="content-wrapper-card">
        <div v-if="!isSubmitted" class="content-wrapper">
          <div class="paper-preview-section">
            <template v-if="assignmentContentType === '0'">
              <PaperAnswer :paper-id="paperId" @answer-update="handleAnswerUpdate" @loaded="handlePaperLoaded" />
            </template>
            <div v-else-if="assignmentContentType === '1'" class="attachment-assignment-wrapper">
              <AttachmentAssignment
                :title="paperData.title"
                :description="paperDetails.paperRequirement"
                @files-updated="updateStudentFiles"
                :existing-files="[]"
              />
            </div>
          </div>
          <PaperDetails
            :paper-details="paperDetails"
            :download-file="downloadFile"
          />
        </div>
        <SubmissionContentViewer v-else :paper-id="paperId" />
      </el-card>
      <div class="page-footer">
        <el-button v-if="!isSubmitted" type="primary" size="large" @click="submitAnswers" :disabled="isSubmitDisabled">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ArrowRight } from '@element-plus/icons-vue';
import PaperAnswer from './components/PaperAnswer.vue';
import PaperDetails from './components/PaperDetails.vue';
import AttachmentAssignment from './components/AttachmentAssignment.vue';
import SubmissionContentViewer from './components/SubmissionContentViewer.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { addAnswer, getUserAnswer } from '@/api/edu/moocSmartCourseTestPaperAnswer';
import { addSubmission, getSubmissionByAssignmentId } from '@/api/edu/moocSmartCourseHomeworkSubmission';
import { getHomeworkOrTestWithPaper } from '@/api/edu/moocSmartCourseHomeworkOrTest';
import useUserStore from '@/store/modules/user';
import { getListByIds } from '@/api/edu/file';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

const paperId = computed(() => route.params.paperId);
const homeworkShowId = computed(() => route.params.homeworkShowId);
const userId = computed(() => userStore.id);

const isSubmitted = ref(false);
const studentAnswers = reactive({});
const totalQuestions = ref(0);
const studentFiles = ref([]);

const paperData = reactive({
  title: '',
  requirement: '',
  type: 1, // 0 作业 1 测试
  score: '',
  submitTime: '',
  questionFiles: [],
  answerFiles: [],
  referenceAnswerContent: '',
  contentType: '0' // 0 作答题 1 附件
});

const isHomework = computed(() => paperData.type == 0);
const isTest = computed(() => paperData.type == 1);
const assignmentContentType = computed(() => paperData.contentType);
const pageTitle = computed(() => {
  if (!paperData.title) return '加载中...';
  return `${isTest.value ? '测试' : '作业'}详情: ${paperData.title}`;
});

const isSubmitDisabled = computed(() => {
  if (assignmentContentType.value === '1') {
    return studentFiles.value.length === 0;
  }
  return false;
});

const paperDetails = computed(() => ({
  isTest: isTest.value,
  isHomework: isHomework.value,
  paperRequirement: paperData.requirement,
  paperScore: paperData.score,
  paperSubmitTime: paperData.submitTime,
  questionFiles: paperData.questionFiles,
  referenceAnswerContent: paperData.referenceAnswerContent,
  answerFiles: paperData.answerFiles
}));

const initializePage = async (pId) => {
  // 1. 重置状态
  isSubmitted.value = false;
  studentFiles.value = [];
  Object.keys(studentAnswers).forEach(key => delete studentAnswers[key]);

  try {
    // 2. 获取作业/测试基础信息，得到contentType
    const paperInfo = await getHomeworkOrTestWithPaper(pId);
    handlePaperLoaded(paperInfo.data);

    // 3. 根据contentType检查提交状态
    if (assignmentContentType.value === '1' && isHomework.value) {
      // 附件作业
      const res = await getSubmissionByAssignmentId(pId);
      if (res.data) {
        isSubmitted.value = true;
      }
    } else {
      // 传统问答题或测试
      const res = await getUserAnswer(pId);
      if (res.data) {
        isSubmitted.value = true;
      }
    }
  } catch (error) {
    console.error("页面初始化失败:", error);
    ElMessage.error("加载作业信息失败，请稍后重试。");
  }
};

watch(
  () => paperId.value,
  (newPaperId) => {
    if (newPaperId) {
      initializePage(newPaperId);
    }
  },
  { immediate: true }
);

const handlePaperLoaded = (data) => {
  if (data.questionQuantity) {
    totalQuestions.value = data.questionQuantity;
  }

  console.log('拉取数据', data);

  paperData.title = data.name;
  paperData.contentType = data.contentType;
  paperData.requirement = data.assignmentRequirement;
  paperData.type = data.assignmentType;
  paperData.score = data.score;
  paperData.submitTime = data.submitEndTime;
  paperData.referenceAnswerContent = data.referenceAnswerContent;

  const filePromises = [];
  if (data.attachedMaterialIds) {
    filePromises.push(getListByIds(data.attachedMaterialIds).then(res => { paperData.questionFiles = res.rows; }));
  }
  if (data.referenceAnswerMaterialIds) {
    filePromises.push(getListByIds(data.referenceAnswerMaterialIds).then(res => { paperData.answerFiles = res.rows; }));
  }

  // 不在此处等待Promise.all，因为这只是加载辅助信息
  Promise.all(filePromises).catch(error => {
    console.error('获取附件文件失败:', error);
    ElMessage.error('获取作业相关文件失败。');
  });
};

const handleAnswerUpdate = (answers) => {
  Object.assign(studentAnswers, answers);
};

const updateStudentFiles = (files) => {
  studentFiles.value = files.map(f => ({
    fileName: f.name || f.fileName,
    fileUrl: f.url || f.fileUrl,
    fileType: f.raw ? f.raw.type : f.fileType,
    fileSize: f.size || f.fileSize
  }));
};

const submitAnswers = () => {
  if (isHomework.value && assignmentContentType.value === '1') {
    submitAttachmentHomework();
  } else {
    submitQuizOrAnswer();
  }
};

const submitAttachmentHomework = () => {
  const confirmMessage = '确定要提交吗?';

  ElMessageBox.confirm(confirmMessage, '提交确认', {
    confirmButtonText: '确定提交',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const payload = {
        assignmentId: paperId.value,
        homeworkShowId: homeworkShowId.value,
        files: studentFiles.value,
        submitContent: "附件作业提交"
      };

      await addSubmission(payload);

      ElMessage.success('作业提交成功！');
      isSubmitted.value = true;
      // 提交成功后，视图将切换到Viewer，由其自行获取最新数据
    } catch (error) {
      console.error('作业提交失败:', error);
      ElMessage.error('作业提交失败，请稍后再试。');
    }
  }).catch(() => {
    ElMessage.info('已取消提交');
  });
};

const submitQuizOrAnswer = () => {
  let confirmMessage = '确定要提交吗?';
  const dataToSubmit = {
    assignmentId: paperId.value,
    homeworkShowId: homeworkShowId.value,
    answerType: '1' // 1: 待批改
  };
  let successMessage = '作业已提交！';
  let errorMessage = '作业提交失败，请稍后再试。';

  const answeredCount = Object.values(studentAnswers).filter(
    (item) => {
      if (Array.isArray(item.answer)) {
        return item.answer.length > 0;
      }
      return item.answer !== null && item.answer !== undefined && item.answer !== '';
    }
  ).length;

  if (answeredCount < totalQuestions.value) {
    confirmMessage = `您还有 ${totalQuestions.value - answeredCount} 道题目未完成，确定要提交吗?`;
  }
  dataToSubmit.answerContent = JSON.stringify(studentAnswers);
  successMessage = '答案已提交！现在为您展示答题结果。';
  errorMessage = '答案提交失败，请稍后再试。';

  ElMessageBox.confirm(confirmMessage, '提交确认', {
    confirmButtonText: '确定提交',
    cancelButtonText: '继续答题',
    type: 'warning'
  }).then(() => {
    addAnswer(dataToSubmit).then(() => {
      ElMessage.success(successMessage);
      isSubmitted.value = true;
    }).catch(error => {
      console.error('提交失败:', error);
      ElMessage.error(errorMessage);
    });
  }).catch(() => {
    ElMessage.info('已取消提交');
  });
};

const goBack = () => {
  router.back();
};

const downloadFile = async (file) => {
  ElMessage.info('正在准备下载...');
  const fileUrl = file.url || file.fileUrl;
  if (!fileUrl) {
    ElMessage.error('文件地址不存在！');
    return;
  }
  try {
    const response = await fetch(fileUrl);
    if (!response.ok) {
      throw new Error('网络响应错误');
    }
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', file.fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('下载失败:', error);
    ElMessage.error('文件下载失败。将在新标签页中打开文件。');
    window.open(fileUrl, '_blank');
  }
};
</script>

<style lang="scss" scoped>
.filters {
  width: 70%;
  margin: -50px auto 20px auto;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  box-sizing: border-box;
  width: 95%;
  margin-left: 2.5%;
}

.breadcrumb_text {
  font-size: 14px;
  font-weight: 400;
  line-height: 40px;
}

.btn {
  height: 40px;
  width: 120px;
  border-radius: 4px;
}

.paper-page-container {
  padding: 0 20px 20px 20px;
  background-color: #f0f2f5;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 150px);
  align-items: center;
}

.content-wrapper-card {
  width: 70%;
  flex: 1;
  overflow: hidden;
  :deep(.el-card__body) {
    height: 100%;
    padding: 20px;
  }
  flex-direction: column;
  gap: 20px;
}

.content-wrapper {
  display: flex;
  gap: 20px;
  height: 100%;
  overflow: hidden;
}

.paper-preview-section {
  flex: 3;
  background-color: #ffffff;
  border-radius: 4px;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.attachment-assignment-wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.submitted-attachment-info {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.assignment-description {
  flex-grow: 1;
  overflow-y: auto;
  padding-bottom: 20px;
}

.requirement-content {
  margin-top: 10px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
  min-height: 450px;
}

.submitted-files-list {
  flex-shrink: 0;
}

.file-icon {
  width: 40px;
  height: 40px;
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 10px;
}

.attachment-item {
  a {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #606266;
    text-decoration: none;
    width: 80px;

    span {
      margin-top: 8px;
      font-size: 12px;
      text-align: center;
      word-break: break-all;
    }

    &:hover {
      color: #409eff;
    }
  }
}

.page-footer {
  padding: 20px 0;
  text-align: center;
  background-color: #f0f2f5;
}
</style>