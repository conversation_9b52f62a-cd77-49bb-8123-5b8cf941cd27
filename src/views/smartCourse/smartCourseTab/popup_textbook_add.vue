<template>
  <el-dialog v-model="dialogVisible" title="创建教材任务" width="800" :before-close="handleClose" style="margin-top: 10vh !important">
    <el-form ref="formRef" style="max-width: 600px" :model="form" label-width="auto" class="demo-ruleForm" :rules="rules">
      <el-form-item label="标题" prop="missionTitle">
        <el-input v-model="form.missionTitle" type="text" autocomplete="off" placeholder="请输入标题" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" :rows="5" type="textarea" show-word-limit="true" maxlength="500" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="学习内容" prop="missionChapterIds">
        <el-tree
            style="max-width: 600px"
            ref="treeRef"
            :data="chapterList"
            show-checkbox
            v-model="form.missionChapterIds"
            accordion
            :props="{
              children: 'children',
              label: item => item.name,
              disabled: isNodeDisabled
            }"
            :default-checked-keys="initialCheckedKeys"
            node-key="chapterId"
            check-strictly
            @check="handleCheckChange"
            :check-descendants="false"
        >
        </el-tree>
      </el-form-item>
      <el-form-item label="学习要求描述" prop="missionDescribe">
        <el-input v-model="form.missionDescribe" :rows="5" type="textarea" show-word-limit="true" maxlength="500" placeholder="请输入学习要求描述" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="form.missionId" @click="confirmFrom(0)">保存</el-button>
        <el-button v-else @click="confirmFrom(1)">保存</el-button>
        <el-button type="primary" @click="confirmFrom(2)"> 开始 </el-button>
      </div>
    </template>
  </el-dialog>

  <PopupTextbookStart ref="popupTextbookStart" @formValue="form" @refreshList="refreshLists" />
</template>

<script setup>
import {getCurrentInstance, nextTick, ref} from 'vue'
import {listChapter} from "@/api/book/chapter";
import {addMission, updateMission,getMission} from "@/api/edu/mission.js";
import PopupTextbookStart from "@/views/smartCourse/smartCourseTab/popup_textbook_start.vue";
const { proxy } = getCurrentInstance()
const dialogVisible = ref(false)
const formRef = ref(null)
const props = defineProps({
  /*  course: {
      type: Object,mission
      default: {},
    }*/
  classDetail: {
    type: Object,
    default: {
    },
  },
  /*测试用*/
  course: {
    type: Object,
    default: {
      /*      "createBy": null,
            "createTime": null,
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "courseId": "11",
            "courseName": "网络安全互动课堂用",
            "courseCode": "SEC1012",
            "courseType": 1,
            "categoryId": "101",
            "planId": "210",
            "bookId": "310",
            "description": "了解网络安全基础知识",
            "coverImageUrl": "https://example.com/images/sec101.jpg",
            "classroomQuestionWeight": 25,
            "homeworkWeight": 35,
            "examWeight": 40,
            "hour": 48,
            "status": 1,
            "userId": "1854076781086117972",
            "createdBy": null,
            "updatedBy": null,
            "delFlag": null,
            "classTotal": 3,
            "realName": "13840917725",
            "searchVar": null*/
      /*"createBy": null,
      "createTime": null,
      "updateBy": null,
      "updateTime": null,
      "remark": null,
      "courseId": "1943215209544761346",
      "courseName": "第六个教务课程",
      "courseCode": "999",
      "courseType": 1,
      "categoryId": "1912780772558868481",
      "planId": null,
      "bookId": "1893128341502074882",
      "description": "99",
      "coverImageUrl": "https://dutp-test.oss-cn-beijing.aliyuncs.com/1752133581962.png",
      "classroomQuestionWeight": null,
      "homeworkWeight": null,
      "examWeight": null,
      "hour": 1,
      "status": 0,
      "userId": "1854076781086117925",
      "createdBy": null,
      "updatedBy": null,
      "delFlag": null,
      "classTotal": 0,
      "realName": "学生(雪)1",
      "searchVar": null*/
    },
  },
});
const initialCheckedKeys = ref([])
const form = ref({
  missionTitle: '',
  remark: '',
  missionChapterId: '',
  missionDescribe: '',
  // classId:props.classDetail.classId
  classId:25,
  missionChapterIds:[]
})
const chapterList = ref([])
const rules = ref({
  missionTitle: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
  missionChapterIds: [{ type: 'array', min: 1, message: '学习内容不能为空', trigger: 'change' }]
})
const emits = defineEmits(['refreshList'])
const treeRef = ref(null)
// 记录上一次选中的 key
const lastCheckedKey = ref(null)
const editFlag = ref(null)
const missionId = ref(null)
const popupTextbookStart = ref(null)
// 开启弹窗
const open = (val,id) => {
  console.log(id)
  dialogVisible.value = true
  if (formRef.value) formRef.value.resetFields()
  /*获取教材章节目录*/
  getBookChapterList()
  if (val == 2) {
    missionId.value = id
    // 获取教材任务详情
    getMissionDetail()
  }
}

const getMissionDetail = async () => {
  let response = await getMission(missionId.value)
  console.log(response)
  form.value = response.data
  form.value.missionChapterIds = form.value.missionChapterId.split(",")
  initialCheckedKeys.value = form.value.missionChapterId.split(",")
  console.log(initialCheckedKeys.value)
  form.value.missionChapterId = response.data.missionChapterId;
  loadCheckedOptions()
}

const loadCheckedOptions = () => {
  console.log(initialCheckedKeys.value)
  nextTick(() => {
    proxy.$refs.treeRef.setCheckedNodes(initialCheckedKeys.value)
  })

}



const isNodeDisabled = (node) => {
  return node.parentId !== '0';
}

/** 获取章节目录*/
const getBookChapterList = async () => {
  let response = await listChapter({bookId: props.course.bookId})
  console.log(response)
  chapterList.value = response.data
  if (missionId.value) {

  } else {
    chapterList.value.map(item => {
      if (item.parentId != 0) {
        item.disabled = true
      } else {
        item.disabled = false
      }
    })
  }

  // if (missionId.value) {
  //   console.log(form.value.missionChapterId)
  //   chapterList.value.forEach((chapter) => {
  //     if (chapter.chapterId == form.value.missionChapterId) {
  //       chapter.checked = true
  //     }
  //   })
  // }
}

// function disableChildCheckbox(data) {
//   return data.map(item => {
//     item.disabled = (item.parentId !== '0'); // 只有一级节点可勾选，其余禁用
//     if (item.children) {
//       item.children = disableChildCheckbox(item.children);
//     }
//     return item;
//   });
// }
const handleCheckChange = (checkedNodes, checkedKeys) => {
  console.log(checkedNodes)
  console.log(checkedKeys)
  form.value.missionChapterIds = checkedKeys.checkedKeys
  console.log(form.value.missionChapterIds)
  // chapterList.value.map(item => {
  //   if (item.parentId != 0) {
  //     item.disabled = true
  //   }
  // })
  // const parentKeys = checkedNodes
  //     .filter(node => node.parentId == 0)
  //     .map(node => node.chapterId);
  // proxy.$refs.treeRef.setCheckedKeys(parentKeys)
}

// 关闭弹窗
const confirmFrom = (n) => {
  formRef.value.validate(valid => {
    console.log(proxy.$refs.treeRef.getCheckedNodes())
    console.log(form.value.missionChapterIds)
    if (valid) {
      console.log('submit!')
      form.value.missionChapterId = form.value.missionChapterIds.join(',');
      console.log(form.value.missionChapterId)
      console.log(form.value)
      if (n == 0) {
        // 编辑
        updateCourseMisson()
        proxy.$modal.msgSuccess('保存成功')
        emits("refreshList")
      } else if (n == 1) {
        // 新增
        saveCourseMission()
        proxy.$modal.msgSuccess('新增成功')
        emits("refreshList")
      } else if (n == 2) {
        popupTextbookStart.value.open(form.value,0)
      }
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}

const refreshLists = (item) => {
  form.value = item
  form.value.startTime = formatDateForBackend(new Date())
  form.value.status = 2
  if (form.value.missionId) {
    updateCourseMisson()
  } else {
    saveCourseMission()
  }
  proxy.$modal.msgSuccess('任务已开始')
  emits("refreshList")
  refreshList()
}

function formatDateForBackend(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

const saveCourseMission = async () => {
  let response = await addMission(form.value)
}

const reset = () => {
  form.value = {
    missionTitle: '',
    remark: '',
    missionChapterId: '',
    missionDescribe: '',
    classId:props.classDetail.classId
  }
}

const updateCourseMisson = async () => {
  let response = await updateMission(form.value)
}

const handleClose = () => {
  dialogVisible.value = false
  reset()
}
defineExpose({
  open
})
</script>
<style lang="scss">
// .el-dialog.signin_popup_dialog :not(.is-fullscreen) {
//   margin-top: 30vh !important;
// }
</style>
