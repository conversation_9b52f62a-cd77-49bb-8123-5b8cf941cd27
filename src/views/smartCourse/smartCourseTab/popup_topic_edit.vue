<template>
  <el-dialog v-model="dialogVisible" width="500" :before-close="handleClose" style="margin-top: 30vh !important">
    <el-form ref="formRef" style="max-width: 600px" :model="form" label-width="auto" class="demo-ruleForm" hide-required-asterisk="true" :rules="rules">
      <el-form-item label="修改回复" label-position="top">
        <el-input v-model="form.bt" :rows="5" type="textarea" show-word-limit="true" maxlength="500" placeholder="请输入" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'

const dialogVisible = ref(false)
const formRef = ref(null)
const form = ref({
  bt: '',
})
const itemCuss = ref()
const rules = ref({
  bt: [{ required: true, message: '回复不能为空', trigger: 'change' }],
})
// 开启弹窗
const open = (item) => {
  itemCuss.value = item
  console.log(item)
  form.value.bt = itemCuss.value.nr
  dialogVisible.value = true
  if (formRef.value) formRef.value.resetFields()
}
// 关闭弹窗
const confirmFrom = () => {
  formRef.value.validate(valid => {
    if (valid) {
      console.log('submit!')
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss">
</style>
