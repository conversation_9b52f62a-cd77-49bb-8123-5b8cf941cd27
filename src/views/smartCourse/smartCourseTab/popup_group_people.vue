<template>
  <el-dialog v-model="dialogVisible" title="评语" width="800" :before-close="handleClose"
    style="margin-top: 20vh !important">
    <div class="score_remark_title">
      <span>小组名称：陈泽小队</span>
      <span>人员个数：125</span>
    </div>
    <div class="people_group_operate">
      <div>
        <el-input v-model="input2" style="width: 240px; margin-right: 10px" placeholder="请输入学号/姓名" />
      </div>
      <div>
        <el-button type="primary" @click="addPeople">添加人员</el-button>
      </div>
    </div>
    <el-table class="score_remark_table" :data="tableData">
      <el-table-column prop="xh" align="center" label="学号" />
      <el-table-column prop="xm" align="center" label="姓名">
        <template #default="scope">
          <span v-if="scope.row.sfzz == 1">
            {{ `${scope.row.xm}（组长）` }}
          </span>
          <span v-else>{{ scope.row.xm }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="ssbj" align="center" label="所属班级" />
      <el-table-column align="center" label="修改状态" width="250">
        <template #default="scope">
          <el-button v-if="scope.row.sfzz == 1" type="primary" link @click="delLeader(scope.row.id)">删除组长</el-button>
          <el-button v-if="scope.row.sfzz == 2" type="primary" link @click="setLeader">设为组长</el-button>
          <el-button type="primary" link @click="delPeople">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <PopupPeopleAdd ref="popupPeopleAdd" />
</template>

<script setup>
import { ref } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import PopupPeopleAdd from './popup_people_add.vue'
const dialogVisible = ref(false)
const form = ref({
  py: '',
})
const tableData = ref([
  {
    id: '1',
    xh: '11231',
    xm: '陈泽',
    ssbj: '阳光一班',
    sfzz: '1'
  },
  {
    id: '2',
    xh: '11231',
    xm: '陈泽',
    ssbj: '阳光一班',
    sfzz: '2'
  }
])
// 开启弹窗
const open = () => {
  dialogVisible.value = true
}
// 删除组长
const delLeader = () => {
  ElMessageBox.confirm('是否删除组长？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage({
      type: 'success',
      message: '删除组长成功'
    })

  }).catch(() => {
    console.log('取消操作')
  })
}
const setLeader = () => {
  ElMessageBox.confirm('是否设为组长？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage({
      type: 'success',
      message: '设为组长成功'
    })
  }).catch(() => {
    console.log('取消操作')
  })
}
const delPeople = () => {
  ElMessageBox.confirm('是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
  }).catch(() => {
    console.log('取消操作')
  })
}
// 添加人员 
const popupPeopleAdd = ref(null)
const addPeople = () => {
  popupPeopleAdd.value.open()
}
// 关闭弹窗
const confirmFrom = n => {
  dialogVisible.value = false
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss">
.score_remark_title {
  font-size: 16px;
}

.score_remark_title span:last-child {
  margin-left: 20px;
}

.score_remark_table {
  margin: 20px 0;
}

.people_group_operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}
</style>
