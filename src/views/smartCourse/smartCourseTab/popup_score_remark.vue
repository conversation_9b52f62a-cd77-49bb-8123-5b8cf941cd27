<template>
  <el-dialog v-model="dialogVisible" title="评语" width="800" :before-close="handleClose" style="margin-top: 20vh !important">
    <div class="score_remark_title">
      <span>学号：250001</span>
      <span>姓名：陈泽</span>
    </div>
    <el-table class="score_remark_table" :data="tableData">
      <el-table-column prop="jcxxcj" label="教材学习成绩" />
      <el-table-column prop="cscj" label="测试成绩" />
      <el-table-column prop="kttwcj" label="课堂提问成绩" />
      <el-table-column prop="zycj" label="作业成绩" />
      <el-table-column prop="kscj" label="考试成绩" />
      <el-table-column prop="pscj" label="平时成绩" />
      <el-table-column prop="zcj" label="总成绩" />
    </el-table>
    <el-form ref="formRef" :model="form" label-width="auto" class="demo-ruleForm" :rules="rules">
      <el-form-item label="评语" prop="py">
        <el-input v-model="form.py" :rows="5" type="textarea" show-word-limit="true" maxlength="500" placeholder="请输入评语" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'

const dialogVisible = ref(false)
const formRef = ref(null)
const form = ref({
  py: '',
})
const tableData = ref([
  {
    id: '1',
    jcxxcj: '20',
    cscj: '20',
    kttwcj: '20',
    zycj: '20',
    kscj: '20',
    pscj: '20',
    zcj: '100'
  }
])
const rules = ref({
  py: [{ required: true, message: '评语不能为空', trigger: 'change' }],
})
// 开启弹窗
const open = () => {
  dialogVisible.value = true
  if (formRef.value) formRef.value.resetFields()
}
// 关闭弹窗
const confirmFrom = n => {
  formRef.value.validate(valid => {
    if (valid) {
      console.log('submit!')
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss">
.score_remark_title {
  font-size: 16px;
}
.score_remark_title span:last-child {
  margin-left: 20px;
}
.score_remark_table {
  margin: 20px 0;
}

</style>
