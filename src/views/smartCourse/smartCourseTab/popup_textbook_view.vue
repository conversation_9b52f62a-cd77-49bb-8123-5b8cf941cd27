<template>
  <el-dialog v-model="dialogVisible" title="数字教材学习详情" width="1000" :before-close="handleClose" style="margin-top: 30vh !important">
    <h3>数字教材学习目录</h3>
    <div class="textbook_view_title">
      <div class="textbook_view_title_left">
        <span>任务完成人数：22/55</span>
        <span>结束时间：2025-08-16</span>
      </div>
      <div>
        <el-select v-model="value" placeholder="本书信息" style="width: 200px; margin-right: 20px">
          <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="value" placeholder="学习时长" style="width: 200px">
          <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </div>
    <el-table :data="tableData">
      <el-table-column prop="xh" label="学号" />
      <el-table-column prop="xm" label="姓名" />
      <el-table-column prop="bj" label="笔记" />
      <el-table-column prop="hx" label="划线" />
      <el-table-column prop="jxzy" label="教学资源" />
      <el-table-column prop="zy" label="作业" />
      <el-table-column prop="ks" label="考试" />
      <el-table-column prop="xxsc" label="学习时长">
        <template #default="scope"> {{ scope.row.xxsc }}分钟 </template>
      </el-table-column>
      <el-table-column prop="jd" label="进度">
        <template #default="scope"> {{ scope.row.jd }}% </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'

const dialogVisible = ref(false)
const formRef = ref(null)
const tableData = ref([
  {
    xh: '1',
    xm: '张三',
    bj: '暂无记录',
    hx: '20',
    jxzy: '20',
    zy: '20',
    ks: '20',
    xxsc: '20',
    jd: '10'
  }
])
// 开启弹窗
const open = () => {
  dialogVisible.value = true
  if (formRef.value) formRef.value.resetFields()
}
// 关闭弹窗
const confirmFrom = n => {
  formRef.value.validate(valid => {
    if (valid) {
      console.log('submit!')
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.textbook_view_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  .textbook_view_title_left {
    span {
      margin-right: 20px;
    }
  }
}
</style>
