<template>
  <el-dialog v-model="dialogVisible" title="评语" width="800" :before-close="handleClose"
    style="margin-top: 20vh !important">
    <div class="people_group_operate">
      <div>
        <el-input v-model="input2" style="width: 240px; margin-right: 10px" placeholder="请输入学号/姓名" />
      </div>
    </div>
    <el-table class="score_remark_table" :data="tableData" @selection-change="handleSelectionChange" ref="tableRef">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="xh" align="center" label="学号" />
      <el-table-column prop="xm" align="center" label="姓名" />
      <el-table-column prop="ssbj" align="center" label="所属班级" />
    </el-table>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定添加 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
const ids = ref([])
const dialogVisible = ref(false)
const tableRef = ref(null)
const form = ref({
  py: '',
})
const tableData = ref([
  {
    id: '1',
    xh: '11231',
    xm: '陈泽',
    ssbj: '阳光一班',
    sfzz: '1'
  },
  {
    id: '2',
    xh: '11231',
    xm: '陈泽',
    ssbj: '阳光一班',
    sfzz: '2'
  }
])
// 开启弹窗
const open = () => {
  dialogVisible.value = true
    // 清空表格选中
  if (tableRef.value) tableRef.value.clearSelection()
}
// 勾选
const handleSelectionChange = val => {
  // 勾选id集合
  ids.value = val.map(item => item.id)
}
// 关闭弹窗
const confirmFrom = n => {
  if (ids.value.length === 0) {
    ElMessage({
      type: 'info',
      message: '请选择学生'
    })
  } else {
    console.log('submit!')
    dialogVisible.value = false
  }
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss">
.score_remark_title {
  font-size: 16px;
}

.score_remark_title span:last-child {
  margin-left: 20px;
}

.score_remark_table {
  margin: 20px 0;
}

.people_group_operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}
</style>
