<template>
  <div class="index_classroom">
    <div class="index_classroom_operate">
      <div>
        <el-select v-model="value" placeholder="全部章节" style="width: 140px;margin-right: 10px;">
          <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="value" placeholder="章节顺序" style="width: 140px">
          <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div>
        <el-button type="primary" @click="addDiscuss">创建讨论</el-button>
      </div>
    </div>
    <el-table ref="tableRef" :data="tableData" class="index_classroom_table">
      <el-table-column type="index" label="序号" align="center" width="50" />
      <el-table-column prop="jcnr" align="center" label="教材内容" show-overflow-tooltip>
        <template #default="scope">
          <el-button type="primary" @click="published(scope.row.id)" link>{{ scope.row.jcnr }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="tlzt" align="center" label="讨论主题" show-overflow-tooltip />
      <el-table-column prop="kssj" width="170" align="center" label="开始时间" />
      <el-table-column prop="jssj" width="170" align="center" label="结束时间" />
      <el-table-column prop="zt" align="center" label="状态" width="100">
        <template #default="scope">
          <span v-if="scope.row.zt == 1">未开始</span>
          <span v-if="scope.row.zt == 2">进行中</span>
          <span v-if="scope.row.zt == 3">已结束</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="220">
        <template #default="scope">
          <el-button v-if="scope.row.zt == '1'" type="primary" link @click="startActivity(scope.row.id)">
            开始活动
          </el-button>
          <el-button v-if="scope.row.zt == '2'" type="primary" link @click="endActivity(scope.row.id)">
            结束活动
          </el-button>
          <el-button v-if="scope.row.zt == '1'" type="primary" link>
            编辑
          </el-button>
          <el-button v-if="scope.row.zt != '1'" type="primary" link>
            查看
          </el-button>
          <el-button v-if="scope.row.zt != '2'" type="primary" link @click="delActivity(scope.row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <PopupTtextbookStart ref="popupTtextbookStart" />
  <PopupTopicPublished ref="popupTopicPublished" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PopupTtextbookStart from './popup_textbook_start.vue'
import PopupTopicPublished from './popup_topic_published.vue'
const tableRef = ref(null)
const ids = ref([])

const { proxy } = getCurrentInstance()
const statusList = ref([
  {
    value: '1',
    label: '未审批'
  },
  {
    value: '2',
    label: '已审批'
  },
])
const tableData = ref([
  {
    id: '1',
    jcnr: '我是教材内容，我宣布所有可乐以后都用蔗糖',
    tlzt: '蔗糖的甜味更自然、醇厚，且甜感停留时间更长，尤其适合需要持久甜味的饮品。相比之下， 高果糖玉米糖浆 （HFCS）的甜味峰值更高，但后味略带苦涩，部分消费者认为其强化气泡带来的刺激感不如蔗糖。',
    kssj: '2021-01-01 12:00:00',
    jssj: '2021-01-01 12:00:00',
    zt: '1',
  },
  {
    id: '2',
    jcnr: '我是教材内容，我宣布所有可乐以后都用蔗糖',
    tlzt: '蔗糖的甜味更自然、醇厚，且甜感停留时间更长，尤其适合需要持久甜味的饮品。相比之下， 高果糖玉米糖浆 （HFCS）的甜味峰值更高，但后味略带苦涩，部分消费者认为其强化气泡带来的刺激感不如蔗糖。',
    kssj: '2021-01-01 12:00:00',
    jssj: '2021-01-01 12:00:00',
    zt: '2',
  },
  {
    id: '3',
    jcnr: '我是教材内容，我宣布所有可乐以后都用蔗糖',
    tlzt: '蔗糖的甜味更自然、醇厚，且甜感停留时间更长，尤其适合需要持久甜味的饮品。相比之下， 高果糖玉米糖浆 （HFCS）的甜味峰值更高，但后味略带苦涩，部分消费者认为其强化气泡带来的刺激感不如蔗糖。',
    kssj: '2021-01-01 12:00:00',
    jssj: '2021-01-01 12:00:00',
    zt: '3',
  },
])
// 开始活动  
const popupTtextbookStart = ref(null)
const startActivity = () => {
  popupTtextbookStart.value.open()
}
// 结束活动 
const endActivity = () => {
  ElMessageBox.confirm('还未到达您所设置的教务任务结束时间，是否提前结束此任务?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消操作'
      })
    })
}
// 删除 
const delActivity = (id) => {
  ElMessageBox.confirm('删除后不可恢复，确定删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消操作'
      })
    })
}
// 详情
const popupTopicPublished = ref(null)
const published = (id) => {
  popupTopicPublished.value.open(id)
}
// 创建讨论 
const addDiscuss = () => {
  window.open(`/reader?k=${'1920071967706288129'}`, '_blank')
}

</script>

<style scoped lang="scss">
.index_classroom {
  margin-top: 8px;

  .index_classroom_operate {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .classroom_operate_num {
      font-size: 16px;
      color: #333333;
      margin: 0 20px 0 10px;
    }
  }

  .index_classroom_table {
    width: 100%;
    margin-top: 20px;
  }
}
</style>
