<template>
  <div class="index_classroom">
    <div>
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px">
        <el-form-item label="考试名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入考试名称" />
        </el-form-item>
        <el-form-item label="状态：" prop="status">
          <el-select v-model="queryParams.status" placeholder="状态" style="width: 200px">
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" :loading="loading" class="index_classroom_table" :key="childKey">
      <el-table-column type="index" label="序号" align="center" width="50" />
      <el-table-column prop="name" align="center" label="考试名称">
        <template #default="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.name" placement="top">
            <span class="ellipsis">{{ getShortName(scope.row.name,6) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="contentType" align="center" label="考试类型">
        <template #default="scope">
          <span v-if="scope.row.contentType == '0'">题库考试</span>
          <span v-if="scope.row.contentType == '1'">附件考试</span>
          <span v-if="scope.row.contentType == '2'">登分考试</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" align="center" label="状态">
        <template #default="scope">
          <span v-if="scope.row.status == '1'">未开始</span>
          <span v-if="scope.row.status == '2'">进行中</span>
          <span v-if="scope.row.status == '3'">已结束</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" align="center" label="创建时间" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button v-if="scope.row.status == 1" type="primary" link @click="goStart(scope.row)">开始</el-button>
          <el-button v-if="scope.row.status == 1" type="primary" link @click="goEdit(scope.row.assignmentId, 1)">编辑</el-button>
          <el-button v-if="scope.row.status == 1" type="primary" link @click="goDel(scope.row.assignmentId)">删除</el-button>
          <el-button v-if="scope.row.status != 1" type="primary" link @click="goEdit(scope.row.assignmentId,2)">查看</el-button>
          <el-button v-if="scope.row.status == 2" type="primary" link @click="goStop(scope.row)">结束</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList"/>
  </div>
  <PopupExamEdit ref="popupExamEdit" @refushList="refushList" :courseId="course.courseId" :classId="classDetail.classId" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PopupExamEdit from './popup_exam_edit.vue'
import {
  delHomeworkOrTest,
  getHomeworkOrTestListByUserId,
  startOrStopTest
} from "@/api/edu/moocSmartCourseHomeworkOrTest.js";

const props = defineProps({
/*  course: {
    type: Object,
    default: {},
  }*/
  classDetail: {
    type: Object,
    default: {
    },
  },
  /*测试用*/
  course: {
    type: Object,
    default: {
      "createBy": null,
      "createTime": null,
      "updateBy": null,
      "updateTime": null,
      "remark": null,
      "courseId": "11",
      "courseName": "网络安全互动课堂用",
      "courseCode": "SEC1012",
      "courseType": 1,
      "categoryId": "101",
      "planId": "210",
      "bookId": "310",
      "description": "了解网络安全基础知识",
      "coverImageUrl": "https://example.com/images/sec101.jpg",
      "classroomQuestionWeight": 25,
      "homeworkWeight": 35,
      "examWeight": 40,
      "hour": 48,
      "status": 1,
      "userId": "1854076781086117972",
      "createdBy": null,
      "updatedBy": null,
      "delFlag": null,
      "classTotal": 3,
      "realName": "13840917725",
      "searchVar": null
      /*"createBy": null,
      "createTime": null,
      "updateBy": null,
      "updateTime": null,
      "remark": null,
      "courseId": "1943215209544761346",
      "courseName": "第六个教务课程",
      "courseCode": "999",
      "courseType": 1,
      "categoryId": "1912780772558868481",
      "planId": null,
      "bookId": "1893128341502074882",
      "description": "99",
      "coverImageUrl": "https://dutp-test.oss-cn-beijing.aliyuncs.com/1752133581962.png",
      "classroomQuestionWeight": null,
      "homeworkWeight": null,
      "examWeight": null,
      "hour": 1,
      "status": 0,
      "userId": "1854076781086117925",
      "createdBy": null,
      "updatedBy": null,
      "delFlag": null,
      "classTotal": 0,
      "realName": "学生(雪)1",
      "searchVar": null*/
    },
  },
});
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    assignmentType: 1,
    courseId: props.course.courseId,
/*    classId: props.classDetail.classId,*/
    classId: 25, // 测试用
  },
});
const { queryParams } = toRefs(data);
const { proxy } = getCurrentInstance()
// 状态
const statusList = ref([
  {
    label: '未开始',
    value: '1'
  },
  {
    label: '进行中',
    value: '2'
  },
  {
    label: '已结束',
    value: '3'
  }
])


const tableData = ref([])
const total = ref(0)
const loading = ref(false)
const popupExamEdit = ref(null)
const childKey = ref(0)

onMounted(() => {
  console.log(props.course)
  console.log(props.classDetail)
  getList()
})

/** 查询列表 */
const getList = async () => {
  console.log(props.classDetail)
  console.log(queryParams.value)
  loading.value = true;
  let response = await getHomeworkOrTestListByUserId(queryParams.value)
  tableData.value = response.rows;
  total.value = response.total;
  loading.value = false;
}

// 编辑试卷
const goEdit = (id, type) => {
  if (type == 1) {
    // 编辑
    popupExamEdit.value.open(id,1)
  } else if (type == 2) {
    // 查看
    popupExamEdit.value.open(id, 2)
  }
}

const resetQuery = () => {
  childKey.value = 0
  proxy.resetForm('queryRef')
  handleQuery();
}

const refushList = () => {
  proxy.resetForm('queryRef')
  childKey.value++
  handleQuery();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

const getShortName = (name,length) => {
  if (!name) return '';
  return name.length > length ? name.substr(0, length) + '...' : name;
}

// 删除试卷
const goDel = id => {
  proxy.$modal
      .confirm("确定删除该条数据？")
      .then(function () {
        return delExam(id)
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
      .catch(() => {});
}

/** 开始考试 */
const goStart = (row) => {
  proxy.$modal
      .confirm("确定开始该考试？")
      .then(function () {
        row.status = 2
        return startOrStopTest(row)
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("考试已开始");
      })
      .catch(() => {});
}

const delExam = async (id) => {
  let response = await delHomeworkOrTest(id)
}

/** 结束考试 */
const goStop = async (row) => {
  proxy.$modal
      .confirm("确定结束该考试？")
      .then(function () {
        row.status = 3
        return startOrStopTest(row)
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("考试已结束");
      })
      .catch(() => {});
}

</script>

<style scoped lang="scss">
.index_classroom {
  margin-top: 8px;
  .index_classroom_table {
    width: 100%;
    margin-top: 20px;
  }
}
</style>
