<template>
  <el-table ref="tableRef" :data="dataList" class="index_classroom_table" @selection-change="handleSelectionChange">
    <el-table-column type="selection" width="55" />
    <el-table-column prop="userNo" align="center" label="学号"/>
    <el-table-column prop="realName" align="center" label="姓名" />
    <el-table-column prop="schoolName" align="center" label="学校" />
    <el-table-column prop="academyName" align="center" label="院系" />
    <el-table-column prop="cteateTime" align="center" label="申请时间" />
    <!-- <el-table-column prop="status" align="center" label="状态">
        <template #default="scope">
          {{ statusList[scope.row.zt / 1 - 1].label }}
        </template>
</el-table-column> -->
    <el-table-column align="center" label="操作">
      <template #default="scope">
        <div v-if="scope.row.status == 0">
          <el-button type="success" link @click="handleAuditApply(1, scope.row.applyId)">
            通过
          </el-button>
          <el-button type="danger" link @click="handleAuditApply(2, scope.row.applyId)">
            不通过
          </el-button>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
    @pagination="getList" />
  <div class="bottom_btn" v-if="total > 0">
    <el-button size="large" @click="handleAuditApply(2)">
      不通过
    </el-button>
    <el-button type="primary" size="large" @click="handleAuditApply(1)">
      通过
    </el-button>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { watchDebounced } from '@vueuse/core'
import { listForClass, auditApply } from '@/api/edu/apply'
const props = defineProps({
  realName: {
    type: String,
    default: () => null
  },
  status: {
    type: String,
    default: () => null
  },
  classId: {
    type: String,
    default: () => null
  }
})
const { proxy } = getCurrentInstance()


const tableRef = ref(null)
const ids = ref([])

const popupSetAssistant = ref(null)

const dataList = ref([])
const loadding = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  classId: 1,
  realName: '',
  status: ''
})
const total = ref(0)

watchDebounced(() => [props.realName, props.status], ([nValue, nStatus]) => {
  queryParams.value.realName = nValue;
  queryParams.value.status = nStatus;
  queryParams.value.pageNum = 1;
  getList()
}, { debounce: 500, maxWait: 2000 },)

const getList = () => {
  loadding.value = true
  queryParams.value.classId = props.classId
  listForClass(queryParams.value).then(res => {
    dataList.value = res.rows
    total.value = res.total
    loadding.value = false
  })
}


// 刷新数据
const refresh = () => {
  queryParams.value.pageNum = 1
  getList()
}


// 设置助教
const setAss = (id, type) => {
  popupSetAssistant.value.open(id)
}
// 勾选
const handleSelectionChange = val => {
  // 勾选id集合
  ids.value = val.map(item => item.applyId)
}

// 审核 
const handleAuditApply = (status, id) => {
  let applyIdList = id ? [id] : ids.value
  if (applyIdList && applyIdList.length <= 0) {
    proxy.$modal.msgWarning('请选择要审核的学生')
    return;
  }
  auditApply({ status, applyIdList }).then(() => {
    refresh()
    proxy.$modal.msgSuccess('审核成功')
  })
    .catch((err) => {
      console.error(err)
      proxy.$modal.msgError('审核失败')
    })
}

onMounted(() => {
  getList()
})


defineExpose({
  refresh,
});
</script>

<style scoped lang="scss">
.index_classroom_table {
  width: 100%;
  margin-top: 20px;
}

.bottom_btn {
  margin-top: 40px;
  display: flex;
  justify-content: center;
}
</style>
