<template>
  <el-dialog v-model="dialogVisible" title="编辑成绩" width="550" :before-close="handleClose" style="margin-top: 10vh !important">
    <el-form ref="formRef" style="max-width: 600px" :model="params" label-width="150" class="demo-ruleForm" :rules="rules">
      <el-form-item label="学号">
        {{ params.xh }}
      </el-form-item>
      <el-form-item label="姓名">
        {{ params.xm }}
      </el-form-item>
      <el-form-item label="教材学习成绩" prop="jcxxcj">
        <el-input-number v-model="params.jcxxcj" style="width: 240px" placeholder="请输入教材学习成绩" />
      </el-form-item>
      <el-form-item label="测试成绩" prop="cscj">
        <el-input-number v-model="params.cscj" style="width: 240px" placeholder="请输入测试成绩" />
      </el-form-item>
      <el-form-item label="课堂提问成绩" prop="kttwcj">
        <el-input-number v-model="params.kttwcj" style="width: 240px" placeholder="请输入课堂提问成绩" />
      </el-form-item>
      <el-form-item label="作业成绩" prop="zycj">
        <el-input-number v-model="params.zycj" style="width: 240px" placeholder="请输入作业成绩" />
      </el-form-item>
      <el-form-item label="考试成绩" prop="kscj">
        <el-input-number v-model="params.kscj" style="width: 240px" placeholder="请输入考试成绩" />
      </el-form-item>
      <el-form-item label="平时成绩" prop="pscj">
        <el-input-number v-model="params.pscj" style="width: 240px" placeholder="请输入平时成绩" />
      </el-form-item>
      <el-form-item label="总成绩" prop="zcj">
        <el-input-number v-model="params.zcj" style="width: 240px" placeholder="请输入总成绩" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'

const dialogVisible = ref(false)
const formRef = ref(null)
const params = ref()
const form = ref({
  bt: '',
  lx: '1',
  fs: '1'
})
const rules = ref({
  jcxxcj: [{ required: true, message: '教材学习成绩不能为空', trigger: 'change' }],
  cscj: [{ required: true, message: '测试成绩不能为空', trigger: 'change' }],
  kttwcj: [{ required: true, message: '课堂提问成绩不能为空', trigger: 'change' }],
  zycj: [{ required: true, message: '作业成绩不能为空', trigger: 'change' }],
  kscj: [{ required: true, message: '考试成绩不能为空', trigger: 'change' }],
  pscj: [{ required: true, message: '平时成绩不能为空', trigger: 'change' }],
  zcj: [{ required: true, message: '总成绩不能为空', trigger: 'change' }]
})
// 开启弹窗
const open = (e) => {
  params.value = e
  dialogVisible.value = true
  if (formRef.value) formRef.value.resetFields()
}
// 关闭弹窗
const confirmFrom = () => {
  formRef.value.validate(valid => {
    if (valid) {
      console.log('submit!')
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss">
// .el-dialog.signin_popup_dialog :not(.is-fullscreen) {
//   margin-top: 30vh !important;
// }
</style>
