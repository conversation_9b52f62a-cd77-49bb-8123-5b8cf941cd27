<template>
  <div class="index_classroom">
    <div class="index_classroom_operate">
      <div>
        <el-input v-model="input2" style="width: 240px; margin-right: 10px" placeholder="请输入互动讨论标题"
          :suffix-icon="Search" />
      </div>
      <div>
        <el-button type="primary" @click="panelAdd">创建讨论</el-button>
      </div>
    </div>
    <el-table ref="tableRef" :data="tableData" class="index_classroom_table">
      <el-table-column type="index" label="序号" align="center" width="50" />
      <el-table-column prop="jcnr" align="center" label="互动讨论标题" show-overflow-tooltip>
        <template #default="scope">
          <el-button type="primary" link @click="chat(scope.row.id)">{{ scope.row.jcnr }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="tlzt" width="100" align="center" label="参与人数" show-overflow-tooltip />
      <el-table-column prop="kssj" width="170" align="center" label="开始时间" />
      <el-table-column prop="jssj" width="170" align="center" label="结束时间" />
      <el-table-column prop="zt" align="center" label="状态" width="100">
        <template #default="scope">
          <span v-if="scope.row.zt == 1">未开始</span>
          <span v-if="scope.row.zt == 2">进行中</span>
          <span v-if="scope.row.zt == 3">已结束</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="220">
        <template #default="scope">
          <el-button v-if="scope.row.zt == '1'" type="primary" link @click="startActivity(scope.row.id)">
            开始活动
          </el-button>
          <el-button v-if="scope.row.zt == '2'" type="primary" link @click="endActivity(scope.row.id)">
            结束活动
          </el-button>
          <el-button v-if="scope.row.zt == '1'" type="primary" link @click="panelAdd(scope.row.id)">
            编辑
          </el-button>
          <el-button v-if="scope.row.zt != '1'" type="primary" link @click="panelAdd(scope.row.id)">
            查看
          </el-button>
          <el-button v-if="scope.row.zt != '2'" type="primary" link @click="delActivity(scope.row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>

    </el-table>
  </div>
  <PopupTtextbookStart ref="popupTtextbookStart" />
  <PopupPanelAdd ref="popupPanelAdd" />
  <PopupPanelChat ref="popupPanelChat" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PopupTtextbookStart from './popup_textbook_start.vue'
import PopupPanelAdd from './popup_panel_add.vue'
import PopupPanelChat from './popup_panel_chat.vue'
const tableRef = ref(null)
const ids = ref([])

const { proxy } = getCurrentInstance()
const statusList = ref([
  {
    value: '1',
    label: '未审批'
  },
  {
    value: '2',
    label: '已审批'
  },
])
const tableData = ref([
  {
    jcnr: '我是教材内容，我宣布所有可乐以后都用蔗糖',
    tlzt: '12',
    kssj: '2021-01-01 12:00:00',
    jssj: '2021-01-01 12:00:00',
    zt: '1',
  },
  {
    jcnr: '我是教材内容，我宣布所有可乐以后都用蔗糖',
    tlzt: '33',
    kssj: '2021-01-01 12:00:00',
    jssj: '2021-01-01 12:00:00',
    zt: '2',
  },
  {
    jcnr: '我是教材内容，我宣布所有可乐以后都用蔗糖',
    tlzt: '3',
    kssj: '2021-01-01 12:00:00',
    jssj: '2021-01-01 12:00:00',
    zt: '3',
  },
])
// 聊天框 
const popupPanelChat = ref(null)
const chat = (id) => {
  popupPanelChat.value.open(id)
}
const popupPanelAdd = ref(null)
const panelAdd = () => {
  popupPanelAdd.value.open()
}
// 开始活动  
const popupTtextbookStart = ref(null)
const startActivity = () => {
  popupTtextbookStart.value.open()
}
// 结束活动 
const endActivity = () => {
  ElMessageBox.confirm('还未到达您所设置的教务任务结束时间，是否提前结束此任务?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消操作'
      })
    })
}
// 删除  
const delActivity = (id) => {
  ElMessageBox.confirm('删除后不可恢复，确定删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消操作'
      })
    })
}
</script>

<style scoped lang="scss">
.index_classroom {
  margin-top: 8px;

  .index_classroom_operate {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .classroom_operate_num {
      font-size: 16px;
      color: #333333;
      margin: 0 20px 0 10px;
    }
  }

  .index_classroom_table {
    width: 100%;
    margin-top: 20px;
  }
}
</style>
