<template>
  <div class="index_classroom">
    <div class="index_classroom_operate">
      <div>
        <span class="classroom_operate_num">班级人数：50人</span>
        <el-input v-model="input2" style="width: 240px; margin-right: 10px" placeholder="请输入小组名称"
          :suffix-icon="Search" />
      </div>
      <div>
        <el-button type="primary" @click="addGroup">创建小组</el-button>
        <el-button type="primary" @click="autoGroup">自动分组</el-button>
        <el-button type="primary" plain @click="exportGroup">导出</el-button>
      </div>
    </div>
    <el-table ref="tableRef" :data="tableData" class="index_classroom_table">
      <el-table-column type="index" label="序号" align="center" width="50" />
      <el-table-column prop="xzmc" align="center" label="小组名称" />
      <el-table-column prop="rygs" align="center" label="人员个数" />
      <el-table-column prop="zz" align="center" label="组长" />
      <el-table-column prop="cjsj" align="center" label="创建时间" />
      <el-table-column align="center" label="操作" width="220">
        <template #default="scope">
            <el-button type="primary" link @click="viewPeople(scope.row.id)">
              查看人员
            </el-button>
            <el-button type="primary" link @click="addGroup(scope.row.id)">
              重命名
            </el-button>
            <el-button type="primary" link @click="delGroup(scope.row.id)">
              删除
            </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <PopupGroupAdd ref="popupGroupAdd" />
  <PopupGroupAuto ref="popupGroupAuto" />
  <PopupGroupPeople ref="popupGroupPeople" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PopupGroupAdd from './popup_group_add.vue'
import PopupGroupAuto from './popup_group_auto.vue'
import PopupGroupPeople from './popup_group_people.vue'
const tableRef = ref(null)
const ids = ref([])

const { proxy } = getCurrentInstance()
const statusList = ref([
  {
    value: '1',
    label: '未审批'
  },
  {
    value: '2',
    label: '已审批'
  },
])
const tableData = ref([
  {
    xzmc: '阿宾教学组',
    rygs: '50',
    zz: '阿宾',
    cjsj: '2021-01-01 12:00:00'
  }
])
const popupGroupAdd = ref(null)
// 新建
const addGroup = (id) => {
  popupGroupAdd.value.open(id)
}
const popupGroupAuto = ref(null)
// 自动
const autoGroup = () => {
  popupGroupAuto.value.open()
}
// 查看人员 
const popupGroupPeople = ref(null)
const viewPeople = () => {
  popupGroupPeople.value.open()
}
// 导出 
const exportGroup = () => {
  console.log('导出')
}
// 批量删除
const delBatch = () => {
  if (ids.value && ids.value.length == 0) {
    ElMessage({
      type: 'info',
      message: '请勾选需要删除的学生'
    })
  } else {
    ElMessageBox.confirm('确定删除已选学生吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '取消操作'
        })
      })
  }
}
// 审核 
const delGroup = (id) => {
  ElMessageBox.confirm('确定删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消操作'
      })
    })
}
</script>

<style scoped lang="scss">
.index_classroom {
  margin-top: 8px;

  .index_classroom_operate {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .classroom_operate_num {
      font-size: 16px;
      color: #333333;
      margin: 0 20px 0 10px;
    }
  }

  .index_classroom_table {
    width: 100%;
    margin-top: 20px;
  }
}
</style>
