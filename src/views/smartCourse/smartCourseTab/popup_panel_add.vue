<template>
  <el-dialog v-model="dialogVisible" title="创建互动讨论" width="800" :before-close="handleClose"
    style="margin-top: 20vh !important">
    <el-form ref="formRef" style="max-width: 600px" :model="form" label-width="auto" class="demo-ruleForm"
      :rules="rules">
      <el-form-item label="标题" prop="bt">
        <el-input v-model="form.bt" type="text" autocomplete="off" placeholder="请输入标题" />
      </el-form-item>
      <el-form-item label="讨论主题" prop="bz">
        <el-input v-model="form.bz" :rows="5" type="textarea" show-word-limit="true" maxlength="500"
          placeholder="请输入讨论主题" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="form.bt" type="text" show-word-limit="true" maxlength="50" autocomplete="off"
          placeholder="请输入备注" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="confirmFrom(1)">保存</el-button>
        <el-button type="primary" @click="confirmFrom(2)"> 开始 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'

const dialogVisible = ref(false)
const formRef = ref(null)
const form = ref({
  bt: '',
  bz: '',
  xxbr: '',
  xxyqms: ''
})
const data = ref(
  [
    {
      id: 1,
      label: '房东',
      children: [
        {
          id: 3,
          label: '学姐',
          children: [
            {
              id: 4,
              label: '迷乱舞会'
            },
            {
              id: 5,
              label: '图书馆'
            }
          ]
        },
        {
          id: 2,
          label: '初识钰慧',
          children: [
            {
              id: 6,
              label: '逛街'
            },
            {
              id: 7,
              label: '寒假开始'
            }
          ]
        }
      ]
    }
  ]
)
const rules = ref({
  bt: [{ required: true, message: '签到标题不能为空', trigger: 'change' }],
  lx: [{ required: true, message: '签到类型不能为空', trigger: 'change' }],
  fs: [{ required: true, message: '结束方式不能为空', trigger: 'change' }]
})
// 开启弹窗
const open = () => {
  dialogVisible.value = true
  if (formRef.value) formRef.value.resetFields()
}
// 关闭弹窗
const confirmFrom = (n) => {
  formRef.value.validate(valid => {
    if (valid) {
      console.log('submit!')
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss">
// .el-dialog.signin_popup_dialog :not(.is-fullscreen) {
//   margin-top: 30vh !important;
// }</style>
