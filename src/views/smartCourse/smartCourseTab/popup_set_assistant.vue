<template>
  <el-dialog v-model="dialogVisible" title="设置助教" width="700" :before-close="handleClose"
    style="margin-top: 10vh !important">
    <el-form ref="formRef" style="max-width: 650px" :model="form" class="demo-ruleForm" label-width="120px"
      :rules="rules">
      <h3>设置助教</h3>
      <el-form-item label="助教人员" prop="userId">
        <el-select v-model="form.userId" placeholder="请选择助教人员" @change="changeAssistant">
          <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.userId" />
        </el-select>
      </el-form-item>
      <h3>权限配置</h3>
      <el-form-item label="班级学生:">
        <el-checkbox-group v-model="form.classStuPermList">
          <el-checkbox label="分组" :value="1" />
          <el-checkbox label="移除" :value="2" />
          <el-checkbox label="添加分组" :value="3" />
          <el-checkbox label="一键分组" :value="4" />
          <el-checkbox label="加入审核" :value="5" />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="作业:">
        <el-checkbox-group v-model="form.workPermList">
          <el-checkbox label="发布" :value="1" />
          <el-checkbox label="查看" :value="2" />
          <el-checkbox label="导出成绩" :value="3" />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="考试:">
        <el-checkbox-group v-model="form.examPermList">
          <el-checkbox label="发布" :value="1" />
          <el-checkbox label="查看" :value="2" />
          <el-checkbox label="导出成绩" :value="3" />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="成绩:">
        <el-checkbox-group v-model="form.gradePermList">
          <el-checkbox label="导出" :value="1" />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="互动讨论:">
        <el-checkbox-group v-model="form.discussionPermList">
          <el-checkbox label="回复" :value="1" />
          <el-checkbox label="删除" :value="2" />
        </el-checkbox-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { listTeachingAssistant } from '@/api/edu/member'
import { getAssistant, updateAssistant } from '@/api/edu/assistant'
const props = defineProps({
  classId: {
    type: String,
    default: () => null
  }
})
const emit = defineEmits(['refresh'])
const { proxy } = getCurrentInstance()

const dialogVisible = ref(false)
const formRef = ref(null)
const form = ref({
  userId: null,
})
const userList = ref([])
const rules = ref({
  userId: [{ required: true, message: '助教人员不能为空', trigger: 'change' }],
})

// 切换助教人员
const changeAssistant = (userId) => {
  getAssistantInfo(userId)
}

// 获取助教信息
const getAssistantInfo = (userId) => {
  getAssistant({ classId: props.classId, userId: userId }).then(response => {
    form.value = {
      ...response.data,
      userId: form.value.userId
    }
  })
}

// 开启弹窗
const open = () => {
  dialogVisible.value = true
  form.value = {
    useId: null,
  }
  getList()
  if (formRef.value) formRef.value.resetFields()
}

const getList = () => {
  listTeachingAssistant({ classId: props.classId }).then(response => {
    userList.value = response.data || []
  })
}

// 关闭弹窗
const confirmFrom = () => {
  console.log(form.value)
  formRef.value.validate(valid => {
    if (valid) {
      updateAssistant({
        classStuPerm: form.value.classStuPermList.join(','),
        workPerm: form.value.workPermList.join(','),
        examPerm: form.value.examPermList.join(','),
        gradePerm: form.value.gradePermList.join(','),
        discussionPerm: form.value.discussionPermList.join(','),
        classId: props.classId,
        userId: form.value.userId,
        assistantId: form.value.assistantId
      }).then(response => {
        proxy.$modal.msgSuccess('设置成功')
        emit('refresh')
        dialogVisible.value = false
      })

    }
  })
}

const handleClose = () => {
  dialogVisible.value = false
}

defineExpose({
  open
})
</script>
<style lang="scss">
// .el-dialog.signin_popup_dialog :not(.is-fullscreen) {
//   margin-top: 30vh !important;
// }</style>
