<template>
  <el-dialog v-model="dialogVisible" title="选择试卷" width="680" :before-close="handleClose" style="margin-top: 10vh !important">
    <div class="show_change">
      <div class="popup_show_change" v-for="(item, index) in tableData" :key="index">
        <div class="change_content">
          <el-checkbox class="change_content_left" v-model="item.checked" size="large" />
          <div class="change_content_right">
            <div class="change_content_right_all">
              <h3>{{ item.paperTitle }}</h3>
              <div class="change_content_right_bottom">{{ item.collectionType == '1' ? '自动组卷' : '手动组卷' }}</div>
            </div>
            <div>
              <span>题目数量：{{ item.questionQuantity }}</span>
              <span>试卷分数：{{ item.totalScore }}</span>
              <span>创建时间：{{ item.createTime }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {defineProps, getCurrentInstance, ref} from 'vue'
import { ElMessage } from 'element-plus'
import { getTestListNoPage } from '@/api/edu/moocSmartCourseTestPaper.js'
const emits = defineEmits(['changeVal'])
const { proxy } = getCurrentInstance()
const dialogVisible = ref(false)
const tableData = ref([])
const props = defineProps({
  paperDetail: {
    type: Object,
    default: () => ({})
  }
});

// 开启弹窗
const open = () => {
  dialogVisible.value = true
  getListNoPage()
}

// 获取试卷
const getListNoPage = async () => {
  let response = await getTestListNoPage()
  tableData.value = response.data
  if (props.paperDetail) {
    for (let i = 0; i < tableData.value.length; i++) {
      if (props.paperDetail.paperId == tableData.value[i].paperId) {
        tableData.value[i].checked = true
      } else {
        tableData.value[i].checked = false
      }
    }
  } else {
    for (let i = 0; i < tableData.value.length; i++) {
      tableData.value[i].checked = false
    }
  }
}

// 关闭弹窗
const confirmFrom = () => {
  const val = tableData.value.filter(item => item.checked == true)
  if (!val || val.length == 0) {
    proxy.$message.error("请选择试卷");
    return
  }
  if (val.length > 1) {
    proxy.$message.error("只能选择一个试卷");
    return;
  }
  emits('changeVal', val[0])
  dialogVisible.value = false
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.show_change {
  max-height: 400px;
  overflow-y: auto;

  .popup_show_change {
    margin: 20px;

    .change_content {
      display: flex;
      width: 100%;
      height: 80px;
      background-image: url('../../../assets/images/smartCourse/smartCourseDetails/green_bg.png');
      background-size: 800px auto;
      background-repeat: no-repeat;

      .change_content_left {
        margin: 20px 20px 20px 25px;
      }
      .change_content_right_all {
        display: flex;
      }

      .change_content_right {
        h3 {
          font-size: 17px;
          color: #333333;
          margin-top: 17px;
          margin-bottom: 7px;
        }
        .change_content_right_bottom {
          width: 70px;
          height: 17px;
          border-radius: 20px 20px 20px 20px;
          border: 1px solid #969aaa;
          font-size: 13px;
          color: #969aaa;
          line-height: 17px;
          text-align: center;
          margin-top: 17px;
          margin-left: 8px;
        }
        div {
          font-size: 14px;
          color: #585e76;

          span {
            margin-right: 40px;
          }
        }
      }
    }
  }
}
</style>
