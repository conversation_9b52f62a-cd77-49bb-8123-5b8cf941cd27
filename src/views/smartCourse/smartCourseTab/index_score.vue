<template>
  <div class="index_classroom">
    <div class="index_score_set">
      <div>
        <el-input v-model="input2" style="width: 240px; margin-right: 10px" placeholder="请输入活动名称" :suffix-icon="Search" />
        <el-select v-model="value" placeholder="状态" style="width: 140px">
          <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div>
        <el-button type="primary" @click="goEditScore">编辑成绩</el-button>
        <el-button type="primary" @click="goPublish">发布成绩</el-button>
        <el-button type="primary">导出</el-button>
      </div>
    </div>
    <el-table :data="tableData"  ref="table" @selection-change="handleSelectionChange"
    @select="handleSelect"
    @select-all="handleSelectAll"
 class="index_classroom_table">
      <el-table-column type="selection" width="55" />
      <el-table-column type="index" width="50" />
      <el-table-column prop="xh" label="学号" />
      <el-table-column prop="xm" label="姓名" />
      <el-table-column prop="jcxxcj" label="教材学习成绩" />
      <el-table-column prop="cscj" label="测试成绩" />
      <el-table-column prop="kttwcj" label="课堂提问成绩" />
      <el-table-column prop="zycj" label="作业成绩" />
      <el-table-column prop="kscj" label="考试成绩" />
      <el-table-column prop="pscj" label="平时成绩" />
      <el-table-column prop="zcj" label="总成绩" />
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="primary" @click="goRemark(scope.row.id)" link>评语</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <PopupScoreRemark ref="popupScoreRemark" />
  <PopupScoreEdit ref="popupScoreEdit" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PopupScoreRemark from './popup_score_remark.vue'
import PopupScoreEdit from './popup_score_edit.vue'
const { proxy } = getCurrentInstance()
const table = ref();
const tableItem = ref()
// 状态
const statusList = ref([
  {
    label: '未开始',
    value: '1'
  },
  {
    label: '进行中',
    value: '2'
  },
  {
    label: '已结束',
    value: '3'
  }
])
const tableData = ref([
  {
    id: '1',
    xh: '1111111',
    xm: '陈泽',
    jcxxcj: '20',
    cscj: '20',
    kttwcj: '20',
    zycj: '20',
    kscj: '20',
    pscj: '20',
    zcj: '100'
  },
  {
    id: '2',
    xh: '222222',
    xm: '李志',
    jcxxcj: '20',
    cscj: '20',
    kttwcj: '20',
    zycj: '20',
    kscj: '20',
    pscj: '20',
    zcj: '100'
  },
])
// 评语 
const popupScoreRemark = ref(null)
const goRemark = id => {
  popupScoreRemark.value.open(id)
}
// 当用户选择新行时，清空其他所有选择
const handleSelect = (selection, row) => {
  if (selection.includes(row)) {
    // 清空所有选中行
    table.value.clearSelection();
    // 重新选中当前行（需在下一个事件循环执行）
    setTimeout(() => {
      table.value.toggleRowSelection(row, true);
    });
  }
};

// 禁用全选功能
const handleSelectAll = () => {
  table.value.clearSelection();
};

// 选中 
const handleSelectionChange = (selection) => {
  console.log(selection)
  tableItem.value = selection
}
// 编辑成绩
const popupScoreEdit = ref(null)
const goEditScore = () => {
  if (!tableItem.value || tableItem.value.length <1) {
    return ElMessage.error('请选择要编辑成绩的学生')
  } else {
    popupScoreEdit.value.open(tableItem.value[0])
  }
}
// 发布成绩
const goPublish = () => {
  ElMessageBox.confirm('当发布成绩后，则无法再次编辑成绩。您确认发布成绩吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      ElMessage({
        type: 'success',
        message: '发布成功'
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消操作'
      })
    })
}
</script>

<style scoped lang="scss">
.index_classroom {
  margin-top: 8px;
  .index_classroom_table {
    width: 100%;
    margin-top: 20px;
  }
  .index_score_set {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    margin-bottom: 10px;
  }
}
::v-deep .el-table__header-wrapper .el-table-column--selection .el-checkbox {
  display: none;
} 
</style>
