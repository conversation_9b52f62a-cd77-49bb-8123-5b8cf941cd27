<template>
  <el-dialog v-model="dialogVisible" title="自动分组" width="500" :before-close="handleClose" style="margin-top: 30vh !important">
    <el-form ref="formRef" style="max-width: 600px" :model="form" label-width="150" class="demo-ruleForm" :rules="rules">
      <el-form-item label="创建分组个数" prop="bt">
          <el-input-number v-model="form.bt" :min="1" :max="10" style="width: 230px;"/>
      </el-form-item>
    </el-form>
        <p>注意：自动分组后手动创建小组的所有信息将会被全部覆盖，请您确认后再进行自动分组。</p>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'

const dialogVisible = ref(false)
const formRef = ref(null)
const form = ref({
  bt: ''
})
const rules = ref({
  bt: [{ required: true, message: '创建分组个数不能为空', trigger: 'change' }]
})
// 开启弹窗
const open = () => {
  dialogVisible.value = true
  if (formRef.value) formRef.value.resetFields()
}
// 关闭弹窗
const confirmFrom = () => {
  formRef.value.validate(valid => {
    if (valid) {
      console.log('submit!')
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss">
</style>
