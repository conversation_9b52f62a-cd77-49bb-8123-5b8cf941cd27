<template>
  <el-dialog v-model="dialogVisible" title="学校库导入学生" width="1000" :before-close="handleClose"
    style="margin-top: 30vh !important">
    <div class="index_classroom_operate">
      <div>
        <el-input v-model="input2" style="width: 240px; margin-right: 10px" placeholder="请输入学生姓名/学号"
          :suffix-icon="Search" />
        <el-select v-model="value" placeholder="院系" style="width: 140px; margin-right: 10px">
          <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="value" placeholder="年级" style="width: 140px">
          <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div>
        <el-button type="primary" @click="stuExamine">查询</el-button>
        <el-button type="primary" @click="stuExamine">下载模板</el-button>
      </div>
    </div>
    <el-upload v-model:file-list="fileList" class="import_mid_btn" :action="uploadUrl" :headers="headers"
      :before-upload="beforeAvatarUpload" :on-success="handleSuccess" :on-remove="handleRemove">
      <el-button type="primary">上传文件</el-button>
    </el-upload>
    <el-table ref="tableRef" :data="tableData">
      <el-table-column prop="xh" align="center" label="学号" />
      <el-table-column prop="xm" align="center" label="姓名" />
      <el-table-column prop="xy" align="center" label="学院" />
      <el-table-column prop="zy" align="center" label="专业" />
      <el-table-column prop="yx" align="center" label="院系" />
      <el-table-column prop="xn" align="center" label="学年" />
      <el-table-column prop="bj" align="center" label="班级" />
      <el-table-column prop="lxfs" align="center" label="联系方式" />
    </el-table>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken } from '@/utils/auth'
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/file/upload') // 上传的图片服务器地址
const headers = ref({
  Authorization: 'Bearer ' + getToken()
})
const dialogVisible = ref(false)
const formRef = ref(null)
const fileList = ref([])
const tableData = ref([
  {
    xh: '123456789',
    xm: '张三',
    xy: '信息学院',
    zy: '软件工程',
    yx: '信息学院',
    xn: '2020',
    bj: '软件工程1班',
    lxfs: '13888888888'
  }
])
const rules = ref({
  bt: [{ required: true, message: '签到标题不能为空', trigger: 'change' }],
  lx: [{ required: true, message: '签到类型不能为空', trigger: 'change' }],
  fs: [{ required: true, message: '结束方式不能为空', trigger: 'change' }]
})
// 开启弹窗
const open = () => {
  dialogVisible.value = true
  if (formRef.value) formRef.value.resetFields()
}
// 上传(只能上传excel文件)
const beforeAvatarUpload = rawFile => {
  console.log(rawFile)
  if (rawFile.type !== 'application/vnd.ms-excel' && rawFile.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
    ElMessage.error('上传失败！请上传 .xls, .xlsx格式文件！')
    return false
  }
  // else if (rawFile.size / 1024 / 1024 > 60) {
  //   ElMessage.error('上传失败！文件大小不能超过 60MB!')
  //   return false
  // }
  return true
}
// 关闭弹窗
const confirmFrom = () => {
  if (fileList.value.length === 0) {
    ElMessage({ type: 'info', message: '请上传文件' })
  } else {
    console.log('submit!')
    dialogVisible.value = false
  }
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.index_classroom_operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.import_mid_btn {
  margin: 20px 0;
}
</style>
