<template>
  <el-dialog v-model="dialogVisible" title="学校库导入学生" width="1000" :before-close="handleClose"
    style="margin-top: 30vh !important">
    <div class="index_classroom_operate">
      <div>
        <el-input v-model="queryParams.realName" style="width: 240px; margin-right: 10px" placeholder="请输入学生姓名/学号"
          :suffix-icon="Search" />
        <el-select v-model="queryParams.academyId" placeholder="院系" style="width: 140px; margin-right: 10px">
          <el-option v-for="item in academyList" :key="item.schoolId" :label="item.schoolName" :value="item.schoolId" />
        </el-select>
        <el-select v-model="queryParams.academicYearId" placeholder="年级" style="width: 140px">
          <el-option v-for="item in yearList" :key="item.academicYearId" :label="item.name"
            :value="item.academicYearId" />
        </el-select>
      </div>
      <div>
        <el-button type="primary" @click="handleQuery">查询</el-button>
      </div>
    </div>
    <el-table ref="tableRef" :data="dataList" :loadding="loadding" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="userNo" align="center" label="学号" />
      <el-table-column prop="realName" align="center" label="姓名" />
      <el-table-column prop="schoolName" align="center" label="学校" />
      <el-table-column prop="specialityName" align="center" label="专业" />
      <el-table-column prop="academyName" align="center" label="院系" />
      <el-table-column prop="academicYearName" align="center" label="学年" />
      <el-table-column prop="className" align="center" label="班级" />
      <el-table-column prop="phonenumber" align="center" label="联系方式" />
    </el-table>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { studentListForClass } from '@/api/edu/dutpUser'
import { yearListNoPage } from '@/api/basic/academicYear'
import { queryAcademyNoPage } from '@/api/basic/school'
import { batchAddMember } from '@/api/edu/member'

const props = defineProps({
  classId: {
    type: String,
    default: () => null
  }
})

const emit = defineEmits(['refresh'])

const { proxy } = getCurrentInstance()

const dialogVisible = ref(false)

const ids = ref([])
const dataList = ref([])
const loadding = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
})
const total = ref(0)
const yearList = ref([])
const academyList = ref([])
const getList = () => {
  loadding.value = true
  studentListForClass(queryParams.value).then(res => {
    dataList.value = res.rows
    total.value = res.total
    loadding.value = false
  })
}

const getAcademyList = () => {
  queryAcademyNoPage().then(res => {
    academyList.value = res.data
  })
}

const getYearList = () => {
  yearListNoPage().then(res => {
    yearList.value = res.data
  })
}
const handleQuery = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  }
  getList()
}

const handleSelectionChange = (val) => {
  ids.value = val.map(item => item.userId)
}

// 开启弹窗
const open = () => {
  getAcademyList()
  getYearList()
  dialogVisible.value = true
  handleQuery()
}

// 关闭弹窗
const confirmFrom = () => {
  if (ids.value.length === 0) {
    proxy.$modal.msgWarning('请选择要加入的学生')
    return
  }
  batchAddMember({
    classId: props.classId,
    userIds: ids.value
  }).then(res => {
    proxy.$modal.msgSuccess('加入成功')
    dialogVisible.value = false
    emit('refresh')
  }
  )
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.index_classroom_operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.import_mid_btn {
  margin: 20px 0;
}
</style>
