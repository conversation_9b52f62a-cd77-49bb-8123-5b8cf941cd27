<template>
  <el-dialog v-model="dialogVisible" title="我发布的" width="1000" :before-close="handleClose"
    style="margin-top: 3vh !important">
    <div class="published_introduce">
      <div class="published_introduce_title">
        <h3>{{ zjForm.zjm }}</h3>
        <div>
          <span v-show="zjForm.zt == '1'">未开始</span>
          <span v-show="zjForm.zt == '2'">进行中</span>
          <span v-show="zjForm.zt == '3'">已结束</span>
        </div>
      </div>
      <div class="published_introduce_content">
        <img :src="classTabZttlBlue" alt="">{{ zjForm.xzwz }}
      </div>
      <div class="content">
        <p>{{ zjForm.tlnr }}</p>
      </div>
      <div class="time">
        <p>开始时间：{{ zjForm.kssj }}</p>
        <p>结束时间：{{ zjForm.jssj }}</p>
      </div>
    </div>
    <el-divider />
    <div class="published_discuss">
      <div v-for="(item, index) in discussData" :key="index">
        <div class="discuss_content">
          <div class="content">
            <div class="content_name">
              <p>
                {{ item.fqr }}
              </p>
              <p v-if="item.sffqr == '1'">发起人</p>
            </div>
            <div class="content_content">
              <p @click="openInput(item)">{{ item.nr }} <span>{{ item.sj }}</span></p>
            </div>
          </div>
          <div class="discuss_btn" v-if="item.sffqr == '1'">
            <el-button type="primary" link @click="openEdit(item)">编辑</el-button>
            <el-button type="primary" link @click="delCuss(item.id)">删除</el-button>
          </div>
          <div v-else>
            <img :src="report" alt="">
            <img @click="liked(index, '2')" v-if="item.dz == '1'" :src="chatLike" alt="">
            <img @click="liked(index, '1')" v-else :src="chatStep" alt="">
          </div>
        </div>
        <div class="discuss_reply">
          <!-- <div class="reply_name">
          <p>大长今</p>
        </div> -->
          <div class="reply_content">
            <img class="reply_content_left" :src="topicReply" alt="">
            <div v-for="(hf, indexCuss) in item.xsList" :key="indexCuss" class="reply_content_right">
              <p @click="openInput(hf)"><span><i>{{ hf.fqr }}</i> 回复 <i>{{
                hf.hfs }}：</i></span>{{ hf.nr }}
              </p>
              <div class="discuss_btn" v-if="hf.sffqr == '1'">
                <el-button type="primary" link @click="openEdit(hf)">编辑</el-button>
                <el-button type="primary" link @click="delCuss(item.id)">删除</el-button>
              </div>
              <div v-else>
                <img :src="report" alt="">
                <img @click="likedCuss(index, indexCuss, '2')" v-if="hf.dz == '1'" :src="chatLike" alt="">
                <img @click="likedCuss(index, indexCuss, '1')" v-else :src="chatStep" alt="">
              </div>
            </div>
            <div v-if="item.hfList.length > 3">
              <el-button v-if="item.xsList?.length < item.hfList?.length" type="primary" link
                @click="expand(item)">
                展开更多回复({{ item.hfList.length - item.xsList?.length }}条)
                <el-icon>
                  <ArrowDown />
                </el-icon>
              </el-button>
              <el-button v-else type="primary" link @click="retract(item)">
                收起回复
                <el-icon>
                  <ArrowUp />
                </el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
  <PopupTopicInput ref="popupTopicInput" />
  <PopupTopicEdit ref="popupTopicEdit" />
</template>

<script setup>
import { nextTick, ref } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import classTabZttlBlue from '@/assets/images/smartCourse/smartCourseTab/class_tab_zttl_blue.png'
import topicReply from '@/assets/images/smartCourse/smartCourseTab/topic_reply.png'
import chatLike from '@/assets/images/smartCourse/smartCourseTab/chat_like.png'
import chatStep from '@/assets/images/smartCourse/smartCourseTab/chat_step.png'
import report from '@/assets/images/smartCourse/smartCourseTab/report.png'
import PopupTopicInput from './popup_topic_input.vue'
import PopupTopicEdit from './popup_topic_edit.vue'

const dialogVisible = ref(false)
const zjForm = ref({
  zjm: '第一章 机械制造工艺：从匠艺到智造',
  zt: '1',
  xzwz: '教材中选中的文字内容教材中选中的文字内容教材中选中的文字内容',
  tlnr: '发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容发布的讨论内容',
  kssj: '2025-01-01 12:00:00',
  jssj: '2025-01-01 12:00:00',
})
const discussData = ref([
  {
    id: '1',
    fqr: '李寻欢',
    sffqr: '1',
    nr: '有没有同学知道讨论的内容最终结果，答对前三名有奖励。',
    sj: '2025-08-23 12:00:00',
    dz: '2',
    hfList: [
      {
        id: '2',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '11这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '1',
      },
      {
        id: '3',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
      {
        id: '4555',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
      {
        id: '555',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
      {
        id: '555',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
      {
        id: '555',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
      {
        id: '555',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
      {
        id: '555',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
      {
        id: '555',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
      {
        id: '555',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
      {
        id: '555',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
      {
        id: '555',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
      {
        id: '555',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
      {
        id: '555',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
      {
        id: '555',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
      {
        id: '555',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '22这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
      {
        id: '555',
        fqr: '大长今',
        hfs: '李寻欢',
        sffqr: '2',
        nr: '111这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身',
        dz: '2',
      },
    ],
  },
  {
    id: '44',
    fqr: '琴同学',
    sffqr: '2',
    nr: '大家好，今年也要加油哦',
    sj: '2025-08-23 12:00:00',
    dz: '1',
    hfList: [
      {
        id: '55',
        fqr: '李寻欢',
        hfs: '琴同学',
        sffqr: '1',
        dz: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身'
      },
      {
        id: '66',
        fqr: '李寻欢',
        hfs: '琴同学',
        sffqr: '1',
        dz: '2',
        nr: '这个问题的原理就是认为各位蹦蹦蹦给问个韦尔股份吧为各位二网格物管费为各位岗位个省份深V 让规划玩分身'
      },
    ],
  }
])
// 初始化可见回复
const initVisibleReplies = () => {
  discussData.value.forEach(item => {
    // 初始显示3条
    item.xsList = item.hfList.slice(0, 3)
  })
}
// 开启弹窗
const open = () => {
  dialogVisible.value = true
  nextTick(() => {
    initVisibleReplies()
  })
}
const handleClose = () => {
  dialogVisible.value = false
}
const popupTopicInput = ref(null)
const openInput = (item) => {
  popupTopicInput.value.open(item)
}
const popupTopicEdit = ref(null)
const openEdit = (item) => {
  popupTopicEdit.value.open(item)
}
const liked = (index, num) => {
  discussData.value[index].dz = num
}
const likedCuss = (index, indexCuss, num) => {
  discussData.value[index].hfList[indexCuss].dz = num
}
// zhankai 
const expand = (item) => {
  // 计算了展开了多少
  const xsLength = item.xsList.length
  // 计算还剩多少可展开
  const syLength = Math.min(10, item.hfList.length - xsLength)
  
  // 获取下一批
  const xypList = item.hfList.slice(xsLength, xsLength + syLength)
  item.xsList = [...item.xsList, ...xypList]
  
  // 更新展开状态
}
// 收起回复
const retract = (item) => {
  item.xsList = item.hfList.slice(0, 3)
}

const delCuss = (id) => {
  ElMessageBox.confirm('是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
  }).catch(() => {
    console.log('取消操作')
  })
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.published_introduce {
  .published_introduce_title {
    display: flex;
    align-items: center;

    h3 {
      font-size: 18px;
    }

    span {
      display: block;
    }

    div {
      margin-left: 20px;

      span:nth-child(1) {
        width: 84px;
        height: 26px;
        border-radius: 20px 20px 20px 20px;
        border: 1px solid #969AAA;
        font-size: 14px;
        color: #969AAA;
        text-align: center;
        line-height: 26px;
      }

      span:nth-child(2) {
        width: 84px;
        height: 26px;
        border-radius: 20px 20px 20px 20px;
        border: 1px solid #52C41A;
        font-size: 14px;
        color: #52C41A;
        text-align: center;
        line-height: 26px;
      }

      span:nth-child(3) {
        width: 84px;
        height: 26px;
        border-radius: 20px 20px 20px 20px;
        border: 1px solid #969AAA;
        font-size: 14px;
        color: #969AAA;
        text-align: center;
        line-height: 26px;
      }
    }
  }

  .published_introduce_content {
    font-size: 16px;
    color: #585E76;

    img {
      width: 24px;
      vertical-align: middle;
      margin-right: 6px;
    }
  }

  .content {
    color: #585E76;
  }

  .time {
    color: #969AAA;
    display: flex;

    p {
      margin-right: 30px;
    }
  }
}

.published_discuss {
  max-height: 350px;
  overflow: auto;

  .discuss_content {
    display: flex;
    align-items: center;

    img {
      margin: 3px 20px 0 5px;
      cursor: pointer;
    }

    .content {
      width: 880px;

      // display: flex;
      // align-items: center;
      .content_name {
        display: flex;
        align-items: center;

        p {
          margin-bottom: 5px;
        }

        p:nth-child(1) {
          font-size: 16px;
          color: #585E76;
        }

        p:nth-child(2) {
          width: 52px;
          height: 20px;
          background: #FFEEEE;
          border-radius: 20px;
          font-size: 12px;
          color: #FF4D4F;
          text-align: center;
          line-height: 20px;
          margin: 11px 0 0 5px;
        }
      }

      .content_content {
        p {
          margin-top: 0;
          font-size: 16px;
          color: #333333;

          span {
            margin-left: 10px;
            font-size: 12px;
            color: #969AAA;
          }
        }

        p:hover {
          background: #eeeef0;
          cursor: pointer;
        }
      }
    }

    .discuss_btn {
      display: flex;
      align-items: center;
    }
  }

  .discuss_reply {
    margin: 0 0 0 30px;

    .reply_name {
      p {
        font-size: 16px;
        color: #585E76;
        margin-top: 0;
        margin-bottom: 5px;
      }
    }

    .reply_content {
      position: relative;

      .reply_content_left {
        position: absolute;
        top: -3px;
        left: -31px;
      }

      .reply_content_right {
        position: relative;
        display: flex;

        div {
          // position: absolute;
          // top: -3px;
          // right: 10px;

          img {
            margin: 3px 20px 0 5px;
            cursor: pointer;
          }
        }
      }

      p {
        font-size: 16px;
        color: #333333;
        margin-top: 0;
        margin-bottom: 5px;
        width: 850px;

        i {
          font-style: normal;
          color: #585E76;
        }
      }

      p:hover {
        background: #eeeef0;
        cursor: pointer;
      }
    }
  }
}
</style>
