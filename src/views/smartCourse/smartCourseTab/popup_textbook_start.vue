<template>
  <el-dialog v-model="dialogVisible" title="时间设置" width="400" :before-close="handleClose" style="margin-top: 30vh !important">
    <el-form ref="formRef" :model="form" label-width="auto" class="demo-ruleForm" :rules="rules">
      <el-form-item prop="endTime" label="结束时间">
        <el-date-picker
          v-model="form.endTime"
          type="datetime"
          placeholder="请选择时间"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {inject, ref} from 'vue'
import { ElMessageBox } from 'element-plus'

const emits = defineEmits(['refreshList','confirmStart']);
const dialogVisible = ref(false)
const formRef = ref(null)
const form = ref({})
const type = ref(2)
// 开启弹窗
const open = (formValue,flag) => {
  console.log(formValue)
  form.value = formValue
  dialogVisible.value = true
  if (formRef.value) formRef.value.resetFields()
  type.value = flag
}
// 关闭弹窗
const confirmFrom = n => {
  formRef.value.validate(valid => {
    if (valid) {
      if (type.value == 0) {
        // emits("refreshList",form.value)
        emits("confirmStart",form.value)
      } else if (type.value == 1) {
        emits("confirmStart",form.value)
      }
      formRef.value.resetFields()
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}

const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss">
// .el-dialog.signin_popup_dialog :not(.is-fullscreen) {
//   margin-top: 30vh !important;
// }
</style>
