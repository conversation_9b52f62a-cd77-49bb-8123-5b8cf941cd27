<template>
  <div>
    <div class="stu_manage_container">
      <div v-for="item in stuLabel" :key="item.value" @click="handleClick(item.value)"
        :class="item.value === active ? 'active' : ''">{{ item.label }}</div>
    </div>
    <div class="index_classroom">
      <div class="index_classroom_operate">
        <div>
          <el-input v-model="realName" clearable style="width: 240px; margin-right: 10px" placeholder="请输入学生姓名/学号"
            :suffix-icon="Search" />
          <el-select v-model="status" placeholder="全部" style="width: 240px" v-if="active === 3" clearable>
            <el-option v-for="item in smartCourseClassApplyStatusOptions" :key="item.value" :label="item.label"
              :value="item.value" />
          </el-select>
        </div>
        <div>
          <el-button type="primary" v-if="active === 1" plain @click="setAssistant">设置助教</el-button>
          <el-button type="primary" v-if="active === 1" @click="exportStu">导出学生</el-button>
          <el-button type="danger" v-if="active === 1" plain @click="delBatch">批量移除</el-button>
          <el-button type="primary" v-if="active === 1" @click="handleStuAdd">添加</el-button>
          <el-button type="primary" v-if="active === 3" @click="handleAuditAllApply">一键审批</el-button>
        </div>
      </div>
      <StuManage v-if="active === 1" :realName="realName" :classId="classId" ref="stuManage" />
      <StuExamine v-if="active === 3" :realName="realName" :classId="classId" ref="stuExamine" :status="status" />
    </div>
    <PopupSetAssistant ref="popupSetAssistant" @refresh="refresh" :classId="classId" />
    <popupStuAdd ref="popupStuAddRef" @refresh="refresh" :classId="classId" />
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import StuManage from './stu_manage.vue'
import StuExamine from './stu_examine.vue'
import PopupSetAssistant from './popup_set_assistant.vue'
import popupStuAdd from './popup_stu_add.vue'
import { delMember } from '@/api/edu/member'
import { auditAllApply } from '@/api/edu/apply'
import { smartCourseClassApplyStatusOptions } from '@/utils/optionUtil'
const { proxy } = getCurrentInstance()
const realName = ref("")
const stuLabel = ref([
  {
    label: '学生管理',
    value: 1
  },
  // {
  //   label: '班级扫码',
  //   value: 2,
  // },
  {
    label: '审核管理',
    value: 3
  }
])
const status = ref(null)
const active = ref(1)
const classId = ref(1)

// 导入 
const handleStuAdd = () => {
  proxy.$refs.popupStuAddRef.open()
}

// 设置助教
const setAssistant = () => {
  proxy.$refs.popupSetAssistant.open()
}

const handleClick = (value) => {
  active.value = value
}

// 一键审核
const handleAuditAllApply = () => {
  proxy.$modal
    .confirm('确认一键审核所有待审核的学生吗?')
    .then(() => auditAllApply({
      status: 1,
      classId: classId.value,
    }))
    .then(() => {
      refresh()
      proxy.$modal.msgSuccess('审核成功')
    })
    .catch((err) => {
      console.error(err)
      proxy.$modal.msgError('审核失败')
    })

}

// 导出 
const exportStu = () => {
  proxy.download(
    'edu/smartCourseMember/export',
    {
      classId: classId.value,
      realName: realName.value
    },
    `互动课堂班级成员数据.xlsx`
  )
}


// 刷新数据
const refresh = () => {
  if (active.value === 1) {
    proxy.$refs.stuManage.refresh()
  } else if (active.value === 3) {
    proxy.$refs.stuExamine.refresh()
  }
}

// 批量删除
const delBatch = () => {
  let ids = null;
  if (active.value === 1) {
    ids = proxy.$refs.stuManage.getSelectionIds()
  } else if (active.value === 3) {
    ids = proxy.$refs.stuExamine.getSelectionIds()
  }
  if (ids && ids.length == 0) {
    proxy.$modal.msgWarning('请勾选需要删除的学生')
    return
  }
  proxy.$modal
    .confirm('确定删除已选学生吗?')
    .then(() => delMember(ids))
    .then(() => {
      proxy.$modal.msgSuccess('删除成功')
    })
    .catch((err) => {
      console.error(err)
      proxy.$modal.msgError('删除失败')
    })
}
</script>

<style scoped lang='scss'>
.stu_manage_container {
  display: flex;
  margin: 10px 0 20px 0;

  .active {
    background-color: rgba(85, 91, 116, 1) !important;
    color: #fff !important;
  }

  div {
    width: 98px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 4px;
    border: 1px solid rgb(126, 136, 165);
    // margin: 0.2vw 0 0 0.83vw;
    cursor: pointer;
    overflow-wrap: break-word;
    font-size: 14px;
    font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
    font-weight: normal;
    white-space: nowrap;
    margin-right: 14px;
  }
}

.index_classroom {
  margin-top: 8px;

  .index_classroom_operate {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>