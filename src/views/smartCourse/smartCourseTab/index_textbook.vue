<template>
  <div>
    <div class="textbook_set">
      <div>
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px">
          <el-form-item label="活动名称" prop="missionTitle">
            <el-input v-model="queryParams.missionTitle" placeholder="请输入活动名称" />
          </el-form-item>
          <el-form-item label="状态：" prop="status">
            <el-select v-model="queryParams.status" placeholder="状态" style="width: 200px">
              <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-button type="primary" @click="createTextbook">创建教材任务</el-button>
    </div>
    <div>
      <div v-if="missionList && missionList.length > 0" class="textbook_list" v-for="(item, index) in missionList" :key="indexKey">
        <div class="textbook_list_left">
          <div class="textbook_list_left_top">
            <div>{{ item.missionTitle }}</div>
            <div class="wks" v-if="item.status == '1'">未开始</div>
            <div class="jxz" v-if="item.status == '2'">进行中</div>
            <div v-if="item.status == '3'">已结束</div>
          </div>
          <div class="textbook_list_left_bottom">
            <span v-if="item.status == '1'">任务完成人数：{{ item.finishCount }}</span>
            <span v-else>任务完成人数：{{ item.finishCount }}/{{ item.memberCount }}</span>
            <span v-if="item.status != '1'">开始时间：{{ item.startTime }}</span>
            <span v-if="item.status != '1'">结束时间：{{ item.endTime?item.endTime:"--" }}</span>
          </div>
        </div>
        <div>
          <el-button v-if="item.status == '1'" @click="startTextbook(item)">开始活动</el-button>
          <el-button v-if="item.status == '1'" @click="editTextbook(item)">编辑</el-button>
          <el-button v-if="item.status == '1'" @click="deleteTextbook(item)">删除</el-button>
          <el-button v-if="item.status == '2'" @click="endTextbook(item.missionId)">结束活动</el-button>
          <el-button v-if="item.status != '1'" @click="viewTextbook">查看</el-button>
        </div>
      </div>
      <div v-else>
        <el-empty description="暂无数据"></el-empty>
      </div>
    </div>
  </div>
  <PopupTextbookAdd ref="popupTextbookAdd" :course="course" :classDetail="classDetail" @refreshList="refreshList" />
  <PopupTextbookStart ref="popupTextbookStart" @confirmStart="confirmStart"/>
  <PopupTextbookView ref="popupTextbookView" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import PopupTextbookAdd from './popup_textbook_add.vue'
import PopupTextbookStart from './popup_textbook_start.vue'
import PopupTextbookView from './popup_textbook_view.vue'
import {delMission, getMission, listMission, updateMission} from "@/api/edu/mission.js";
import {now} from "@vueuse/core";

const props = defineProps({
  /*  course: {
      type: Object,
      default: {},
    }*/
  classDetail: {
    type: Object,
    default: {
    },
  },
  /*测试用*/
  course: {
    type: Object,
    default: {
/*      "createBy": null,
      "createTime": null,
      "updateBy": null,
      "updateTime": null,
      "remark": null,
      "courseId": "11",
      "courseName": "网络安全互动课堂用",
      "courseCode": "SEC1012",
      "courseType": 1,
      "categoryId": "101",
      "planId": "210",
      "bookId": "310",
      "description": "了解网络安全基础知识",
      "coverImageUrl": "https://example.com/images/sec101.jpg",
      "classroomQuestionWeight": 25,
      "homeworkWeight": 35,
      "examWeight": 40,
      "hour": 48,
      "status": 1,
      "userId": "1854076781086117972",
      "createdBy": null,
      "updatedBy": null,
      "delFlag": null,
      "classTotal": 3,
      "realName": "13840917725",
      "searchVar": null*/
      /*"createBy": null,
      "createTime": null,
      "updateBy": null,
      "updateTime": null,
      "remark": null,
      "courseId": "1943215209544761346",
      "courseName": "第六个教务课程",
      "courseCode": "999",
      "courseType": 1,
      "categoryId": "1912780772558868481",
      "planId": null,
      "bookId": "1893128341502074882",
      "description": "99",
      "coverImageUrl": "https://dutp-test.oss-cn-beijing.aliyuncs.com/1752133581962.png",
      "classroomQuestionWeight": null,
      "homeworkWeight": null,
      "examWeight": null,
      "hour": 1,
      "status": 0,
      "userId": "1854076781086117925",
      "createdBy": null,
      "updatedBy": null,
      "delFlag": null,
      "classTotal": 0,
      "realName": "学生(雪)1",
      "searchVar": null*/
    },
  },
});
const { proxy } = getCurrentInstance()
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    // classId: props.classDetail.classId
    classId: 25,
  },
});
const { queryParams } = toRefs(data)

// 状态
const statusList = ref([
  {
    label: '未开始',
    value: '1'
  },
  {
    label: '进行中',
    value: '2'
  },
  {
    label: '已结束',
    value: '3'
  }
])
const indexKey = ref(0)
const missionList = ref([])
const total = ref(0)
const missionId = ref(null)

onMounted(() => {
  getList()
})

/** 查询列表 */
const getList = async () => {
  queryParams.pageNum = 1
  let response = await listMission(queryParams.value)
  missionList.value = response.rows;
  total.value = response.total;
}
const popupTextbookAdd = ref(null)
const createTextbook = () => {
  // 获取教材任务详情
  popupTextbookAdd.value.open(1)
}

const refreshList = () => {
  proxy.resetForm('queryRef')
  // proxy.$refs.queryRef
  indexKey.value++
  handleQuery()
}

const resetQuery = () => {
  indexKey.value = 0
  proxy.resetForm('queryRef')
  handleQuery();
}

const editTextbook = async (row) => {
  console.log(row)
  missionId.value = row.missionId
  console.log(missionId.value)
  popupTextbookAdd.value.open(2,missionId.value)
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

// 开始活动
const popupTextbookStart = ref(null)
const startTextbook = (item) => {
  popupTextbookStart.value.open(item,1)
}
// 开始活动确定
const confirmStart = (item) => {
  updateCourseMisson(item,1)
  indexKey.value++;
}

// 查看
const popupTextbookView = ref(null)
const viewTextbook = () => {
  popupTextbookView.value.open()
}
// 删除活动+
const deleteTextbook = (row) => {
  proxy.$modal
      .confirm("删除后不可恢复，确定要删除吗?")
      .then(function () {
        return delMiss(row.missionId)
      })
      .then(() => {
        getList();

      })
      .catch(() => {});
}

const delMiss = async (missionId) => {
  let response = await delMission(missionId)
  if (response.code == 200) {
    proxy.$modal.msgSuccess("删除成功");
  }
}

// 结束活动
const endTextbook = (id) => {
  proxy.$modal
      .confirm("还未到达您所设置的教务任务结束时间，是否提前结束此任务?")
      .then(function () {
        let param = {
          status: 3,
          endTime:Date.now(),
          missionId: id,
        }
        return updateCourseMisson(param,0)
      })
      .then(() => {
        getList();
      })
      .catch(() => {});
}

const updateCourseMisson = async (item,flag) => {
  console.log(flag)
  if (flag == 1) {
    item.startTime = formatDateForBackend(new Date())
    item.status = 2
  } else {
    item.endTime = formatDateForBackend(new Date())
    item.status = 3
  }
  let response = await updateMission(item)
  if (response.code == 200) {
    if (flag == 1) {
      proxy.$modal.msgSuccess('任务已开始')
    } else {
      proxy.$modal.msgSuccess('任务已结束')
    }
  }
}

function formatDateForBackend(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
</script>

<style scoped lang="scss">
.textbook_set {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  margin-bottom: 10px;
}
.textbook_list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-image: url('../../../assets/images/smartCourse/smartCourseDetails/green_bg.png');
  background-size: 100% 100px;
  background-repeat: no-repeat;
  width: 100%;
  height: 100px;
  margin-top: 15px;
  padding: 0 25px;
  .textbook_list_left_top {
    display: flex;
  }
  .textbook_list_left_bottom {
    font-size: 16px;
    color: #585e76;
    margin-top: 10px;
    span {
      margin-right: 20px;
    }
  }
}
.textbook_list_left_top div:first-child {
  font-size: 20px;
  color: #333333;
  margin-right: 10px;
}
.textbook_list_left_top div:last-child {
  width: 84px;
  height: 26px;
  border-radius: 20px 20px 20px 20px;
  border: 1px solid #969aaa;
  font-size: 14px;
  color: #969aaa;
  text-align: center;
  line-height: 26px;
}
.wks {
  border: 1px solid rgb(238, 161, 49) !important;
  color: rgb(238, 161, 49) !important;
}
.jxz {
  border: 1px solid rgb(82, 196, 26)!important;
  color: rgb(82, 196, 26) !important;
}
</style>
