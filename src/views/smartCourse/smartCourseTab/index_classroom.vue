<template>
  <div class="index_classroom">
    <el-date-picker
      v-model="value2"
      type="date"
      placeholder="请选择日期"
      :disabled-date="disabledDate"
      :shortcuts="shortcuts"
      :size="size"
    />
    <el-table :data="tableData" class="index_classroom_table">
      <el-table-column prop="sksj" label="上课时间" />
      <el-table-column prop="jxhd" label="教学活动" />
      <el-table-column prop="jksj" label="结课时间" />
      <el-table-column prop="rs" label="人数" />
      <el-table-column prop="zt" label="状态">
        <template #default="scope">
          <span v-if="scope.row.zt == '1'"> 上课中 </span>
          <span v-if="scope.row.zt == '2'"> 已结课 </span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="primary" v-if="scope.row.zt == '1'" link @click="attendClass(1)">上课</el-button>
          <el-button type="primary" v-if="scope.row.zt == '2'" link @click="attendClass(2)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance()
const tableData = ref([
  {
    id: '1854076781086117968',
    sksj: '2025-06-03 12:12:03',
    jxhd: '教学活动',
    jksj: '2025-06-03 12:12:03',
    rs: '20',
    zt: '1'
  },
  {
    id: '2',
    sksj: '2025-06-03 12:12:03',
    jxhd: '教学活动',
    jksj: '2025-06-03 12:12:03',
    rs: '20',
    zt: '2'
  }
])
// 上课
const attendClass = (type) => { 
  console.log(type)
  proxy.$router.push({
    path: '/smart-course-details',
    query: { lessonId: '1854076781086117968' }
  });
}
</script>

<style scoped lang="scss">
.index_classroom {
  margin-top: 8px;
  .index_classroom_table {
    width: 100%;
    margin-top: 20px;
  }
}
</style>
