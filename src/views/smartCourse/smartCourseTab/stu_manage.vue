<template>
  <el-table :loadding="loadding" :data="dataList" class="index_classroom_table"
    @selection-change="handleSelectionChange">
    <el-table-column type="selection" width="55" />
    <el-table-column prop="userNo" align="center" label="学号">
      <template #default="scope">
        <span v-if="scope.row.role === 2">{{ `${scope.row.userNo}（助教）` }}</span>
        <span v-else>{{ scope.row.userNo }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="realName" align="center" label="姓名" />
    <el-table-column prop="phonenumber" align="center" label="手机号" />
    <el-table-column prop="academyName" align="center" label="所在院系" />
    <el-table-column prop="specialityName" align="center" label="专业" />
    <el-table-column prop="academicYearName" align="center" label="学年" />
  </el-table>
  <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
    @pagination="getList"/>
</template>

<script setup>
import { listForClass } from '@/api/edu/member'
import { watchDebounced } from '@vueuse/core'

const props = defineProps({
  realName: {
    type: String,
    default: () => ''
  },
  classId: {
    type: String,
    default: () => null
  }
})

const { proxy } = getCurrentInstance()

const ids = ref([])
const dataList = ref([])
const loadding = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  classId: 1
})
const total = ref(0)

watchDebounced(() => props.realName, (nValue) => {
  queryParams.value.pageNum = 1
  queryParams.value.realName = nValue;
  getList()
}, { debounce: 500, maxWait: 2000 },)

const getList = () => {
  loadding.value = true
  queryParams.value.classId = props.classId
  listForClass(queryParams.value).then(res => {
    dataList.value = res.rows
    total.value = res.total
    loadding.value = false
  })
}

// 刷新数据
const refresh = () => {
  queryParams.value.pageNum = 1
  getList()
}

// 勾选
const handleSelectionChange = val => {
  // 勾选id集合
  ids.value = val.map(item => item.classMemberId)
}

// 获取勾选id集合
const getSelectionIds = () => {
  return ids.value
}

onMounted(() => {
  getList()
})


defineExpose({
  refresh,
  getSelectionIds,
});
</script>

<style scoped lang="scss">
.index_classroom_table {
  width: 100%;
  margin-top: 20px;
}
</style>
