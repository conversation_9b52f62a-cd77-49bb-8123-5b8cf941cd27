<template>
  <div class="box_4 flex-col">
    <div class="go_back">
      <el-button
        class="go_back_btn"
        @click="goBack"
        type="primary"
        plain>返回</el-button>
    </div>
    <div
      class="details_brain_content">
      <div
        class="details_brain_content_title">
        <h2>{{ form.brainstormTitle }}</h2>
        <el-button
          @click="downloadRes"
          size="small">下载调查结果</el-button>
      </div>
      <div
        class="details_brain_content_time">
        创建时间：{{ form.createTime }}
      </div>
      <p>
        {{ form.brainstormContent }}
      </p>
      <div
        class="details_brain_content_url">
        <div
          v-for="(item, index) in form.fileList"
          :key="index">
          <el-image
            v-if="item.fileUrl.endsWith('.jpg') || item.fileUrl.endsWith('.jpeg') || item.fileUrl.endsWith('.png')"
            class="details_brain_content_url_img"
            :src="item.fileUrl"
            fit="contain" />
          <el-button
            class="details_brain_content_url_btn"
            @click="downloadUrl(item.fileUrl)"
            v-else
            link>{{ item.fileNmae }}</el-button>
        </div>
      </div>
      <div
        class="details_brain_content_title">
        <h2>
          全部回答（{{ form.replyList.length }}）
        </h2>
        <el-button
          v-if="!sortBool"
          size="small"
          @click="sortList(1)"
          :icon="ArrowDown">提交排序显示</el-button>
        <el-button
          v-if="sortBool"
          size="small"
          @click="sortList(2)"
          :icon="ArrowUp">提交排序显示</el-button>
      </div>
      <div
        v-for="(item, index) in form.replyList"
        :key="index">
        <div
          class="details_brain_content_time">
          提交时间：{{ item.createTime }}
        </div>
        <div
          class="details_brain_content_name">
          {{ item.realName }}
        </div>
        <div>
          <el-input
            class="details_brain_content_answer"
            type="textarea"
            :rows="5" readonly
            v-model="item.doingsRespond"></el-input>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import {badgeProps, ElMessage} from 'element-plus';
import {getBrainstorm} from "@/api/edu/brainstorm.js";

const { proxy } = getCurrentInstance()
const emits = defineEmits(['listActivityId'])
const props = defineProps({
  activityId: {
    type: [String, Number],
    default: ''
  }
});
const sortBool = ref(false)
const form = ref({
  replyList:[]
})
// 下载结果
const downloadRes = url => {
  window.open(url)
}
// 下载url
const downloadUrl = url => {
  window.open(url)
}
// 排序
const sortList = n => {
  sortBool.value = !sortBool.value
}

async  function initData() {
  const response = await getBrainstorm(props.activityId);
  if (response.code == 200 ){
    form.value = response.data;
  }else {
    ElMessage.error(response.message);
  }
}

onMounted(() => {
  initData();
})
// 跳转详情页
const goBack = () => {
  emits('listActivityId')
}
</script>

<style scoped lang="scss">
.go_back {
  text-align: right;
  .go_back_btn {
    margin: 20px 20px 0 0;
  }
}
.details_brain_content {
  padding: 10px 20px 20px 20px;
  .details_brain_content_title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    h2 {
      font-size: 20px;
      color: #333333;
      font-weight: 500;
    }
  }
  .details_brain_content_time {
    font-size: 15px;
    color: #999999;
  }
  P {
    font-size: 15px;
    color: #585e76;
  }
  .details_brain_content_url {
    display: flex;
    .details_brain_content_url_img {
      width: 200px;
      margin-right: 10px;
    }
    .details_brain_content_url_btn {
      margin-top: 100px;
      margin-right: 10px;
    }
  }
  .details_brain_content_name {
    font-size: 15px;
    color: #333333;
    margin: 10px 0;
  }
  .details_brain_content_answer {
    margin-bottom: 20px;
  }
}
</style>
