<template>
  <div class="box_4 flex-col">
    <div class="go_back">
      <el-button
        class="go_back_btn"
        @click="goBack"
        type="primary"
        plain>返回</el-button>
    </div>
    <div
      class="details_signin_content">
      <h3>签到详情</h3>
      <div
        class="signin_content_detail">
        <span>{{ res.sj }}的签到</span>
        <span>签到类型：
          <i
            v-if="res.qdlx == '1'">教师签到</i>
          <i
            v-if="res.qdlx == '2'">手势签到</i>
          <i
            v-if="res.qdlx == '3'">签到码</i>
        </span>
        <span>创建时间：{{ res.cjsj }}</span>
        <span>结束时间：{{ res.jssj }}</span>
      </div>
      <div
        class="signin_content_tab">
        <div
          style="display: flex">
          <div
            v-for="(item, index) in list"
            :key="index"
            @click="sortList(index)"
            class="signin_content_tab_title"
            :class="{ signin_content_tab_title_active: index == sortIndex }">
            {{ item.name }}
          </div>
        </div>
        <div
          class="signin_content_tab_search">
          <el-input
            v-model="input1"
            style="width: 240px"
            size="large"
            placeholder="请输入搜索关键字"
            :suffix-icon="Search" />
        </div>
      </div>
      <div>
        <el-table
          :data="res.tableData"
          style="width: 100%">
          <el-table-column
            prop="xh"
            label="学号" />
          <el-table-column
            prop="xm"
            label="姓名" />
          <el-table-column
            prop="ssbj"
            label="所属班级" />
          <el-table-column
            prop="ssxz"
            label="所属小组" />
          <el-table-column
            prop="dfr"
            label="打分人" />
          <el-table-column
            prop="zt"
            label="状态">
            <template
              #default="scope">
              <span
                v-if="scope.row.zt == '1'">已签到</span>
              <span
                v-if="scope.row.zt == '2'">未签到</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="qdsj"
            label="签到时间" />
          <el-table-column
            label="修改状态">
            <template
              #default="scope">
              <el-button
                type="primary"
                link
                v-if="scope.row.zt == '2'">已签到</el-button>
              <el-button
                type="primary"
                link
                v-if="scope.row.zt == '1'">未签到</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ArrowDown, Search, ArrowUp } from '@element-plus/icons-vue'
import { badgeProps } from 'element-plus'

const { proxy } = getCurrentInstance()
const emits = defineEmits(['listActivityId'])
const sortBool = ref(false)
const sortIndex = ref(0)
const res = ref({
  sj: '2025-02-50',
  qdlx: '1',
  cjsj: '2025-06-05 16:11',
  jssj: '2025-06-05 16:11',
  tableData: [
    {
      xh: '123123',
      xm: '李神通',
      ssbj: '网络一班',
      ssxz: '小组2',
      dfr: '李如龙',
      zt: '1',
      qdsj: '2025-01-02 16:11'
    },
    {
      xh: '123123',
      xm: '李神通',
      ssbj: '网络一班',
      ssxz: '小组2',
      dfr: '李如龙',
      zt: '2',
      qdsj: '2025-01-02 16:11'
    }
  ]
})
const list = ref([
  {
    id: '1',
    name: '学生名单'
  },
  {
    id: '2',
    name: '已签到学生'
  },
  {
    id: '3',
    name: '未签到学生'
  }
])
// 下载结果
const downloadRes = url => {
  window.open(url)
}
// 下载url
const downloadUrl = url => {
  window.open(url)
}
// 排序
const sortList = n => {
  sortBool.value = !sortBool.value
  sortIndex.value = n
}
// 跳转详情页
const goBack = () => {
  emits('listActivityId')
}
</script>

<style scoped lang="scss">
.go_back {
  text-align: right;
  .go_back_btn {
    margin: 20px 20px 0 0;
  }
}
.details_signin_content {
  padding: 0 20px 20px 20px;
  .signin_content_detail {
    font-size: 15px;
    color: #585e76;
    span {
      margin-right: 30px;
    }
    i {
      font-style: normal;
    }
  }
  .signin_content_tab {
    display: flex;
    justify-content: space-between;
    .signin_content_tab_title {
      width: 105px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #7e88a5;
      margin: 25px 10px 20px 0;
      color: #333333;
      cursor: pointer;
    }
    .signin_content_tab_search {
      margin-top: 17px;
    }
    .signin_content_tab_title_active {
      background: #555b74;
      color: #ffffff;
    }
  }
}
</style>
