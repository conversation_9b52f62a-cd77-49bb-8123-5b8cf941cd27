<template>
  <el-dialog
    v-model="dialogVisible"
    :title="
      showFlag == 0 ? '新建作品秀' : showFlag == 1 ? '修改作品秀' : '查看作品秀'
    "
    width="900"
    :before-close="handleClose"
    style="margin-top: 3vh !important"
  >
    <el-form
      ref="formRef"
      :model="form"
      label-width="auto"
      class="demo-ruleForm"
      :rules="rules"
    >
      <el-form-item label="标题" prop="workShowTitle">
        <el-input
          show-word-limit
          maxlength="50"
          v-model="form.workShowTitle"
          type="text"
          autocomplete="off"
          placeholder="请输入标题"
        />
      </el-form-item>
      <el-form-item label="题干" prop="workShowContent">
        <el-input
          v-model="form.workShowContent"
          :rows="3"
          type="textarea"
          show-word-limit="true"
          maxlength="500"
          placeholder="请输入题干"
        />
      </el-form-item>
      <el-form-item label="选择作业">
        <el-button type="primary" @click="openChange">选择作业</el-button>
      </el-form-item>
      <div class="popup_show_tag">
        <el-tag
          class="popup_show_tag_item"
          v-for="(tag, index) in changeValList"
          :key="tag.assignmentId"
          closable
          :disable-transitions="false"
          @close="handleCloseTag(index)"
        >
          {{ tag.name }}
        </el-tag>
      </div>
      <el-form-item label="选择作品">
        <el-table
          class="popup_show_table"
          border
          ref="tableRef"
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="40" />
          <el-table-column prop="userNo" label="学号" />
          <el-table-column prop="realName" label="姓名" />
          <el-table-column prop="className" label="所属班级" />
          <el-table-column prop="groupName" label="所属小组" />
          <el-table-column prop="scorerName" label="打分人" />
          <el-table-column prop="zt" label="状态">
            <template #default="scope">
              已提交
              <!-- <span v-if="scope.row.zt == 1">未提交</span> -->
              <!-- <span v-if="scope.row.zt == 2">已提交</span> -->
            </template>
          </el-table-column>
          <el-table-column prop="score" label="作业分数">
            <template #default="scope">
              <span>{{ scope.row.score }}/{{ scope.row.totalScore }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button link type="primary" @click="openShow(scope.row)"
                >查看作业</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmFrom">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <PopupShowChange @changeVal="changeVal" ref="popupShowChange" />
  <PopupShowDetails ref="popupShowDetails" />
</template>

<script setup>
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import PopupShowChange from "./popup_show_change.vue";
import PopupShowDetails from "./popup_show_details.vue";
import { getUserAnswerSpare } from "@/api/edu/answer";

const props = defineProps({ form: { type: Object, default: {} } });
const showFlag = ref(0);

const dialogVisible = ref(false);
const formRef = ref(null);
const ids = ref([]);
const changeValList = ref([]);
const changeValIds = ref([]);
const tableRef = ref(null);
const form = ref({
  workShowTitle: "",
  workShowContent: "",
});
const dynamicTags = ref(["Tag 1", "Tag 2", "Tag 3"]);
const tableData = ref([]);
const rules = ref({
  workShowTitle: [
    { required: true, message: "标题不能为空", trigger: "change" },
  ],
  workShowContent: [
    { required: true, message: "题干不能为空", trigger: "change" },
  ],
});
// 开启弹窗
const open = () => {
  changeValList.value = [];
  changeValIds.value = [];
  ids.value = [];
  dialogVisible.value = true;
  // 清空校验
  if (formRef.value) formRef.value.resetFields();
  // 清空表格选中
  if (tableRef.value) tableRef.value.clearSelection();
};
// 选择作业
const popupShowChange = ref(null);
const openChange = () => {
  popupShowChange.value.open();
};
// 作业返回id
const changeVal = (val) => {
  changeValList.value = changeValList.value.concat(val);
  console.log("changeValList.value", changeValList.value);
  changeValIds.value = changeValList.value.map((item) => item.assignmentId);
  console.log("changeValIds.value", changeValIds.value);
  getUserAnswerSpare(changeValIds.value.join(",")).then((res) => {
    console.log("res", res);
    tableData.value = res.data;
  });
};
const popupShowDetails = ref(null);
// 查看作业详情
const openShow = (row) => {
  popupShowDetails.value.open(row);
};
// 确定
const confirmFrom = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      if (changeValIds.value.length == 0) {
        return ElMessage({
          type: "info",
          message: "请选择作业",
        });
      }
      if (ids.value.length == 0) {
        return ElMessage({
          type: "info",
          message: "请选择作品",
        });
      }
      console.log("submit!");
      dialogVisible.value = false;
    } else {
      console.log("error submit!");
    }
  });
};
// 勾选
const handleSelectionChange = (val) => {
  // 勾选id集合
  ids.value = val.map((item) => item.id);
};
const handleCloseTag = (i) => {
  changeValList.value.splice(i, 1);
  changeValIds.value.splice(i, 1);
  if (changeValIds.value.length > 0) {
    getUserAnswerSpare(changeValIds.value.join(",")).then((res) => {
      tableData.value = res.data;
    });
  } else {
    tableData.value = [];
  }
};

const handleClose = () => {
  dialogVisible.value = false;
};
defineExpose({
  open,
});
</script>
<style lang="scss" scoped>
.popup_show_table {
  max-height: 300px;
  overflow-y: auto;
}
.popup_show_tag {
  margin: 0 20px 15px 75px;
  .popup_show_tag_item {
    margin: 3px;
  }
}
</style>
