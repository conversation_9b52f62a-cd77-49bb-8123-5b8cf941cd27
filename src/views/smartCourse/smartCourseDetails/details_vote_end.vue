<template>
  <div class="box_4 flex-col">
    <div class="go_back">
      <el-button
        class="go_back_btn"
        @click="goBack"
        type="primary"
        plain>返回</el-button>
    </div>
    <div
      class="details_brain_content">
      <div
        class="details_brain_content_title">
        <h2>{{ res.bt }}</h2>
        <el-button
          @click="downloadRes"
          size="small">下载调查结果</el-button>
      </div>
      <div
        class="details_brain_content_time">
        <span>开始时间：{{ res.cjsj }}</span>
        <span>结束时间：{{ res.cjsj }}</span>
      </div>
      <div
        class="details_brain_content_title_small">
        投票小标题</div>
      <p>
        {{ res.nr }}
      </p>
      <!-- 进度条 -->
      <div
        class="details_brain_content_progress_box"
        v-for="(item,index) in res.proList"
        :key="index">
        <div
          class="details_brain_content_progress">
          <div>
            <span>{{item.xx}}.
              {{ item.nr }}</span>
            <span
              class="details_brain_content_progress_span">投票人数：{{ item.tprs }}</span>
          </div>
          <div
            class="details_brain_content_progress_num">
            {{item.jd}} %
          </div>
        </div>
        <el-progress
          :percentage="item.jd" />
      </div>
      <div
        class="details_brain_content_title_small">
        备注</div>
      <p>
        {{ res.nr }}
      </p>
      <div
        class="details_brain_content_title_small">
        参与率</div>
      <div ref="chartDom"
        class="chart-container">
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { badgeProps } from 'element-plus'
import * as echarts from 'echarts'

const { proxy } = getCurrentInstance()
const emits = defineEmits(['listActivityId'])
const sortBool = ref(false)
const format = ref('')
const res = ref({
  bt: '投票问卷标题',
  cjsj: '2025-03-12',
  nr: '头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容头脑风暴内容',
  proList: [
    {
      xx: 'A',
      nr: '我是选项A的内容',
      tprs: '3',
      jd: 70
    },
    {
      xx: 'B',
      nr: '我是选项A的内容',
      tprs: '3',
      jd: 70
    }
  ],
  answerList: [
    {
      id: '1',
      name: '我是名字',
      time: '2025-03-12',
      answer:
        '福建的优势在侨资。如果有了同侨资合营的银行，就提供了引进侨资的渠道。厦门国际银行积极引入侨资、外资，通过债券承销、股权融资等方式筹集资金，引来金融活水为特区建设提供了有力资金支持。'
    },
    {
      id: '2',
      name: '我是名字',
      time: '2025-03-12',
      answer:
        '福建的优势在侨资。如果有了同侨资合营的银行，就提供了引进侨资的渠道。厦门国际银行积极引入侨资、外资，通过债券承销、股权融资等方式筹集资金，引来金融活水为特区建设提供了有力资金支持。'
    }
  ]
})
// 下载结果
const downloadRes = url => {
  window.open(url)
}

// 跳转详情页
const goBack = () => {
  emits('listActivityId')
}

// 图表 DOM 引用
const chartDom = ref(null)

// 新增图表实例
let chartInstance = null

// 初始化echarts
const initChart = () => {
  if (!chartDom.value) return

  // 销毁旧实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建新实例
  chartInstance = echarts.init(chartDom.value)

  // 配置
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {d}%'
    },
    legend: {
      orient: 'vertical',
      left: 400,
      top: 'center'
    },
    series: [
      {
        name: '参与率',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['30%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center',
          formatter: '{d}%', // 显示百分比，其中{d}是内置变量，代表数据百分比
          fontSize: 40 // 字体大小
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '40',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 17, name: '已参与人数 17人' },
          { value: 3, name: '未参与人数 3人' }
        ]
      }
    ]
  }

  chartInstance.setOption(option)

  // 响应式调整
  window.addEventListener('resize', () => {
    chartInstance.resize()
  })
}
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped lang="scss">
.chart-container {
  width: 600px;
  height: 400px;
  display: flex; /* 使用 Flexbox */
  justify-content: flex-start; /* 内容靠左对齐 */
}
::v-deep .el-progress__text {
  display: none;
}
.go_back {
  text-align: right;
  .go_back_btn {
    margin: 20px 20px 0 0;
  }
}
.details_brain_content {
  padding: 10px 20px 20px 20px;
  .details_brain_content_title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    h2 {
      font-size: 20px;
      color: #333333;
      font-weight: 500;
    }
  }
  .details_brain_content_time {
    font-size: 15px;
    color: #999999;
    span {
      margin-right: 30px;
    }
  }
  P {
    font-size: 15px;
    color: #585e76;
  }
  .details_brain_content_title_small {
    margin-top: 20px;
  }
  .details_brain_content_progress_box {
    // width: 80%;
    margin-bottom: 10px;
  }
  .details_brain_content_progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    .details_brain_content_progress_span {
      font-size: 13px;
      color: #585e76;
      margin-left: 15px;
    }
    .details_brain_content_progress_num {
      font-size: 13px;
      color: #585e76;
      margin-top: 5px;
    }
  }
}
</style>
