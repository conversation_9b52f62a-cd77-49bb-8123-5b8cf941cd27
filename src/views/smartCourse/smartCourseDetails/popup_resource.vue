<template>
  <el-dialog v-model="dialogVisible" title="新增分配资源包" width="600" :before-close="handleClose" style="margin-top: 20vh !important">
    <el-form ref="formRef" :model="form" label-width="auto" class="demo-ruleForm" :rules="rules">
      <el-form-item label="发送时间" prop="bt">
        <el-date-picker v-model="form.bt" type="date" placeholder="请选择发送时间" :size="size" />
      </el-form-item>
      <el-form-item label="资源">
        <el-button type="primary" @click="openChange">选择资源 </el-button>
      </el-form-item>
      <div class="popup_show_tag">
        <el-tag
          class="popup_show_tag_item"
          v-for="(tag, index) in changeValList"
          :key="tag.id"
          closable
          :disable-transitions="false"
          @close="handleCloseTag(index)"
        >
          {{ tag.fileName }}
        </el-tag>
      </div>
      <el-form-item label="可否下载" prop="nr">
        <el-select v-model="form.nr" placeholder="请选择是否可下载" style="width: 240px">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消 </el-button>
        <el-button type="primary" @click="confirmFrom">发送 </el-button>
      </div>
    </template>
  </el-dialog>
  <PopupResourceChange @changeVal="changeVal" ref="popupResourceChange" />
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PopupResourceChange from './popup_resource_change.vue'

const dialogVisible = ref(false)
const formRef = ref(null)
const changeValList = ref([])
const changeValIds = ref([])
const tableRef = ref(null)
const form = ref({
  bt: '',
  nr: ''
})
const options = ref([
  {
    value: '1',
    label: '可下载'
  },
  {
    value: '2',
    label: '不可下载'
  }
])
const dynamicTags = ref(['Tag 1', 'Tag 2', 'Tag 3'])

const rules = ref({
  bt: [{ required: true, message: '请选择发送时间', trigger: 'change' }],
  nr: [{ required: true, message: '请选择是否可下载', trigger: 'change' }]
})
// 开启弹窗
const open = () => {
  changeValList.value = []
  changeValIds.value = []
  dialogVisible.value = true
  // 清空校验
  if (formRef.value) formRef.value.resetFields()
  // 清空表格选中
  if (tableRef.value) tableRef.value.clearSelection()
}
// 选择作业
const popupResourceChange = ref(null)
const openChange = () => {
  popupResourceChange.value.open()
}
// 作业返回id
const changeVal = val => {
  changeValList.value = changeValList.value.concat(val)
  changeValIds.value = changeValList.value.map(item => item.id)
}
// 确定
const confirmFrom = () => {
  formRef.value.validate(valid => {
    if (valid) {
      if (changeValIds.value.length == 0) {
        return ElMessage({
          type: 'info',
          message: '请选择试卷'
        })
      }
      console.log('submit!')
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}
const handleCloseTag = i => {
  changeValList.value.splice(i, 1)
  changeValIds.value.splice(i, 1)
}

const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.popup_show_table {
  max-height: 300px;
  overflow-y: auto;
}

.popup_show_tag {
  margin: 0 20px 15px 75px;

  .popup_show_tag_item {
    margin: 3px;
  }
}
</style>
