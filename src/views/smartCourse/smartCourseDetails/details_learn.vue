<template>
  <div class="box_4 flex-col">
    <div class="go_back">
      <el-button
        class="go_back_btn"
        @click="goBack"
        type="primary"
        plain>返回</el-button>
    </div>
    <div
      class="details_signin_content">
      <h3>拓展学习详情</h3>
      <div
        class="signin_content_tab">
        <div
          class="signin_content_detail">
          <span>{{ form.extensionName }}的拓展学习</span>
          <span>创建时间：{{ form.createTime }}</span>
        </div>
        <div
          class="signin_content_tab_search">
          <el-input
            v-model="input1"
            style="width: 240px"
            size="large"
            placeholder="请输入搜索关键字"
            :suffix-icon="Search" />
        </div>
      </div>
      <div>
        <el-table
          :data="form.extensionRecords"
          style="width: 100%">
          <el-table-column
            prop="userNo"
            label="学号" />
          <el-table-column
            prop="realName"
            label="姓名" />
          <el-table-column
            prop="className"
            label="所属班级" />
          <el-table-column
            prop="groupName"
            label="所属小组" />
          <el-table-column
            label="修改状态">
            <template
              #default="scope">
              <el-button
                type="primary"
                link
                @click="goDetails(scope.row.extensionId,scope.row.recordId)">查看拓展学习</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
  <PopupDetailsLearn
    ref="popupDetailsLearn" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ArrowDown, Search, ArrowUp } from '@element-plus/icons-vue'
import {badgeProps, ElMessage} from 'element-plus';
// 拓展学习弹窗
import PopupDetailsLearn from './popup_details_learn.vue'
import {getBrainstorm} from "@/api/edu/brainstorm.js";
import {getExtension} from "@/api/edu/extension.js";

const { proxy } = getCurrentInstance()
const emits = defineEmits(['listActivityId'])
const props = defineProps({
  activityId: {
    type: [String, Number],
    default: ''
  }
});
const form = ref({
  extensionRecords: []
})

const popupDetailsLearn = ref()
// 查看详情
const goDetails = (extensionId,recordId) => {
  popupDetailsLearn.value.open(extensionId,recordId)
}
async  function initData() {
  const response = await getExtension(props.activityId);
  if (response.code == 200 ){
    form.value = response.data;
  }else {
    ElMessage.error(response.message);
  }
}

onMounted(() => {
  initData();
})
// 跳转详情页
const goBack = () => {
  emits('listActivityId')
}
</script>

<style scoped lang="scss">
.go_back {
  text-align: right;

  .go_back_btn {
    margin: 20px 20px 0 0;
  }
}

.details_signin_content {
  padding: 0 20px 20px 20px;

  .signin_content_tab {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .signin_content_detail {
      font-size: 15px;
      color: #585e76;
      margin-top: 30px;
      span {
        margin-right: 30px;
      }

      i {
        font-style: normal;
      }
    }
    .signin_content_tab_title {
      width: 105px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #7e88a5;
      margin: 25px 10px 20px 0;
      color: #333333;
      cursor: pointer;
    }

    .signin_content_tab_search {
      margin-top: 17px;
    }

    .signin_content_tab_title_active {
      background: #555b74;
      color: #ffffff;
    }
  }
}
</style>
