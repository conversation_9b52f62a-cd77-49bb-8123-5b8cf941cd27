<template>
  <el-dialog
    v-model="dialogVisible"
    title="新建提问"
    width="700"
    :before-close="handleClose"
    style="margin-top: 30vh !important"
  >
    <el-form
      ref="formRef"
      style="max-width: 600px"
      :model="form"
      label-width="auto"
      class="demo-ruleForm"
      :rules="rules"
    >
      <el-form-item label="提问标题" prop="questionTitle">
        <el-input
          v-model="form.questionTitle"
          type="text"
          autocomplete="off"
          maxlength="50"
          placeholder="请输入提问标题"
        />
      </el-form-item>
      <el-form-item label="提问内容" prop="questionContent">
        <el-input
          v-model="form.questionContent"
          :rows="3"
          type="textarea"
          show-word-limit="true"
          maxlength="500"
          placeholder="请输入提问内容，最多五百字"
        />
      </el-form-item>
      <el-form-item label="是否过滤未签到学生" prop="signInFlag">
        <el-radio-group v-model="form.signInFlag">
          <el-radio value="0">是</el-radio>
          <el-radio value="1">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="提问方式" prop="questionType">
        <el-radio-group v-model="form.questionType">
          <el-radio value="0">随机提问</el-radio>
          <el-radio value="1">点名提问</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import { ElMessageBox } from "element-plus";
import dayjs from 'dayjs';
import { addActivityList } from "@/api/edu/class";
const props = defineProps({ form: { type: Object, default: {} } });

const dialogVisible = ref(false);
const formRef = ref(null);
const form = ref({
  courseId: "",
  lessonId: "",
  classId: "",
  questionTitle: "",
  questionContent: "",
  signInFlag: "0",
  questionType: "0",
  questionStartDate: ''
});
const rules = ref({
  questionTitle: [
    { required: true, message: "提问标题不能为空", trigger: "change" },
  ],
  questionContent: [
    { required: true, message: "提问内容不能为空", trigger: "change" },
  ],
  signInFlag: [
    {
      required: true,
      message: "是否过滤未签到学生不能为空",
      trigger: "change",
    },
  ],
  questionType: [
    { required: true, message: "提问方式不能为空", trigger: "change" },
  ],
});
// 开启弹窗
const open = () => {
  dialogVisible.value = true;
  form.value = {
    courseId: "",
    lessonId: "",
    classId: "",
    questionTitle: dayjs().format('YYYY-MM-DD HH:mm:ss') + '的提问活动',
    questionContent: "",
    signInFlag: "0",
    questionType: "0",
    questionStartDate: ''
  };
  if (formRef.value) formRef.value.resetFields();
};
// 关闭弹窗
const confirmFrom = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      form.value.courseId = props.form.courseId;
      form.value.classId = props.form.classId;
      form.value.lessonId = props.form.lessonId;
      form.value.questionStartDate =  dayjs().format('YYYY-MM-DD HH:mm:ss');
      const parm = {
        type : 2,
        lessonId: form.value.lessonId,
        classId: form.value.classId,
        moocSmartCourseClassQuestion: form.value
      }
      addActivityList(parm).then((res) => {
        if (res.code == 200) {
          ElMessageBox({
            title: "操作提示",
            message: "添加活动成功",
            type: "success",
          });
        }
      });
      handleClose();
    } else {
      console.log("error submit!");
    }
  });
};
const handleClose = () => {
  dialogVisible.value = false;
  form.value = {
    courseId: "",
    lessonId: "",
    classId: "",
    questionTitle: "",
    questionContent: "",
    signInFlag: "0",
    questionType: "0",
    questionStartDate: ''
  };
};
defineExpose({
  open,
});
</script>
<style lang="scss">
// .el-dialog.signin_popup_dialog :not(.is-fullscreen) {
//   margin-top: 30vh !important;
// }
</style>
