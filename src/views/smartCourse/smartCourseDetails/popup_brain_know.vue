<template>
    <el-dialog v-model="dialogVisible" title="上传须知" width="600" :before-close="handleClose" style="margin-top: 10vh !important">
      <p>根据国家《出版管理条例》《网络出版服务管理规定》等相关规定，上传的资源必须符合以下要求：</p>
      <p>1. 没有法律、法规禁止出版的内容，没有政治性、道德性问题和科学性错误，不泄露国家秘密。</p>
      <p>2. 不含有侵犯他人著作权、肖像权、名誉权等权益的内容，资源具有原创性，引用需指明作者姓名、作品名称，使用他人作品应取得许可。</p>
      <p>3. 采用法定计量单位，名词、术语、符号等符合国家统一规定，尚无统一规定的，可采用习惯用法并保持一致。</p>
      <p>4. 地图具有严肃的政治性、严密的科学性和严格的法定性，使用的地图应根据《地图管理条例》的要求已送相关部门审核并标注审图号。</p>
      <p>5、图文支持上传文件数上限不超过6个文件。</p>
      <p>6、本系统目前支持以下的系统格式文档：</p>
      <p>office文档： doc docx xls xlsx pdf rtf txt ppt pptx</p>
      <p>音视频： mp3 mp4 ，单个视频不能超过2G</p>
      <p>图片： jpg jpeg png gif tif tiff bmp</p>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance()
const dialogVisible = ref(false)
const open = () => {
  dialogVisible.value = true
}
const confirmFrom = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>

<style scoped lang='scss'>
</style>