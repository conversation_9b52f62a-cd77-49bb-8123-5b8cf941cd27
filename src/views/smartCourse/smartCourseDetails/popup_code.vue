<!--
 * @Author: q<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-20 14:01:06
 * @LastEditors: qinqinglin <EMAIL>
 * @LastEditTime: 2025-06-30 09:01:54
 * @FilePath: \dutp-stu-tea-vue\src\views\smartCourse\smartCourseDetails\popup_code.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="课堂邀请码"
    width="500"
    :before-close="handleClose"
    style="margin-top: 26vh !important"
  >
    <el-form
      ref="formRef"
      :model="form"
      label-width="auto"
      class="demo-ruleForm"
      :rules="rules"
    >
      <p>
        本次课堂为{{
          form.title
        }}，将课堂邀请码和二维码分享给来上课的用户，开启您的互动课堂之旅吧！
      </p>
      <h2>课堂邀请码：{{ form.classCode }}</h2>
      <el-form-item label="学生加入方式" prop="studentJoin">
        <el-radio-group v-model="form.studentJoin">
          <el-radio value="0">禁止加入</el-radio>
          <el-radio value="1">邀请码进班</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否开启审核" prop="examine">
        <el-radio-group v-model="form.examine">
          <el-radio value="0">是</el-radio>
          <el-radio value="1">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 发布活动 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { updateLesson } from "@/api/edu/lesson.js";
import { ElMessage } from "element-plus";
const props = defineProps({ form: { type: Object, default: {} } });
const dialogVisible = ref(false);
const formRef = ref(null);
const form = ref({});
const rules = ref({
  studentJoin: [
    { required: true, message: "请选择学生加入方式", trigger: "change" },
  ],
  examine: [
    { required: true, message: "请选择是否开启审核", trigger: "change" },
  ],
});
// 开启弹窗
const open = () => {
  form.value = props.form;
  form.value.studentJoin =  props.form.studentJoin.toString();
  form.value.examine =  props.form.examine.toString();
  dialogVisible.value = true;
};
// 关闭弹窗
const confirmFrom = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      dialogVisible.value = false;
      updateLesson(form.value).then((res) => {
        if (res.code == 200) {
          ElMessage.success("修改成功");
        }
      });
      console.log("submit!");
    } else {
      console.log("error submit!");
    }
  });
};
const handleClose = () => {
  dialogVisible.value = false;
};
defineExpose({ open });
</script>
<style lang="scss" scoped>
h2 {
  text-align: center;
  margin: 30px 0;
}
</style>
