<template>
  <el-dialog
    v-model="dialogVisible"
    :title="addFlag ? '新建拓展学习' : '修改拓展学习'"
    width="700"
    :before-close="handleClose"
    style="margin-top: 18vh !important"
  >
    <el-form
      ref="formRef"
      style="max-width: 600px"
      :model="form"
      label-width="auto"
      class="demo-ruleForm"
      :rules="rules"
    >
      <el-form-item label="拓展标题" prop="extensionName">
        <el-input
          show-word-limit
          maxlength="50"
          v-model="form.extensionName"
          type="text"
          autocomplete="off"
          placeholder="请输入拓展标题"
        />
      </el-form-item>
      <el-form-item label="拓展要求" prop="extensionContent">
        <el-input
          v-model="form.extensionContent"
          :rows="3"
          type="textarea"
          show-word-limit="true"
          maxlength="500"
          placeholder="请输入拓展要求"
        />
      </el-form-item>
      <el-form-item label="辅助材料" prop="fileListVive">
        <el-upload
          v-model:file-list="form.fileListVive"
          :http-request="upload"
          class="upload-demo"
          :action="uploadUrl"
          :before-upload="beforeAvatarUpload"
          :on-success="handleSuccess"
          :on-remove="handleRemove"
        >
          <el-button type="primary">上传附件</el-button>
          <template #tip>
            <div class="el-upload__tip">低于1M的文件不支持大文件上传</div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="confirmFrom(1)">保存</el-button>
        <el-button type="primary" @click="confirmFrom(2)">开始</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { OssService } from "@/utils/aliOss.js";
import dayjs from "dayjs";
import { addActivityList, updateActivityList } from "@/api/edu/class";
import { getExtension } from "@/api/edu/extension";
import { getToken } from "@/utils/auth";
const props = defineProps({ form: { type: Object, default: {} } });
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + "/file/upload"); // 上传的图片服务器地址
const addFlag = ref(false);
const headers = ref({
  Authorization: "Bearer " + getToken(),
});
const dialogVisible = ref(false);
const formRef = ref(null);
const form = ref({
  extensionName: "",
  extensionContent: "",
  fileListVive: [],
});
const rules = ref({
  extensionName: [
    { required: true, message: "拓展标题不能为空", trigger: "change" },
  ],
  extensionContent: [
    { required: true, message: "内容不能为空", trigger: "change" },
  ],
  fileListVive: [{ required: true, message: "请上传文件", trigger: "change" }],
});
// 开启弹窗
const open = (item) => {
  dialogVisible.value = true;
  if (item != null) {
    addFlag.value = false;
    getExtension(item.activityId).then((res) => {
      form.value = res.data;
      form.value.fileListVive = res.data.fileList;
      form.value.fileListVive.forEach((item) => {
        item.url = item.fileUrl;
        item.name = item.fileName;
      });
    });
  } else {
    addFlag.value = true;
    form.value = {
      extensionName: dayjs().format("YYYY-MM-DD HH:mm:ss") + "的拓展学习活动",
      extensionContent: "",
      fileListVive: [],
    };
    if (formRef.value) formRef.value.resetFields();
  }
};
// 上传
const beforeAvatarUpload = (rawFile) => {
  if (rawFile.size / 1024 / 1024 > 1) {
    ElMessage.error("上传失败！文件大小不能超过 1MB!");
    return false;
  }
  return true;
};
const syncFile = async (file) => {
  try {
    // 工具类引用
    const res = await OssService(file.file);
    return res;
  } catch (e) {
    throw e;
  }
};

function upload(file) {
  return syncFile(file);
}
// 确定
const confirmFrom = (number) => {
  formRef.value.validate((valid) => {
    if (valid) {
      if (number === 2) {
        form.value.brainstormStartTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
        form.value.status = 2;
      } else {
        form.value.status = 1;
      }
      if (addFlag.value) {
        form.value.courseId = props.form.courseId;
        form.value.classId = props.form.classId;
        form.value.lessonId = props.form.lessonId;
        const fileList = [];
        form.value.fileListVive.forEach((item) => {
          fileList.push({
            fileName: item.name,
            fileUrl: item.response.url,
            fileType: item.raw.type,
            fileSize: item.size,
          });
        });
        form.value.fileList = fileList;
        const parm = {
          type: 4,
          lessonId: form.value.lessonId,
          classId: form.value.classId,
          moocSmartCourseExtension: form.value,
        };
        addActivityList(parm).then((res) => {
          if (res.code == 200) {
            ElMessageBox({
              title: "操作提示",
              message: "添加活动成功",
              type: "success",
            });
          }
        });
      } else {
        const fileList = [];
        form.value.fileListVive.forEach((item) => {
          fileList.push({
            fileName: item?.name ? item.name : item.fileName,
            fileUrl: item?.response?.url ? item.response.fileUrl : item.fileUrl,
            fileType: item?.raw?.type ? item.raw.type : item.fileType,
            fileSize: item?.size ? item.size : item.fileSize,
          });
        });
        form.value.fileList = fileList;
        const parm = {
          type: 4,
          lessonId: form.value.lessonId,
          classId: form.value.classId,
          moocSmartCourseExtension: form.value,
        };
        updateActivityList(parm).then((res) => {
          if (res.code == 200) {
            ElMessageBox({
              title: "操作提示",
              message: "修改活动成功",
              type: "success",
            });
          }
        });
      }
      handleClose();
    } else {
      console.log("error submit!");
    }
  });
};
const handleClose = () => {
  dialogVisible.value = false;
  form.value = {
    extensionName: "",
    extensionContent: "",
    fileListVive: [],
  };
};
defineExpose({
  open,
});
</script>
