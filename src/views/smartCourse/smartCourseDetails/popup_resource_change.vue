<!--
 * @Author: q<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-27 16:25:11
 * @LastEditors: qinqinglin <EMAIL>
 * @LastEditTime: 2025-06-30 09:16:10
 * @FilePath: \dutp-stu-tea-vue\src\views\smartCourse\smartCourseDetails\popup_resource_change.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog v-model="dialogVisible" title="选择资源" width="750" :before-close="handleClose" style="margin-top: 10vh !important">
    <div class="popup_resource_change_catalog">
      <el-button text
        >我的资源
        <el-icon>
          <ArrowRight />
        </el-icon>
      </el-button>
    </div>
    <div class="popup_resource_change">
      <div class="popup_resource_change_head">
        <div class="resource_change_head_name">文件名称：</div>
        <el-input style="width: 200px" placeholder="请输入文件名称"></el-input>
        <el-button style="margin-left: 10px" type="primary">搜索</el-button>
      </div>
      <div class="resource_change_content">
        <div class="resource_change_index" v-for="(item, index) in resourceList" :key="index" @click="goNext(item.id)">
          <el-checkbox @click.stop="" class="change_index_checkbox" v-model="item.checked" size="large" />
          <!--          文件夹-->
          <img v-if="item.type === '1'" src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6%E5%A4%B9%20131x113%402x.png" />
          <!--          音频-->
          <img v-if="item.type === '2'" src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E9%9F%B3%E9%A2%91.png" />
          <!--          视频-->
          <img v-if="item.type === '3'" src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E8%A7%86%E9%A2%91.png" />
          <!--          虚拟仿真-->
          <img v-if="item.type === '4'" src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" />
          <!--          AR/VR-->
          <img v-if="item.type === '5'" src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" />
          <!--          3D模型-->
          <img v-if="item.type === '6'" src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" />
          <!--          习题-->
          <img v-if="item.type === '7'" src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" /><!--          课件-->
          <img v-if="item.type === '8'" src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" /><!--          文件-->
          <img v-if="item.type === '9'" src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" />
          <p>{{ item.fileName }}</p>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消 </el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowRight } from '@element-plus/icons-vue'

const emits = defineEmits(['changeVal'])
const dialogVisible = ref(false)
const resourceList = ref([
  {
    id: '1',
    fileName: '我的资源',
    type: '1'
  },
  {
    id: '2',
    fileName: '图片视频',
    type: '2'
  },
  {
    id: '3',
    fileName: '迅雷下载',
    type: '3'
  },
  {
    id: '4',
    fileName: '迅雷下载',
    type: '4'
  },
  {
    id: '5',
    fileName: '迅雷下载',
    type: '5'
  },
  {
    id: '6',
    fileName: '迅雷下载',
    type: '6'
  },
  {
    id: '7',
    fileName: '迅雷下载',
    type: '7'
  },
  {
    id: '8',
    fileName: '迅雷下载',
    type: '8'
  },
  {
    id: '9',
    fileName: '迅雷下载',
    type: '9'
  }
])

// 开启弹窗
const open = () => {
  addChecked()
  dialogVisible.value = true
}
// 进入下一层
const goNext = id => {
  console.log(id)
}
// 集中处理
const addChecked = () => {
  resourceList.value.forEach((item, index) => {
    item.checked = false
  })
}
// 提交
const confirmFrom = () => {
  const checkedList = resourceList.value.filter(item => item.checked)
  if (!checkedList || checkedList.length === 0) {
    return ElMessage({
      type: 'info',
      message: '请选择资源'
    })
  }
  emits('changeVal', checkedList)
  dialogVisible.value = false
}
// 关闭弹窗

const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.popup_resource_change_catalog {
  .el-button {
    padding: 8px 0;
  }

  border-top: 1px solid #f1efef;
  border-bottom: 1px solid #f1efef;
}

.popup_resource_change {
  padding: 20px;

  .popup_resource_change_head {
    display: flex;
    justify-content: end;

    .resource_change_head_name {
      margin-top: 6px;
    }
  }

  .resource_change_content {
    margin-top: 20px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    .resource_change_index {
      width: 100px;
      position: relative;
      cursor: pointer;
      margin-right: 10px;

      .change_index_checkbox {
        position: absolute;
        left: 2px;
        top: 11px;
      }

      img {
        width: 70px;
        height: 65px;
        padding: 10px 10px 5px 10px;
        margin: 10px 10px 0 15px;
        text-align: center;
      }

      p {
        margin-top: 0;
        text-align: center;
      }
    }

    .resource_change_index:hover {
      background: #f1efef;
    }
  }
}
</style>
