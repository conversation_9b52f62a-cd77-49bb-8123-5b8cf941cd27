<!--
 * @Author: qin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-24 11:35:45
 * @LastEditors: qinqinglin <EMAIL>
 * @LastEditTime: 2025-06-26 14:20:03
 * @FilePath: \dutp-stu-tea-vue\src\views\smartCourse\smartCourseDetails\index_activity.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <DetailsSignin
    v-if="activityParamType == '1'"
    @listActivityId="listActivityId" />
  <DetailsAsk
    v-if="activityParamType == '2'"
    @listActivityId="listActivityId" />
  <DetailsBrain
    v-if="activityParamType == '3'"
    :activityId="activityParam.activityId"
    @listActivityId="listActivityId" />
  <DetailsLearn
    v-if="activityParamType == '4'"
    :activityId="activityParam.activityId"
    @listActivityId="listActivityId" />
  <DetailsVote
    v-if="activityParamType == '5' && activityParam.state != '3'"
    :activityId="activityParam.activityId"
    @listActivityId="listActivityId" />
  <DetailsVoteEnd
    v-if="activityParamType == '5' && activityParam.state == '3'"
    :activityId="activityParam.activityId"
    @listActivityId="listActivityId" />
  <PopupShow
    ref="popupShow" />
  <DetailsHomework
    v-if="activityParamType == '7'"
    @listActivityId="listActivityId" />
  <DetailsTest
    v-if="activityParamType == '8'"
    @listActivityId="listActivityId" />
  <ListActivity
    v-if="!activityParamType || activityParamType == '6'"
    :data="data"
    @listActivityId="listActivityId" />
  <!-- <component :is="componentComputed" @listActivityId="listActivityId"></component> -->
</template>

<script setup>
import { ref, reactive, getCurrentInstance, computed } from 'vue'
import ListActivity from './list_activity'
// 学生签到详情
import DetailsSignin from './details_signin.vue'
// 提问详情
import DetailsAsk from './details_ask.vue'
// 头脑风暴
import DetailsBrain from './details_brain.vue'
// 拓展学习详情
import DetailsLearn from './details_learn.vue'
// 作品秀
import PopupShow from './popup_show.vue'
// 投票
import DetailsVote from './details_vote.vue'
// 投票已结束
import DetailsVoteEnd from './details_vote_end.vue'
// 作业
import DetailsHomework from './details_homework.vue'
// 测试
import DetailsTest from './details_test.vue'
const { proxy } = getCurrentInstance()
const props = defineProps(['data'])
console.log(props.data);
const activityParam = ref()
const activityParamType = ref()
// 判断跳转

const componentComputed = computed(() => {
  if (!activityParam.value) {
    return ListActivity
  } else {
    switch (true) {
      case activityParam.value.type == '1':
        return DetailsSignin
    }
  }
})
const popupShow = ref()
const listActivityId = e => {
  activityParam.value = e
  activityParamType.value = e?.type
  console.log(activityParam.value)
  // 作品秀
  if (activityParam.value?.type == '6') {
    popupShow.value.open(activityParam.value.id)
  }
}
</script>

<style scoped lang="scss"></style>
