<template>
  <div class="box_4 flex-col">
    <div class="go_back">
      <el-button
        class="go_back_btn"
        @click="goBack"
        type="primary"
        plain>返回</el-button>
    </div>
    <div
      class="details_signin_content">
      <div
        class="signin_content_tab">
        <h3>投票问卷标题</h3>
        <div
          class="signin_content_tab_search">
          <el-input
            v-model="input1"
            style="width: 240px"
            size="large"
            placeholder="请输入搜索关键字"
            :suffix-icon="Search" />
        </div>
      </div>
      <div>
        <el-table
          :data="form.tableData"
          style="width: 100%">
          <el-table-column
            prop="userNo"
            label="学号" />
          <el-table-column
            prop="realName"
            label="姓名" />
          <el-table-column
            prop="className"
            label="所属班级" />
          <el-table-column
            prop="groupName"
            label="所属小组" />
          <el-table-column
            prop="answerStatus"
            label="状态">
            <template
              #default="scope">
              <span
                v-if="scope.row.answerStatus == '1'">已参与</span>
              <span
                v-if="scope.row.answerStatus == '2'">未参与</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="填报时间" >
            <template
              #default="scope">
              <span
                v-if="scope.row.createTime">{{ scope.row.createTime }}</span>
              <span
                v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作">
            <template
              #default="scope">
              <el-button
                v-if="scope.row.answerStatus == '1'"
                type="primary"
                link
                @click="goDetails(scope.row)">查看问卷</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
  <PopupDetailsVote
    ref="popupDetailsVote" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ArrowDown, Search, ArrowUp } from '@element-plus/icons-vue'
import {badgeProps, ElMessage} from 'element-plus';
// 拓展学习弹窗
import PopupDetailsVote from './popup_details_vote.vue'
import {getBrainstorm} from "@/api/edu/brainstorm.js";
import {getCourseSurvey, getStudentAnswerList} from "@/api/edu/survey.js";

const { proxy } = getCurrentInstance()
const emits = defineEmits(['listActivityId'])
const props = defineProps({
  activityId: {
    type: [String, Number],
    default: ''
  }
});
const form = ref({
  sj: '2025-02-50',
  qdlx: '1',
  cjsj: '2025-06-05 16:11',
  jssj: '2025-06-05 16:11',
  tableData: []
})

const popupDetailsVote = ref()
// 查看详情
const goDetails = (rowData) => {
  popupDetailsVote.value.open(rowData)
}
async  function initData() {
  const response = await getStudentAnswerList(props.activityId);
  if (response.code == 200 ){
    form.value.tableData = response.data;
  }else {
    ElMessage.error(response.message);
  }
}

onMounted(() => {
  initData();
})
// 跳转详情页
const goBack = () => {
  emits('listActivityId')
}
</script>

<style scoped lang="scss">
.go_back {
  text-align: right;

  .go_back_btn {
    margin: 20px 20px 0 0;
  }
}

.details_signin_content {
  padding: 0 20px 20px 20px;

  .signin_content_tab {
    display: flex;
    justify-content: space-between;
    margin: 25px 0 20px 0;
    h3 {
      margin-top: 10px;
    }
    .signin_content_tab_title {
      width: 105px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #7e88a5;
      margin: 25px 10px 20px 0;
      color: #333333;
      cursor: pointer;
    }

    .signin_content_tab_title_active {
      background: #555b74;
      color: #ffffff;
    }
  }
}
</style>
