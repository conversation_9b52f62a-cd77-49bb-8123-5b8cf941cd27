<!--
 * @Author: qin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-20 14:01:06
 * @LastEditors: qinqinglin <EMAIL>
 * @LastEditTime: 2025-06-30 17:26:52
 * @FilePath: \dutp-stu-tea-vue\src\views\smartCourse\smartCourseDetails\popup_code.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog v-model="dialogVisible" title="选择课件" width="1030" :before-close="handleClose" style="margin-top: 8vh !important">
    <div>
      <el-input v-model="input1" style="width: 240px" size="large" placeholder="请输入搜索关键字" :suffix-icon="Search" />
    </div>
    <div class="popup_courseware_list">
      <div class="courseware_list_item" v-for="(item, index) in res" :key="index">
        <div class="list_item_bg">
          <el-checkbox class="item_bg_radio" v-model="item.checked" />
          <el-popover popper-class="custom_popover" width="55" class="box-item" trigger="click" placement="bottom-end">
            <template #reference>
              <el-icon class="item_bg_icon"><MoreFilled /></el-icon>
            </template>
            <div>
              <el-button size="small">删除</el-button>
              <el-button size="small" style="margin-left: 0">复制</el-button>
              <el-button size="small" style="margin-left: 0">分享</el-button>
            </div>
          </el-popover>
        </div>
        <div class="list_item_font">
          <h3>{{ item.bt }}</h3>
          <p>创建时间：{{ item.cjsj }}</p>
          <div class="list_item_font_bottom">
            <div class="font_bottom_star">
              <img src="../../../assets/images/smartCourse/smartCourseDetails/star.png" alt="" />
              <span>授课老师：{{ item.skjs }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose"> 取消 </el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <PopupCoursewareChange ref="popupCoursewareChange" />
</template>

<script setup>
import { ref } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Search, MoreFilled } from '@element-plus/icons-vue'
import PopupCoursewareChange from './popup_courseware_change.vue'
const dialogVisible = ref(false)
const popupCoursewareChange = ref()
const res = ref([
  {
    id: '1',
    bt: '如果地球倒转 24 小时',
    bt: '如果地球倒转 24 小时',
    cjsj: '2025-04-12 10:20:55',
    cjsj: '2025-04-12 10:20:55',
    skjs: '林砚秋 陈默'
  },
  {
    id: '2',
    bt: '如果地球倒转 24 小时',
    bt: '如果地球倒转 24 小时',
    cjsj: '2025-04-12 10:20:55',
    cjsj: '2025-04-12 10:20:55',
    skjs: '林砚秋 陈默'
  },
  {
    id: '3',
    bt: '如果地球倒转 24 小时',
    bt: '如果地球倒转 24 小时',
    cjsj: '2025-04-12 10:20:55',
    cjsj: '2025-04-12 10:20:55',
    skjs: '林砚秋 陈默'
  },
  {
    id: '4',
    bt: '如果地球倒转 24 小时',
    bt: '如果地球倒转 24 小时',
    cjsj: '2025-04-12 10:20:55',
    cjsj: '2025-04-12 10:20:55',
    skjs: '林砚秋 陈默'
  },
  {
    id: '5',
    bt: '如果地球倒转 24 小时',
    bt: '如果地球倒转 24 小时',
    cjsj: '2025-04-12 10:20:55',
    cjsj: '2025-04-12 10:20:55',
    skjs: '林砚秋 陈默'
  }
])

// 开启弹窗
const open = () => {
  res.value = res.value.map(item => {
    item.checked = false
    return item
  })
  console.log(res.value)
  dialogVisible.value = true
}

// 确定弹窗
const confirmFrom = () => {
  const list = res.value.filter(item => {
    return item.checked === true
  })
  if (list.length == 0) {
    return ElMessage({
      type: 'info',
      message: '请选择课件'
    })
  } else if (list.length > 1) {
    return ElMessage({
      type: 'info',
      message: '只能选择一个课件'
    })
  } else {
    popupCoursewareChange.value.open(list[0].id)
  }
  // dialogVisible.value = false
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss">
.custom_popover {
  min-width: 0 !important;
  padding: 3px !important;
}
.popup_courseware_list {
  display: flex;
  flex-wrap: wrap;
  max-height: 400px;
  overflow-y: auto;
  .courseware_list_item {
    width: 316px;
    height: 208px;
    background: #ffffff;
    border-radius: 14px 14px 14px 14px;
    border: 2px solid #e6ebf5;
    margin: 10px 10px 0 0;
    .list_item_bg {
      position: relative;
      background-image: url('../../../assets/images/smartCourse/smartCourseDetails/couser_bg.png');
      width: 313px;
      height: 93px;
      .item_bg_radio {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
      }
      .item_bg_icon {
        font-size: 20px;
        color: #ffffff;
        position: absolute;
        right: 20px;
        top: 7px;
        cursor: pointer;
      }
    }
    .list_item_font {
      padding: 10px;
      h3 {
        font-size: 18px;
        color: #000000;
        margin: 0;
      }
      p {
        font-size: 16px;
        color: #969aaa;
        margin: 8px 0 !important;
      }
      .list_item_font_bottom {
        width: 208px;
        height: 32px;
        background: #f2f5fa;
        border-radius: 8px;
        line-height: 32px;
        .font_bottom_star {
          display: flex;
          img {
            width: 15px;
            object-fit: contain;
            margin: -1px 5px 0 5px;
            display: block;
          }
        }
      }
    }
  }
}
</style>
