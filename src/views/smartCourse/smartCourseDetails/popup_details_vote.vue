<template>
  <el-dialog
    v-model="dialogVisible"
    title="投票问卷" width="700"
    :before-close="handleClose"
    style="margin-top: 10vh !important">
    <div
      class="popup_details_vote">
      <div
        class="details_vote_title">
        <span>学号：{{ form.userNo }}</span>
        <span>姓名：{{ form.realName }}</span>
      </div>
      <div
        class="details_vote_content">
        <div
          class="vote_content_ques"
          v-for="(item,index) in form"
          :key="index">
          <div
            class="vote_content_th">
            第{{ index + 1 }}题
          </div>
          <div>
            标题：{{ item.questionTitle }}
          </div>
          <div>
            题干：{{ item.questionContent }}
          </div>
          <div>
            <div
              v-for="(option,index) in item.options"
              :key="index">
              <span>{{ option.sort }}.</span>
              <span>{{ option.optionContent }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div
        class="dialog-footer">
        <el-button
          @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'
import { getSurveyTeachDetails } from '@/api/edu/survey.js'

const dialogVisible = ref(false)
const formRef = ref(null)
const form = ref({
  id: '1',
  xh: '123123',
  xm: '飒东风',
  questionList: [
    {
      bt: '为什么全世界都是我的',
      tg: '你在说什么',
      optionList: [
        {
          id: '1',
          th: 'A',
          nr: '选项1'
        },
        {
          id: '2',
          th: 'B',
          nr: '选项2'
        },
        {
          id: '3',
          th: 'C',
          nr: '选项3'
        },
        {
          id: '4',
          th: 'D',
          nr: '选项4'
        }
      ]
    },
    {
      bt: '为什么全世界都是我的',
      tg: '你在说什么',
      optionList: [
        {
          id: '1',
          th: 'A',
          nr: '选项1'
        },
        {
          id: '2',
          th: 'B',
          nr: '选项2'
        },
        {
          id: '3',
          th: 'C',
          nr: '选项3'
        },
        {
          id: '4',
          th: 'D',
          nr: '选项4'
        }
      ]
    }
  ]
})
// 开启弹窗
const open = async (surveyId,userId1) => {
  dialogVisible.value = true
  const surveyId1 = 2001
  const userId = 10001
  const res = await getSurveyTeachDetails(surveyId1,userId);
  form.value = res.data;
  console.log(form.value);
}

const handleClose = () => {
  dialogVisible.value = false
}

defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.popup_details_vote {
  max-height: 400px;
  overflow-y: auto;
  .details_vote_title {
    span {
      margin-right: 30px;
    }
  }
  .details_vote_content {
    div {
      margin: 5px 0;
    }
    .vote_content_ques {
      padding: 15px;
      margin-top: 10px;
      border: 1px solid #ccc;
    }
    .vote_content_th {
      margin-bottom: 10px;
      font-weight: 600;
    }
  }
}
</style>
