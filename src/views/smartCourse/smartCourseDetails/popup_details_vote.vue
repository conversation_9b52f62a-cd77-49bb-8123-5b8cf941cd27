<template>
  <el-dialog
    v-model="dialogVisible"
    title="投票问卷" width="700"
    :before-close="handleClose"
    style="margin-top: 10vh !important">
    <div
      class="popup_details_vote">
      <div
        class="details_vote_title">
        <span>学号：{{ form.userNo }}</span>
        <span>姓名：{{ form.realName }}</span>
      </div>
      <div
        class="details_vote_content">
        <div
          class="vote_content_ques"
          v-for="(item,index) in form.questions"
          :key="index">
          <div
            class="vote_content_th">
            第{{ index + 1 }}题
          </div>
          <div>
            标题：{{ item.questionTitle }}
          </div>
          <div>
            题干：{{ item.questionContent }}
          </div>
          <div>
            <div
              v-for="(option,optionIndex) in item.options"
              :key="optionIndex">
              <span>{{ option.sort }}.</span>
              <span>{{ option.optionContent }}</span>
              <span v-if="isOptionSelected(item, option.optionId)" class="check-mark">✓</span>
            </div>
            <!-- 填空题答案显示 -->
            <div v-if="item.questionType === '填空' && item.userAnswer?.answerValue" class="fill-answer">
              <span class="answer-label">学生答案：</span>
              <span class="answer-content">{{ item.userAnswer.answerValue }}</span>
              <span class="check-mark">✓</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div
        class="dialog-footer">
        <el-button
          @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'
import { getSurveyTeachDetails } from '@/api/edu/survey.js'
import useUserStore from "@/store/modules/user.js";
const userStore = useUserStore();

const dialogVisible = ref(false);
const formRef = ref(null);
const form = ref({
  userNo:'',
  realName:'',
});

// 判断选项是否被选中
const isOptionSelected = (question, optionId) => {
  if (!question.userAnswer || !question.userAnswer.answerValue) {
    return false
  }

  const answerValue = question.userAnswer.answerValue

  // 单选题
  if (question.optionType === 0 || question.questionType === '单选') {
    return answerValue === optionId
  }

  // 多选题
  if (question.optionType === 1 || question.questionType === '多选') {
    const selectedOptions = answerValue.split(',')
    return selectedOptions.includes(optionId)
  }

  return false
}

// 开启弹窗
const open = async (rowData) => {
  console.log(rowData);
  dialogVisible.value = true
  // const surveyId = rowData.surveyId
  // const userId = rowData.userId
  const surveyId1 = 2001
  const userId = 10001
  const res = await getSurveyTeachDetails(surveyId1,userId);
  form.value.questions = res.data;
  form.value.userNo = rowData.userNo;
  form.value.realName = rowData.realName;
}

const handleClose = () => {
  dialogVisible.value = false
}

defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.popup_details_vote {
  max-height: 400px;
  overflow-y: auto;
  .details_vote_title {
    span {
      margin-right: 30px;
    }
  }
  .details_vote_content {
    div {
      margin: 5px 0;
    }
    .vote_content_ques {
      padding: 15px;
      margin-top: 10px;
      border: 1px solid #ccc;
    }
    .vote_content_th {
      margin-bottom: 10px;
      font-weight: 600;
    }

    .check-mark {
      margin-left: 8px;
      color: #67c23a;
      font-weight: bold;
      font-size: 16px;
    }

    .fill-answer {
      margin: 10px 0;
      padding: 8px;
      background-color: #f5f5f5;
      border-radius: 4px;

      .answer-label {
        font-weight: 500;
        margin-right: 8px;
      }

      .answer-content {
        color: #303133;
      }
    }
  }
}
</style>
