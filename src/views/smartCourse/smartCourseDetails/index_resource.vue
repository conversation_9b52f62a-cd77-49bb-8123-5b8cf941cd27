<!--
 * @Author: qin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-24 11:35:45
 * @LastEditors: qinqinglin <EMAIL>
 * @LastEditTime: 2025-06-27 16:07:26
 * @FilePath: \dutp-stu-tea-vue\src\views\smartCourse\smartCourseDetails\index_activity.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <DetailsSignin
    v-if="activityParamType == '1'"
    @listActivityId="listActivityId" />
  <DetailsAsk
    v-if="activityParamType == '2'"
    @listActivityId="listActivityId" />
  <ListResource
    v-if="!activityParamType"
    :data="data"
    @listActivityId="listActivityId" />

</template>

<script setup>
import { ref, reactive, getCurrentInstance, computed } from 'vue'
import ListResource from './list_resource.vue'
// 课件
import DetailsSignin from './details_signin.vue'
// 我的资源
import DetailsAsk from './details_ask.vue'

const { proxy } = getCurrentInstance()
const props = defineProps(['data'])
const activityParam = ref()
const activityParamType = ref()
// 判断跳转
const listActivityId = e => {
  activityParam.value = e
  activityParamType.value = e?.type
  console.log(activityParam.value)
}
</script>

<style scoped lang="scss"></style>
