<template>
  <el-dialog v-model="dialogVisible" title="新建测试" width="600" :before-close="handleClose" style="margin-top: 20vh !important">
    <el-form ref="formRef" :model="form" label-width="auto" class="demo-ruleForm" :rules="rules">
      <el-form-item label="测试名称" prop="bt">
        <el-input show-word-limit maxlength="50" v-model="form.bt" type="text" autocomplete="off" placeholder="请输入测试名称" />
      </el-form-item>
      <el-form-item label="测试试卷">
        <el-button type="primary" @click="openChange">添加试卷</el-button>
      </el-form-item>
      <div class="popup_show_tag">
        <el-tag
          class="popup_show_tag_item"
          v-for="(tag, index) in changeValList"
          :key="tag.id"
          closable
          :disable-transitions="false"
          @close="handleCloseTag(index)"
        >
          {{ tag.zymc }}
        </el-tag>
      </div>
      <el-form-item label="开始时间">
          <el-date-picker v-model="form.kssj" type="datetime" placeholder="请选择开始时间" />
      </el-form-item>
      <el-form-item label="提交时间">
          <el-date-picker v-model="form.jssj" type="datetime" placeholder="请选择提交时间" />
      </el-form-item>
      <el-form-item label="测试要求">
        <el-input v-model="form.nr" :rows="3" type="textarea" show-word-limit="true" maxlength="500" placeholder="请输入测试要求" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmFrom">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <PopupTestChange @changeVal="changeVal" ref="popupTestChange" />
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PopupTestChange from './popup_test_change.vue'

const dialogVisible = ref(false)
const formRef = ref(null)
const changeValList = ref([])
const changeValIds = ref([])
const tableRef = ref(null)
const form = ref({
  bt: '',
  nr: ''
})
const dynamicTags = ref(['Tag 1', 'Tag 2', 'Tag 3'])

const rules = ref({
  bt: [{ required: true, message: '测试名称不能为空', trigger: 'change' }]
})
// 开启弹窗
const open = () => {
  changeValList.value = []
  changeValIds.value = []
  dialogVisible.value = true
  // 清空校验
  if (formRef.value) formRef.value.resetFields()
  // 清空表格选中
  if (tableRef.value) tableRef.value.clearSelection()
}
// 选择作业
const popupTestChange = ref(null)
const openChange = () => {
  popupTestChange.value.open()
}
// 作业返回id
const changeVal = val => {
  changeValList.value = changeValList.value.concat(val)
  changeValIds.value = changeValList.value.map(item => item.id)
}
// 确定
const confirmFrom = () => {
  formRef.value.validate(valid => {
    if (valid) {
      if (changeValIds.value.length == 0) {
        return ElMessage({
          type: 'info',
          message: '请选择试卷'
        })
      }
      console.log('submit!')
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}
const handleCloseTag = i => {
  changeValList.value.splice(i, 1)
  changeValIds.value.splice(i, 1)
}

const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.popup_show_table {
  max-height: 300px;
  overflow-y: auto;
}
.popup_show_tag {
  margin: 0 20px 15px 75px;
  .popup_show_tag_item {
    margin: 3px;
  }
}
</style>
