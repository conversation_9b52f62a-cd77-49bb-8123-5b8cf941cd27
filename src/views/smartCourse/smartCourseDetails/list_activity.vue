<template>
  <div>
    <div class="box_4 flex-col">
      <div class="text-wrapper_37 flex-row">
        <span class="text_14">活动列表</span>
      </div>
      <div class="group_18">
        <div
          class="section_1 flex-col"
          :class="'bg' + item.type"
          v-for="(item, index) in data"
          :key="index"
          @click="clickListActivity(item.activityId, item.type, item.state)"
        >
          <div class="section_1_inerl">
            <div>
              <div class="inerl_title">
                <div class="inerl_title_left">
                  {{ item.title }}
                </div>
                  <div class="inerl_title_right" v-if="item.state == 2">
                    进行中
                  </div>
                  <div class="inerl_title_right_no" v-else>
                    {{ item.state == 1 ? "未开始" : "已结束" }}
                  </div>
              </div>
              <div class="inerl_cont">
                <div v-if="item.type == 0">
                  签到教师：<span>{{ item.signInTeacherName }}</span>
                </div>
                <!-- <div
                  v-if="item.type == 1 || item.type == 2 || item.type == 4 || item.type == 7 || item.type == 8">
                  参与人数：<span>{{ item.participantsNumber }}</span>
                </div> -->
                <div v-if="item.type != 0">
                  任务完成人数：<span>{{ item.completeNumber }}</span
                  >/{{ item.totalNumber }}
                </div>
                <div v-if="item.type == 1">
                  签到类型：{{ item.displayType }}
                </div>
                <div v-if="item.type == 2">
                  提问类型：{{ item.displayType }}
                </div>
                <div v-if="item.type == 7">作业类型：{{ item.jobType }}</div>
                <div v-if="item.type == 5">
                  题目数：{{ item.questionsNumber }}
                </div>
                <div v-if="item.state == 2">
                  开始时间：{{ item.startTime }}
                </div>
                <div v-if="item.state == 3 && item.type != 0">结束时间：{{ item.endTime }}</div>
                <div v-if="(item.type == 1 || item.type == 7 || item.type == 8) && item.djs !== '已结束'">
                  倒计时：<span style="color: #ff8526">{{ item.djs }}</span>
                </div>
                <!-- <div
                  v-if="
                    item.type == 0 ||
                    item.type == 1 ||
                    item.type == 2 ||
                    item.type == 5 ||
                    item.type == 6 ||
                    item.type == 7 ||
                    item.type == 8
                  "
                > -->
                <div>
                  创建时间：{{ item.createTime }}
                </div>
              </div>
            </div>
            <div v-if="item.type != 0" class="inerl_btn">
              <el-button
                @click.stop="startActivity(item)"
                size="small"
                v-if="item.state == 1 && item.type != 1 && teacherSign"
                >开始活动</el-button
              >
              <el-button
                @click.stop="endActivity(item)"
                size="small"
                v-if="item.state == 2"
                >结束活动</el-button
              >
              <el-button size="small" @click.stop="upActivity(item)" 
              v-if="item.state == 1 && (item.type == 3 || item.type == 7 || item.type == 8 || item.type == 4|| item.type == 5)"
                ><img :src="editImg" />编辑</el-button
              >
              <el-button
                @click.stop="delActivity(item)"
                size="small"
                v-if="item.state == 1"
                ><img :src="delImg" />删除</el-button
              >
              <el-button size="small" v-if="item.state == 3"
                ><img :src="eyeImg" />查看</el-button
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <PopupBrain ref="popupBrain"/>
  <PopupLearn ref="popupLearn"/>
  <PopupVote ref="popupVote"/>
</template>

<script setup>
import editImg from "@/assets/images/smartCourse/smartCourseDetails/edit.png";
import eyeImg from "@/assets/images/smartCourse/smartCourseDetails/eye.png";
import delImg from "@/assets/images/smartCourse/smartCourseDetails/del.png";

import { ref, watch, getCurrentInstance, onMounted, onUnmounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { updateActivityList, delActivityList } from "@/api/edu/class";
import dayjs from 'dayjs';
// 头脑风暴
import PopupBrain from "./popup_brain.vue";
// 拓展学习
import PopupLearn from "./popup_learn.vue";
// 投票问卷
import PopupVote from "./popup_vote.vue";
const popupBrain = ref(null);
const popupLearn = ref(null);
const popupVote = ref(null);
const { proxy } = getCurrentInstance();
const props = defineProps(["data"]);
const emits = defineEmits(["listActivityId"]);
const teacherSign = ref(false);
const timers = ref([]); // [{ activityId, timer }]
// 跳转详情页
const clickListActivity = (activityId, type, state) => {
  emits("listActivityId", { activityId, type, state });
};
// 编辑活动
const upActivity = (item) => {
   if(item.type == 3) { 
    popupBrain.value.open(item);
   }
   else if(item.type == 4) { 
    popupLearn.value.open(item);
   }
   else if(item.type == 5) { 
    popupVote.value.open(item);
   }
};
// 删除活动
const delActivity = (item) => {
  ElMessageBox.confirm("删除后不可恢复，确定要删除吗?", "删除提醒", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      let parm = null;
      if (item.type === 1) {
        parm = {
          type: 1,
          lessonId: item.lessonId,
          moocSmartCourseClassAttendance: {
            attendanceActivityId: item.activityId
          },
        };
      }
      else if (item.type === 2) {
        parm = {
          type: 2,
          lessonId: item.lessonId,
          moocSmartCourseClassQuestion: {
            questionId: item.activityId
          },
        };
      }
      else if (item.type === 3) {
        parm = {
          type: 3,
          lessonId: item.lessonId,
          moocSmartCourseBrainstorm : {
            brainstormId: item.activityId
          },
        };
      }
      else if (item.type === 4) {
        parm = {
          type: 4,
          lessonId: item.lessonId,
          moocSmartCourseExtension : {
            extensionId: item.activityId
          },
        };
      }
      else if (item.type === 5) {
        parm = {
          type: 5,
          lessonId: item.lessonId,
          moocSmartCourseSurvey : {
            surveyId: item.activityId
          },
        };
      }
    delActivityList(parm)
      .then((res) => {
        if (res.code === 200) {
          ElMessage({
            type: "success",
            message: `活动 [${item.title}] 已删除`,
          });
        }
      })
      .catch(() => {
        ElMessage({
          type: "error",
          message: `活动 [${item.title}] 删除失败`,
        });
      });
    })
};

// 开始活动
const startActivity = (item) => {
  ElMessageBox.confirm("确定开始活动吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      let parm = null;
      if (item.type === 3) {
        parm = {
          type: 3,
          lessonId: item.lessonId,
          moocSmartCourseBrainstorm: {
            brainstormId: item.activityId,
            status: 2,
            brainstormStartTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          },
        };
      }
      else if (item.type === 4) {
        parm = {
          type: 4,
          lessonId: item.lessonId,
          moocSmartCourseExtension: {
            extensionId: item.activityId,
            status: 2,
            extensionStartTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          },
        };
      }
      else if (item.type === 5) {
        parm = {
          type: 5,
          lessonId: item.lessonId,
          moocSmartCourseSurvey : {
            surveyId: item.activityId,
            status: 2,
            startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          },
        };
      }
      updateActivityList(parm).then((res) => {
        if (res.code == 200) {
          const index = timers.value.findIndex(
            (t) => t.activityId === item.activityId
          );
          if (index !== -1) {
            clearInterval(timers.value[index].timer);
            timers.value.splice(index, 1);
          }
          ElMessage({
            type: "success",
            message: `活动 [${item.title}] 开始成功`,
          });
        }
      });
    })
    .catch((e) => {
      console.log(e);
      ElMessage({ type: "info", message: "已取消开始" });
    });
};

// 结束活动
const endActivity = (item) => {
  ElMessageBox.confirm(
    "还未到达您所设置的教务任务结束时间，是否希望提前结束此任务?",
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      let parm = null;
      if (item.type === 1) {
        parm = {
          type: 1,
          lessonId: item.lessonId,
          moocSmartCourseClassAttendance: {
            attendanceActivityId: item.activityId,
            status: 3,
            activityEndTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          },
        };
      }
      else if (item.type === 2) {
        parm = {
          type: 2,
          lessonId: item.lessonId,
          moocSmartCourseClassQuestion: {
            questionId: item.activityId,
            status: 3,
            questionEndDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          },
        };
      }
      else if (item.type === 3) {
        parm = {
          type: 3,
          lessonId: item.lessonId,
          moocSmartCourseBrainstorm: {
            brainstormId: item.activityId,
            status: 3,
            brainstormEndTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          },
        };
      }
      else if (item.type === 4) {
        parm = {
          type: 4,
          lessonId: item.lessonId,
          moocSmartCourseExtension: {
            extensionId: item.activityId,
            status: 3,
            extensionEndTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          },
        };
      }
      else if (item.type === 5) {
        parm = {
          type: 5,
          lessonId: item.lessonId,
          moocSmartCourseSurvey : {
            surveyId: item.activityId,
            status: 3,
            endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          },
        };
      }
      updateActivityList(parm).then((res) => {
        if (res.code == 200) {
          const index = timers.value.findIndex(
            (t) => t.activityId === item.activityId
          );
          if (index !== -1) {
            clearInterval(timers.value[index].timer);
            timers.value.splice(index, 1);
          }
          ElMessage({
            type: "success",
            message: `活动 [${item.title}] 已结束`,
          });
        }
      });
    })
    .catch(() => {
      ElMessage({ type: "info", message: "结束活动失败" });
    });
};
const autoEndActivity = (item) => {
    let parm = null;
    if (item.type === 1) {
      parm = {
        type: 1,
        lessonId: item.lessonId,
        moocSmartCourseClassAttendance: {
          attendanceActivityId: item.activityId,
          status: 3,
          activityEndTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        },
      };
    }
    updateActivityList(parm)
      .then((res) => {
        if (res.code === 200) {
          ElMessage({
            type: "success",
            message: `活动 [${item.title}] 已自动结束`,
          });
        }
      })
      .catch(() => {
        ElMessage({
          type: "error",
          message: `活动 [${item.title}] 自动结束失败`,
        });
      });
};

// 倒计时计算
const calculateCountdown = (item) => {
  const endTime = new Date(item.endTime).getTime();
  const now = new Date().getTime();
  const diff = endTime - now;

  if (diff <= 0) {
    item.djs = "已结束";
    // 1. 清除定时器
    const index = timers.value.findIndex(
      (t) => t.activityId === item.activityId
    );
    if (index !== -1) {
      clearInterval(timers.value[index].timer);
      timers.value.splice(index, 1);
    }
    if (((item.type == 1 && item.endMethod == 2) || item.type == 7 || item.type == 8) && !item.upFlag){
      autoEndActivity(item);
    }
    return;
  }

  const hours = Math.floor(diff / 1000 / 60 / 60);
  const minutes = Math.floor((diff / 1000 / 60) % 60);
  const seconds = Math.floor((diff / 1000) % 60);
  item.djs = `${hours}小时${minutes}分${seconds}秒`;
};

// 初始化倒计时
const initCountdown = () => {
  clearAllTimers();
  props.data.forEach((item) => {
    if ([1, 7, 8].includes(item.type)) {
      calculateCountdown(item);
      const timer = setInterval(() => calculateCountdown(item), 1000);
      timers.value.push({ activityId: item.activityId, timer });
    }
  });
};

// 清除所有定时器
const clearAllTimers = () => {
  timers.value.forEach((t) => clearInterval(t.timer));
  timers.value = [];
};
watch(
  () => props.data,
  (newData) => {
    if (newData && newData.length > 0) {
      const typeZeroItems = newData.filter(item => item.type === 0);
      if (typeZeroItems.length > 0) {
        teacherSign.value = true;
      }
      initCountdown();
    }
  },
  { deep: false }
);

onMounted(() => {
  const typeZeroItems = props.data.filter(item => item.type === 0);
  if (typeZeroItems.length > 0) {
    teacherSign.value = true;
  }
  initCountdown();
});
onUnmounted(() => {
  clearAllTimers();
});
</script>

<style scoped lang="scss"></style>
