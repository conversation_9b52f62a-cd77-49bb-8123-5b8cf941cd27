<template>
  <div class="page_course_details_one flex-col">
    <div class="block_13 flex-col">
      <div class="box_31 flex-row">
        <!-- <span class="text_9">课程管理&nbsp;&gt;&nbsp;班级列表&nbsp;&gt;&nbsp;班级名称名称</span> -->
        <el-breadcrumb class="text_9" :separator-icon="ArrowRight">
          <el-breadcrumb-item :to="{ path: '/' }">课程管理</el-breadcrumb-item>
          <el-breadcrumb-item>班级列表</el-breadcrumb-item>
          <el-breadcrumb-item>班级名称</el-breadcrumb-item>
        </el-breadcrumb>
        <span @click="clickCode" class="text_10">课堂邀请码：{{lessonData.classCode}}</span>
        <div class="text-wrapper_1 flex-col">
          <el-button type="primary" @click="teacherSign" :disabled="teacherCheckDatetime">教师签到</el-button>
        </div>
        <div class="text-wrapper_2 flex-col">
          <el-button type="primary">智能投屏</el-button>
        </div>
        <div class="text-wrapper_3 flex-col">
          <el-button type="danger" plain @click="classOver">下课</el-button>
        </div>
      </div>
      <div class="box_18 flex-col">
        <div class="text-wrapper_38 flex-row justify-between">
          <span
            class="text_91"
            :style="{
              color:
                tabIndex == 1
                  ? 'rgba(51, 51, 51, 1)'
                  : 'rgba(153, 153, 153, 1)',
            }"
            @click="changeTab(1)"
            >课堂活动</span
          >
          <span
            class="text_92"
            :style="{
              color:
                tabIndex == 2
                  ? 'rgba(51, 51, 51, 1)'
                  : 'rgba(153, 153, 153, 1)',
            }"
            @click="changeTab(2)"
            >教学资源</span
          >
        </div>
        <div class="section_24 flex-row justify-between">
          <div
            :style="{ visibility: tabIndex == 1 ? 'visible' : 'hidden' }"
            class="group_11 flex-col"
          ></div>
          <div
            :style="{ visibility: tabIndex == 2 ? 'visible' : 'hidden' }"
            class="group_12 flex-col"
          ></div>
        </div>

        <div
          class="box_20 flex-row"
          v-if="tabIndex == 1"
          v-for="(item, index) in leftTab"
          :key="index"
          :style="{ background: item.bg }"
          @click="clickTab(item.id)"
        >
          <img
            class="label_11"
            referrerpolicy="no-referrer"
            :src="item.imgUrl"
          />
          <div class="text-wrapper_39 flex-col justify-between">
            <span class="text_93">{{ item.title }}</span>
            <span class="text_94">{{ item.text }}</span>
          </div>
        </div>
        <div
          class="box_20 flex-row"
          v-if="tabIndex == 2"
          v-for="(item, index) in rightTab"
          :key="index"
          :style="{ background: item.bg }"
          @click="clickTab(item.id)"
        >
          <img
            class="label_11"
            referrerpolicy="no-referrer"
            :src="item.imgUrl"
          />
          <div class="text-wrapper_39 flex-col justify-between">
            <span class="text_93">{{ item.title }}</span>
            <span class="text_94">{{ item.text }}</span>
          </div>
        </div>
      </div>
      <!-- 课堂活动 -->
      <IndexActivity v-if="tabIndex == 1" :teacherSignIn = 'teacherCheckDatetime' :data="activityList" />
      <!-- 教学资源 -->
      <IndexResource v-else :data="hdDataRight" />
    </div>
  </div>
  <PopupSignin ref="popupSignin" :form="lessonData" :activityList="activityList" />
  <PopupAsk ref="popupAsk" :form="lessonData"/>
  <PopupBrain ref="popupBrain" :form="lessonData"/>
  <PopupLearn ref="popupLearn" :form="lessonData"/>
  <PopupVote ref="popupVote" :form="lessonData"/>
  <PopupShow ref="popupShow" :form="lessonData" />
  <PopupCode ref="popupCode" :form="lessonData"/>
  <PopupHomework ref="popupHomework" />
  <PopupTest ref="popupTest" />
  <PopupCourseware ref="popupCourseware" />
  <PopupResource ref="popupResource" />
</template>
<script setup>
// 统一导入所有图片资源
import signinImg from "@/assets/images/smartCourse/smartCourseDetails/signin.png";
import questionImg from "@/assets/images/smartCourse/smartCourseDetails/question.png";
import brainImg from "@/assets/images/smartCourse/smartCourseDetails/brain.png";
import learnImg from "@/assets/images/smartCourse/smartCourseDetails/learn.png";
import voteImg from "@/assets/images/smartCourse/smartCourseDetails/vote.png";
import showImg from "@/assets/images/smartCourse/smartCourseDetails/show.png";
import homeworkImg from "@/assets/images/smartCourse/smartCourseDetails/homework.png";
import testImg from "@/assets/images/smartCourse/smartCourseDetails/test.png";
import courseware from "@/assets/images/smartCourse/smartCourseDetails/courseware.png";
import resources from "@/assets/images/smartCourse/smartCourseDetails/resources.png";
import { ElMessage, ElMessageBox } from "element-plus";
import { ArrowRight } from "@element-plus/icons-vue";
import { ref, getCurrentInstance, onMounted, onBeforeUnmount, onUnmounted } from "vue";
import { useStompWebSocket } from '@/utils/useStompWebSocket.js';
import { getActivityList, addActivityList } from "@/api/edu/class";

const { proxy } = getCurrentInstance();
// 签到
import PopupSignin from "./popup_signin.vue";
// 提问
import PopupAsk from "./popup_ask.vue";
// 头脑风暴
import PopupBrain from "./popup_brain.vue";
// 拓展学习
import PopupLearn from "./popup_learn.vue";
// 投票问卷
import PopupVote from "./popup_vote.vue";
// 作品秀
import PopupShow from "./popup_show.vue";
// 签到码
import PopupCode from "./popup_code.vue";
// 作业
import PopupHomework from "./popup_homework.vue";
// 测试
import PopupTest from "./popup_test.vue";
// 课堂活动
import IndexActivity from "./index_activity.vue";
// 教学资源
import IndexResource from "./index_resource.vue";
// 课件
import PopupCourseware from "./popup_courseware.vue";
// 我的资源
import PopupResource from "./popup_resource.vue";
import { getLesson, updateLesson } from "@/api/edu/lesson.js";
const route = useRoute();

const activityList = ref([]);
// 定义接收消息的处理函数
function handleWebSocketMessage(data) {
  console.log('🧾 页面收到 WebSocket 消息：', data)
  activityList.value = data
}
const { connect, disconnect } = useStompWebSocket(handleWebSocketMessage)

const fetchActivityList = async (lessonId) => {
  const res = await getActivityList({ lessonId: lessonId });
  activityList.value = res.data;
  console.log('fetchActivityList', res);
};

const hdData = ref([
  {
    state: 1,
    activityId: 0,
    type: 0,
    title: "教师签到",
    quantity: 2,
    questionsNumber: 2,
    startTime: "2025-01-10",
    endTime: "2025-03-13",
  },
  {
    state: 1,
    activityId: 5,
    type: 5,
    title: "投票问卷未开始",
    quantity: 2,
    questionsNumber: 2,
    startTime: "2025-01-10",
    endTime: "2025-03-13",
  },
  {
    state: 2,
    activityId: 2,
    type: 2,
    title: "提问",
    quantity: 2,
    questionsNumber: 2,
    startTime: "2025-01-10",
    endTime: "2025-03-13",
  },
  {
    state: 3,
    activityId: 3,
    type: 3,
    title: "头脑风暴",
    quantity: 2,
    questionsNumber: 2,
    startTime: "2025-01-10",
    endTime: "2025-03-13",
  },
  {
    state: 3,
    activityId: 4,
    type: 4,
    title: "扩展学习",
    quantity: 2,
    questionsNumber: 2,
    startTime: "2025-01-10",
    endTime: "2025-03-13",
  },
  {
    state: 3,
    activityId: 5,
    type: 5,
    title: "投票问卷",
    quantity: 2,
    questionsNumber: 2,
    startTime: "2025-01-10",
    endTime: "2025-03-13",
  },
  {
    state: 3,
    activityId: 6,
    type: 6,
    title: "作品秀",
    quantity: 2,
    questionsNumber: 2,
    startTime: "2025-01-10",
    endTime: "2025-03-13",
  },
  {
    state: 3,
    activityId: 7,
    type: 7,
    title: "作业",
    quantity: 2,
    questionsNumber: 2,
    startTime: "2025-01-10",
    endTime: "2025-03-13",
  },
  {
    state: 3,
    activityId: 8,
    type: 8,
    title: "测试",
    quantity: 2,
    questionsNumber: 2,
    startTime: "2025-01-10",
    endTime: "2025-03-13",
  },
]);
const hdDataRight = ref([
  { state: 1, bt: "课程名称123", cjsj: "2025-01-10", type: 1 },
  { state: 2, bt: "课程名称123", cjsj: "2025-01-10", type: 2 },
]);
const leftTab = ref([
  {
    id: 1,
    bg: "linear-gradient( 180deg, rgba(202,194,255,0.6) 0%, #EEEBFF 71%)",
    imgUrl: signinImg,
    title: "签到",
    text: "签到有责，教泽流长",
  },
  {
    id: 2,
    bg: "linear-gradient( 180deg, rgba(194,214,255,0.6) 0%, #EBF1FF 85%)",
    imgUrl: questionImg,
    title: "提问",
    text: "问出精彩，答出深度",
  },
  {
    id: 3,
    bg: "linear-gradient( 180deg, rgba(255,220,167,0.6) 0%, #FFF4E2 69%), linear-gradient( 180deg, rgba(255,218,194,0.6) 0%, #FEF3EB 73%)",
    imgUrl: brainImg,
    title: "头脑风暴",
    text: "思维碰撞，灵感迸发",
  },
  {
    id: 4,
    bg: "linear-gradient( 180deg, rgba(169,245,236,0.6) 0%, #E6FDFA 69%)",
    imgUrl: learnImg,
    title: "拓展学习",
    text: "求知无界，学海扬帆",
  },
  {
    id: 5,
    bg: "linear-gradient( 180deg, rgba(194,214,255,0.6) 0%, #EBF1FF 85%)",
    imgUrl: voteImg,
    title: "投票问卷",
    text: "你的选择，至关重要",
  },
  {
    id: 6,
    bg: "linear-gradient( 180deg, rgba(255,220,167,0.6) 0%, #FFF4E2 69%)",
    imgUrl: showImg,
    title: "作品秀",
    text: "匠心呈现，惊艳全场",
  },
  {
    id: 7,
    bg: "linear-gradient( 180deg, rgba(169,245,236,0.6) 0%, #E6FDFA 69%)",
    imgUrl: homeworkImg,
    title: "作业",
    text: "作业用心做，知识自然牢",
  },
  {
    id: 8,
    bg: "linear-gradient( 180deg, rgba(202,194,255,0.6) 0%, #EEEBFF 71%)",
    imgUrl: testImg,
    title: "测试",
    text: "测试挑战，突破极限",
  },
]);
const rightTab = ref([
  {
    id: 9,
    bg: "linear-gradient( 180deg, rgba(202,194,255,0.6) 0%, #EEEBFF 71%)",
    imgUrl: courseware,
    title: "课件",
    text: "课件里藏着星辰大海",
  },
  {
    id: 10,
    bg: "linear-gradient( 180deg, rgba(194,214,255,0.6) 0%, #EBF1FF 85%)",
    imgUrl: resources,
    title: "我的资源",
    text: "资源为翼，创新为帆",
  },
]);
// 切换tab
const tabIndex = ref(1);
const changeTab = (e) => {
  tabIndex.value = e;
};
const popupCode = ref(null);
const popupSignin = ref(null);
const popupAsk = ref(null);
const popupBrain = ref(null);
const popupLearn = ref(null);
const popupVote = ref(null);
const popupShow = ref(null);
const popupTest = ref(null);
const popupHomework = ref(null);
const popupCourseware = ref(null);
const popupResource = ref(null);
const lessonId = ref(null);
const lessonData = ref({});
const teacherCheckDatetime = ref(false);
// 点击tab
const clickTab = (e) => {
  switch (true) {
    case e == 1:
    if(teacherCheckDatetime.value){
        popupSignin.value.open();
      }else{
        ElMessage.warning('请先进行教师签到！')
      }
      break;
    case e == 2:
      if(teacherCheckDatetime.value){
        popupAsk.value.open();
      }else{
        ElMessage.warning('请先进行教师签到！')
      }
      break;
    case e == 3:
      popupBrain.value.open(null);
      break;
    case e == 4:
      popupLearn.value.open(null);
      break;
    case e == 5:
      popupVote.value.open(null);
      break;
    case e == 6:
      popupShow.value.open();
      break;
    case e == 7:
      popupHomework.value.open();
      break;
    case e == 8:
      popupTest.value.open();
      break;
    case e == 9:
      popupCourseware.value.open();
      break;
    case e == 10:
      popupResource.value.open();
      break;
  }
};

// 签到码
const clickCode = () => {
  popupCode.value.open();
};
// 教师签到
const teacherSign = () => {
  ElMessageBox.confirm("确定进行签到吗?", "教师签到", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      const now = new Date();
      const formatted = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}-${String(now.getDate()).padStart(2, "0")} ${String(now.getHours()).padStart(2, "0")}:${String(now.getMinutes()).padStart(2, "0")}:${String(now.getSeconds()).padStart(2, "0")}`;
      lessonData.value.teacherCheckDatetime = formatted;
      const parm = {
        type : 0,
        lessonId: lessonId.value,
        classId: lessonData.value.classId,
        moocSmartCourseLesson: lessonData.value
      }
      console.log(parm);
      addActivityList(parm).then((res) => {
        if (res.code == 200) {
          ElMessage({ type: "success", message: "签到成功" });
          teacherCheckDatetime.value = true;
        }
      });
    })
    .catch(() => {
      ElMessage({ type: "info", message: "已取消" });
    });
};
// 下课
const classOver = () => {
  ElMessageBox.confirm("确定下课吗?", "下课", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      lessonData.value.status = 2;
      const now = new Date();
      const formatted = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}-${String(now.getDate()).padStart(2, "0")} ${String(now.getHours()).padStart(2, "0")}:${String(now.getMinutes()).padStart(2, "0")}:${String(now.getSeconds()).padStart(2, "0")}`;
      lessonData.value.endDatetime = formatted;
      updateLesson(lessonData.value).then((res) => {
        if (res.code == 200) {
          proxy.$router.push("/smart-course-list");
        }
      });
    })
    .catch(() => {});
};
onMounted(() => {
  lessonId.value = route.query.lessonId;
  fetchActivityList(lessonId.value);
  getLesson(lessonId.value).then((res) => {
    if(res.code == 200){
      teacherCheckDatetime.value = res.data.teacherCheckDatetime == null ? false : true;
      lessonData.value = res.data;
    }
  });
  connect();
});
onBeforeUnmount(() => {
  disconnect()
  })
</script>
<style lang="scss">
.page_course_details_one {
  background-color: rgba(242, 245, 250, 1);
  position: relative;
  width: 100vw;
  height: 56.25vw;
  overflow: hidden;
  .flex-col {
    display: flex;
    flex-direction: column;
  }
  .flex-row {
    display: flex;
    flex-direction: row;
  }
  .justify-between {
    display: flex;
    justify-content: space-between;
  }
  .block_13 {
    position: relative;
    width: 100vw;
    height: 52.35vw;
    margin-bottom: 0.06vw;
    .box_31 {
      width: 72.09vw;
      height: 2.09vw;
      margin: 1.04vw 0 0 14.32vw;
      .text_9 {
        width: 13.29vw;
        height: 0.84vw;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 0.83vw;
        font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.84vw;
        margin-top: 0.47vw;
      }
      .text_10 {
        width: 9.54vw;
        height: 0.94vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.93vw;
        font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.94vw;
        margin: 0.57vw 0 0 25.31vw;
        cursor: pointer;
      }
      .text-wrapper_1 {
        border-radius: 4px;
        height: 2.09vw;
        margin-left: 1.46vw;
        width: 7.35vw;
        .el-button {
          background: linear-gradient(90deg, #226bff 0%, #0448d2 100%);
        }
      }
      .text-wrapper_2 {
        border-radius: 4px;
        margin-left: 1.1vw;
        height: 2.09vw;
        width: 7.35vw;
        .el-button {
          background: linear-gradient(90deg, #226bff 0%, #0448d2 100%);
        }
      }
      .text-wrapper_3 {
        border-radius: 4px;
        height: 2.09vw;
        // border: 1px solid rgba(255, 77, 79, 1);
        margin-left: 0.99vw;
        width: 5.73vw;
        .text_13 {
          width: 1.67vw;
          height: 0.84vw;
          overflow-wrap: break-word;
          color: red;
          font-size: 0.83vw;
          font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.84vw;
          margin: 0.62vw 0 0 2.03vw;
        }
      }
    }
    .box_4 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 8px;
      height: 49.07vw;
      width: 58.08vw;
      position: absolute;
      left: 28.39vw;
      top: 4.17vw;
      overflow-y: auto;
      .text-wrapper_40 {
        margin: 1.35vw 1vw 0 1.4vw;
        justify-content: flex-end;
      }
      .text-wrapper_37 {
        width: 4.95vw;
        height: 1.25vw;
        margin: 1.35vw 0 0 1.4vw;
        .text_14 {
          width: 4.95vw;
          height: 1.25vw;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 1.25vw;
          font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 1.25vw;
        }
      }
      .group_18 {
        .bg0 {
          background-image: url("../../../assets/images/smartCourse/smartCourseDetails/purple_bg.png");
          background-size: 55vw auto;
          background-repeat: no-repeat;
        }
        .bg1 {
          background-image: url("../../../assets/images/smartCourse/smartCourseDetails/purple_bg.png");
          background-size: 55vw auto;
          background-repeat: no-repeat;
        }
        .bg2 {
          background-image: url("../../../assets/images/smartCourse/smartCourseDetails/blue_bg.png");
          background-size: 55vw auto;
          background-repeat: no-repeat;
        }
        .bg3 {
          background-image: url("../../../assets/images/smartCourse/smartCourseDetails/red_bg.png");
          background-size: 55vw auto;
          background-repeat: no-repeat;
        }
        .bg4 {
          background-image: url("../../../assets/images/smartCourse/smartCourseDetails/green_bg.png");
          background-size: 55vw auto;
          background-repeat: no-repeat;
        }
        .bg5 {
          background-image: url("../../../assets/images/smartCourse/smartCourseDetails/blue_bg.png");
          background-size: 55vw auto;
          background-repeat: no-repeat;
        }
        .bg6 {
          background-image: url("../../../assets/images/smartCourse/smartCourseDetails/red_bg.png");
          background-size: 55vw auto;
          background-repeat: no-repeat;
        }
        .bg7 {
          background-image: url("../../../assets/images/smartCourse/smartCourseDetails/green_bg.png");
          background-size: 55vw auto;
          background-repeat: no-repeat;
        }
        .bg8 {
          background-image: url("../../../assets/images/smartCourse/smartCourseDetails/purple_bg.png");
          background-size: 55vw auto;
          background-repeat: no-repeat;
        }
        width: 55.21vw;
        height: 8.86vw;
        margin: 1.04vw 0 0 1.4vw;
        .section_1 {
          border-radius: 16px;
          width: 55vw;
          height: 5.86vw;
          cursor: pointer;
          .section_1_inerl {
            display: flex;
            justify-content: space-between;
            .inerl_title {
              display: flex;
              margin-left: 1.5vw;
              margin-top: 1vw;
              .inerl_title_left {
                color: #333;
                font-size: 1vw;
              }
              .inerl_title_right {
                height: 1.5vw;
                width: 3.5vw;
                line-height: 1.5vw;
                text-align: center;
                border-radius: 40%;
                border-radius: 1vw;
                border: 1px solid #52c41a;
                font-size: 0.67vw;
                color: #52c41a;
                margin-left: 0.5vw;
              }
              .inerl_title_right_no {
                height: 1.5vw;
                width: 3.5vw;
                line-height: 1.5vw;
                text-align: center;
                border-radius: 40%;
                border-radius: 1vw;
                border: 1px solid #969aaa;
                font-size: 0.67vw;
                color: #969aaa;
                margin-left: 0.5vw;
              }
            }
            .inerl_cont {
              color: #585e76;
              font-size: 0.8vw;
              display: flex;
              margin-top: 0.6vw;
              div {
                margin-left: 1.5vw;
              }
              span {
                color: #333;
              }
            }
            .inerl_btn {
              margin-right: 1vw;
              margin-top: 2vw;
              img {
                width: 15px;
              }
            }
          }
          .image_1 {
            width: 8.96vw;
            height: 0.53vw;
            margin-left: 0.53vw;
          }
          .group_19 {
            width: 15.68vw;
            height: 1.67vw;
            margin-left: 1.25vw;
            .text-wrapper_5 {
              background-color: rgba(252, 202, 188, 1);
              border-radius: 20px;
              height: 1.15vw;
              margin-top: 0.7vw;
              width: 3.08vw;
              .text_15 {
                width: 2.04vw;
                height: 0.94vw;
                overflow-wrap: break-word;
                color: rgba(184, 122, 122, 1);
                font-size: 0.67vw;
                font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.94vw;
                margin: 0.1vw 0 0 0.52vw;
              }
            }
            .image-wrapper_1 {
              box-shadow: 0px 1px 2px 0px rgba(82, 88, 102, 0.06);
              background-color: rgba(255, 255, 255, 1);
              border-radius: 8px;
              height: 1.67vw;
              margin-left: 8vw;
              width: 1.67vw;
              margin-top: 0.45vw;
            }
            .image-wrapper_2 {
              box-shadow: 0px 1px 2px 0px rgba(82, 88, 102, 0.06);
              background-color: rgba(255, 255, 255, 1);
              border-radius: 8px;
              height: 1.67vw;
              margin-left: 0.47vw;
              width: 1.67vw;
              margin-top: 0.45vw;
            }
          }
          .text_16 {
            width: 8.34vw;
            height: 0.94vw;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.93vw;
            font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.94vw;
            margin: 0.98vw 0 0 1.25vw;
          }
          .text_17 {
            width: 8.34vw;
            height: 0.73vw;
            overflow-wrap: break-word;
            color: rgba(184, 122, 122, 1);
            font-size: 0.72vw;
            font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.73vw;
            margin: 0.36vw 0 0 1.25vw;
          }
          .text_18 {
            width: 8.34vw;
            height: 0.73vw;
            overflow-wrap: break-word;
            color: rgba(184, 122, 122, 1);
            font-size: 0.72vw;
            font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.73vw;
            margin: 0.36vw 0 0 1.25vw;
          }
          .group_20 {
            width: 15.68vw;
            height: 1.25vw;
            margin: 0.67vw 0 0.62vw 1.25vw;
            .text_20 {
              width: 10.06vw;
              height: 0.73vw;
              overflow-wrap: break-word;
              color: rgba(184, 122, 122, 1);
              font-size: 0.72vw;
              font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.73vw;
              margin-top: 0.27vw;
            }
          }
        }
        .section_2 {
          border-radius: 16px;
          width: 17.56vw;
          height: 8.86vw;
          margin-left: 1.25vw;
          .image_2 {
            width: 8.96vw;
            height: 0.53vw;
            margin-left: 0.53vw;
          }
          .block_14 {
            width: 15.79vw;
            height: 1.67vw;
            margin-left: 1.25vw;
            .text-wrapper_7 {
              background-color: rgba(184, 234, 255, 1);
              border-radius: 20px;
              height: 1.15vw;
              margin-top: 0.7vw;
              width: 3.08vw;
              .text_21 {
                width: 2.04vw;
                height: 0.94vw;
                overflow-wrap: break-word;
                color: rgba(124, 151, 174, 1);
                font-size: 0.67vw;
                font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.94vw;
                margin: 0.1vw 0 0 0.52vw;
              }
            }
            .label_3 {
              width: 1.67vw;
              height: 1.67vw;
              margin-left: 3.86vw;
            }
          }
          .text_25 {
            width: 8.34vw;
            height: 0.73vw;
            overflow-wrap: break-word;
            color: rgba(121, 159, 189, 1);
            font-size: 0.72vw;
            font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.73vw;
            margin: 0.36vw 0 0 1.25vw;
          }
          .text_26 {
            width: 8.34vw;
            height: 0.73vw;
            overflow-wrap: break-word;
            color: rgba(121, 159, 189, 1);
            font-size: 0.72vw;
            font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.73vw;
            margin: 0.36vw 0 0 1.25vw;
          }
          .block_15 {
            width: 15.79vw;
            height: 1.25vw;
            margin: 0.67vw 0 0.62vw 1.25vw;
            .text-wrapper_9 {
              box-shadow: inset 0px 4px 4px 0px rgba(255, 255, 255, 0.25);
              background-color: rgba(0, 181, 255, 1);
              border-radius: 4px;
              height: 1.25vw;
              width: 3.75vw;
              .text_27 {
                width: 2.92vw;
                height: 0.94vw;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 0.72vw;
                font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.94vw;
                margin: 0.15vw 0 0 0.41vw;
              }
            }
            .text_28 {
              width: 10.06vw;
              height: 0.73vw;
              overflow-wrap: break-word;
              color: rgba(121, 159, 189, 1);
              font-size: 0.72vw;
              font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.73vw;
              margin-top: 0.27vw;
            }
          }
        }
        .section_5 {
          border-radius: 12px;
          width: 17.56vw;
          height: 8.86vw;
          margin-left: 1.25vw;
          .group_21 {
            width: 15.68vw;
            height: 1.67vw;
            margin-left: 1.31vw;
            .text-wrapper_10 {
              background-color: rgba(230, 220, 251, 1);
              border-radius: 20px;
              height: 1.15vw;
              margin-top: 0.7vw;
              width: 3.08vw;
              .text_29 {
                width: 2.04vw;
                height: 0.94vw;
                overflow-wrap: break-word;
                color: rgba(140, 124, 174, 1);
                font-size: 0.67vw;
                font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 0.94vw;
                margin: 0.1vw 0 0 0.52vw;
              }
            }
            .image-wrapper_3 {
              box-shadow: 0px 1px 2px 0px rgba(82, 88, 102, 0.06);
              background-color: rgba(255, 255, 255, 1);
              border-radius: 8px;
              height: 1.67vw;
              margin-left: 0.47vw;
              width: 1.67vw;
            }
          }
          .text_31 {
            width: 8.34vw;
            height: 0.73vw;
            overflow-wrap: break-word;
            color: rgba(140, 124, 174, 1);
            font-size: 0.72vw;
            font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.73vw;
            margin: 0.36vw 0 0 1.25vw;
          }
          .text_32 {
            width: 8.34vw;
            height: 0.73vw;
            overflow-wrap: break-word;
            color: rgba(140, 124, 174, 1);
            font-size: 0.72vw;
            font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.73vw;
            margin: 0.36vw 0 0 1.25vw;
          }
          .text_33 {
            width: 10.06vw;
            height: 0.73vw;
            overflow-wrap: break-word;
            color: rgba(140, 124, 174, 1);
            font-size: 0.72vw;
            font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.73vw;
            margin: 0.27vw 0 0.93vw 5.7vw;
          }
        }
      }
    }
    .box_18 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 8px;
      position: absolute;
      left: 13.55vw;
      top: 4.17vw;
      width: 13.96vw;
      height: 49.07vw;
      .text-wrapper_38 {
        width: 12.19vw;
        height: 1.25vw;
        margin: 1.35vw 0 0 0.78vw;
        .text_91 {
          width: 4.95vw;
          height: 1.25vw;
          overflow-wrap: break-word;
          font-size: 1.25vw;
          font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 1.25vw;
          cursor: pointer;
        }
        .text_92 {
          width: 4.95vw;
          height: 1.25vw;
          overflow-wrap: break-word;
          font-size: 1.25vw;
          font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 1.25vw;
          cursor: pointer;
        }
      }
      .section_24 {
        width: 12.19vw;
        height: 0.11vw;
        margin: 0.15vw 0 0 0.78vw;
        .group_11 {
          border-radius: 2px;
          width: 4.95vw;
          height: 0.11vw;
          background: linear-gradient(153deg, #c7c7c7 0%, #696969 100%);
        }
        .group_12 {
          border-radius: 2px;
          width: 4.95vw;
          height: 0.11vw;
          background: linear-gradient(153deg, #c7c7c7 0%, #696969 100%);
        }
      }
      .box_20 {
        border-radius: 8px;
        width: 12.61vw;
        height: 3.6vw;
        margin: 0.78vw 0 0 0.78vw;
        cursor: pointer;
        .label_11 {
          width: 2.09vw;
          height: 2.09vw;
          margin: 0.73vw 0 0 0.83vw;
        }
        .text-wrapper_39 {
          width: 5.58vw;
          height: 1.78vw;
          margin: 0.95vw 3.28vw 0 0.83vw;
          .text_93 {
            width: 1.88vw;
            height: 0.94vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.93vw;
            font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.94vw;
          }
          .text_94 {
            width: 5.58vw;
            height: 0.63vw;
            overflow-wrap: break-word;
            color: rgba(150, 154, 170, 1);
            font-size: 0.62vw;
            font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.63vw;
            margin-top: 0.21vw;
          }
        }
      }
    }
  }
}
</style>
