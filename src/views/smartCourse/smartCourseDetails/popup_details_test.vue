<template>
  <el-dialog
    v-model="dialogVisible"
    title="测试详情" width="1000"
    :before-close="handleClose"
    style="margin-top: 5vh !important">
    <el-container
      class="popup_show_details">
      <el-main>
        <div
          class="PapersContentItem"
          v-for="(ele, i) in papersData"
          :key="ele.collectionId">
          <!-- {{ NUM[i] }}、{{ TYPE[ele.questionType - 1] }} -->
          <div
            v-if="ele.questionType != 8">
            <div
              class="title">
              <span
                style="padding-right: 10px">{{ NUM[i] }}</span>{{
              TYPE[ele.questionType - 1] === "排序题"
                ? TYPE[ele.questionType - 1] +
                  "提示：请对以下排序题进行操作，将选项拖拽至正确的位置。"
                : TYPE[ele.questionType - 1]
            }}
            </div>
            <div
              class="content">
              <component
                v-for="item in ele.questionList"
                :key="item.collectionId"
                :is="COMPONENTSLIST[ele.questionType - 1]"
                :OPTION="OPTION"
                :TYPE="TYPE"
                :data="item.questionsData"
                :footer="false">
              </component>
            </div>
          </div>
        </div>
      </el-main>
      <el-aside
        class="show_details_aside">
        <div
          class="details_aside_title">
          {{ form.csmc }}
        </div>
        <div
          class="details_aside_cont">
          <div>
            学号：{{ form.xh }}
          </div>
          <div>
            姓名：{{ form.xm }}
          </div>
          <div>
            分数：<i>{{ form.fs }}分</i>
          </div>
          <div>
            测试内容：{{ form.ckda }}
          </div>
          <div>测试要求：</div>
          <el-input readonly
            v-model="form.zyyq"
            :rows="4"
            type="textarea" />
        </div>
      </el-aside>
    </el-container>
    <template #footer>
      <div
        class="dialog-footer">
        <el-button
          @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { getTestPaperQuestions } from '@/api/book/reader'

const dialogVisible = ref(false)
import Topic_1 from '../../reader/sub/PageModule/QuestionsItem/Topic_1.vue'
import Topic_2 from '../../reader/sub/PageModule/QuestionsItem/Topic_2.vue'
import Topic_3 from '../../reader/sub/PageModule/QuestionsItem/Topic_3.vue'
import Topic_4 from '../../reader/sub/PageModule/QuestionsItem/Topic_4.vue'
import Topic_5 from '../../reader/sub/PageModule/QuestionsItem/Topic_5.vue'
import Topic_6 from '../../reader/sub/PageModule/QuestionsItem/Topic_6.vue'
const COMPONENTSLIST = [Topic_1, Topic_2, Topic_3, Topic_4, Topic_5, Topic_6, Topic_1]
const NUM = ['一', '二', '三', '四', '五', '六', '七']
const TYPE = ['单选题', '多选题', '填空题', '排序题', '连线题', '简答题', '判断题']
const OPTION = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'G',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z'
]
const form = ref({
  csmc: '我的测试001',
  xh: '10010',
  xm: '柳如烟',
  fs: 99,
  xsxzrn:
    '开学之前，他考虑到每天通车恐怕太过于辛苦，于是就在学校旁边租了间学生房，只在周末假日，才回家看看妈妈。他所租的是专门分租给学生的一层楼，在旧公寓六楼顶木板加盖的小违建，一共有六个房间，共享一套卫浴设备和一小间厨房，外头屋顶还留有一小片阳台可以晒衣服。阿宾搬进去的时候，还要五六天才开学，也不知道其它房间住的是什么人。',
  xszyfj: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1750656047737.pdf',
  zymc: '阿宾的一天',
  zylx: '1',
  pffs: '1',
  zyfz: 100,
  tjsj: '2025-01-01 08:11:07',
  zyyq: '要求标准规范，不少于一万字',
  zyyq1: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1750656047737.pdf',
  zyyq2: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1750656047737.pdf',
  ckda: '一万字的非常好',
  ckda1: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1750656047737.pdf',
  ckda2: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/1750656047737.pdf',
  zyfs: 80
})
// 开启弹窗
const open = () => {
  dialogVisible.value = true
  getTestPaper()
}
// 获取试卷
const papersData = ref()
const getTestPaper = async () => {
  let lastSaveAnswer = []
  const res = await getTestPaperQuestions('1920022145074827266')
  if (res.code === 200) {
    console.log(res)
    // 遍历试题
    papersData.value = res.data.map(ele => {
      return {
        ...ele,
        questionList: ele.questionList.map((item, index) => {
          const val = item.questionContent
          const lastSaveAnswerItem = lastSaveAnswer.find(_e => _e.questionId === item.questionId)
          return {
            ...item,
            questionsData: {
              questionText: TYPE[val.questionType - 1],
              questionType: val.questionType,
              questionContent: addserialNumber(index + 1, val.questionContent, item.questionScore),
              analysis: val.analysis,
              options: val.options,
              rightAnswer: val.rightAnswer,
              questionScore: item.questionScore, //分值
              questionId: item.questionId,
              defaultValue: lastSaveAnswerItem?.answerContent
            }
          }
        })
      }
    })
    // isShow.value = true
  }
}
/**
 * 向题干开头和结尾添加小题序号和分值
 */
const addserialNumber = (num, str, branch) => {
  let _t = ''
  if (isRichText(str)) {
    const div = document.createElement('div')
    div.innerHTML = str
    div.children[0].insertAdjacentHTML('afterbegin', `${num}、`)
    div.children[0].insertAdjacentHTML('beforeend', `(${branch}分)`)
    _t = div.innerHTML
    div.remove()
  } else {
    _t = `${num}、${str}(${branch}分)`
  }
  return _t
}
/**
 * 监测字符串是否为富文本
 */
function isRichText(str) {
  const div = document.createElement('div')
  div.innerHTML = str
  const _s = div.children.length > 0 || div.childNodes.length > 1
  div.remove()
  return _s
}
// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.popup_show_details {
  overflow-y: auto;
  max-height: 500px;
}

.show_details_aside {
  width: 250px;
  .details_aside_title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 17px;
  }
  .details_aside_cont {
    font-size: 15px;
    color: rgba(0, 0, 0, 0.65);
    img {
      width: 35px;
      margin-right: 10px;
      margin-top: 10px;
      cursor: pointer;
    }
    .details_aside_input {
      width: 100%;
      // margin-right: 10px;
    }
    i {
      font-style: normal;
      color: #ff4d4f;
    }
  }
}
</style>
