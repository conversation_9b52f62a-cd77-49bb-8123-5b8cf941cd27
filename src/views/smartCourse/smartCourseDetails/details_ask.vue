<template>
  <div class="box_4 flex-col">
    <div class="go_back">
      <el-button
        class="go_back_btn"
        @click="goBack"
        type="primary"
        plain>返回</el-button>
    </div>
    <div
      class="details_signin_content">
      <h3>提问详情</h3>
      <div
        class="signin_content_detail">
        <span>{{ res.sj }}的提问</span>
        <span>提问类型：
          <i
            v-if="res.qdlx == '1'">随机提问</i>
          <i
            v-if="res.qdlx == '2'">点名提问</i>
        </span>
        <span>创建时间：{{ res.cjsj }}</span>
        <span>结束时间：{{ res.jssj }}</span>
      </div>
      <div
        class="signin_content_ask">
        <img class="ask_img"
          :src="ask" alt="">
        <div
          class="content_ask_name">
          <div
            class="ask_name_bgimg">
            <div
              class="name_bgimg_name">
              {{ '阿松大' }} 同学
            </div>
          </div>
          <div
            class="ask_name_input">
            请评分
            <el-input
              placeholder="输入"
              style="width: 50px;"></el-input>
            分
          </div>
        </div>
        <el-button
          class="content_ask_but"
          type="primary">确定</el-button>
        <div
          class="content_ask_but_img">
          <el-button
            class="content_ask_but">
            <img
              :src="changePeople"
              alt="">随机选人</el-button>
          <el-button
            class="content_ask_but">
            <img
              :src="changeThem"
              alt="">随机选组</el-button>
          <el-button
            class="content_ask_but"
            @click="popupHand">
            <img
              :src="changeHand"
              alt="">学生举手</el-button>
        </div>
      </div>
      <div>
        <el-table
          :data="res.tableData"
          style="width: 100%">
          <el-table-column
            prop="xh"
            label="学号" />
          <el-table-column
            prop="xm"
            label="姓名" />
          <el-table-column
            prop="ssbj"
            label="所属班级" />
          <el-table-column
            prop="ssxz"
            label="所属小组" />
          <el-table-column
            prop="df"
            label="分数" />
          <el-table-column
            label="操作">
            <template
              #default="scope">
              <el-button
                type="primary"
                link
                @click="scoring(scope.row.id)">打分</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
  <PopupDetailsAsk
    ref="popupDetailsAsk" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ArrowDown, Search, ArrowUp } from '@element-plus/icons-vue'
import { badgeProps } from 'element-plus'
import ask from '@/assets/images/smartCourse/smartCourseDetails/ask.png'
import changePeople from '@/assets/images/smartCourse/smartCourseDetails/change_people.png'
import changeThem from '@/assets/images/smartCourse/smartCourseDetails/change_them.png'
import changeHand from '@/assets/images/smartCourse/smartCourseDetails/change_hand.png'
// 提问弹窗
import PopupDetailsAsk from './popup_details_ask.vue'

const { proxy } = getCurrentInstance()
const emits = defineEmits(['listActivityId'])
const res = ref({
  sj: '2025-02-50',
  qdlx: '1',
  cjsj: '2025-06-05 16:11',
  jssj: '2025-06-05 16:11',
  tableData: [
    {
      xh: '123123',
      xm: '李神通',
      ssbj: '网络一班',
      ssxz: '小组2',
      df: '45',
      zt: '1',
      qdsj: '2025-01-02 16:11'
    },
    {
      xh: '123123',
      xm: '李神通',
      ssbj: '网络一班',
      ssxz: '小组2',
      df: '34',
      zt: '2',
      qdsj: '2025-01-02 16:11'
    }
  ]
})
// 打分
const scoring = id => {}
// 学生举手
const popupDetailsAsk = ref()
const popupHand = id => {
  popupDetailsAsk.value.open(id)
}

// 跳转详情页
const goBack = () => {
  emits('listActivityId')
}
</script>

<style scoped lang="scss">
.go_back {
  text-align: right;
  .go_back_btn {
    margin: 20px 20px 0 0;
  }
}
.details_signin_content {
  padding: 0 20px 20px 20px;
  .signin_content_detail {
    font-size: 15px;
    color: #585e76;
    margin-bottom: 20px;
    span {
      margin-right: 30px;
    }
    i {
      font-style: normal;
    }
  }
  .signin_content_ask {
    background-image: url('../../../assets/images/smartCourse/smartCourseDetails/blue_bg.png');
    background-size: auto 100px;
    background-repeat: no-repeat;
    width: 100%;
    height: 100px;
    display: flex;
    margin-bottom: 20px;
    .ask_img {
      width: 90px;
      object-fit: contain;
      margin: 0 10px 0 20px;
    }
    .content_ask_name {
      display: flex;
      width: 300px;
      height: 56px;
      border-radius: 10px;
      border: 1px solid #021a63;
      margin-top: 23px;
      .ask_name_bgimg {
        background-image: url('../../../assets/images/smartCourse/smartCourseDetails/name_bg.png');
        background-size: 160px 55px;
        background-repeat: no-repeat;
        width: 160px;
        height: 55px;
        margin-right: 5px;
        .name_bgimg_name {
          line-height: 55px;
          margin-left: 30px;
          font-size: 18px;
          color: #ffffff;
        }
      }
      .ask_name_input {
        margin-top: 12px;
      }
    }
    .content_ask_but {
      margin: 35px 0 0 10px;
    }
    .content_ask_but_img {
      img {
        width: 15px;
        object-fit: contain;
        margin-right: 5px;
      }
    }
  }
  .signin_content_tab {
    display: flex;
    justify-content: space-between;
    .signin_content_tab_title {
      width: 105px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #7e88a5;
      margin: 25px 10px 20px 0;
      color: #333333;
      cursor: pointer;
    }
    .signin_content_tab_search {
      margin-top: 17px;
    }
    .signin_content_tab_title_active {
      background: #555b74;
      color: #ffffff;
    }
  }
}
</style>
