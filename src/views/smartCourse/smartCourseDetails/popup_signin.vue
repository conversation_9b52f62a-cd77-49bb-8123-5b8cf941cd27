<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建签到"
    width="500"
    :before-close="handleClose"
    style="margin-top: 30vh !important"
  >
    <el-form
      ref="formRef"
      style="max-width: 600px"
      :model="form"
      label-width="auto"
      class="demo-ruleForm"
      :rules="rules"
    >
      <el-form-item label="签到标题" prop="title">
        <el-input
          v-model="form.title"
          type="text"
          autocomplete="off"
          placeholder="请输入签到标题"
        />
      </el-form-item>
      <el-form-item label="签到类型" prop="attendanceType">
        <el-radio-group v-model="form.attendanceType">
          <el-radio value="1">教师签到</el-radio>
          <el-radio value="2">手势签到</el-radio>
          <el-radio value="3">签到码</el-radio>
        </el-radio-group>
        <el-input
          v-if="form.attendanceType === '3'"
          v-model="form.signInCode"
          maxlength="6"
          style="width: 240px"
          placeholder="请输入签到码"
        />
        <GesturePad
          v-if="form.attendanceType === '2'"
          mode="create"
          @finish="saveGesture"
        />
      </el-form-item>
      <el-form-item label="结束方式" prop="endMethod">
        <el-radio-group v-model="form.endMethod">
          <el-radio value="1">手动结束</el-radio>
          <el-radio value="2">自动结束</el-radio>
        </el-radio-group>
        <el-time-picker
          v-if="form.endMethod === '2'"
          v-model="form.durationMinutes"
          format="HH:mm:ss"
          :default-value="new Date(0, 0, 0, 0, 0, 0)"
          style="width: 240px"
          placeholder="选择签到时长"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted } from "vue";
import dayjs from 'dayjs';
// 手势签到
import GesturePad from "./index_gesture_pad.vue";
import { ElMessageBox } from "element-plus";
import { addActivityList } from "@/api/edu/class";
const pattern = ref("");
const props = defineProps({ form: { type: Object, default: {} }, activityList: { type: Object, default: [] }});
const form = ref({
  courseId: "",
  lessonId: "",
  classId: "",
  title: '',
  attendanceType: "1",
  endMethod: "1",
  durationMinutes: "",
  signInCode: "",
  gesturePattern: "",
  activityStartTime: '',
  activityEndTime: '',
});
const dialogVisible = ref(false);
const formRef = ref(null);
const rules = ref({
  title: [{ required: true, message: "签到标题不能为空", trigger: "change" }],
  attendanceType: [
    { required: true, message: "签到类型不能为空", trigger: "change" },
  ],
  endMethod: [
    { required: true, message: "结束方式不能为空", trigger: "change" },
  ],
});
// 开启弹窗
const open = () => {
  dialogVisible.value = true;
  form.value = {
    courseId: "",
    lessonId: "",
    classId: "",
    title: dayjs().format('YYYY-MM-DD HH:mm:ss') + '的签到活动',
    attendanceType: "1",
    endMethod: "1",
    durationMinutes: new Date(0, 0, 0, 0, 0, 0).toString(), 
    signInCode: "",
    gesturePattern: "",
    activityStartTime: '',
    activityEndTime: '',
  };
  if (formRef.value) formRef.value.resetFields();
};
function parseDuration(timeStr) {
  const [hours, minutes, seconds] = timeStr.split(':').map(Number);
  return (hours * 60 + minutes) * 60 + seconds;
}
// 提交
const confirmFrom = () => {
  if (form.value.endMethod === "2" && form.value.durationMinutes === null) {
    ElMessageBox({
      title: "操作提示",
      message: "请输入结束时长",
      type: "warning",
    });
    return;
  }
  if (form.value.attendanceType === "2" &&  pattern.value === "") {
    ElMessageBox({
      title: "操作提示",
      message: "请绘制手势",
      type: "warning",
    });
    return;
  }
  if (form.value.attendanceType === "3" && form.value.signInCode === "") {
    ElMessageBox({
      title: "操作提示",
      message: "请输入签到码",
      type: "warning",
    });
    return;
  }
  // 纯数字校验
  else if (
    form.value.attendanceType === "3" &&
    !/^[0-9]*$/.test(form.value.signInCode)
  ) {
    ElMessageBox({
      title: "操作提示",
      message: "请输入纯数字的签到码",
      type: "warning",
    });
    return;
  }
  formRef.value.validate(async (valid) => {
    if (valid) {
      form.value.courseId = props.form.courseId;
      form.value.classId = props.form.classId;
      form.value.lessonId = props.form.lessonId;
      form.value.activityStartTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
      // 时间格式转换
        if (form.value.endMethod === '2') {
          // 使用 dayjs 格式化 durationMinutes
          form.value.durationMinutes = dayjs(form.value.durationMinutes).format('HH:mm:ss');
          
          // 计算结束时间
          const durationSeconds = parseDuration(form.value.durationMinutes);
          const start = dayjs(form.value.activityStartTime);
          const end = start.add(durationSeconds, 'second');
          form.value.activityEndTime = end.format('YYYY-MM-DD HH:mm:ss');
        } else {
          form.value.durationMinutes = null;
          form.value.activityEndTime = null; 
        }
      const parm = {
        type : 1,
        lessonId: form.value.lessonId,
        classId: form.value.classId,
        moocSmartCourseClassAttendance: form.value
      }
      addActivityList(parm).then((res) => {
        if (res.code == 200) {
          ElMessageBox({
            title: "操作提示",
            message: "添加活动成功",
            type: "success",
          });
        }
      });
      handleClose();
    } else {
      console.log("error submit!");
    }
  });
};
const handleClose = () => {
  dialogVisible.value = false;
  form.value = {
    courseId: "",
    lessonId: "",
    classId: "",
    title: "",
    attendanceType: "1",
    endMethod: "1",
    durationMinutes: new Date(0, 0, 0, 0, 0, 0).toString(), 
    signInCode: "",
    gesturePattern: "",
    activityStartTime: '',
    activityEndTime: '',
  };
  pattern.value = "";
};
defineExpose({ open });

async function saveGesture(p) {
  pattern.value = p;
  form.value.gesturePattern = pattern.value;
  // 提示手势保存成功
  ElMessageBox({ title: "操作提示", message: "手势保存成功", type: "success" });
}
</script>
<style lang="scss">
// .el-dialog.signin_popup_dialog :not(.is-fullscreen) {
//   margin-top: 30vh !important;
// }
</style>
