<template>
  <el-dialog
    v-model="dialogVisible"
    title="举手名单" width="600"
    :before-close="handleClose"
    style="margin-top: 30vh !important">
    <el-table
      :data="tableData"
      style="width: 100%">
      <el-table-column
        prop="xh"
        label="学号" />
      <el-table-column
        prop="xm"
        label="姓名" />
      <el-table-column
        prop="sj"
        label="举手时间" />
      <el-table-column
        label="操作">
        <template
          #default="scope">
          <el-button
            type="primary"
            link
            @click="goAsk(scope.row.id)">提问</el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div
        class="dialog-footer">
        <el-button
          @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'

const dialogVisible = ref(false)
const formRef = ref(null)
const tableData = ref([
  {
    xh: '11223',
    xm: 'tom',
    sj: '2025-02-02 13:30:22'
  },
  {
    xh: '11223',
    xm: 'tom',
    sj: '2025-02-02 13:30:22'
  }
])
// 开启弹窗
const open = () => {
  dialogVisible.value = true
  if (formRef.value) formRef.value.resetFields()
}
// 提问
const goAsk = id => {
  dialogVisible.value = false
}
// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss">
// .el-dialog.signin_popup_dialog :not(.is-fullscreen) {
//   margin-top: 30vh !important;
// }
</style>
