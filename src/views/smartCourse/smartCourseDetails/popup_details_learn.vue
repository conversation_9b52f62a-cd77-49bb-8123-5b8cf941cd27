<template>
  <el-dialog
    v-model="dialogVisible"
    title="拓展训练详情" width="850"
    :before-close="handleClose"
    style="margin-top: 5vh !important">
    <el-container
      class="popup_show_details">
      <el-main>
        <div
          class="show_details_top">
          <span>学号：{{ form.userNo }}</span>
          <span>姓名：{{ form.realName }}</span>
          <!-- <span>分数：<i>{{ form.fs }}分</i></span> -->
        </div>
        <div
          class="show_details_mid">
          <div>学生拓展学习内容</div>
          <el-input readonly
            v-model="form.questionContent"
            :rows="15"
            type="textarea" />
          <div>拓展学习附件</div>
          <div>
            <img
                v-for="item in form.stuFiles"
                @click="downloadUrl(item.fileUrl)"
                :src="attaDownload"
                alt="下载附件" />
          </div>
          <div
              class="show_details_mid">
            <div>提交时间：{{ form.createTime }}</div>
          </div>
        </div>
      </el-main>
      <el-aside
        class="show_details_aside">
        <div
          class="details_aside_title">
          拓展内容</div>
        <div
          class="details_aside_cont">
          <div>
            拓展名称：{{ form.extensionName }}
          </div>
          <div>拓展要求：</div>
          <el-input readonly
            v-model="form.extensionContent"
            :rows="4"
            type="textarea" />
          <div>辅助材料：</div>
          <div>
            <img
              v-for="item in form.fileList"
              @click="downloadUrl(item.fileUrl)"
              :src="attaDownload"
              alt="下载附件" />
          </div>
        </div>
      </el-aside>

    </el-container>
    <template #footer>
      <div
        class="dialog-footer">
        <el-button
          @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmFrom">
          确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import attaDownload from '@/assets/images/smartCourse/smartCourseDetails/atta_download.png'
import {getRecord} from "@/api/edu/extensionRecord.js";
import {getExtension} from "@/api/edu/extension.js";
const dialogVisible = ref(false)

const form = ref({
  userNo: '',
  realName: '',
  questionContent: '',
})
// 开启弹窗
const open  = async (extensionId,recordId) => {
  const extension = await getExtension(extensionId);
  const record = await getRecord(recordId);
  form.value = extension.data;
  form.value.realName = record.data.realName;
  form.value.userNo = record.data.userNo;
  form.value.questionContent = record.data.questionContent;
  form.value.stuFiles = record.data.files;
  dialogVisible.value = true
}
// 下载附件
const downloadUrl = url => {
  window.open(url)
}
// 关闭弹窗
const confirmFrom = () => {
  if (!form.value.zyfs && form.value.zyfs != 0) {
    return ElMessage({
      type: 'info',
      message: '请输入作业分数'
    })
  }
  dialogVisible.value = false
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.popup_show_details {
  overflow-y: auto;
  max-height: 500px;
}
.show_details_top {
  margin-bottom: 20px;
  font-size: 17px;
  color: rgba(0, 0, 0, 0.85);
  span {
    margin-right: 25px;
  }
  i {
    font-style: normal;
    color: #ff4d4f;
  }
}
.show_details_mid {
  div {
    font-size: 17px;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 10px;
  }
  img {
    width: 35px;
    margin-right: 10px;
    margin-top: 10px;
    cursor: pointer;
  }
}
.show_details_aside {
  width: 250px;
  .details_aside_title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 17px;
  }
  .details_aside_cont {
    font-size: 15px;
    color: rgba(0, 0, 0, 0.65);
    img {
      width: 35px;
      margin-right: 10px;
      margin-top: 10px;
      cursor: pointer;
    }
    .details_aside_input {
      width: 100%;
      // margin-right: 10px;
    }
  }
}
</style>
