<template>
  <el-dialog
    v-model="dialogVisible"
    :title="addFlag ? '创建头脑风暴' : '修改头脑风暴'"
    width="700"
    :before-close="handleClose"
    style="margin-top: 18vh !important"
  >
    <el-form
      ref="formRef"
      style="max-width: 600px"
      :model="form"
      label-width="auto"
      class="demo-ruleForm"
      :rules="rules"
    >
      <el-form-item label="标题" prop="brainstormTitle">
        <el-input
          v-model="form.brainstormTitle"
          type="text"
          autocomplete="off"
          placeholder="请输入标题"
        />
      </el-form-item>
      <el-form-item label="内容" prop="brainstormContent">
        <el-input
          v-model="form.brainstormContent"
          :rows="3"
          type="textarea"
          show-word-limit="true"
          maxlength="300"
          placeholder="请输入内容"
        />
      </el-form-item>
      <el-form-item label="上传文件" prop="fileListVive">
        <el-upload
          v-model:file-list="form.fileListVive"
          :http-request="upload"
          class="upload-demo"
          :action="uploadUrl"
          :before-upload="beforeAvatarUpload"
          :on-success="handleSuccess"
          :on-remove="handleRemove"
        >
          <el-button type="primary">上传文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              请上传 jpg, jpeg, png, pdf文件, 大小在60M以内
            </div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          v-model="form.brainstormRemark"
          :rows="2"
          type="textarea"
          show-word-limit="true"
          maxlength="50"
          placeholder="请输入备注"
        />
      </el-form-item>
      <el-form-item label=" ">
        <el-checkbox v-model="form.scxz" label="我已阅读并同意" size="large" />
        <el-button link type="primary" size="small" @click="goUploadKnow"
          >上传须知</el-button
        >
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="confirmFrom(1)">保存</el-button>
        <el-button type="primary" @click="confirmFrom(2)">开始</el-button>
      </div>
    </template>
  </el-dialog>
  <PopupBrainKnow ref="popupBrainKnow"></PopupBrainKnow>
</template>

<script setup>
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import PopupBrainKnow from "./popup_brain_know.vue";
import { OssService } from "@/utils/aliOss.js";
import dayjs from "dayjs";
import { addActivityList, updateActivityList } from "@/api/edu/class";
import { getBrainstorm } from "@/api/edu/brainstorm";

const props = defineProps({ form: { type: Object, default: {} } });
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + "/file/upload"); // 上传的图片服务器地址
const dialogVisible = ref(false);
const formRef = ref(null);
const popupBrainKnow = ref(null);
const addFlag = ref(false);
const form = ref({
  brainstormTitle: dayjs().format("YYYY-MM-DD HH:mm:ss") + "的头脑风暴活动",
  brainstormContent: "",
  fileListVive: [],
  brainstormRemark: "",
  scxz: false,
});
const rules = ref({
  brainstormTitle: [
    { required: true, message: "标题不能为空", trigger: "change" },
  ],
  brainstormContent: [
    { required: true, message: "内容不能为空", trigger: "change" },
  ],
  fileListVive: [{ required: true, message: "请上传文件", trigger: "change" }],
});
const syncFile = async (file) => {
  try {
    // 工具类引用
    const res = await OssService(file.file);
    return res;
  } catch (e) {
    throw e;
  }
};

function upload(file) {
  return syncFile(file);
}
// 开启弹窗
const open = (item) => {
  dialogVisible.value = true;
  if (item != null) {
    addFlag.value = false;
    getBrainstorm(item.activityId).then((res) => {
      form.value = res.data;
      form.value.fileListVive = res.data.fileList;
      form.value.fileListVive.forEach((item) => {
        item.url = item.fileUrl;
        item.name = item.fileName;
      });
    });
  } else {
    addFlag.value = true;
    form.value = {
      brainstormTitle: dayjs().format("YYYY-MM-DD HH:mm:ss") + "的头脑风暴活动",
      brainstormContent: "",
      fileListVive: [],
      brainstormRemark: "",
      scxz: false,
    };
    if (formRef.value) formRef.value.resetFields();
  }
};
// 查看上传须知
const goUploadKnow = () => {
  popupBrainKnow.value.open();
};
// 上传
const beforeAvatarUpload = (rawFile) => {
  if (
    rawFile.type !== "image/jpeg" &&
    rawFile.type !== "image/jpg" &&
    rawFile.type !== "image/png" &&
    rawFile.type !== "application/pdf"
  ) {
    ElMessage.error("上传失败！请上传 jpg, jpeg, png, pdf格式文件！");
    return false;
  } else if (rawFile.size / 1024 / 1024 > 60) {
    ElMessage.error("上传失败！文件大小不能超过 60MB!");
    return false;
  }
  return true;
};
// 确定
const confirmFrom = (number) => {
  formRef.value.validate((valid) => {
    if (valid) {
      if (!form.value.scxz) {
        ElMessage({
          type: "info",
          message: "请阅读并同意上传须知",
        });
        return;
      }
      if (addFlag.value) {
        form.value.courseId = props.form.courseId;
        form.value.classId = props.form.classId;
        form.value.lessonId = props.form.lessonId;
        if (number === 2) {
          form.value.brainstormStartTime = dayjs().format(
            "YYYY-MM-DD HH:mm:ss"
          );
          form.value.status = 2;
        } else {
          form.value.status = 1;
        }
        const fileList = [];
        form.value.fileListVive.forEach((item) => {
          fileList.push({
            fileName: item.name,
            fileUrl: item.response.url,
            fileType: item.raw.type,
            fileSize: item.size,
          });
        });
        form.value.fileList = fileList;
        const parm = {
          type: 3,
          lessonId: form.value.lessonId,
          classId: form.value.classId,
          moocSmartCourseBrainstorm: form.value,
        };
        console.log("form.value.fileListVive", form.value.fileListVive);
        addActivityList(parm).then((res) => {
          if (res.code == 200) {
            ElMessageBox({
              title: "操作提示",
              message: "添加活动成功",
              type: "success",
            });
          }
        });
      } else {
        if (number === 2) {
          form.value.brainstormStartTime = dayjs().format(
            "YYYY-MM-DD HH:mm:ss"
          );
          form.value.status = 2;
        } else {
          form.value.status = 1;
        }
        const fileList = [];
        form.value.fileListVive.forEach((item) => {
          fileList.push({
            fileName: item?.name ? item.name : item.fileName,
            fileUrl: item?.response?.url ? item.response.fileUrl:item.fileUrl,
            fileType: item?.raw?.type ? item.raw.type : item.fileType,
            fileSize: item?.size ? item.size : item.fileSize,
          });
        });
        form.value.fileList = fileList;
        const parm = {
          type: 3,
          lessonId: form.value.lessonId,
          classId: form.value.classId,
          moocSmartCourseBrainstorm: form.value,
        };
        console.log("form.value.fileListVive", form.value.fileListVive);
        updateActivityList(parm).then((res) => {
          if (res.code == 200) {
            ElMessageBox({
              title: "操作提示",
              message: "修改活动成功",
              type: "success",
            });
          }
        });
      }
      handleClose();
    } else {
      console.log("error submit!");
    }
  });
};
const handleClose = () => {
  form.value = {
    brainstormTitle: "",
    brainstormContent: "",
    fileListVive: [],
    brainstormRemark: "",
    scxz: false,
  };
  dialogVisible.value = false;
};
defineExpose({
  open,
});
</script>
