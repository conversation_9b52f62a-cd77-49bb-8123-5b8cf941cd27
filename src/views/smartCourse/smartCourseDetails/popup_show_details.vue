<template>
  <el-dialog v-model="dialogVisible" title="作业详情" width="850" :before-close="handleClose" style="margin-top: 5vh !important">
    <el-container class="popup_show_details">
      <el-main>
        <div class="show_details_top">
          <span>学号：{{ form.userNo }}</span>
          <span>姓名：{{ form.realName }}</span>
          <span
            >分数：<i>{{ form.score }}分</i></span
          >
        </div>
        <div class="show_details_mid">
          <div>学生写作内容</div>
          <el-input readonly v-model="form.answerContent" :rows="15" type="textarea" />
          <div>学生作业附件</div>
          <div v-for="value in form.fileList">
            <img @click="downloadUrl(value.fileUrl)" :src="attaDownload" alt="下载附件" >{{value.fileName}}</img>
          </div>
        </div>
      </el-main>
      <el-aside class="show_details_aside">
        <div class="details_aside_title">作业内容</div>
        <div class="details_aside_cont">
          <div>作业名称：{{ homeworkForm.name }}</div>
          <div>
            作业类型：
            <span >附件作业</span>
          </div>
          <div>
            评分方式：
            <span v-if="homeworkForm.contentType == '1'">组内评分</span>
            <span v-if="homeworkForm.contentType == '2'">组间评分</span>
            <span v-if="homeworkForm.contentType == '3'">老师评分</span>
          </div>
          <div>作业分值：{{ homeworkForm.score }}</div>
          <div>提交时间：{{ homeworkForm.submitEndTime }}</div>
          <div>作业要求：</div>
          <el-input readonly v-model="homeworkForm.assignmentRequirement" :rows="4" type="textarea" />
          <div v-for="value in homeworkForm.attachedMaterialFileList">
            <img @click="downloadUrl(value.fileUrl)" :src="attaDownload" alt="下载附件" >{{value.fileName}}</img>
          </div>
          <div>参考答案：</div>
          <el-input readonly v-model="homeworkForm.referenceAnswerContent" :rows="4" type="textarea" />
          <div v-for="value in homeworkForm.referenceAnswerMaterialFileList">
            <img @click="downloadUrl(value.fileUrl)" :src="attaDownload" alt="下载附件" >{{value.fileName}}</img>
          </div>
          <div>作业分数：</div>
          <span >{{homeworkForm.score}}</span>
          <!-- <el-button type="primary" size="small">确定</el-button> -->
        </div>
      </el-aside>
    </el-container>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import attaDownload from '@/assets/images/smartCourse/smartCourseDetails/atta_download.png'
import { getTest } from '@/api/edu/test'
const dialogVisible = ref(false)

const form = ref({
})
const homeworkForm = ref({
})
// 开启弹窗
const open = (row) => {
  dialogVisible.value = true
  form.value = row
  getTest(form.value.assignmentId).then(res => {
    if (res.code == 200) {
      homeworkForm.value = res.data
    }
  })
}
// 下载附件
const downloadUrl = fileUrl => {
    window.open(fileUrl)
}
// 关闭弹窗
const confirmFrom = () => {
  if (!form.value.zyfs && form.value.zyfs != 0) {
    return ElMessage({
      type: 'info',
      message: '请输入作业分数'
    })
  }
  dialogVisible.value = false
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.popup_show_details {
  overflow-y: auto;
  max-height: 500px;
}
.show_details_top {
  margin-bottom: 20px;
  font-size: 17px;
  color: rgba(0, 0, 0, 0.85);
  span {
    margin-right: 25px;
  }
  i {
    font-style: normal;
    color: #ff4d4f;
  }
}
.show_details_mid {
  div {
    font-size: 17px;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 10px;
  }
  img {
    width: 35px;
    cursor: pointer;
  }
}
.show_details_aside {
  width: 250px;
  .details_aside_title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 17px;
  }
  .details_aside_cont {
    font-size: 15px;
    color: rgba(0, 0, 0, 0.65);
    img {
      width: 35px;
      margin-right: 10px;
      margin-top: 10px;
      cursor: pointer;
    }
    .details_aside_input {
      width: 100%;
      // margin-right: 10px;
    }
  }
}
</style>
