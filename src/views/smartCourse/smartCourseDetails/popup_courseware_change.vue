<!--
 * @Author: qin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-30 17:10:58
 * @LastEditors: qinqinglin <EMAIL>
 * @LastEditTime: 2025-06-30 19:31:55
 * @FilePath: \dutp-stu-tea-vue\src\views\smartCourse\smartCourseDetails\popup_courseware_change.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog v-model="dialogVisible" title="新增课件" width="1000" :before-close="handleClose"
             style="margin-top: 5vh !important">
    <el-form ref="formRef" :model="form" label-width="auto" class="demo-ruleForm" :rules="rules">
      <el-form-item label="发送时间" prop="bt">
        <el-date-picker v-model="value1" type="date" placeholder="请选择日期" :size="size"/>
      </el-form-item>
    </el-form>
    <el-container>
      <el-aside width="350px" style="background: #fff; padding: 0 24px">
        <div class="course_tree">
          <div>
            <div class="course_tree_title">
              <el-icon class="tree_title_icon">
                <Tickets/>
              </el-icon>
              <p>目录</p>
            </div>
            <el-divider style="margin: 0"/>
            <!-- 树状图 -->
            <div class="course_tree_tree">
              <el-tree style="max-width: 600px" :data="data" :props="defaultProps" show-checkbox/>
            </div>
          </div>
        </div>
      </el-aside>
      <el-main class="course_main">
        <el-collapse expand-icon-position="'left'">
          <el-collapse-item v-for="(item,index) in dataContent" :key="index" :title="`第${indexCN[index]}章 ${item.zm}`"
                            :name="index + 1">
            <div v-for="(title,index) in item.xj" :key="index">
              <div class="course_main_title">
                第{{ indexCN[index] }}节 {{ title.bt }}
              </div>
              <div v-for="(content,index) in title.content" class="course_main_content">
                <div>{{ sumType(content.type) }}</div>
                <p>{{ content.nr }}</p>
                <span>
                <el-icon><CircleClose/></el-icon>
              </span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-main>
    </el-container>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 发送</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {computed, ref} from 'vue'
import {ElMessageBox, ElCollapse, ElCollapseItem} from 'element-plus'

const position = ref('left')
const dialogVisible = ref(false)
const formRef = ref(null)
const form = ref({
  bt: ''
})
const rules = ref({
  bt: [{required: true, message: '请选择日期', trigger: 'change'}]
})
const data = ref(
    [
      {
        id: 1,
        label: '房东',
        children: [
          {
            id: 3,
            label: '学姐',
            children: [
              {
                id: 4,
                label: '迷乱舞会'
              },
              {
                id: 5,
                label: '图书馆'
              }
            ]
          },
          {
            id: 2,
            label: '初识钰慧',
            children: [
              {
                id: 6,
                label: '逛街'
              },
              {
                id: 7,
                label: '寒假开始'
              }
            ]
          }
        ]
      }
    ]
)
const dataContent = ref(
    [
      {
        zm: '房东大大',
        xj: [
          {
            bt: '初识阿宾',
            content: [
              {
                type: '1',
                nr: '副文本标题内容',
              }, {
                type: '2',
                nr: '副文本标题内容',
              }, {
                type: '3',
                nr: '副文本标题内容',
              }, {
                type: '4',
                nr: '副文本标题内容',
              }, {
                type: '5',
                nr: '副文本标题内容',
              }, {
                type: '6',
                nr: '副文本标题内容',
              }, {
                type: '7',
                nr: '副文本标题内容',
              }
            ]
          }
        ]
      }, {
      zm: '房东大大',
      xj: [
        {
          bt: '初识阿宾',
          content: [
            {
              type: '1',
              nr: '副文本标题内容',
            }, {
              type: '2',
              nr: '副文本标题内容',
            }
          ]
        }
      ]
    }
    ]
)
const indexCN = ref(
    ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
)
const sumType = computed(() => {
  return (type) => {
    switch (true) {
      case type == 1:
        return '图文'
      case type == 2:
        return '资源'
      case type == 3:
        return '虚拟仿真'
      case type == 4:
        return '链接'
      case type == 5:
        return '习题'
      case type == 6:
        return '测试'
      case type == 7:
        return '压缩包'
    }
  }
})
// 开启弹窗
const open = () => {
  dialogVisible.value = true
  if (formRef.value) formRef.value.resetFields()
}

// 关闭弹窗
const confirmFrom = () => {
  formRef.value.validate(valid => {
    if (valid) {
      console.log('submit!')
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.course_tree {
  height: 400px;
  border-radius: 8px;
  border: 1px solid #dcdcdc;

  .course_tree_title {
    font-size: 17px;
    // display: flex;
    display: inline-flex;
    color: #0966b4;
    margin-left: 40px;
    border-bottom: 3px solid #0966b4;

    .tree_title_icon {
      margin-top: 25px;
    }
  }

  .course_tree_tree {
    padding: 17px;
  }
}

.course_main {
  height: 400px;
  border-radius: 10px 8px 0px 0px;
  border: 1px solid #e6ebf5;

  :deep(.el-collapse-item .el-collapse-item__header) {
    background: #F7F8F9 !important;
    padding-left: 8px !important;
  }

  :deep(.el-collapse-item .el-collapse-item__wrap) {
    padding-left: 8px !important;
  }

  .course_main_title {
    color: #333333;
    font-weight: bold;
    margin-top: 10px;
  }

  .course_main_content {
    display: flex;
    align-items: center;
    margin-top: 8px;

    div {
      width: 111px;
      height: 31px;
      background: linear-gradient(174deg, #F1ECFF 0%, #ECF3FD 100%);
      border-radius: 8px;
      text-align: center;
      line-height: 31px;
      color: #3D2A67;
    }

    p {
      margin: 0;
      color: #333333;
      margin-left: 5px;
    }

    span {
      display: block;
      margin-top: 5px;
      margin-left: 5px;
    }
  }
}
</style>
