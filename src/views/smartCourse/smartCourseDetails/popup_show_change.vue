<template>
  <el-dialog v-model="dialogVisible" title="选择作业" width="680" :before-close="handleClose" style="margin-top: 10vh !important">
    <div class="show_change">
      <div class="popup_show_change" v-for="(item, index) in tableData" :key="index">
        <div class="change_content">
          <el-checkbox class="change_content_left" v-model="item.checked" size="large" />
          <div class="change_content_right">
            <h3>{{ item.name }}</h3>
            <div>
              <span>参与人数：{{ item.numberOfParticipants }}</span>
              <span v-if="item.contentType == '1'">作业类型：题库作业</span>
              <span v-if="item.contentType == '2'">作业类型：附件作业</span>
              <span v-if="item.contentType == '3'">作业类型：登分作业</span>
              <span>创建时间：{{ item.createTime }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmFrom"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { getHomeworkOrTestActivity } from "@/api/edu/test";
const emits = defineEmits(['changeVal'])
const dialogVisible = ref(false)
const tableData = ref([
  {
    assignmentId: '',
    name: '',
    numberOfParticipants: '',
    contentType: '',
    createTime: ''
  }
])

// 开启弹窗
const open = () => {
  getHomeworkOrTestActivity({assignmentType:'0', contentType: '1', status: 3}).then(res => { 
    tableData.value = res.data
  })
  dialogVisible.value = true
  for (let i = 0; i < tableData.value.length; i++) {
    tableData.value[i].checked = false
  }
}
// 关闭弹窗
const confirmFrom = () => {
  const val = tableData.value.filter(item => item.checked == true)
  if (!val || val.length == 0) {
    return ElMessage({
      type: 'info',
      message: '请选择作业'
    })
  }
  emits('changeVal', val)
  dialogVisible.value = false
}
const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.show_change {
  max-height: 400px;
  overflow-y: auto;

  .popup_show_change {
    margin: 20px;

    .change_content {
      display: flex;
      width: 100%;
      height: 80px;
      background-image: url('../../../assets/images/smartCourse/smartCourseDetails/green_bg.png');
      background-size: 800px auto;
      background-repeat: no-repeat;

      .change_content_left {
        margin: 20px 20px 20px 25px;
      }

      .change_content_right {
        h3 {
          font-size: 17px;
          color: #333333;
          margin-top: 17px;
          margin-bottom: 7px;
        }

        div {
          font-size: 14px;
          color: #585e76;

          span {
            margin-right: 40px;
          }
        }
      }
    }
  }
}
</style>
