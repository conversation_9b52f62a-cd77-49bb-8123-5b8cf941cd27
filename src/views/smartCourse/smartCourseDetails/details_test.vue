<template>
  <div class="box_4 flex-col">
    <div class="go_back">
      <el-button
        class="go_back_btn"
        @click="goBack"
        type="primary"
        plain>返回</el-button>
    </div>
    <div
      class="details_signin_content">
      <h3>测试详情</h3>
      <div
        class="signin_content_tab">
        <div
          class="signin_content_detail">
          <span>{{ res.sj }}测试</span>
          <span>创建时间：{{ res.cjsj }}</span>
        </div>
        <div
          class="signin_content_tab_search">
          <el-input
            v-model="input1"
            style="width: 240px"
            size="large"
            placeholder="请输入搜索关键字"
            :suffix-icon="Search" />
        </div>
      </div>
      <div>
        <el-table
          :data="res.tableData"
          style="width: 100%">
          <el-table-column
            prop="xh"
            label="学号" />
          <el-table-column
            prop="xm"
            label="姓名" />
          <el-table-column
            prop="ssbj"
            label="所属班级" />
          <el-table-column
            prop="ssxz"
            label="所属小组" />
          <el-table-column
            prop="zyfs"
            label="作业分数">
            <template
              #default="scope">
              {{ scope.row.zyfs}}
              /
              {{ scope.row.zf }}
            </template>
          </el-table-column>
          <el-table-column
            prop="zt"
            label="状态">
            <template
              #default="scope">
              <span
                v-if="scope.row.zt == '1'">已提交</span>
              <span
                v-if="scope.row.zt == '2'">未提交</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作">
            <template
              #default="scope">
              <el-button
                type="primary"
                link
                @click="goDetails(scope.row.id)">查看测试</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
  <PopupDetailsTest
    ref="popupDetailsTest" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { ArrowDown, Search, ArrowUp } from '@element-plus/icons-vue'
import { badgeProps } from 'element-plus'
// 拓展学习弹窗
import PopupDetailsTest from './popup_details_test.vue'

const { proxy } = getCurrentInstance()
const emits = defineEmits(['listActivityId'])
const res = ref({
  sj: '2025-02-50',
  qdlx: '1',
  cjsj: '2025-06-05 16:11',
  jssj: '2025-06-05 16:11',
  tableData: [
    {
      xh: '123123',
      xm: '李神通',
      ssbj: '网络一班',
      ssxz: '小组2',
      dfr: '李如龙',
      zt: '1',
      qdsj: '2025-01-02 16:11',
      zyfs: '76',
      zf: '100'
    },
    {
      xh: '123123',
      xm: '李神通',
      ssbj: '网络一班',
      ssxz: '小组2',
      dfr: '李如龙',
      zt: '2',
      qdsj: '2025-01-02 16:11',
      zyfs: '76',
      zf: '100'
    }
  ]
})
const popupDetailsTest = ref()
// 查看详情
const goDetails = id => {
  popupDetailsTest.value.open(id)
}
// 跳转详情页
const goBack = () => {
  emits('listActivityId')
}
</script>

<style scoped lang="scss">
.go_back {
  text-align: right;

  .go_back_btn {
    margin: 20px 20px 0 0;
  }
}

.details_signin_content {
  padding: 0 20px 20px 20px;
  h3 {
    margin-bottom: 10px;
  }
  .signin_content_tab {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .signin_content_detail {
      font-size: 15px;
      color: #585e76;
      margin-top: 30px;
      span {
        margin-right: 30px;
      }

      i {
        font-style: normal;
      }
    }
    .signin_content_tab_title {
      width: 105px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #7e88a5;
      margin: 25px 10px 20px 0;
      color: #333333;
      cursor: pointer;
    }

    .signin_content_tab_search {
      margin-top: 17px;
    }

    .signin_content_tab_title_active {
      background: #555b74;
      color: #ffffff;
    }
  }
}
</style>
