<template>
  <el-dialog v-model="dialogVisible" title="新建作业" width="700" :before-close="handleClose" style="margin-top: 13vh !important">
    <div class="popup_homework_content">
      <el-form ref="formRef" :model="form" label-width="auto" class="demo-ruleForm" :rules="rules">
        <el-form-item label="作业名称" prop="zymc">
          <el-input show-word-limit maxlength="50" v-model="form.zymc" type="text" autocomplete="off" placeholder="请输入作业名称" />
        </el-form-item>
        <el-form-item label="作业类型" prop="zylx">
          <el-radio-group v-model="form.zylx">
            <el-radio value="1">题库作业</el-radio>
            <el-radio value="2">附件作业</el-radio>
            <el-radio value="3">登分作业</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.zylx != '1'" label="作答单位" prop="zddw">
          <el-radio-group v-model="form.zddw">
            <el-radio value="1">个人作业</el-radio>
            <el-radio value="2">小组作业</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.zylx != '1'" label="设置分值" prop="szfz">
          <el-input-number v-model="form.szfz" />
        </el-form-item>
        <el-form-item v-if="form.zylx == '2'" label="评分方式" prop="pffs">
          <el-radio-group v-model="form.pffs">
            <el-radio value="1">教师评分</el-radio>
            <el-radio value="2">组间评分</el-radio>
            <el-radio value="3">组内打分</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.zylx == '2'" label="批改开始时间" prop="pgkssj">
          <el-date-picker v-model="form.pgkssj" type="datetime" placeholder="请选择批改开始时间" />
        </el-form-item>
        <el-form-item v-if="form.zylx == '2'" label="批改截止时间" prop="pgjzsj">
          <el-date-picker v-model="form.pgjzsj" type="datetime" placeholder="请选择批改截止时间" />
        </el-form-item>
        <el-form-item v-if="form.zylx != '3'" label="作业开始时间" prop="zykssj">
          <el-date-picker v-model="form.zykssj" type="datetime" placeholder="请选择作业开始时间" />
        </el-form-item>
        <el-form-item label="作业截至时间" prop="zyjzsj">
          <el-date-picker v-model="form.zyjzsj" type="datetime" placeholder="请选择作业截至时间" />
        </el-form-item>
        <el-form-item label="选择试卷" v-if="form.zylx == '1'">
          <el-button type="primary" @click="openChange">添加试卷</el-button>
        </el-form-item>
        <div v-if="form.zylx == '1'" class="popup_show_tag">
          <el-tag
            class="popup_show_tag_item"
            v-for="(tag, index) in changeValList"
            :key="tag.id"
            closable
            :disable-transitions="false"
            @close="handleCloseTag(index)"
          >
            {{ tag.zymc }}
          </el-tag>
        </div>
        <el-form-item v-if="form.zylx == '3'" label="发布时间" prop="fbsj">
          <el-date-picker v-model="form.fbsj" type="datetime" placeholder="请选择发布时间" />
        </el-form-item>
        <el-form-item label="作业要求">
          <el-input v-model="form.zyyq" :rows="3" type="textarea" show-word-limit="true" maxlength="500" placeholder="请输入作业要求" />
        </el-form-item>
        <el-form-item label="作业材料" v-if="form.zylx == '2'">
          <el-upload
            v-model:file-list="form.fileList"
            class="upload-demo"
            :action="uploadUrl"
            :headers="headers"
            :before-upload="beforeAvatarUpload"
            :on-success="handleSuccess"
            :on-remove="handleRemove"
          >
            <el-button type="primary">上传附件</el-button>
            <template #tip>
              <div class="el-upload__tip">低于1M的文件不支持大文件上传</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item v-if="form.zylx == '2'" label="参考答案">
          <el-input v-model="form.ckda" :rows="3" type="textarea" show-word-limit="true" maxlength="500" placeholder="请输入参考答案" />
        </el-form-item>
        <el-form-item v-if="form.zylx == '2'" label="答案附件">
          <el-upload
            v-model:file-list="form.fileList2"
            class="upload-demo"
            :action="uploadUrl"
            :headers="headers"
            :before-upload="beforeAvatarUpload"
            :on-success="handleSuccess"
            :on-remove="handleRemove"
          >
            <el-button type="primary">上传附件</el-button>
            <template #tip>
              <div class="el-upload__tip">低于1M的文件不支持大文件上传</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmFrom">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <PopupTestChange @changeVal="changeVal" ref="popupTestChange" />
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PopupTestChange from './popup_test_change.vue'
import { getToken } from '@/utils/auth'
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/file/upload') // 上传的图片服务器地址
const headers = ref({
  Authorization: 'Bearer ' + getToken()
})

const dialogVisible = ref(false)
const formRef = ref(null)
const changeValList = ref([])
const changeValIds = ref([])
const tableRef = ref(null)
const form = ref({
  zymc: '',
  zylx: '1',
  zddw: '1',
  szfz: '',
  pffs: '1',
  pgkssj: '',
  pgjzsj: '',
  zykssj: '',
  zyjzsj: '',
  fbsj: '',
  zyyq: '',
  ckda: '',
  fileList: [],
  fileList2: []
})
const dynamicTags = ref(['Tag 1', 'Tag 2', 'Tag 3'])

const rules = ref({
  zymc: [{ required: true, message: '作业名称不能为空', trigger: 'change' }],
  zylx: [{ required: true, message: '作业类型不能为空', trigger: 'change' }],
  zddw: [{ required: true, message: '作答单位不能为空', trigger: 'change' }],
  szfz: [{ required: true, message: '设置分值不能为空', trigger: 'change' }],
  pffs: [{ required: true, message: '评分方式不能为空', trigger: 'change' }],
  pgkssj: [{ required: true, message: '批改开始时间不能为空', trigger: 'change' }],
  pgjzsj: [{ required: true, message: '批改截止时间不能为空', trigger: 'change' }],
  zykssj: [{ required: true, message: '作业开始时间不能为空', trigger: 'change' }],
  zyjzsj: [{ required: true, message: '作业截至时间不能为空', trigger: 'change' }],
  fbsj: [{ required: true, message: '发布时间不能为空', trigger: 'change' }]
})
// 开启弹窗
const open = () => {
  changeValList.value = []
  changeValIds.value = []
  form.value.fileList = []
  form.value.fileList2 = []
  dialogVisible.value = true
  // 清空校验
  if (formRef.value) formRef.value.resetFields()
  // 清空表格选中
  if (tableRef.value) tableRef.value.clearSelection()
}
// 选择作业
const popupTestChange = ref(null)
const openChange = () => {
  popupTestChange.value.open()
}
// 作业返回id
const changeVal = val => {
  changeValList.value = changeValList.value.concat(val)
  changeValIds.value = changeValList.value.map(item => item.id)
}
// 上传
const beforeAvatarUpload = rawFile => {
  // if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/jpg' && rawFile.type !== 'image/png' && rawFile.type !== 'application/pdf') {
  //   ElMessage.error('上传失败！请上传 jpg, jpeg, png, pdf格式文件！')
  //   return false
  // } else if (rawFile.size / 1024 / 1024 > 60) {
  //   ElMessage.error('上传失败！文件大小不能超过 60MB!')
  //   return false
  // }
  return true
}
// 确定
const confirmFrom = () => {
  formRef.value.validate(valid => {
    let list = []
    let list2 = []
    if (valid) {
      if (changeValIds.value.length == 0 && form.value.zylx == '1') {
        return ElMessage({
          type: 'info',
          message: '请选择试卷'
        })
      }
      if (form.value.zylx == '2') {
        // 上传文件数组
        list = form.value.fileList.map(item => item.response.data)
        list2 = form.value.fileList2.map(item => item.response.data)
      }
      // console内为上传数组
      console.log(list)
      console.log(list2)
      console.log('submit!')
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}
const handleCloseTag = i => {
  changeValList.value.splice(i, 1)
  changeValIds.value.splice(i, 1)
}

const handleClose = () => {
  dialogVisible.value = false
}
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.popup_homework_content {
  max-height: 400px;
  overflow-y: auto;
}

.popup_show_tag {
  margin: 0 20px 15px 75px;
  .popup_show_tag_item {
    margin: 3px;
  }
}
</style>
