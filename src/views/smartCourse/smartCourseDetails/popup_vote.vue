<template>
  <el-dialog
    v-model="dialogVisible"
    :title="addFlag ? '创建投票问卷' : '修改投票问卷'"
    width="800"
    :before-close="handleClose"
    style="margin-top: 10px !important"
  >
    <!-- 标题表单 -->
    <el-form
      ref="formRefTitle"
      :model="formTitle"
      label-width="auto"
      class="demo-ruleForm"
      :rules="rules"
    >
      <el-form-item label="问卷标题" prop="surveyName">
        <el-input
          show-word-limit
          maxlength="50"
          v-model="formTitle.surveyName"
          type="text"
          autocomplete="off"
          placeholder="请输入问卷标题"
        />
      </el-form-item>
      <el-form-item label="问卷主题">
        <el-input
          show-word-limit
          maxlength="50"
          v-model="formTitle.surveyTopic"
          type="text"
          autocomplete="off"
          placeholder="请输入问卷主题"
        />
      </el-form-item>
    </el-form>
    <!-- 按钮 -->
    <div class="vote_btn">
      <el-button size="small" type="primary" @click="addQuestion"
        >添加题目</el-button
      >
    </div>
    <!-- 内容表单 -->
    <div class="vote_content">
      <div
        v-for="(question, qIndex) in formData"
        :key="qIndex"
        class="question-item"
      >
        <h4>第{{ qIndex + 1 }}题</h4>
        <el-button
          v-if="formData.length > 1"
          type="danger"
          link
          class="delete-question-btn"
          @click="removeQuestion(qIndex)"
        >
          <el-icon><Close /></el-icon>
        </el-button>
        <el-form
          :ref="(el) => (formRefs[qIndex] = el)"
          :model="question"
          label-width="auto"
          class="demo-ruleForm"
          :rules="questionRules"
        >
          <el-form-item label="标题" prop="questionTitle">
            <el-input
              show-word-limit
              maxlength="50"
              v-model="question.questionTitle"
              type="text"
              autocomplete="off"
              placeholder="请输入标题"
            />
          </el-form-item>
          <el-form-item label="题干" prop="questionContent">
            <el-input
              v-model="question.questionContent"
              :rows="3"
              type="textarea"
              show-word-limit
              maxlength="500"
              placeholder="请输入题干"
            />
          </el-form-item>
          <el-form-item label="题型" prop="optionType">
            <el-radio-group v-model="question.optionType">
              <el-radio value="1">单选</el-radio>
              <el-radio value="2">多选</el-radio>
            </el-radio-group>
          </el-form-item>

          <div class="optionList-container">
            <el-form-item
              v-for="(option, oIndex) in question.optionList"
              :key="oIndex"
              :label="getOptionLabel(oIndex)"
              :prop="`optionList.${oIndex}.optionContent`"
              :rules="[
                {
                  required: true,
                  message: `选项${getOptionLabel(oIndex)}内容不能为空`,
                  trigger: 'change',
                },
              ]"
              class="vote_option"
            >
              <el-input
                show-word-limit
                maxlength="50"
                v-model="option.optionContent"
                type="text"
                autocomplete="off"
                :placeholder="`请输入${getOptionLabel(oIndex)}项内容`"
              />
              <el-button
                link
                :icon="Close"
                @click="removeOption(qIndex, oIndex)"
                :disabled="question.optionList.length <= 2"
              />
            </el-form-item>
          </div>

          <el-button
            type="primary"
            link
            class="vote_option_add"
            @click="addOption(qIndex)"
          >
            +添加选项
          </el-button>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <!-- <el-button @click="handleClose">取消</el-button> -->
        <el-button @click="confirmFrom(1)">保存</el-button>
        <el-button type="primary" @click="confirmFrom(2)">开始</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Close } from "@element-plus/icons-vue";
import { getCourseSurveyInfo } from "@/api/edu/survey";
import dayjs from "dayjs";
import { addActivityList, updateActivityList } from "@/api/edu/class";
const props = defineProps({ form: { type: Object, default: {} } });
const addFlag = ref(false);
// 生成A-Z标签的方法
const getOptionLabel = (index) => {
  return String.fromCharCode(65 + index);
};

const dialogVisible = ref(false);
const formRefTitle = ref(null);
const formRefs = ref([]);

// 问卷标题表单
const formTitle = reactive({
  surveyName: "",
  surveyTopic: "",
});

// 题目表单规则
const questionRules = reactive({
  questionTitle: [
    { required: true, message: "标题不能为空", trigger: "change" },
  ],
  questionContent: [
    { required: true, message: "题干不能为空", trigger: "change" },
  ],
  optionType: [{ required: true, message: "请选择题型", trigger: "change" }],
});

// 整体表单规则
const rules = reactive({
  surveyName: [
    { required: true, message: "问卷标题不能为空", trigger: "change" },
  ],
});

// 题目数据
const formData = ref([createNewQuestion()]);

// 创建新题目
function createNewQuestion() {
  return {
    questionTitle: "",
    questionContent: "",
    optionType: "1", // 默认单选
    optionList: [
      { optionContent: "" },
      { optionContent: "" },
      { optionContent: "" },
      { optionContent: "" },
    ],
  };
}

// 添加题目
const addQuestion = () => {
  formData.value.push(createNewQuestion());
  nextTick(() => {
    formRefs.value = [];
  });
};

// 删除题目
const removeQuestion = (index) => {
  if (formData.value.length <= 1) {
    ElMessage.warning("至少保留一道题目");
    return;
  }
  formData.value.splice(index, 1);
};

// 添加选项
const addOption = (qIndex) => {
  if (formData.value[qIndex].optionList.length >= 26) {
    ElMessage.warning("最多只能添加26个选项");
    return;
  }
  formData.value[qIndex].optionList.push({ optionContent: "" });
};

// 删除选项
const removeOption = (qIndex, oIndex) => {
  if (formData.value[qIndex].optionList.length <= 2) {
    ElMessage.warning("至少保留两个选项");
    return;
  }
  formData.value[qIndex].optionList.splice(oIndex, 1);
};

// 开启弹窗
const open = (item) => {
  dialogVisible.value = true;
  if (item != null) {
    addFlag.value = false;
    getCourseSurveyInfo(item.activityId).then((res) => {
      // 在重置表单后再设置默认值
      nextTick(() => {
        formData.value = res.data.questionList;
        formTitle.surveyName = res.data.surveyName;
        formTitle.surveyTopic = res.data.surveyTopic;
        formTitle.surveyId = res.data.surveyId;
        formTitle.courseId = res.data.courseId;
        formTitle.classId = res.data.classId;
        formTitle.lessonId = res.data.lessonId;
        formData.value.forEach((question) => {
          question.optionType = question.optionType.toString();
        });
        console.log("formTitle", formTitle);
        console.log("formData.value", formData.value);
      });
    });
  } else {
    addFlag.value = true;
    // 每次打开都更新默认标题
    resetForm();
    if (formRefTitle.value) formRefTitle.value.resetFields();
    if (formRefs.value[0]) formRefs.value[0].resetFields();
  }
  // 在重置表单后再设置默认值
  nextTick(() => {
    formTitle.surveyName =
      dayjs().format("YYYY-MM-DD HH:mm:ss") + "的投票问卷活动";
  });
};

// 重置表单
const resetForm = () => {
  // 在重置表单后再设置默认值
  nextTick(() => {
    (formTitle.surveyName = ""), (formTitle.surveyTopic = "");
    formData.value = [createNewQuestion()];
  });
};

// 确定提交
const confirmFrom = async (type) => {
  // try {
  // 验证问卷标题
  await formRefTitle.value.validate();

  // 验证所有题目
  for (const formRef of formRefs.value) {
    if (formRef) {
      await formRef.validate();
    }
  }

  // 验证每个题目的选项
  for (const question of formData.value) {
    if (question.optionList.length < 2) {
      ElMessage.error("每道题至少需要两个选项");
      return;
    }

    for (const option of question.optionList) {
      if (!option.optionContent.trim()) {
        ElMessage.error("选项内容不能为空");
        return;
      }
    }
  }
  console.log("提交数据:", formTitle);
  // 构建提交数据
  const surveyData = {
    surveyName: formTitle.surveyName,
    surveyTopic: formTitle.surveyTopic,
    questionList: formData.value.map((q, i) => ({
      questionTitle: q.questionTitle,
      questionContent: q.questionContent,
      optionType: q.optionType,
      optionList: q.optionList.map((o, idx) => ({
        optionContent: o.optionContent,
      })),
    })),
    startTime: type === 2 ? dayjs().format("YYYY-MM-DD HH:mm:ss") : null,
    status: type === 2 ? 2 : 1,
    courseId: addFlag.value ? props.form.courseId : formTitle.courseId,
    classId: addFlag.value ? props.form.classId : formTitle.classId,
    lessonId: addFlag.value ? props.form.lessonId : formTitle.lessonId,
    surveyId: formTitle.surveyId,
  };
  const parm = {
    type: 5,
    lessonId: addFlag.value ? surveyData.lessonId : formTitle.lessonId,
    classId: addFlag.value ? surveyData.classId : formTitle.classId,
    moocSmartCourseSurvey: surveyData,
  };
  console.log("提交数据:", parm);
  if (addFlag.value) {
    addActivityList(parm).then((res) => {
      if (res.code == 200) {
        ElMessageBox({
          title: "操作提示",
          message: "添加活动成功",
          type: "success",
        });
      }
    });
  } else {
    updateActivityList(parm).then((res) => {
      if (res.code == 200) {
        ElMessageBox({
          title: "操作提示",
          message: "修改活动成功",
          type: "success",
        });
      }
    });
  }

  ElMessage.success(type === 1 ? "问卷已保存" : "问卷已开始");
  dialogVisible.value = false;
  resetForm();
};

// 关闭弹窗
const handleClose = () => {
  ElMessageBox.confirm("确定要关闭吗？未保存的内容将会丢失", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      dialogVisible.value = false;
      resetForm();
    })
    .catch(() => {});
};

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
.vote_btn {
  text-align: right;
  margin: 20px 30px 20px;
}
.vote_content {
  border: 1px solid #ccc;
  padding: 20px 30px 20px 20px;
  max-height: 400px;
  overflow-y: auto;

  .question-item {
    position: relative;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px dashed #eee;
    border-radius: 4px;

    &:hover {
      border-color: #c0c4cc;
    }

    .delete-question-btn {
      position: absolute;
      top: 10px;
      right: 10px;
    }
  }
}
.vote_option {
  position: relative;
  // margin-bottom: 10px;

  .el-button {
    position: absolute;
    right: -22px;
    top: 7px;
  }
}
.vote_option_add {
  margin-left: 20px;
  margin-top: 10px;
  margin-bottom: 15px;
}
.el-input {
  width: 100% !important;
}
.optionList-container {
  // max-height: 300px;
  // overflow-y: auto;
  padding-right: 10px;
}
</style>
