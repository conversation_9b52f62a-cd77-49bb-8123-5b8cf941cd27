<template>
  <div>
    <div
      class="box_4 flex-col">
      <div
        class="text-wrapper_40 flex-row">
        <el-button
          type="primary"
          round>批量发送</el-button>
      </div>
      <el-table :data="data"
        @selection-change="handleSelectionChange"
        style="width: 100%
        !important;
        margin-top: 20px">
        <el-table-column
          type="selection"
          width="40" />
        <el-table-column fixed
          prop="bt"
          label="课程名称">
          <template
            #default="scope">
            <el-button link
              type="primary"
              size="small">{{ scope.row.bt }}</el-button>
          </template>
        </el-table-column>
        <el-table-column fixed
          prop="type"
          label="类型">
          <template
            #default="scope">
            <span
              v-if="scope.row.type == 1">课件</span>
            <span
              v-if="scope.row.type == 2">文件</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="cjsj"
          label="创建时间" />
        <el-table-column
          prop="zt"
          label="状态">
          <template
            #default="scope">
            <span
              v-if="scope.row.zt == 1">未发送</span>
            <span
              v-if="scope.row.zt == 2">已发送</span>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作">
          <template
            #default="scope">
            <el-button link
              type="primary"
              size="small"
              v-if="scope.row.zt == 1">发送</el-button>
            <el-button link
              type="primary"
              size="small"
              v-if="scope.row.zt == 1">修改</el-button>
            <el-button link
              type="primary"
              size="small"
              @click="delActivity(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import editImg from '@/assets/images/smartCourse/smartCourseDetails/edit.png'
import eyeImg from '@/assets/images/smartCourse/smartCourseDetails/eye.png'
import delImg from '@/assets/images/smartCourse/smartCourseDetails/del.png'
import { ref, reactive, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance()
const props = defineProps(['data'])
// 勾选
const ids = ref([])
const handleSelectionChange = val => {
  // 勾选id集合
  ids.value = val.map(item => item.id)
}
// 删除活动
const delActivity = id => {
  ElMessageBox.confirm('删除后不可恢复，确定要删除吗?', '删除提醒', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      ElMessage({
        type: 'success',
        message: 'Delete completed'
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: 'Delete canceled'
      })
    })
}
</script>

<style scoped lang="scss"></style>
