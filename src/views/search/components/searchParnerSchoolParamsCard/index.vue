<template>
  <div class="search-criteria-card-con">
    <div class="search-criteria-card">
      <div class="search-criteria-row" v-if="searchParamsList[0]">
        <div class="row-params">
          <div
              class="params-item"
              :class="{ active: chItem.isChecked }"
              v-if="searchParamsList[0].children && searchParamsList[0].children.length > 0"
              v-for="(chItem, chIndex) in searchParamsList[0].children"
              :key="chIndex"
              @click="doClickItem(0, chIndex)"
          >
            <span>{{ chItem.subjectName }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="SearchCriteriaCard">
import { listDutpSubjectEducation } from '@/api/basic/subject';
import { SubjectLevelOptions } from '@/api/dic';
import useSiteConfigStore from '@/store/modules/siteConfig';
import { listBookTypeEducation } from '@/api/openApi/openApi';
import { cloneDeep } from 'lodash';
const emit = defineEmits(['paramsChanged', 'bookTypeChanged']);
const clcThreeList = ref([]);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 100,
    subjectName: null,
    sort: null,
  },
});
const bookType = ref('');
const { queryParams } = toRefs(data);
const tempResponseData = ref({}); // 页面缓存数据
const searchParamsList = ref([]);
const searchParamsListTemp = ref([]);

function initListRowShow() {
  //
  searchParamsList.value[0].children[0].isChecked = true;
  //
  const siteConfigStore = useSiteConfigStore();
  const siteConfigInfo = siteConfigStore.siteConfigInfo;
  const searchParams = searchParamsList.value;
  const visibleLevels = ref([true, true, true, true]);
  if (siteConfigInfo) {
    visibleLevels.value = [
      siteConfigInfo.firstLevelVisible === 1 ? true : false,
      siteConfigInfo.secondLevelVisible === 1 ? true : false,
      siteConfigInfo.thirdLevelVisible === 1 ? true : false,
      siteConfigInfo.fourthLevelVisible === 1 ? true : false,
    ];
  }
  for (let i = 0; i < Math.min(visibleLevels.value.length, searchParams.length); i++) {
    searchParams[i].show = Boolean(visibleLevels.value[i]);
  }
}

function doClickItem(index, chIndex) {
  if (index < 0 || chIndex < 0 || index >= searchParamsList.value.length || chIndex >= searchParamsList.value[index].children.length) {
    console.error('Invalid index or chIndex');
    return;
  }
  let chItemIsChecked = searchParamsList.value[index].children[chIndex].isChecked;
  searchParamsList.value[index].children.forEach((item) => {
    item.isChecked = false;
  });
  searchParamsList.value[index].children[chIndex].isChecked = !chItemIsChecked;
  // 初始化下一级菜单
  let currentParentId = searchParamsList.value[index].children[chIndex].parentId;
  let currentSubjectId = searchParamsList.value[index].children[chIndex].subjectId;
  searchParamsList.value[index + 1].children = [];
  let temp = searchParamsListTemp.value[index + 1].children.filter((chitem) => {
    if (currentParentId === '-1') {
      return true;
    }
    return chitem.parentId === currentSubjectId || chitem.parentId === '-1';
  });
  searchParamsList.value[index + 1].children = cloneDeep(temp);
  let currentSubject = searchParamsList.value[index].children[chIndex];
  // subjectId
  emit('paramsChanged', currentSubject.subjectId);
}
function getSubjectName(key) {
  let subjectName = '其他';
  SubjectLevelOptions.value.forEach((item) => {
    if (item.val === key) {
      subjectName = item.label;
    }
  });
  return subjectName;
}
async function initOptions() {
  await listDutpSubjectEducation(queryParams.value).then((response) => {
    if (!response.data) {
      return;
    }
    searchParamsList.value = [];
    tempResponseData.value = JSON.parse(JSON.stringify(response.data));
    const keys = Object.keys(tempResponseData.value);
    if (keys && keys.length > 0) {
      // key
      keys.forEach((key, index) => {
        searchParamsList.value.push({
          subjectName: getSubjectName(key),
          key: key,
          value: 0,
          isChecked: false,
          children: tempResponseData.value[key],
        });
      });
    }
    searchParamsListTemp.value = cloneDeep(searchParamsList.value);
    initListRowShow();
  });
}
const getListType = () => {
  const parm = {
    pageNum: 1,
    pageSize: 99999,
  };
  listBookTypeEducation(parm).then((res) => {
    clcThreeList.value = res.data;
  });
};
const changeBookType = () => {
  emit('bookTypeChanged', bookType.value);
};
// 监视searchParamsListTemp的变化
watch(
    searchParamsListTemp,
    (newVal, oldVal) => {
      // 在这里处理searchParamsListTemp的变化
    },
    { deep: true }
);

// 初始化数据

onMounted(() => {
  initOptions();
  getListType();
});
</script>
<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.search-criteria-card-con {
  @extend .base-flex-row;
  justify-content: center;
  align-items: flex-start;
  .search-criteria-card {
    min-height: 26px;
    padding-left: 35px;
    .search-criteria-row {
      width: 100%;
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: center;
      .row-label {
        min-width: 74px;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #8c8c8c;
        line-height: 46px;
        text-align: left;
        margin-right: 20px;
      }
      .row-params {
        flex: 1;
        @extend .base-flex-row;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: wrap;
        .params-item {
          min-width: 50px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          margin-right: 20px;
          padding: 10px;
          cursor: pointer;
          @extend .base-flex-row;
          justify-content: center;
          align-items: center;
        }
        .params-item:hover {
          opacity: 0.7;
        }
        .active {
          background: #0966b4;
          border-radius: 12px;
          color: #ffffff;
        }
      }
    }
  }
}
.clc-tree-sty {
  width: 293px;

  :deep(.el-select__wrapper) {
    min-height: 40px !important;
  }
}
.label {
  min-width: 74px;
  font-family:
      PingFangSC,
      PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #8c8c8c;
  line-height: 46px;
  text-align: left;
  margin-right: 20px;
}
</style>
