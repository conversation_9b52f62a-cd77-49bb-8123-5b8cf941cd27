<template>
  <div class="special-text-book-con">
    <div class="divider-line" />
    <div class="type-item" v-for="(item, index) in typeList" :key="item.areaId">
      <div class="item-title" @click="doClickItem(item.areaId)">{{ item.areaName }}</div>
      <div class="divider-line" />
    </div>
  </div>
</template>

<script setup name="SpecialSubjectBookTab">
import { bookAreaList } from '@/api/basic/bookArea';
const emit = defineEmits(['subjectClicked']);
const router = useRouter();
const { proxy } = getCurrentInstance();
const props = defineProps({
  selfAnchorChain: {
    type: Boolean,
    default: false,
  },
});
const typeList = ref([]);
const doClickItem = (typeVal) => {
  if (props.selfAnchorChain) {
    emit('scrollToAnchor', typeVal);
  } else {
    router.push({
      path: '/special-subject-books-list',
      query: {
        type: typeVal,
      },
    });
  }
};
const getInFo = () => {
  bookAreaList({}).then((res) => {
    typeList.value = res.data;
  });
};
getInFo()
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.special-text-book-con {
  width: 100%;
  @extend .base-flex-row;
  justify-content: center;
  align-items: center;
  .divider-line {
    width: 1px;
    height: 30px;
    border: 1px solid #e5e6e7;
  }
  .type-item {
    height: 60px;
    line-height: 60px;
    text-align: center;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    cursor: pointer;
    @extend .base-flex-row;
    justify-content: center;
    align-items: center;
    .item-title {
      width: 160px;
      margin-left: 30px;
      margin-right: 30px;
    }
  }
  .type-item:hover {
    color: #0966b4;
    opacity: 0.8;
  }
  .active {
    color: #0966b4;
  }
}
</style>
