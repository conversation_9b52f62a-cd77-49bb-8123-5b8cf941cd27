<template>
  <div class="comp-con">
    <!-- 01 教材列表   -->
    <div class="data-list-con">
      <div class="common-scroll-btn" @click="handleBooksScroll(-1)">
        <img src="@/assets/images/home/<USER>" alt="" />
      </div>
      <div class="books-list">
        <div class="item-card" v-for="(item, index) in relatedTextbooks" :key="index">
          <div class="list-item" @click="handleBookClick(item)">
            <img class="item-img" :src="item.cover ? item.cover : bookCoverDefault" alt="" />
            <div class="item-name">{{ item.bookName }}</div>
            <div class="item-author">{{ item.authorValue ? '主编：' + item.authorValue : '' }}</div>
            <div class="item-price">{{ item.priceSale ? '￥' + item.priceSale : '' }}</div>
          </div>
        </div>
      </div>
      <div class="common-scroll-btn" @click="handleBooksScroll(1)">
        <img src="@/assets/images/home/<USER>" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup name="RecommendedTextbooks">
import { ref, onMounted } from 'vue';
import { searchBook } from '@/api/elasticsearch/elasticsearch';
import { useRouter } from 'vue-router';
import bookCoverDefault from '@/assets/images/book-cover-default.png';
const router = useRouter();

const relatedTextbooks = ref([]);
const booksAllList = ref([]);
const relatedPageNum = ref(1);

const props = defineProps({
  title: {
    type: String,
    default: '重点教材推荐',
  },
  searchStr: {
    type: String,
    required: false,
    default: '', // 默认值
  },
});
function handleBooksScroll(step) {
  const maxPage = Math.ceil(booksAllList.value.length / 5);
  if (relatedPageNum.value <= 1 && step < 0) return;
  if (relatedPageNum.value >= maxPage && step > 0) return;
  relatedPageNum.value += step;
  let startIndex = (relatedPageNum.value - 1) * 5;
  let endIndex = startIndex + 5;
  if (step > 0) {
    relatedTextbooks.value = booksAllList.value.slice(startIndex, endIndex);
    triggerSlideAnimation('right');
  } else {
    relatedTextbooks.value = booksAllList.value.slice(startIndex, endIndex);
    triggerSlideAnimation('left');
  }
}

function handleBookClick (item) {
  const query = { key: item.bookId };
  router.push({ path: '/book-detail', query: query });
}
// 触发横移动画
function triggerSlideAnimation(direction) {
  const listElement = document.querySelector('.books-list');
  if (!listElement) return;

  const animationClass = direction === 'left' ? 'slide-left' : 'slide-right';
  listElement.classList.add(animationClass);

  setTimeout(() => {
    listElement.classList.remove(animationClass);
  }, 100); // 动画持续时间与 CSS 一致
}
// 搜索相关教材
async function searchRelatedTextbooks() {
  const param = {
    bookName: props.searchStr,
    authorValue: props.searchStr,
    isbn: props.searchStr,
  };
  // const param = {
  //   bookName: '',
  //   authorValue: '',
  //   isbn: '',
  // };
  try {
    const response = await searchBook(param);
    booksAllList.value = JSON.parse(JSON.stringify(response.data.list));
    relatedTextbooks.value = response.data.list ? response.data.list.slice(0, 5) : null;
  } catch (error) {
    console.error('搜索失败：', error);
  }
}
// 初次加载时触发搜索
onMounted(async () => {
  await searchRelatedTextbooks();
});
// 监听 searchStr 的变化并重新搜索
watch(
  () => props.searchStr,
  async (newSearchStr) => {
    if (newSearchStr) {
      await searchRelatedTextbooks();
    }
  }
);
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index';

.comp-con {
  width: 100%;
  @extend .base-flex-row;
  justify-content: flex-start;
  align-items: center;

  .data-list-con {
    width: 100%;
    padding-top: 46px;
    @extend .base-flex-row;
    justify-content: space-between;
    align-items: center;

    .common-scroll-btn {
      width: 59px;
      height: 59px;
      border-radius: 20px;
      cursor: pointer;
      margin-right: 20px;
      @extend .base-flex-row;
      justify-content: center;
      align-items: center;

      img {
        width: 105px;
        height: 105px;
      }
    }

    .common-scroll-btn:last-child {
      margin-right: 0;
    }

    .books-list {
      width: 100%;
      height: 100%;

      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: center;

      /* 设置新的动画 */
      &.slide-left {
        animation: slideLeft 2.6s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
        /* 模拟重力效果 */
      }

      &.slide-right {
        animation: slideRight 2.6s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
      }

      @keyframes slideLeft {
        from {
          transform: translateX(0);
        }

        to {
          transform: translateX(100%);
        }
      }

      @keyframes slideRight {
        from {
          transform: translateX(0);
        }

        to {
          transform: translateX(-100%);
        }
      }

      .item-card {
        width: 238px;
        height: 418px;
        background: #ffffff;
        box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);
        padding-top: 16px;
        border-radius: 16px;
        margin-right: 26px;
        @extend .base-flex-column;
        justify-content: flex-start;
        align-items: center;

        .list-item {
          padding: 0 16px;
          @extend .base-flex-column;
          justify-content: flex-start;
          align-items: center;
          .item-img {
            width: 197px;
            height: 275px;
            border-radius: 4px;
            margin-bottom: 16px;
          }
          .item-name {
            width: 197px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #333333;
            text-align: left;
            text-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
            font-style: normal;
            margin-bottom: 7px;
            align-self: flex-start;
            @extend .base-text-ellipsis;
          }

          .item-author {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            text-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
            text-align: left;
            font-style: normal;
            margin-bottom: 16px;
            align-self: flex-start;
          }

          .item-price {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #0966b4;
            text-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
            text-align: left;
            font-style: normal;
            align-self: flex-start;
          }
        }
      }
    }
  }
}
</style>
