<template>
  <div class="catalog-tree-list-con">
    <!-- 前言,介绍等 -->
    <!-- <catalogHeadCell :item="item" :key="item" v-for="item in dataObj.headItemList" /> -->
    <!-- 章节 -->
    <chapterInfoCell
      v-if="dataObj"
      v-for="item in dataObj"
      :item="item"
      :key="item.id"
      :expandAllFlag="props.expandAllFlag"
    />
  </div>
</template>

<script setup name="CatalogTreeListComp">
// import catalogHeadCell from '@/views/book/components/catalogHeadCell/index.vue';
import chapterInfoCell from '@/views/book/components/chapterInfoCell/index.vue';

const props = defineProps({
  dataObj: {
    type: Object,
    default: () => ({}), // 使用函数返回默认对象
  },
  expandAllFlag: {
    type: Boolean,
    default: false,
  },
});

// 监视 expandAllFlag 属性变化
watch(
  () => props.expandAllFlag,
  (newValue) => {
  }
);

// 监视 dataObj 属性变化
watch(
  () => props.dataObj,
  (newValue, oldValue) => {
    reloadContent(); // 调用重新加载逻辑
  },
  { deep: true } // 深度监听，适用于嵌套对象
);

// 定义重新加载逻辑
function reloadContent() {
  // 在此执行重新加载数据或更新页面的逻辑
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';

.catalog-tree-list-con {
  width: 100%;
  min-height: 500px;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;
}
</style>
