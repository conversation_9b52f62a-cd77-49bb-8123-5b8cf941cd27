<template>
  <div class="app-container">
    <Cards>
      <el-row :gutter="10" class="mb8" type="flex" justify="space-between" align="middle">
        <el-col :span="10">
          <!-- Tab 切换组件 -->
          <el-tabs :model-value="activeTab" @tab-click="handleTabClick">
            <el-tab-pane
                v-for="tab in tabs"
                :key="tab.name"
                :label="tab.label"
                :name="tab.name"
                :disabled="tab.name === activeTab"
            >
            </el-tab-pane>
          </el-tabs>
        </el-col>

        <!-- 搜索框 -->
        <el-col :span="1.5">
          <el-input
              class="floating-input"
              placeholder="请输入搜索关键词"
              v-model="searchKey"
          >
            <template #suffix>
              <img class="floating-img" @click="selOpenCourse(searchKey)" :src="openCourseListSelect"/>
            </template>
          </el-input>
        </el-col>
      </el-row>

      <!-- 内容 -->
      <div class="tab-content">
        <slot></slot>
      </div>
    </Cards>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import openCourseListSelect from "@/assets/images/openCourse/open_course_list_select.png";
import MyCoursewareDesign from "@/views/coursewareDesign/MyCoursewareDesign/index.vue";
import MyShareCoursewareDesign from "@/views/coursewareDesign/MyShareCoursewareDesign/index.vue";

const route = useRoute();
const router = useRouter();

const activeTab = computed(() => {
  return route.name;
});

const tabs = [
  { label: '我的创建', name: 'MyCoursewareDesign' },
  { label: '我的分享', name: 'MyShareCoursewareDesign' },
];

const handleTabClick = (tab) => {
  console.log(tab)
  const targetTabName = tab.props.name;
  if (targetTabName !== activeTab.value) {
    router.push({ name: targetTabName });
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-tabs__item) {
  font-size: 20px;
  height: 40px;
  line-height: 40px;
  color: #999999;
  font-weight: normal;
  border-bottom: none !important;
}

:deep(.el-tabs__item.is-active) {
  color: #333333;
}

:deep(.el-tabs__nav-wrap:after) {
  background-color: transparent !important;
}

.floating-input {
  position: relative;
  width: 300px;
  padding: 0px 30px;
}

.tab-content {
  margin-top: 20px;
  padding: 10px;
  background-color: #fff;
}
</style>
