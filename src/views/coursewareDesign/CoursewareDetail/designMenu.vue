<template>
  <div>
    <div class="MenusContent-header">
      <el-icon style="margin-right: 5px">
        <Fold/>
      </el-icon>
      <span>目录</span>
      <span v-if="treeData && treeData.length > 0" @click="openMenu" ref="menuStatusRef"
      >{{ menuStatus ? `折叠` : `展开` }}全部目录</span
      >
    </div>
    <div class="MenusContent-main __hidden-scroll__">
      <div v-if="treeData && treeData.length > 0">
        <el-menu class="elMenuStyle" ref="menuItemsRef">

        </el-menu>
      </div>
      <div v-else>
        <p style="color: #A6ACBB">目录创建后自动生成</p>
      </div>
    </div>
  </div>
</template>

<script setup name="DesignMenu">

import {ref} from "vue";

const treeData = ref([
  {
    id: 1,
    label: '根节点',
    isRoot: true,
    children: [
      {id: 2, label: '子节点 1'},
      {id: 3, label: '子节点 2'}
    ]
  }
])
const defaultProps = ref(
    {
      children: 'children',
      label: 'label'
    }
)

const formData = ref({
  id: null,
  name: '',
  parentName: ''
})
const editingNode = ref(null)
const editingParent = ref(null)
const menuItemsRef = ref(null);
const menuStatus = ref(false);
let toExpandeCatalogIds = [];



const openMenu = () => {
  if (!menuStatus.value) {
    toExpandeCatalogIds.forEach((catalogId) => {
      menuItemsRef.value.open(catalogId);
    });
  } else {
    toExpandeCatalogIds.forEach((catalogId) => {
      menuItemsRef.value.close(catalogId);
    });
  }
  menuStatus.value = !menuStatus.value;
};

</script>

<style scoped lang="scss">
$bordercolor: #e3e3e3;
.MenusContent-header {
  display: flex; /* 使图标和文本水平排列 */
  align-items: center; /* 垂直居中对齐 */
  justify-content: flex-start; /* 左对齐 */
  border-bottom: 1px solid $bordercolor;
  padding-bottom: 16px;
  box-sizing: border-box;
  color: #0966B4;

  & > span:nth-of-type(1) {
    font-weight: 600;
    font-size: 16px;
    color: var(--fontColor);
    margin-left: 5px; /* 确保图标和文字之间有间隔 */
  }

  & > span:nth-of-type(2) {
    font-weight: 400;
    font-size: 12px;
    color: #0966b4;
    line-height: 12px;
    cursor: pointer;
    margin-left: auto;
  }
}

.MenusContent-main {
  width: 100%;
  height: calc(100% - 37px);
  overflow-y: auto;

  .MenusContent-main-item {
    width: 100%;
    font-size: 14px;
    height: 44px;
    line-height: 44px;
    text-align: left;
    cursor: pointer;
    padding-left: 20px;
    box-sizing: border-box;
    border-bottom: 1px solid $bordercolor;

    &:hover {
      background-color: var(--hoverBackgroundColor);
      color: var(--hoverfont);
    }
  }
}

</style>
