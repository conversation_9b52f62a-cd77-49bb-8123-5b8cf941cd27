<template>
  <el-container style="background-color: #F1F5FB">
    <el-main>
      <div class="main-header">
        <div>
          <el-row :gutter="20" class="filters">
            <el-col :span="8" :offset="3">
              <el-breadcrumb :separator-icon="ArrowRight" class="breadcrumb_text">
                <el-breadcrumb-item :to="{ path: '/index' }"
                >我的课件
                </el-breadcrumb-item
                >
                <el-breadcrumb-item> 课件设计</el-breadcrumb-item>
              </el-breadcrumb>
            </el-col>
          </el-row>
        </div>
        <div>
          <el-button type="primary" @click="stuExamine">推送课件</el-button>
          <el-button type="primary" @click="stuExamine">预览</el-button>
          <el-button type="primary" @click="stuExamine">保存</el-button>
          <el-button type="primary" @click="stuExamine">取消</el-button>
          <el-button type="primary" @click="stuExamine">返回</el-button>
        </div>
      </div>
      <div class="main-course">
        <!--    左侧目录    -->
        <el-row :gutter="20">
          <el-col :span="6">
            <Cards>
              <div style="min-height: 70vh;">
                <design-menu>
                </design-menu>
              </div>
            </Cards>
          </el-col>

          <!--    右侧详情    -->
          <el-col :span="18">
            <Cards>
              <div style="min-height: 70vh;">
                <design-detail></design-detail>
              </div>
            </Cards>
          </el-col>
        </el-row>
      </div>
    </el-main>
  </el-container>
</template>

<script setup lang="ts">

import {ArrowRight} from "@element-plus/icons-vue";
import {ref} from "vue";
import DesignMenu from "@/views/coursewareDesign/CoursewareDetail/designMenu.vue";
import DesignDetail from "@/views/coursewareDesign/CoursewareDetail/designDetail.vue";




</script>

<style scoped lang="scss">

.main-header {
  width: 80%;
  height: 60px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .filters {
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .breadcrumb_text {
    width: 263px;
    height: 16px;
    font-family: Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
    font-weight: normal;
    font-size: 1rem;
    color: #666666;
    line-height: 37px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}

.main-course {
  width: 80%;
  height: 860px;
  margin: 0 auto;
  border-radius: 10px;
}

</style>
