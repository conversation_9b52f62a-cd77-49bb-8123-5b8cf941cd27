<template>
  <div>
    <el-row>
      <el-col>
        <div class="design-title">这是课件名称</div>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <div class="design-add-container" @click="addDesign">
          <div class="design-around">
            <img src="@/assets/images/coursewareDesign/design-add.png"/>
          </div>
          <span class="design-add">添加章</span>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <div v-if="isDefault" class="empty-design">
          <img src="@/assets/images/coursewareDesign/empty-design.png"/>
          <span style="color: #B6BFCC">当前暂无课件内容，点击<el-button size="large" type="primary" link
           @click="addDesignItem">【添加章节】</el-button>并创建教学内容</span>
        </div>
        <div v-else style="margin-top: 20px">
          <el-table
              :data="tableData" :show-header="false"
              style="width: 100%"
              row-key="id"
              :expand-row-keys="expandRowKeys"
              @expand-change="handleExpandChange"
              :span-method="arraySpanMethod"
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              class="scrollable-table"
              :row-class-name="rowClassName"
          >
          <el-table-column
              type="selection"
              width="40"/>
          <!-- 父级节点的可编辑列 -->
          <el-table-column
              label="节点名称"
              fixed="left"
              prop="label"
          >
            <template #default="{ row }">
              <div class="table-item" v-if="row.level != 3"
                   :style="{ paddingLeft: `${row.level * 20}px`, gap: '10px', fontSize: getFontSize(row.level) }">
                <el-input
                    v-if="row.editable"
                    v-model="row.label"
                    @focus="handleFocus(row.label)"
                    @blur="handleBlur(row)"
                    ref="iptRefs"
                    style="width: 300px"
                    placeholder="请输入"
                ></el-input>
                <img v-if="row.editable" @click="handleSave(row)" title="确认" class="addImg"
                     src="@/assets/images/coursewareDesign/confirm.png"/>
                <img v-if="row.editable" class="addImg" @click="cancelItem(row)" title="取消"
                     src="@/assets/images/coursewareDesign/cancel.png"/>
                <span v-else>{{ row.label }}</span>
              </div>
              <div v-else>
                <design-item-detail :detailForm="row">

                </design-item-detail>
              </div>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" width="260px" align="right">
            <template #default="{ row }">
              <el-button
                  v-if="!row.editable && row.level == 1"
                  type="text"
                  @click="handleAddAfter(row)"
              >添加章
              </el-button>

              <el-button
                  v-if="!row.editable && row.level == 1"
                  type="text"
                  @click="handleAddItem(row)"
              >添加节
              </el-button>
              <el-button
                  v-if="!row.editable && row.level == 2"
                  type="text"
                  @click="handleAddItemDetail(row)"
              >添加内容
              </el-button>
              <el-button
                  v-if="!row.editable"
                  type="text"
                  @click="handleEdit(row)"
              >编辑
              </el-button>
              <el-button
                  v-if="!row.editable"
                  type="text"
                  @click="handleDelete(row)"
              >删除
              </el-button>
              <el-button
                  v-if="!row.editable"
                  type="text"
                  @click="handleCopy(row)"
              >复制
              </el-button>
            </template>
          </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import {ref} from 'vue';
import DesignItemDetail from "@/views/coursewareDesign/CoursewareDetail/designItemDetail.vue";

const isDefault = ref(true)
const {proxy} = getCurrentInstance();
const iptRefs = ref(null)
const originalValue = ref(null)//输入框原始数据

onMounted(() => {
  if (tableData.value.length == 0) {
    isDefault.value = true;
  } else {
    isDefault.value = false;
  }
})
const addDesign = () => {
  // 添加章节时，确保只有一个元素被添加
  if (isDefault.value) {
    isDefault.value = false;
    tableData.value = [
      {
        id: Date.now(), // 生成唯一 id
        label: '新章节',
        editable: true,
        level: 1,
        children: []
      }
    ];
  } else {
    // 如果已经有章节，添加新章节
    tableData.value.push({
      id: Date.now(), // 生成唯一 id
      label: '新章节',
      editable: true,
      level: 1,
      children: []
    });
  }
};

const rowClassName = (row) => {
  if (row.level === 1) {
    return 'level-one-row'; // 返回自定义的类名
  }
  return '';
}

// 删除行
const handleDelete = (row) => {
  // 如果是顶层节点，直接删除
  const index = tableData.value.findIndex(item => item.id === row.id);
  if (index !== -1) {
    tableData.value.splice(index, 1); // 从数据中删除该项
    if (tableData.value.length == 0) {
      isDefault.value = true;
    }
  } else {
    // 否则需要找到父节点并删除该子节点
    const parent = findParentNode(tableData.value, row.parentId);
    if (parent) {
      const childIndex = parent.children.findIndex(child => child.id === row.id);
      if (childIndex !== -1) {
        parent.children.splice(childIndex, 1); // 删除子节点
      }
    }
  }
};

// 通过 parentId 查找父节点
const findParentNode = (nodes, parentId) => {
  for (const node of nodes) {
    if (node.id === parentId) {
      return node;
    }
    if (node.children) {
      const result = findParentNode(node.children, parentId);
      if (result) return result;
    }
  }
  return null;
};

/** 指定元素后添加节 */
const handleAddItem = (row) => {
  console.log(tableData.value);

  const newItem = {
    id: Date.now(), // 使用时间戳或其他逻辑生成唯一id
    label: '新节',   // 新子节点的默认名称
    level: 2,       // 节点层级
    parentId: row.id,  // 设置父节点ID
    editable: true, // 新子节点为可编辑状态
    children: [],   // 子节点为空
    hasChildren: false, // 初始没有子节点
  };

  // 将新节添加到父节点的 children 数组中
  row.children.push(newItem);
  // row.hasChildren = true;

  // 展开父节点
  expandRowKeys.value.push(row.id);  // 直接推入展开的行id
  if (!expandRowKeys.value.includes(row.id)) {
    expandRowKeys.value.push(row.id);
  }
}

/** 添加内容*/
const handleAddItemDetail = (row) => {
  console.log(tableData.value);

  const newItem = {
    id: Date.now(), // 使用时间戳或其他逻辑生成唯一id
    label: '新内容',   // 新子节点的默认名称
    level: 3,       // 节点层级
    parentId: row.id,  // 设置父节点ID
    editable: true, // 新子节点为可编辑状态
    children: [],   // 子节点为空
    hasChildren: false, // 初始没有子节点
  };

  // 将新节添加到父节点的 children 数组中
  row.children.push(newItem);
  // row.hasChildren = true;

  // 展开父节点
  // expandRowKeys.value.push(row.id);  // 直接推入展开的行id
  // if (!expandRowKeys.value.includes(row.id)) {
  //   expandRowKeys.value.push(row.id);
  // }
}

const handleFocus = (label) => {
  originalValue.value = label
}

/** 指定元素后添加章 */
const handleAddAfter = (row) => {
  const index = tableData.value.findIndex(item => item.id === row.id);
  if (index !== -1) {
    const newId = Date.now(); // 可使用更合理的 id 生成逻辑
    const newParent = {
      id: newId,
      label: '新章节',
      level: 1,
      editable: true, // 默认进入编辑状态
      children: []
    };
    tableData.value.splice(index + 1, 0, newParent);
  }
}
/** 添加章 */
const addDesignItem = () => {
  addDesign()
}

//
// // 通过 parentId 查找父节点
// const findParentNode = (nodes, parentId) => {
//   for (const node of nodes) {
//     if (node.id === parentId) {
//       return node;
//     }
//     if (node.children) {
//       const result = findParentNode(node.children, parentId);
//       if (result) return result;
//     }
//   }
//   return null;
// }


// const tableData = ref([
// /*  {
//     id: 1,
//     label: '父节点 1',
//     childLabel: '子节点 1-1',
//     level: 1,
//     children: [
//       { id: 11, label: '子节点 1-1',level: 2, childLabel: '子节点 1-1-1', editable: false },
//       { id: 12, label: '子节点 1-2',level: 2, childLabel: '子节点 1-2-1', editable: false }
//     ],
//     editable: false
//   }*/
// ])
const tableData = ref([
  // {
  //   "id": 1753856732017,
  //   "label": "新章节",
  //   "editable": false,
  //   "level": 1,
  //   "children": []
  // },
  // {
  //   "id": 1753856738833,
  //   "label": "新章节1",
  //   "level": 1,
  //   "editable": false,
  //   "children": [
  //     {
  //       "id": 1753856743033,
  //       "label": "新节·1",
  //       "level": 2,
  //       "parentId": 1753856738833,
  //       "editable": false
  //     },
  //     {
  //       "id": 1753856748257,
  //       "label": "新节2",
  //       "level": 2,
  //       "parentId": 1753856738833,
  //       "editable": false
  //     },
  //     {
  //       "id": 1753856756217,
  //       "label": "新节",
  //       "level": 2,
  //       "parentId": 1753856738833,
  //       "editable": true
  //     }
  //   ]
  // }
])

// 控制是否展开的行
const expandRowKeys = ref([])

// 展开/收起子行
const handleExpandChange = (expandedRows) => {
  console.log('expandedRows', expandedRows)
}

// 编辑按钮点击事件
const handleEdit = (row) => {
  row.editable = true
  if (row.children) {
    row.children.forEach(child => child.editable = true)
  }
}
// 输入框非空验证
const handleBlur = (row) => {
  if (!row.label.trim()) {
    proxy.$modal.msgError(`请输入章节名称!`);
    // 阻止焦点失去
    proxy.$nextTick(() => {
      iptRefs.value.focus()
    });
  } else {
    // handleSave(row);
  }
}

// 保存编辑内容
const handleSave = (row) => {
  row.editable = false
  if (row.children) {
    row.children.forEach(child => child.editable = false)
  }
  originalValue.value = null
}

// 取消编辑
const cancelItem = (row) => {
  if (row.editable) {
    if (originalValue.value) {
      row.label = originalValue.value
    } else {
      const index = tableData.value.findIndex((item) => item.id === row.id);
      if (index !== -1 && tableData.value.length == 1 && !row.label) {
        tableData.value.splice(index, 1); // 从数据中删除该项
        if (tableData.value.length === 0) {
          isDefault.value = true
        }
      } else {
        // row.label = index.label
      }
    }
  }
  row.editable = false; // 取消编辑状态
  originalValue.value = null
};

// 复制节点
const handleCopy = (row) => {
  const newNode = { ...row, id: Date.now(), editable: false }; // 复制当前节点，重新生成 ID
  newNode.label = `${row.label} - 副本`; // 可以给副本添加后缀或自定义
  if (row.level === 1) {
    // 如果是章节级别的节点，将副本添加到顶层节点
    tableData.value.push(newNode);
  } else {
    // 否则将副本添加到父节点的 children 数组中
    const parent = findParentNode(tableData.value, row.parentId);
    if (parent) {
      parent.children.push(newNode);
    }
  }
};

const arraySpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 判断当前行的数据的 level
  if (row.level === 3) {
    if (column.property === 'label') {
      return [1, 2]; // 横向合并2个单元格
    }
  }
  return [1, 1]; // 默认不合并
  // if (columnIndex === 0) {
  //   const rowspan = row.children ? row.children.length + 1 : 1;
  //   return {
  //     rowspan,
  //     colspan: 1
  //   };
  // }
};

const getFontSize = (level) => {
  if (level === 1) {
    return '20px';
  } else if (level === 2) {
    return '16px';
  } else {
    return '14px';
  }
  return '14px';
}

</script>

<style scoped lang="scss">
.design-title {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
}

.design-add {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  margin-left: 5px;
}

.design-add-container {
  display: flex;
  align-items: center;
  width: 13%;
  border-radius: 6px 6px 6px 6px;
  border: 2px dotted #dcdcdc;
  padding: 5px 30px;
  margin-top: 20px;
  cursor: pointer;
}

.design-around {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background-color: #d9e1ff;
  border-radius: 50%;
}

.design-add-container img {
  width: 17px;
  height: 17px;
}

.empty-design {
  height: 450px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.empty-design img {
  width: 200px;
  height: 200px;
}

.empty-design span {
  margin-top: 10px;
}

.bold-text {
  font-weight: bold;
}

.addImg {
  width: 25px;
  height: 25px;
  cursor: pointer;
}

.table-item {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: nowrap !important;
  width: 100%; /* 确保它占据满行 */
}

/* 禁止折行 */
.scrollable-table .el-table__cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  align-items: center;
}

.level-one-row {
  background-color: red !important; /* 设置背景色为红色 */
}

//.el-table >>> .el-table__expand-icon {
//  margin-right: 10px; /* 设置展开图标的右边距 */
//  display: inline-block;
//}

.scrollable-table .el-table__expand-icon {
  margin-right: 10px; /* 设置展开图标的右边距 */
  display: inline-block;
}


//.parent-label{
//  font-size: 14px;
//  font-weight: bold;
//  color: red;
//  text-align: left;
//  text-transform: none;
//}
</style>
