<template>

  <div>
    <div class="table-container" v-if="dataList.length>0">
      <el-table
          v-loading="loading"
          :data="dataList"
          :show-header="false"
          :header-cell-style="{ background: '#EDF4FD !important', color: '#666666', textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }">
        <el-table-column label="姓名" prop="resourceTitle" />
        <el-table-column label="姓名" prop="resourceType" />
        <el-table-column label="操作" width="260px" align="right">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="grid-container">
      <div class="table-add" v-for="item in addItemTypeList">
        <div>
          <div class="item" @click="addDetail(item)">
            <div class="design-around">
              <img src="@/assets/images/coursewareDesign/design-add.png"/>
            </div>
            <div class="design-text">
              {{item.label}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加图文 -->
  <el-dialog :title="addImgTitle" width="70%" style="margin-top: 120px !important;" v-model="addImgVisible">
    <el-form :model="imgFormData" :rules="imgRules" ref="imgFormRef" label-width="100px">
      <el-form-item label="标题">
        <el-input v-model="imgFormData.resourceTitle" style="margin-right: 123px" placeholder="请输入标题" />
      </el-form-item>
      <el-form-item label="内容">
        <div class="editor">
          <quill-editor
              ref="quillEditorRef"
              v-model:content="imgFormData.content"
              contentType="html"
              @textChange="(e) => $emit('update:modelValue', content)"
              :options="options"
              :style="styles"
          />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitImgForm">确 定</el-button>
        <el-button @click="cancelImgForm">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 添加资源、虚拟仿真、压缩包 -->
  <el-dialog :title="addResourcesTitle" width="95%" style="margin-top: 120px !important;" v-model="addResourcesVisible">
    <el-form :model="resourcesFormData" :rules="resourcesRules" ref="resourcesFormRef" label-width="100px">
      <el-form-item label="资源类型">
        <el-row>
          <el-radio-group v-model="resourcesFormData.type">
            <el-radio label="本地资源" value="0" />
            <el-radio label="我的资源" value="1" />
          </el-radio-group>
        </el-row>
      </el-form-item>
      <el-form-item>
        <div style="margin-left: -70px;margin-right: 30px;box-sizing: content-box;width: 100%">
          <div v-if="resourcesFormData.type==='1'">

            <!-- 添加前进后退按钮和面包屑导航 -->
            <div class="navigation-bar" v-if="resourcesFormData.parentId !=='0'">
              <div class="nav-buttons">
                <el-button
                    icon="ArrowLeft"
                    :disabled="historyIndex <= 0"
                    @click="handleBack"
                >后退</el-button>
                <el-button
                    icon="ArrowRight"
                    :disabled="historyIndex >= folderHistory.length - 1"
                    @click="handleForward"
                >前进</el-button>
              </div>
              <el-breadcrumb separator="/" class="breadcrumb-nav">
                <el-breadcrumb-item
                    v-for="(folder, index) in folderPath"
                    :key="index"
                    class="clickable"
                    @click="handleBreadcrumbClick(folder.id)"
                >
                  {{ folder.name }}
                </el-breadcrumb-item>
              </el-breadcrumb>
            </div>
            <div v-loading="resourceLoading" style="min-height: 200px">
              <div v-if="hasContent" class="folder-grid">
                <div v-for="item in [...userResourceFolderList, ...userResourceList]"
                     :key="item.type === 'folder' ? `folder_${item.resourceId}` : `file_${item.resourceId}`"
                     class="folder-item"
                     @click="handleFolderClick(item)">
                  <div class="folder-checkbox" @click.stop v-if="item.defaultType != '1'">
                    <el-checkbox
                        v-model="item.isSelected"
                        @change="(val) => handleSelectionChange(item, val)"
                    />
                  </div>
                  <div class="folder-content">
                    <template v-if="item.type === 'folder'">
                      <el-icon size="70" class="folder-icon"><Folder /></el-icon>
                    </template>
                    <template v-else>
                      <div v-if="item.fileType === '1'" class="preview-container">
                        <img
                            :src="item.fileUrl"
                            :alt="item.fileName"
                            class="preview-image"
                        />
                      </div>
                      <div v-else-if="item.fileType === '3'" class="preview-container">
                        <video
                            :src="item.fileUrl"
                            class="preview-video"
                            preload="metadata"
                        >
                          <el-icon size="70"><VideoCamera /></el-icon>
                        </video>
                      </div>
                      <el-icon v-else size="70" class="folder-icon">
                        <Headset v-if="item.fileType === '2'" />
                        <Monitor v-else-if="item.fileType === '4'" />
                        <Monitor v-else-if="item.fileType === '5'" />
                        <Box v-else-if="item.fileType === '6'" />
                        <QuestionFilled v-else-if="item.fileType === '7'" />
                        <Document v-else-if="item.fileType === '8'" />
                        <Document v-else />
                      </el-icon>
                    </template>
                    <div class="folder-name">{{ item.folderName || item.fileName }}</div>
                  </div>
                </div>
              </div>
              <el-empty v-else-if="!resourceLoading" description="该文件夹为空" style="padding-top: 60px" />
            </div>

            <pagination
                v-show="resourceTotal>0"
                :total="resourceTotal"
                v-model:page="resourceQueryParams.pageNum"
                v-model:limit="resourceQueryParams.pageSize"
                @pagination="getMyResourceList"
            />
          </div>
          <div v-else>
            <el-button type="primary">上传文件</el-button>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitResourcesForm">确 定</el-button>
        <el-button @click="cancelResourcesForm">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 添加链接 -->
  <el-dialog :title="addLinkTitle" width="40%" style="margin-top: 120px !important;" v-model="addLinkVisible">
    <el-form :model="linkFormData" :rules="linkRules" ref="linkFormRef" label-width="100px">
      <el-form-item label="链接主题" prop="resourceTitle">
        <el-input v-model="linkFormData.resourceTitle" style="margin-right: 40px" placeholder="请输入标题" />
      </el-form-item>
      <el-form-item label="链接地址" prop="resourceContent">
        <el-input v-model="linkFormData.resourceContent" style="margin-right: 40px" placeholder="请输入链接地址" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitLinkForm">确 定</el-button>
        <el-button @click="cancelLinkForm">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 添加习题 -->
  <el-dialog :title="addExampleTitle" width="60%" style="margin-top: 120px !important;" v-model="addExampleVisible">
    <el-form :model="exampleFormData" :rules="exampleRules" ref="exampleFormRef" label-width="100px">
      <Cards>
        <div>
          <div class="question">
            <el-checkbox
                value="2"
                name="type">单选题-简单</el-checkbox>
          </div>
          <div class="question">
            <span class="question-text">题干：的经营活动安防路由器将需重新配置网络路由器；数据流量交换业务管理将通过路由器口卡进行配置并设定路由器路径扩展。</span>
          </div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
<!--          <el-radio-group v-model="exampleFormData.answer" class="answer-options">-->
<!--            <el-radio label="A">选项内容</el-radio>-->
<!--            <el-radio label="B">选项内容</el-radio>-->
<!--            <el-radio label="C">选项内容</el-radio>-->
<!--            <el-radio label="D">选项内容</el-radio>-->
<!--          </el-radio-group>-->
        </div>
      </Cards>


<!--      <el-form-item label="习题主题">-->
<!--        <el-input v-model="exampleFormData.resourceTitle" style="margin-right: 40px" placeholder="请输入标题" />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="习题内容">-->
<!--        <el-input v-model="exampleFormData.content" style="margin-right: 40px" placeholder="请输入习题内容" />-->
<!--      </el-form-item>-->
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitExampleForm">确 定</el-button>
        <el-button @click="cancelExampleForm">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 添加测试 -->
  <el-dialog :title="addTestTitle" width="60%" style="margin-top: 120px !important;" v-model="addTestVisible">
    <div>
      <el-form :model="testFormData" :rules="testRules" ref="testFormRef" label-width="100px">
        <el-form-item label="测试名称">
          <el-input v-model="testFormData.resourceTitle" style="margin-right: 40px" placeholder="请输入测试名称" />
        </el-form-item>
      </el-form>
    </div>
    <div>
      <Cards>
        <el-row :gutter="20">
          <el-col :span="8">
            <img src="@/assets/images/coursewareDesign/test.png">
          </el-col>
          <el-col :span="8">
            <div>高等数学第一章测试试卷</div>
            <div>试题数量：10</div>
<!--            <el-row></el-row>-->
<!--            <el-row></el-row>-->
          </el-col>
          <el-col :span="6">
            <span>2025-01-03 12:23:26</span>
          </el-col>
          <el-col :span="2">
            <span>自动组卷</span>
          </el-col>
        </el-row>
      </Cards>
    </div>

  </el-dialog>


</template>

<script setup  >
import {QuillEditor} from "@vueup/vue-quill";
import PDF from "@/assets/svg/pdf.svg"
import IMAGES from "@/assets/svg/images.svg"
import PPT from "@/assets/svg/ppt.svg"
import MUICS from "@/assets/svg/muics.svg"
import VIDEO from "@/assets/svg/video.svg"
import RAR from "@/assets/svg/rar.svg"
import {getMoocResource, listMoocResource, listMoocResourceNoPage} from "@/api/edu/moocResource.js";
import {addMoocResourceFolder, resourceList} from "@/api/edu/moocResourceFolder.js";

const props = defineProps({
  detailForm: {
    type: Object,
    default: () => ({})
  }
})

const data = reactive({
  imgFormData: {},
  resourcesFormData: {
    type:'1',
    parentId: 0
  },
  linkFormData: {
    resourceType:'4',
  },
  exampleFormData: {},
  testFormData: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dictName: undefined,
    dictType: undefined,
    status: undefined,
  },
  resourceQueryParams: {
    pageNum: 1,
    pageSize: 30,
    parentId: 0
  },
  imgRules: {
    dictName: [{ required: true, message: '字典名称不能为空', trigger: 'blur' }],
    dictType: [{ required: true, message: '字典类型不能为空', trigger: 'blur' }],
  },
  resourcesRules: {
    type: [{ required: true, message: '字典名称不能为空', trigger: 'blur' }],
    dictType: [{ required: true, message: '字典类型不能为空', trigger: 'blur' }],
  },
  linkRules: {
    resourceTitle: [{ required: true, message: '链接主题不能为空', trigger: 'blur' }],
    resourceContent: [{ required: true, message: '链接地址不能为空', trigger: 'blur' }],
  },
  exampleRules: {
    resourceTitle: [{ required: true, message: '习题主题不能为空', trigger: 'blur' }],
    content: [{ required: true, message: '习题内容不能为空', trigger: 'blur' }],
  },
  testRules: {
    resourceTitle: [{ required: true, message: '测试主题不能为空', trigger: 'blur' }],
    content: [{ required: true, message: '测试内容不能为空', trigger: 'blur' }],
  },
})

const { queryParams, imgFormData, imgRules,resourcesRules,
  resourcesFormData, linkFormData, linkRules, exampleFormData, exampleRules, testFormData, testRules,
  resourceQueryParams} = toRefs(data)
const { proxy } = getCurrentInstance();
const loading = ref(false);
const dataList = ref([
    {
        resourceType:'1',
        resourceTitle:'dfdfdfdf',
        resourceContent:''
    }
]);
// const folderPath = ref([
//   { id: 0, name: '我的资源' }
// ]);
// 添加前进后退相关数据
const folderHistory = ref([]);
const historyIndex = ref(-1);
const MAX_HISTORY = 5;
const addItemTypeList = ref([
  {
    label: '添加图文',
    value: '1',
    url:""
  },
  {
    label: '添加资源',
    value: '2',
    url:""
  },
  {
    label: '添加虚拟仿真',
    value: '3',
    url:""
  },
  {
    label: '添加链接',
    value: '4',
    url:""
  },
  {
    label: '添加习题',
    value: '5',
    url:""
  },
  {
    label: '添加测试',
    value: '6',
    url:""
  },
  {
    label: '添加压缩包',
    value: '7',
    url:""
  }
])
const options = ref({
  theme: "snow",
  bounds: document.body,
  debug: "warn",
  modules: {
    // 工具栏配置
    toolbar: [
      ["bold", "italic", "underline", "strike"],      // 加粗 斜体 下划线 删除线
      ["blockquote", "code-block"],                   // 引用  代码块
      [{ list: "ordered" }, { list: "bullet" }],      // 有序、无序列表
      [{ indent: "-1" }, { indent: "+1" }],           // 缩进
      [{ size: ["small", false, "large", "huge"] }],  // 字体大小
      [{ header: [1, 2, 3, 4, 5, 6, false] }],        // 标题
      [{ color: [] }, { background: [] }],            // 字体颜色、字体背景颜色
      [{ align: [] }],                                // 对齐方式
      ["clean"],                                      // 清除文本格式
      ["link", "image", "video"]                      // 链接、图片、视频
    ],
  },
  placeholder: "请输入内容",
  readOnly: props.readOnly
});
const addImgVisible = ref(false)
const addImgTitle = ref('添加图文')
const addResourcesVisible = ref(false)
const addResourcesTitle = ref('')
const resourceTotal = ref(0);
const userResourceList = ref([]); // 新增文件列表
const resourceLoading = ref(false);
const addLinkVisible = ref(false)
let getListCallCount = 0;
const addLinkTitle = ref('添加链接')
const addExampleVisible = ref(false)
const addExampleTitle = ref('添加习题')
const addTestVisible = ref(false)
const addTestTitle = ref('添加测试')
const resourceFileList = ref([])
const resourceCheckFileList = ref([]) // 选中的资源列表
const userResourceFolderList = ref([])

const hasContent = computed(() => {
  return userResourceFolderList.value.length > 0 || userResourceList.value.length > 0;
});

// 修改选中项存储结构
const selectedItems = ref({
  folders: [], // 存储选中的文件夹
  files: []    // 存储选中的文件
});

const styles = computed(() => {
  let style = {};
  style.minHeight = `300px`;
  style.height = `500px`;
  return style;
});



const selectTypeName = computed(() => {
  return function (extension) {
    if (!extension) return null;

    if (extension === 'doc' || extension === 'docx') {
      return 'doc';
    }

    if (extension === 'pdf') {
      return 'pdf';
    }

    if (extension === 'ppt' || extension === 'pptx') {
      return 'ppt';
    }

    if (extension === 'xls' || extension === 'xlsx') {
      return 'xls';
    }

    if (extension === 'txt') {
      return 'txt';
    }

    if (extension === 'zip') {
      return 'zip';
    }

    if (extension === 'rar') {
      return 'rar';
    }

    if (extension === 'mp3') {
      return 'mp3';
    }

    if (extension === 'mp4' || extension === 'avi' || extension === 'mov') {
      return 'avi';
    }

    if (extension === 'jpg' || extension === 'jpeg' || extension === 'png' || extension === 'gif') {
      return 'jpg';
    }
  };
});

/** 获取我的资源文件夹列表和文件列表*/
async function getMyResourceList(type) {
  resourceLoading.value = true;
  try {

    const response = await resourceList(resourceQueryParams.value);
    const allItems = response.rows || [];
    console.log('allItems',allItems)
    userResourceFolderList.value = allItems
        .filter(item => item.type === 'folder')
        .map(folder => ({ ...folder, resourceId: folder.userFolderId, folderName: folder.name, type: 'folder', isSelected: false }));
    userResourceList.value = allItems
        .filter(item => item.type !== 'folder')
        .map(file => ({ ...file, fileName: file.name, isSelected: false }));
    resourceTotal.value = response.total;

    if (queryParams.value.parentId === 0 && allItems.length === 0 && !queryParams.value.type && !queryParams.value.folderName && getListCallCount < 1) {
      getListCallCount++;
      try {
        await addMoocResourceFolder({ folderName: "默认文件夹", parentId: 0, defaultType: '1' });
        await getList();
      } catch (error) {
        proxy.$modal.msgError("创建默认文件夹失败：" + error.message);
      }
    }
  } catch (error) {
    proxy.$modal.msgError("获取列表失败：" + error.message);
  } finally {
    resourceLoading.value = false;
    selectedItems.value = { folders: [], files: [] };
  }
}

/** 获取测试列表*/
/** 获取习题列表*/

const getFileIcon = (fileType) => {
  switch (fileType) {
    case 'pdf':
      return PDF;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return IMAGES;
    case 'pptx':
    case 'ppt':
      return PPT;
    case 'mp3':
    case 'wav':
    case 'aac':
      return MUICS;
    case 'mp4':
      return VIDEO;
    case 'zip':
    case 'rar':
      return RAR;
    default:
      return IMAGES; // 默认图标
  }
}

const addDetail = (item) => {
  if (item.value == '1') {
    // 添加图文
    addImgVisible.value = true
  } else if (item.value == '2') {
    // 添加资源
    addResourcesTitle.value = '添加资源'
    addResourcesVisible.value = true
    getMyResourceList()
  } else if (item.value == '3') {
    // 添加虚拟仿真
    addResourcesTitle.value = '添加虚拟仿真'
    addResourcesVisible.value = true
    getMyResourceList(4)
  } else if (item.value == '4') {
    // 添加链接
    addLinkVisible.value = true
  } else if (item.value == '5') {
    // 添加习题
    addExampleVisible.value = true
    console.log("添加习题")
  } else if (item.value == '6') {
    // 添加测试
    addTestVisible.value = true
  } else if (item.value == '7') {
    // 添加压缩包
    addResourcesTitle.value = '添加压缩包'
    addResourcesVisible.value = true
    getMyResourceList(9)
  }
}

// 处理文件夹点击
const handleFolderClick = (item) => {
  if (item.type == 'folder') {
    // 文件夹导航逻辑保持不变
    console.log('文件夹点击',item)
    console.log('文件夹点击',item.resourceId)
    resourceQueryParams.value.parentId = item.resourceId;
    resourcesFormData.value.parentId = item.resourceId;
    // folderPath.value.push({
    //   id: item.resourceId,
    //   name: item.folderName
    // });
    getMyResourceList();
  } else {
  }
}

// 资源表单重置
function cancelResourcesForm() {
  resourcesFormData.value = {
    type:'1',
    resource_type:null,
    coursewareDesignId:null,
    resource_title:null,
    resource_content:null,
  };
  proxy.resetForm("resourcesFormRef");
  addResourcesVisible.value = false
}

// 链接表单重置
function cancelLinkForm() {
  linkFormData.value = {
    type:'1',
    resource_type:null,
    coursewareDesignId:null,
    resource_title:null,
    resource_content:null,
  };
  proxy.resetForm("linkFormRef");
  addLinkVisible.value = false
}

// 资源表单提交
async function submitResourcesForm() {
  try {
    proxy.$refs["resourcesFormRef"].validate(valid => {
      console.log(resourceCheckFileList.value)
      // if (valid) {
      //   dataList.value.push(resourcesFormData)
      //   cancelResourcesForm()
      // }
    })
  } catch (error) {
    console.error('添加失败:', error);
    ElMessage.error('添加失败');
  }
}

// 链接表单提交
async function submitLinkForm() {
  try {
    proxy.$refs["linkFormRef"].validate((valid) => {
      console.log(valid)
      if (valid) {
        dataList.value.push({
          resourceTitle: linkFormData.resourceTitle,
          resourceType: linkFormData.resourceType
        });
        cancelLinkForm()
      }
    })
    // proxy.$refs["linkFormRef"].validate(valid => {
    //   console.log(valid)
    //   if (valid) {
    //     dataList.value.push(linkFormData)
    //     cancelLinkForm()
    //   }
    // })
  } catch (error) {
    console.error('添加失败:', error);
    ElMessage.error('添加失败');
  }
}


</script>

<style scoped lang="scss">
.grid-container {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(7, 1fr); /* 每行7列 */
  gap: 5px; /* 列间距 */
  justify-items: center;
  align-items: center;
}

.table-container, .table-add{
  width: 100%;
  margin-bottom: 10px;
  text-align: center;
}

.grid-content{
  display: flex;
  flex-direction: column;

  //width: 90%;
  //height: 90px;
}

.item {
  height: 120px;
  justify-content: center;
  align-items: center;
  display: flex;
  border-radius: 6px 6px 6px 6px;
  border: 2px dotted #dcdcdc;
  flex-direction: column;
}

.design-around {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #D9E1FF;
  align-items: center;
  justify-content: center;
  display: flex;
}

.design-around img {
  width: 30px;
  height: 30px;
}
.design-text {
  font-size: 14px;
  color: rgba(0,0,0,0.6);
  margin-top: 5px;
}

.editor {
  white-space: pre-wrap !important;
  line-height: normal !important;
}

:deep(.el-checkbox-button__inner) {
  width: 100%;
  height: 100%;
  border: none;
}
.el-checkbox-button {
  display: flex; /* 使用 flex 布局 */
  justify-content: center; /* 水平居中 */
  width: 100%;
  height: 100%;
  align-items: center; /* 垂直居中 */
  border: none !important; /* 去掉默认的边框 */
  background-color: transparent; /* 背景透明 */
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-checkbox-button__inner:hover) {
  background-color: #409eff;
  color: white; /* 字体颜色为白色 */
  transform: scale(1.05);
}

.file-icon {
  margin-top: 2px;
  align-items: center;
  height: 100%;
  display: flex;
}

.file-icon img {
  width: 60px;
  height: 100px;
  margin-left: 0px;
  margin-top: -15px;
}

.file-list {
  display: grid;
  flex-wrap: wrap;
  justify-content: flex-start;
  margin-top: 10px
}




.folder-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px;
  min-height: 40vh;
}

.folder-item {
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s;
  height: 190px;
  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .folder-content{
    margin:auto;
    margin-top: 10px;
  }
}

.folder-checkbox {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1;
}

.folder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 10px;
  text-align: center;
  position: relative;

  .more-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    z-index: 10;
  }
}

.folder-icon {
  margin-bottom: 10px;
  color: #409EFF;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.folder-name {
  font-size: 14px;
  margin-bottom: 10px;
  word-break: break-all;
}

.folder-actions {
  display: none;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
  padding: 5px;
  border-radius: 0 0 4px 4px;
}

.folder-item:hover .folder-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.image-preview-container,
.audio-preview-container,
.video-preview-container,
.file-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.image-preview-container img {
  object-fit: contain;
}

.video-preview-container video {
  max-height: 70vh;
}

.audio-preview-container audio {
  width: 100%;
}

.file-preview-container iframe {
  border: none;
  width: 100%;
  height: 80vh;
}

.preview-container {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 4px;
  background-color: #f5f7fa;
  margin-bottom: 10px;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;


  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
  }
}
</style>
