<template>
  <courseware-design-tab>
    <div class="grid-container">
      <el-card class="card" shadow="always" style="align-items: center;justify-content: center;display: flex" @click="addDesign">
        <div class="add-card">
          <img src="@/assets/images/coursewareDesign/design-add.png" style="width: 50px;height: 50px;">
        </div>
      </el-card>
      <div class="grid-item" v-for="(item, index) in items" :key="index">
        <el-card class="card" shadow="always">
          <!-- 操作按钮 -->
          <div class="image-header">
            <div class="card-header">
              <el-button size="mini" type="primary" class="action-btn">编辑</el-button>
              <el-button size="mini" type="primary" class="action-btn">复制</el-button>
              <el-button size="mini" type="primary" class="action-btn">推荐</el-button>
              <el-button size="mini" type="danger" class="action-btn">删除</el-button>
            </div>
          </div>
          <!-- 卡片内容 -->
          <div class="card-body">
            <h3 class="title">如果地球倒转 24 小时</h3>
            <p class="date-range">创建时间：2025-04-12 - 2025-07-12</p>
            <div style="padding: 5px; background-color: #F2F5FA; height: 32px; width: auto; align-items: center; display: flex; flex-wrap: nowrap;">
              <img class="thumbnail_6" referrerpolicy="no-referrer" :src="star" style="flex-shrink: 0; height: 100%;" />
              <span class="instructor" style="white-space: nowrap;">授课老师：林硕秋 陈默</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </courseware-design-tab>

  <el-dialog :title="addTitle" v-model="dialogVisible" width="600px">
    <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="right"
    >
      <el-form-item label="课件名称" prop="description">
        <el-input
            v-model="form.coursewareName"
            :rows="4"
            maxlength="500"
            show-word-limit
            placeholder="请输入教师简介"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="goEdit">开始设计</el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script setup>

import CoursewareDesignTab from "@/views/coursewareDesign/coursewareDesignTab.vue";
import star from '@/assets/images/openCourse/star.png'
import {addCoursewareDesign} from "@/api/edu/coursewareDesign.js";
import {Plus} from "@element-plus/icons-vue";
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    folderId: null,
  },
  rules: {
    coursewareName: [
      { required: true, message: "课件名称不能为空", trigger: "change" }
    ],
  },

});
const { proxy } = getCurrentInstance();
const { queryParams, form, rules } = toRefs(data);
const items = ref([
  'Item 1', 'Item 2', 'Item 3', 'Item 4',
  'Item 5', 'Item 6', 'Item 7', 'Item 8',
  'Item 9', 'Item 10', 'Item 11', 'Item 12',
])
const addTitle = ref("");
const dialogVisible = ref(false);
const addDesign = async () => {
  addTitle.value = "新建课件"
  dialogVisible.value = true;
  // await addCoursewareDesign({
  //   name: '新的课件设计',
  //   description: '这是一个新的课件设计',
  //   type: 1,
  //   status: 1,
  // })
}

const goEdit = async () => {
  dialogVisible.value = false;
  proxy.$router.push({ path: '/courseware-detail' });
}


</script>

<style scoped lang="scss">
.grid-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 每行四列 */
  gap: 20px; /* 列间距 */
}

.grid-item {
  //text-align: center;
}


.card {
  border-radius: 10px;
  overflow: hidden;
  background-color: #fff;
}

.card-header {
  display: flex;
  gap: 10px;
}

.image-header {
  background-image: url('@/assets/images/coursewareDesign/default-avater.png');
  background-size: cover; /* 背景图片覆盖整个容器 */
  background-repeat: no-repeat; /* 不平铺背景图片 */
  background-position: center; /* 背景图片居中 */
  height: 110px; /* 设置容器高度 */
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  display: flex;
}

.action-btn {
  font-size: 14px;
  background-color: #f5f7fa;
  border-color: #dcdfe6;
  color: #333333;
}

.action-btn:hover {
  background-color: #e4e7ed;
}

.card-body {
  padding: 10px;
  justify-content: flex-start;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-top: 0px;
}

.date-range {
  font-size: 14px;
  color: #888;
}

.instructor {
  font-size: 14px;
  color: #888;
  margin-left: 5px;
}

.thumbnail_6 {
  width: 1vw;
  height: 1vw;
  margin-top: -0.1vw;
}
.add-card{
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #D9E1FF;
  align-items: center;
  justify-content: center;
  display: flex;
}

</style>
