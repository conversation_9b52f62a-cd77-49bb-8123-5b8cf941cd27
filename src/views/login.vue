<template>
  <div class="login-page-con">
    <div class="page flex-col">
      <div class="box_2 flex-col align-center">
        <!-- <img class="background-img" referrerpolicy="no-referrer" src="@/assets/images/background.png" />-->
        <div class="block_5_1 flex-col">
          <div class="group_20_1">
            <img referrerpolicy="referrer" style="width: 205px;height: 54px;" :src="blueLogoUrl" />
            <div class="nav-right-menu">
              <template v-for="(item, index) in menus" :key="index">
                <div
                  :class="[{ 'menu-item': !isLoginPage }, { 'menu-item-ln': isLoginPage }, { active: index === activeMenuIndex }]"
                  @click="handleSelect(item, index)" v-if="isShowItem(item)">
                  <el-badge :is-dot="true" :hidden="index !== 6">
                    <span class="label"> {{ item.menuName }} </span>
                  </el-badge>
                </div>
              </template>
            </div>
            <!--招标结束，删除-->
            <!-- <el-link href="https://www.carsi.edu.cn/" underline="false" class="text_8_1" target="_blank">carsi</el-link> -->
          </div>
          <div class="box_8 flex-col"></div>
          <div class="box_9 flex-col"></div>
          <div class="box_10 flex-col"></div>
          <div class="box_6 flex-row">
            <div class="group_3 flex-col">
              <el-carousel height="617px" style="border-top-left-radius: 26px; border-bottom-left-radius: 26px">
                <el-carousel-item v-for="(image, index) in banners" :key="index">
                  <div class="small justify-center image-container" style="text-align: center; height: 100%">
                    <img :src="image.imageUrl" class="rounded-image" style="max-height: 100%; max-width: 100%"
                      @click="handImageClick(image)" />
                    <!-- <h3 class="small justify-center" text="2xl">{{ index + 1 }}</h3> -->
                  </div>
                </el-carousel-item>
              </el-carousel>
            </div>
            <div class="group_22 flex-col" v-if="!showSecurityVerification">
              <div class="group_23 flex-row" v-if="showBusiness !== 'Retrieve'">
                <img class="image_5 pointer" referrerpolicy="no-referrer" src="@/assets/images/vx.png"
                  @click="handleThumbnailClick" />
              </div>
              <span v-if="
                showBusiness !== 'Retrieve' && showBusiness !== 'register'
              " class="text_11">{{ siteInfo.siteName }}</span>
              <span v-if="showBusiness === 'register'" style="margin-top: -50px"
                class="text_11">{{ siteInfo.siteName }}</span>
              <!-- 二维码扫描-->
              <div v-if="scanCodeToLoginShow">
                <div class="tips-title">打开{{ siteInfo.siteName }}（App）扫码登录</div>
                <div v-if="qr_status === 'NOT_SCAN' || qr_status === 'EXPIRED'" class="qr-img">
                  <!--直接把后端返回的二维码的base64编码作为图片的src值即可显示二维码-->
                  <el-image v-if="qr_status === 'NOT_SCAN'" style="width: 250px; height: 250px; border-radius: 16px"
                    :src="qr_src" fit="cover">
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                    <div slot="placeholder" class="image-slot">
                      加载中
                      <span class="dot">...</span>
                    </div>
                  </el-image>
                  <div v-else style="
                      position: relative;
                      margin: 0 auto;
                      width: 250px;
                      height: 250px;
                      font-size: 16px;
                      color: #f3f3f3;
                    ">
                    <el-image style="width: 100%; height: 100%; border-radius: 16px" :src="loseEfficacyQr"
                      fit="cover" />
                    <div class="overlay">
                      <div class="expired-title">二维码已过期</div>
                      <el-button type="primary" style="margin-top: 20px" width="80px" @click="refreshQr">刷新</el-button>
                    </div>
                  </div>
                </div>
                <div v-else-if="
                  qr_status === 'SCANNED' || qr_status === 'VERIFIED'
                " style="
                    position: relative;
                    margin: 0 auto;
                    width: 250px;
                    height: 250px;
                    font-size: 16px;
                    color: #f3f3f3;
                    margin: 10px 0px 0px 45px;
                  ">
                  <el-image style="width: 100%; height: 100%; border-radius: 16px" :src="loseEfficacyQr" fit="cover" />
                  <div class="overlay">
                    <img style="width: 30%; height: 30%" src="@/assets/icons/svg/match-number.svg" />
                    <div class="expired-title">扫描成功</div>
                    <div class="expired-title">请在手机端确认登录</div>
                  </div>
                </div>
              </div>
              <!-- 二维码扫描-->
              <!--注册-->
              <div v-if="showBusiness === 'register' && !scanCodeToLoginShow" style="margin-top: 30px">
                <span class="text_12">注册</span>
                <div class="form-content" style="width: 310px">
                  <el-input v-model="registerPhone" autocomplete="off" placeholder="请输入手机号" class="input-with-button"
                    prefix-icon="Iphone"></el-input>
                  <el-input v-model="verificationCode" autocomplete="off" placeholder="请输入4位验证码"
                    class="input-with-button">
                    <template #prefix>
                      <img src="@/assets/icons/svg/shield.svg" />
                    </template>
                    <template #suffix>
                      <el-button type="primary" link :disabled="isSendingCode"
                        @click="sendVerificationCode(registerPhone)">
                        <span :class="{ 'gray-text': isSendingCode }">{{ sendCodeText }}</span>
                      </el-button>
                    </template>
                  </el-input>
                  <el-input v-model="registerPassword" autocomplete="new-password"
                    :type="showPassword ? 'text' : 'password'" placeholder="请输入&nbsp;8-14&nbsp;位密码" prefix-icon="lock"
                    class="input-with-button">
                    <template #suffix>
                      <el-icon v-if="showPassword" :size="size" :color="color" @click="showPassword = !showPassword"
                        class="pointer">
                        <View />
                      </el-icon>
                      <el-icon v-else :size="size" :color="color" @click="showPassword = !showPassword" class="pointer">
                        <Hide />
                      </el-icon>
                    </template>
                  </el-input>
                  <el-input v-model="registerConfirmPassword" :type="showPassword ? 'text' : 'password'"
                    placeholder="请再次确认新密码" prefix-icon="lock" class="input-with-button">
                    <template #suffix>
                      <el-icon v-if="showPassword" :size="size" :color="color" @click="showPassword = !showPassword"
                        class="pointer">
                        <View />
                      </el-icon>
                      <el-icon v-else :size="size" :color="color" @click="showPassword = !showPassword" class="pointer">
                        <Hide />
                      </el-icon>
                    </template>
                  </el-input>
                </div>
              </div>
              <!--找回密码-->
              <div v-if="showBusiness === 'Retrieve' && !scanCodeToLoginShow" class="retrieve-group_22 flex-col">
                <el-icon style="font-size: 40px" :size="size" :color="color" @click="showBusiness = 'login'"
                  class="pointer">
                  <Back />
                </el-icon>
                <span class="text_11" style="margin-top: 30px">找回密码</span>
                <div class="form-content">
                  <el-input v-model="retrievePhone" autocomplete="off" placeholder="请输入手机号" class="input-with-button"
                    prefix-icon="Iphone"></el-input>
                  <el-input v-model="verificationCode" autocomplete="off" placeholder="请输入4位验证码"
                    class="input-with-button">
                    <template #prefix>
                      <img src="@/assets/icons/svg/shield.svg" />
                    </template>
                    <template #suffix>
                      <el-button type="primary" link :disabled="isSendingCode"
                        @click="sendVerificationCode(retrievePhone)">
                        <span :class="{ 'gray-text': isSendingCode }">{{ sendCodeText }}</span>
                      </el-button>
                    </template>
                  </el-input>
                  <el-input v-model="retrievePassword" autocomplete="new-password"
                    :type="showPassword ? 'text' : 'password'" placeholder="请输入&nbsp;8-14&nbsp;位密码" prefix-icon="lock"
                    class="input-with-button">
                    <template #suffix>
                      <el-icon v-if="showPassword" :size="size" :color="color" @click="showPassword = !showPassword"
                        class="pointer">
                        <View />
                      </el-icon>
                      <el-icon v-else :size="size" :color="color" @click="showPassword = !showPassword" class="pointer">
                        <Hide />
                      </el-icon>
                    </template>
                  </el-input>
                  <span
                    class="paragraph_1">密码：请输入&nbsp;8-14&nbsp;位，数字/字母以及特殊符号至少包含&nbsp;2&nbsp;<br />种组合，不如允许有空格、中文。</span>
                  <el-input v-model="retrieveConfirmPassword" :type="showPassword ? 'text' : 'password'"
                    placeholder="请再次确认新密码" prefix-icon="lock" class="input-with-button">
                    <template #suffix>
                      <el-icon v-if="showPassword" :size="size" :color="color" @click="showPassword = !showPassword"
                        class="pointer">
                        <View />
                      </el-icon>
                      <el-icon v-else :size="size" :color="color" @click="showPassword = !showPassword" class="pointer">
                        <Hide />
                      </el-icon>
                    </template>
                  </el-input>
                  <el-button :type="retrievePassword !== '' && retrieveConfirmPassword !== '' && verificationCode !== '' && retrievePhone !== '' ? 'primary' : ''
                    " style="margin-top: 60px" @click="retrievePwd" class="full-width-button"
                    :disabled="retrievePassword === '' || retrieveConfirmPassword === '' || verificationCode === '' || retrievePhone === ''">确定
                  </el-button>
                </div>
              </div>
              <!--登录-->
              <div v-if="showBusiness === 'login' && !scanCodeToLoginShow">
                <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="no-border-tabs"
                  style="width: 310px; margin-top: 30px">
                  <el-tab-pane label="验证码登录" name="code">
                    <div class="form-content">
                      <el-input v-model="phone" placeholder="请输入手机号" class="input-with-button"
                        prefix-icon="Iphone"></el-input>
                      <el-input v-model="verificationCode" autocomplete="off" placeholder="请输入4位验证码"
                        class="input-with-button">
                        <template #prefix>
                          <img src="@/assets/icons/svg/shield.svg" />
                        </template>
                        <template #suffix>
                          <el-button type="primary" link :disabled="isSendingCode" @click="sendVerificationCode(phone)">
                            <span :class="{ 'gray-text': isSendingCode }">{{ sendCodeText }}</span>
                          </el-button>
                        </template>
                      </el-input>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="密码登录" name="password">
                    <div class="form-content">
                      <el-input v-model="phone" placeholder="请输入手机号" class="input-with-button"
                        prefix-icon="Iphone"></el-input>
                      <el-input v-model="password" :type="showPassword ? 'text' : 'password'" placeholder="请输入密码"
                        prefix-icon="lock" class="input-with-button">
                        <template #suffix>
                          <el-icon v-if="showPassword" :size="size" :color="color" @click="showPassword = !showPassword"
                            class="pointer">
                            <View />
                          </el-icon>
                          <el-icon v-else :size="size" :color="color" @click="showPassword = !showPassword"
                            class="pointer">
                            <Hide />
                          </el-icon>
                        </template>
                      </el-input>
                    </div>
                  </el-tab-pane>
                  <div class="remember-me">
                    <el-checkbox v-model="rememberMe" v-if="activeTab === 'password'" @click="handleRememberMe"><span
                        class="bold-text">记住我</span></el-checkbox>
                    <el-button type="text" @click="forgotPassword" v-if="activeTab === 'password'"
                      class="forget-password">忘记密码</el-button>
                  </div>
                </el-tabs>
              </div>
              <div v-if="showBusiness !== 'Retrieve' && !scanCodeToLoginShow" style="width: 310px">
                <div class="fixed-buttons">
                  <el-button v-if="showBusiness !== 'register'" :loading="loading" :type="!loginBtn ? 'primary' : ''"
                    @click="handleLogin" class="full-width-button" :disabled="loginBtn">
                    <span v-if="!loading">登 录</span>
                    <span v-else>登 录 中...</span>
                  </el-button>
                  <el-button v-if="showBusiness === 'register'" :loading="loading" :type="registerPhone !== '' && registerPassword !== '' && registerConfirmPassword !== '' && verificationCode !== '' ? 'primary' : ''
                    " @click="handleRegisterLogin" class="full-width-button"
                    :disabled="registerPhone === '' || registerPassword === '' || registerConfirmPassword === '' || verificationCode === ''">
                    <span v-if="!loading">{{ showBusiness === 'register' ? '注 册' : '登 录' }}</span>
                    <span v-else>{{ showBusiness === 'register' ? '注 册 中...' : '登 录 中...' }}</span>
                  </el-button>
                  <div v-if="showBusiness === 'login'" class="agreement">
                    <span style="font-size: 13px; font-weight: 100">没有账号，前往</span>
                    <el-button type="text" @click="registerLogin">注册</el-button>
                  </div>
                  <div v-else class="agreement">
                    <span style="font-size: 13px; font-weight: 100">有账号，前往</span>
                    <el-button type="text" @click="showBusiness = 'login'">登录</el-button>
                  </div>
                  <div v-if="showBusiness === 'login'" class="register-link">
                    <el-tooltip class="box-item" effect="dark" content="请先同意用户协议和隐私政策" placement="top-end"
                      v-model:visible="showAgreementPopover">
                      <el-checkbox v-model="agreeTerms" @click="visible = !showAgreementPopover">登录即代表同意
                        <el-link type="primary" href="/user-agreement" style="margin-bottom: 3px"
                          target="_blank">《用户协议》</el-link>和
                        <el-link type="primary" href="/privacy-policy" style="margin-bottom: 3px"
                          target="_blank">《隐私政策》</el-link>
                      </el-checkbox>
                    </el-tooltip>
                  </div>
                  <div v-else style="margin-top: 20px; text-align: center">
                    <el-tooltip class="box-item" effect="dark" content="请先同意用户协议和隐私政策" placement="top-end"
                      v-model:visible="showAgreementPopover">
                      <el-checkbox v-model="agreeTerms" @click="visible = !showAgreementPopover">登录即代表同意
                        <el-link type="primary" href="/user-agreement" style="margin-bottom: 3px"
                          target="_blank">《用户协议》</el-link>和
                        <el-link type="primary" href="/privacy-policy" style="margin-bottom: 3px"
                          target="_blank">《隐私政策》</el-link>
                      </el-checkbox>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
            <div class="group_22 flex-col" v-else>
              <div class="slide-verify-block_2 flex-col justify-between">
                <el-icon style="font-size: 25px" :size="size" :color="color"
                  @click="showSecurityVerification = !showSecurityVerification" class="pointer">
                  <Back />
                </el-icon>
                <div class="slide-verify-box_4 flex-col">
                  <span class="slide-verify-text_11">请完成下列验证后继续</span>
                  <div class="slide-verify-box_5 flex-col">
                    <slide-verify :width="300" :height="150" :imgs="slideVerifyImages" sliderText="按住左边按钮拖动完成上方拼图"
                      @success="onSuccess" @fail="onFail" @refresh="onRefresh" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style src="@/assets/styles/common.css" />
<style src="@/assets/styles/index.css" />
<script setup>
import { defineComponent, ref, onMounted } from "vue";
import { View, Hide } from "@element-plus/icons-vue"; // 引入图标组件
import SlideVerify from "vue3-slide-verify";
import "vue3-slide-verify/dist/style.css";
import { ElNotification, ElMessageBox, ElMessage } from "element-plus";
import {
  sendAliyun,
  login,
  register,
  forgotPasswordEducation,
  createQrCode,
  queryIsScannedOrVerified,
} from "@/api/login";
import useUserStore from "@/store/modules/user";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import Cookies from "js-cookie";
// import { encryptData } from '@/utils/encrypt.js';
import useSiteStore from "@/store/modules/site";
import { getBanners } from "@/api/basic/home.js";
const isLoginPage = ref(false);
const loseEfficacyQr = ref("");
const qrLogUrl = ref("");
const banners = ref([]);
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();
const menus = computed(() => useSiteStore().menus);
const { proxy } = getCurrentInstance();
const activeMenuIndex = ref(-1);
const loginForm = ref({
  username: '',
  password: '',
  rememberMe: false,
  code: '',
  uuid: '',
});
const userName = ref('');
const msg = ref('');
const block = ref();

const activeTab = ref('password');
const phone = ref('');
const verificationCode = ref('');
const password = ref('');
const showPassword = ref(false);
const rememberMe = ref(false);
const agreeTerms = ref(false);
const size = ref(16); // 图标大小
const color = ref('#606266'); // 图标颜色
// 找回密码
const retrievePhone = ref('');
const retrievePassword = ref('');
const retrieveConfirmPassword = ref('');
// 注册
const registerPhone = ref('');
const registerPassword = ref('');
const registerConfirmPassword = ref('');
const redirect = ref(undefined);

const isSendingCode = ref(false);
const sendCodeText = ref('获取验证码');
const countdown = ref(60);
const maxCodeAttempts = 5;
const codeAttempts = ref(0);
const showSecurityVerification = ref(false);
const showAgreementPopover = ref(false);
const showBusiness = ref('login');
const loginBtn = ref(true);
const verificationPhone = ref('');
const loading = ref(false);
const captchaErrorCount = ref(0);
const retrievePwdCheckCount = ref(0);
const registerCheckCount = ref(0);
const scanCodeToLoginShow = ref(false);
const images = ref([
  new URL('@/assets/images/teachingResources.png', import.meta.url).href,
  // 其他图片路径
]);
const slideVerifyImages = ref([
  new URL('@/assets/images/img.jpg', import.meta.url).href,
  new URL('@/assets/images/img1.jpg', import.meta.url).href,
  new URL('@/assets/images/img2.jpg', import.meta.url).href,
  new URL('@/assets/images/img3.jpg', import.meta.url).href,
  new URL('@/assets/images/img4.jpg', import.meta.url).href,
  new URL('@/assets/images/img5.jpg', import.meta.url).href,
  // 其他图片路径
]);
// 随机背景图片 URL
const backgroundImage = ref('');
const isShowItem = (item) => {
  return item.key !== 7 || (userName.value && item.key === 7);
};
const handleSelect = (item, index) => {
  activeMenuIndex.value = index;
  // 未登录跳转登录页
  if (item.menuName === '教学平台') {
  return ElNotification({
      title: "错误",
      message: "请您先进行登录,再进入教学平台",
      type: "error",
    });
  }
  // 内部链接
  if (item.routeType == 1) {
    if (item.menuRoute || item.menuRoute === 0) {
      proxy.$router.push({ path: item.menuRoute });
    } else {
      proxy.$router.push({ path: '/user' });
    }
    // 外部链接
  } else {
    let url = item.menuRoute;
    if (url.indexOf('http') == -1) {
      url = 'http://' + item.menuRoute;
    }
    window.open(url, '_blank');
  }
};
// 获取随机图片
const fetchRandomImage = async () => {
  try {
    const response = await fetch('https://api.unsplash.com/300x150/?book,library');
    if (response.ok) {
      backgroundImage.value = response.url;
    } else {
      console.error('获取图片失败:', response.status);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
};
function doGetBanners () {
  getBanners({ bannerPosition: 2, device: 'PC' })
    .then((res) => {
      if (res.code == 200) {
        banners.value = res.data;
        // sessionStorage.setItem(BANNER_KEY, JSON.stringify(banners.value));
      }
    })
    .catch((error) => { });
}
const onSuccess = async () => {
  msg.value = '验证通过';
  showSecurityVerification.value = false;
  if (codeAttempts.value >= maxCodeAttempts) {
    ElNotification({
      title: '操作提示',
      message: '超过最大发送次数，请稍后再试',
      type: 'error',
    });
    return;
  }
  if (isSendingCode.value) return;
  isSendingCode.value = true;
  sendCodeText.value = `${countdown.value}秒后重新发送`;
  let timer = setInterval(() => {
    countdown.value--;
    sendCodeText.value = `${countdown.value}秒后重新发送`;
    if (countdown.value <= 0) {
      clearInterval(timer);
      isSendingCode.value = false;
      sendCodeText.value = '获取验证码';
      countdown.value = 60;
    }
  }, 1000);
  codeAttempts.value++;
  // 发送验证码的API请求
  // 获取后端的公钥
  //  const publicKey = useSiteStore().publicKey;
  // 使用公钥加密数据
  // const { encryptedData } = await encryptData(verificationPhone.value, publicKey);
  // if (!encryptedData) {
  //   console.error('数据加密失败');
  //   return;
  // }
  await sendAliyun({ phone: verificationPhone.value }).then((res) => {
    if (res.code === 200) {
      ElNotification({
        title: '操作提示',
        message: '验证码已发送，请查收短信',
        type: 'success',
      });
    }
  });
};

const onFail = () => {
  msg.value = '验证不通过';
  // 刷新
  block.value?.refresh();
};

const onRefresh = () => {
  // fetchRandomImage();
};
const onAgain = () => {
  msg.value = '请重试！';
  // 刷新
  block.value?.refresh();
};

const handleTabClick = (tab) => {
  verificationCode.value = '';
  password.value = '';
  loginBtn.value = true;
  // 处理标签切换事件
};

const validatePhone = (phone) => {
  const phoneRegex = /^1\d{10}$/;
  if (!phoneRegex.test(phone)) {
    ElNotification({
      title: '操作提示',
      message: '手机号格式不正确',
      type: 'error',
    });
    return false;
  }
  return true;
};
// 校验函数
const validateInput = (inputValue) => {
  const value = inputValue.trim(); // 去除前后空格
  const lengthValid = value.length >= 8 && value.length <= 14;
  const noChineseOrSpace = !/[一-龥\s]/.test(value); // 不允许中文和空格
  const hasDigit = /\d/.test(value);
  const hasLetter = /[a-zA-Z]/.test(value);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);

  const typesCount = [hasDigit, hasLetter, hasSpecialChar].filter(Boolean).length;

  if (!lengthValid) {
    ElNotification({
      title: '操作提示',
      message: '请输入8-14位密码',
      type: 'error',
    });
    return false;
  } else if (!noChineseOrSpace) {
    ElNotification({
      title: '操作提示',
      message: '不允许输入中文和空格',
      type: 'error',
    });
    return false;
  } else if (typesCount < 2) {
    ElNotification({
      title: '操作提示',
      message: '至少包含数字、字母和特殊符号中的两种组合',
      type: 'error',
    });
    return false;
  }
  return true;
};
const sendVerificationCode = (phone) => {
  if (!phone) {
    ElNotification({
      title: '操作提示',
      message: '请输入手机号',
      type: 'error',
    });
    return;
  }
  if (validatePhone(phone)) {
    verificationPhone.value = phone;
    showSecurityVerification.value = true;
  }
};
watch(
  route,
  (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect;
  },
  { immediate: true }
);
watch([activeTab, verificationCode, password], () => {
  if (activeTab.value === 'code' && verificationCode.value !== '') {
    loginBtn.value = false;
  } else if (activeTab.value === 'password' && password.value !== '') {
    loginBtn.value = false;
  } else {
    loginBtn.value = true;
  }
});
const navigateToRegister = () => {
  // 跳转到注册页面
};

const navigateToUserAgreement = () => {
  // 跳转到用户协议页面
};

const navigateToPrivacyPolicy = () => {
  // 跳转到隐私政策页面
};
const handImageClick = (item) => {
  if (item.urlType === 2) {
    window.open(item.jumpUrl, '_blank');
  } else {
    router.push({ path: item.jumpUrl });
  }
};

const handleLogin = async () => {
  if (!agreeTerms.value) {
    showAgreementPopover.value = true;
    return;
  }
  if (activeTab.value === 'code') {
    if (!phone.value || !verificationCode.value) {
      ElNotification({
        title: '操作提示',
        message: '请输入手机号和验证码',
        type: 'error',
      });

      return;
    }
    if (!validatePhone(phone.value)) return;
    captchaErrorCount.value += 1;
    if (captchaErrorCount.value > 5) {
      ElMessageBox.alert('输入错误次数过多，请重新获取验证码', '提示', {
        confirmButtonText: 'OK',
        type: 'warning',
        showClose: false,
        callback: (action) => {
          if (action === 'confirm') {
            sendVerificationCode(phone.value);
          }
        },
      });
      captchaErrorCount.value = 0;
      return;
    }
    // 验证手机号和验证码
    const codeLoginForm = {
      phoneNumber: phone.value,
      code: verificationCode.value,
    };
    // 登录请求
    loading.value = true;
    userStore
      .codeLogin(codeLoginForm)
      .then(() => {
        router.push({ path: redirect.value || '/' });
      })
      .catch((e) => {
        loading.value = false;
        if (e.code === 7001) {
          loading.value = false;
          verificationCode.value = '';
          ElNotification({
            title: '操作提示',
            message: '验证码输入错误（' + captchaErrorCount.value + '/5）',
            type: 'error',
          });
        } else {
          loading.value = false;
          verificationCode.value = '';
          ElNotification({
            title: '操作提示',
            message: e.msg,
            type: 'error',
          });
        }
      });
  } else if (activeTab.value === 'password') {
    if (!phone.value || !password.value) {
      ElNotification({
        title: '操作提示',
        message: '请输入账号和密码',
        type: 'error',
      });
      return;
    }
    if (!validatePhone(phone.value)) return;
    // 验证账号和密码
    // 登录请求
    // 调用action的登录方法
    loginForm.value.username = phone.value;
    loginForm.value.password = password.value;
    // 获取后端的公钥
    // const publicKey = useSiteStore().publicKey;
    // if (!publicKey) {
    //   console.error('无法获取公钥');
    //   return;
    // }

    // 使用公钥加密数据
    // const { encryptedData } = await encryptData(loginForm.value, publicKey);

    // if (!encryptedData) {
    //   console.error('数据加密失败');
    //   return;
    // }

    userStore
      .login({ loginForm: JSON.stringify(loginForm.value) })
      .then(() => {
        router.push({ path: redirect.value || '/' });
      })
      .catch((e) => {
        loading.value = false;
        ElNotification({
          title: '操作提示',
          message: e.msg,
          type: 'error',
        });
      });
  }
};
const verifySecurity = () => {
  // 安全验证逻辑
  showSecurityVerification.value = false;
};
let timer = null;
const qr_status = ref("");
const qr_src = ref("");
const qrUrl = ref("");
const handleThumbnailClick = () => {
  scanCodeToLoginShow.value = !scanCodeToLoginShow.value;
  //清除之前设置的长轮询
  clearInterval(timer);
  if (scanCodeToLoginShow.value) {
    //设置二维码初始状态为未扫描
    qr_status.value = "NOT_SCAN";
    //清空二维码url
    qr_src.value = "";
    createQrCode(qrLogUrl.value)
      .then((res) => {
        qrUrl.value = res.data;
        qr_src.value = `data:image/png;base64,${res.data}`;
        loseEfficacyQr.value = `data:image/png;base64,${res.data}`;
        //二维码请求成功后开始长轮询查询二维码状态
        checkQr();
      })
      .catch((err) => {
        console.log(err);
      });
  }
};
const refreshQr = () => {
  //清除之前设置的长轮询
  clearInterval(timer);
  //设置二维码初始状态为未扫描
  qr_status.value = "NOT_SCAN";
  //清空二维码url
  qr_src.value = "";
  createQrCode(qrLogUrl.value)
    .then((res) => {
      qrUrl.value = res.data;
      qr_src.value = `data:image/png;base64,${res.data}`;
      loseEfficacyQr.value = `data:image/png;base64,${res.data}`;
      //二维码请求成功后开始长轮询查询二维码状态
      checkQr();
    })
    .catch((err) => {
      console.log(err);
    });
};
//轮询检查二维码是否过期或者是否被扫描
const checkQr = () => {
  timer = setInterval(() => {
    // const data = { img: qr_src.value };
    console.log("qr_src.value", qr_src.value);
    queryIsScannedOrVerified(qrUrl.value)
      .then((res) => {
        if (res.code === 200) {
          if (Object.keys(res.data).length === 0) {
            qr_status.value = "EXPIRED";
            clearInterval(timer);
          } else if (res.data.status === "SCANNED") {
            qr_status.value = "SCANNED";
          } else if (res.data.status === "VERIFIED") {
            clearInterval(timer);
            userStore.scanCodeToLogin(res.data.access_token).then(() => {
              console.log("res.data.access_token", res.data.access_token);
              router.push({ path: redirect.value || "/" });
            });
          }
        }
      })
      .catch((err) => {
        console.log(err);
      });
  }, 1500);
};

const handleRememberMe = () => {
  // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
  if (!rememberMe.value) {
    Cookies.set('phone', phone.value, { expires: 30 });
    Cookies.set('password', encrypt(password.value), {
      expires: 30,
    });
    Cookies.set('rememberMe', !rememberMe.value, { expires: 30 });
  } else {
    // 否则移除
    Cookies.remove('phone');
    Cookies.remove('password');
    Cookies.remove('rememberMe');
  }
};
const forgotPassword = () => {
  showBusiness.value = 'Retrieve';
  retrievePhone.value = '';
  retrievePassword.value = '';
  retrieveConfirmPassword.value = '';
  verificationCode.value = '';
};
const registerLogin = () => {
  showBusiness.value = 'register';
  registerPhone.value = '';
  registerPassword.value = '';
  registerConfirmPassword.value = '';
  verificationCode.value = '';
};
const handleRegisterLogin = () => {
  if (!agreeTerms.value) {
    showAgreementPopover.value = true;
    return;
  }
  if (!registerPhone.value || !verificationCode.value) {
    ElNotification({
      title: '操作提示',
      message: '请输入手机号和验证码',
      type: 'error',
    });

    return;
  }
  if (!validatePhone(registerPhone.value)) return;
  registerCheckCount.value += 1;
  if (registerCheckCount.value > 5) {
    ElMessageBox.alert('输入错误次数过多，请重新获取验证码', '提示', {
      confirmButtonText: 'OK',
      type: 'warning',
      showClose: false,
      callback: (action) => {
        if (action === 'confirm') {
          sendVerificationCode(registerPhone.value);
        }
      },
    });
    registerCheckCount.value = 0;
    return;
  }
  if (registerPassword.value === registerConfirmPassword.value) {
    if (!validateInput(registerPassword.value)) return;
    const registerForm = {
      username: registerPhone.value,
      password: registerPassword.value,
      phoneNumber: registerPhone.value,
      code: verificationCode.value,
    };
    loading.value = true;
    register(registerForm)
      .then((res) => {
        if (res.code === 200) {
          loading.value = false;
          ElNotification({
            title: '操作提示',
            message: '注册成功',
            type: 'success',
          });
          const loginForm = {
            username: registerPhone.value,
            password: registerPassword.value,
          };
          userStore
            .login({ loginForm: JSON.stringify(loginForm) })
            .then(() => {
              // 登录成功后跳转到首页
              router.push({ path: redirect.value || '/' });
            })
            .catch((e) => {
              loading.value = false;
              ElNotification({
                title: '操作提示',
                message: e.msg,
                type: 'error',
              });
            });
        } else if (res.code === 7001) {
          loading.value = false;
          verificationCode.value = '';
          ElNotification({
            title: '操作提示',
            message: '验证码输入错误（' + registerCheckCount.value + '/5）',
            type: 'error',
          });
        } else {
          loading.value = false;
          verificationCode.value = '';
          ElNotification({
            title: '操作提示',
            message: res.msg,
            type: 'error',
          });
        }
      })
      .catch((e) => {
        ElNotification({
          title: '操作提示',
          message: e.msg,
          type: 'error',
        });
      });
  } else {
    ElNotification({
      title: '操作提示',
      message: '密码不一致重新输入',
      type: 'error',
    });
    registerConfirmPassword.value = '';
    return;
  }
};
const retrievePwd = () => {
  if (!retrievePhone.value || !verificationCode.value) {
    ElNotification({
      title: '操作提示',
      message: '请输入手机号和验证码',
      type: 'error',
    });

    return;
  }
  if (!validatePhone(retrievePhone.value)) return;
  retrievePwdCheckCount.value += 1;
  if (retrievePwdCheckCount.value > 5) {
    ElMessageBox.alert('输入错误次数过多，请重新获取验证码', '提示', {
      confirmButtonText: 'OK',
      type: 'warning',
      showClose: false,
      callback: (action) => {
        if (action === 'confirm') {
          sendVerificationCode(retrievePhone.value);
        }
      },
    });
    retrievePwdCheckCount.value = 0;
    return;
  }
  if (retrievePassword.value === retrieveConfirmPassword.value) {
    const forgotPasswordEducationForm = {
      username: retrievePhone.value,
      phoneNumber: retrievePhone.value,
      password: retrievePassword.value,
      code: verificationCode.value,
      delFlag: 0
    };
    loading.value = true;
    forgotPasswordEducation(forgotPasswordEducationForm).then((res) => {
      loading.value = false;
      if (res.code === 200) {
        router.push({ path: redirect.value || '/' });
        ElNotification({
          title: '操作提示',
          message: '找回密码成功',
          type: 'success',
        });
      } else if (res.code === 7001) {
        loading.value = false;
        verificationCode.value = '';
        ElNotification({
          title: '操作提示',
          message: '验证码输入错误（' + retrievePwdCheckCount.value + '/5）',
          type: 'error',
        });
      } else {
        loading.value = false;
        verificationCode.value = '';
        ElNotification({
          title: '操作提示',
          message: res.msg,
          type: 'error',
        });
      }
    });
  } else {
    ElNotification({
      title: '操作提示',
      message: '密码不一致重新输入',
      type: 'error',
    });
    retrieveConfirmPassword.value = '';
    return;
  }
};
function getCookie () {
  phone.value = Cookies.get('phone');
  password.value = decrypt(Cookies.get('password'));
  rememberMe.value = Boolean(Cookies.get('rememberMe'));
}
const siteInfo = ref({});
const blueLogoUrl = ref('');
import { getSiteInfoById } from '@/api/basic/home.js';
import { SiteId } from '@/utils/constant';
onMounted(async () => {
  doGetBanners();
  getCookie();
  await getSiteInfoById(SiteId)
    .then((res) => {
      if (res.code === 200) { }
      siteInfo.value = res.data;
    });
  console.log('siteInfo.value ', siteInfo.value);
  qrLogUrl.value = siteInfo.value.qrLogoUrl;
  blueLogoUrl.value = siteInfo.value.blueLogoUrl;
  const cookies = document.cookie.split("; ");
  for (const cookie of cookies) {
    const [key, value] = cookie.split('=');
    if (key === 'password') {
      if (decodeURIComponent(value)) {
        loginBtn.value = false;
      }
      break;
    }
  }
  if (!password.value) {
    password.value = '';
  }
});
</script>
<style lang="scss" scoped>
@import '@/assets/styles/index';

.nav-right-menu {
  height: 48px;
  margin-left: 60px;
  @extend .base-flex;
  justify-content: space-between;
  align-items: center;

  .menu-item {
    height: 30px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #0966b4;
    text-align: justify;
    font-style: normal;
    padding: 0 30px;
    cursor: pointer;
    @extend .base-flex;
    justify-content: center;
    align-items: center;
  }

  .menu-item:hover {
    opacity: 0.7;
  }

  .menu-item-ln {
    height: 30px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    text-align: justify;
    font-style: normal;
    padding: 0 12px;
    cursor: pointer;
    @extend .base-flex;
    justify-content: center;
    align-items: center;
  }

  .menu-item-ln:hover {
    opacity: 0.7;
  }

  .active {
    border-bottom: 1px solid white;
  }
}

.outer-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  /* 使容器高度占满整个视口 */
}

.login-container {
  display: flex;
  width: 80%;
  /* 根据需要调整宽度 */
  height: 80%;
  /* 根据需要调整高度 */
  background-color: white;
  box-shadow: 0 0px 8px 8px rgba(0, 0, 0, 0.1);
  /* 添加阴影效果 */
  border-radius: 8px;
}

.left-side {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.right-side {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.top-right {
  text-align: right;
  margin-bottom: 20px;
}

.qrcode-img {
  width: 100px;
  /* 根据需要调整图片大小 */
  height: 100px;
  /* 根据需要调整图片大小 */
}

.form-content {
  width: 100%;
  margin-bottom: 30px;
}

.input-with-button {
  margin-top: 30px;
  height: 46px;
}

.remember-me {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: -35px;
  height: 40px;
}

.fixed-buttons {
  margin-top: auto;

  /* 确保按钮和协议部分始终在底部 */
  :deep(.el-button.is-disabled) {
    background-color: #e1e1e1;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 18px;
    color: #ffffff;
  }
}

.full-width-button {
  width: 100%;
  height: 48px;
  line-height: 48px;
  text-align: center;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 600;
  font-size: 18px;
  color: #ffffff;
}

/* 确保按钮和协议部分始终在底部  */
:deep(.el-button.is-disabled) {
  background-color: #e1e1e1;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 600;
  font-size: 18px;
  color: #ffffff;
}

.agreement {
  margin-top: 5px;
  text-align: center;

  :deep(.el-button--text) {
    color: #0966b4;
  }
}

.register-link {
  margin-top: 25px;
  text-align: center;
}

:deep(.el-link) {
  color: #0966b4 !important;
}

.pointer {
  cursor: pointer;
}

.no-border-tabs .el-tabs__header .el-tabs__nav-wrap::after {
  display: none;
}

:deep(.el-tabs__item.is-active, .el-tabs__item:hover) {
  color: #0966b4 !important;
}

.forget-password {
  color: #909399;
  /* 灰色 */
}

.gray-text {
  color: #909399;
  /* 灰色 */
}

.custom-popper {
  background-color: black !important;
  color: white !important;
}

.title {
  width: 144px;
  height: 28px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 600;
  font-size: 24px;
  color: #333333;
  line-height: 28px;
  text-align: justify;
  font-style: normal;
}

:deep(.el-tabs__item) {
  font-family:
    PingFangSC,
    PingFang SC;
  font-size: 18px;
  line-height: 18px;
  text-align: justify;
  font-style: normal;
}

.demonstration {
  color: var(--el-text-color-secondary);
}

.el-carousel__item h3 {
  color: #475669;
  opacity: 0.75;
  line-height: 150px;
  margin: 0;
  text-align: center;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.login {
  position: relative;
  overflow: hidden;
  background-color: #5fd05d;
  min-height: 100vh;

  /* 设置最小高度为视口高度的100% */
  .cover {
    width: fit-content;
    background-color: aliceblue;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
  }
}

.paragraph_1 {
  width: 320px;
  height: 40px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 12px;
  font-weight: normal;
  text-align: justify;
  line-height: 20px;
  margin: 24px 0 0 1px;
}

.text_carsi {
  font-size: 20px;
  font-weight: bold;
  color: rgb(0 139 255);
  cursor: pointer;
}

/*.text_8 {
  width: 63px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 14px;
  margin: 18px 0 0 40px;
}*/
/* 新增样式 */
.image-container {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  overflow: hidden;
  /* 关键属性：裁剪超出圆角部分 */
  height: 100%;
}

.rounded-image {
  width: 100%;
  height: 100%;
  // transition: transform 0.3s ease; /* 可选：添加缩放动画 */
}

// /* 如果希望鼠标悬停时有缩放效果 */
// .rounded-image:hover {
//   transform: scale(1.05);
// }
.qr-img {
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  margin: 10px 80px 10px 25px;

  .expired-title {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .el-button {
    width: 50%;
    margin-top: 20px;
    max-width: 200px;
  }

  .fresh {
    margin-top: 20px;
    text-align: center;
    margin-left: -60px;

    .el-link {
      color: #0966b4;
      font-size: 14px;

      i {
        margin-right: 5px;
      }
    }
  }
}

.scand {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .scand-icon {
    font-size: 40px;
    color: #67c23a;
    margin-bottom: 20px;
  }

  .scan-title {
    font-size: 18px;
    color: #303133;
    margin-bottom: 10px;
  }

  .scan-h1 {
    font-size: 14px;
    color: #606266;
  }
}

.image-slot {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 14px;

  .dot {
    animation: blink 1.4s infinite;
  }
}

@keyframes blink {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.tips-title {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #333333;
  line-height: 16px;
  text-align: justify;
  font-style: normal;
  margin: 20px 0px 0px 44px;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  /* 半透明黑色背景 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  border-radius: 16px;
}
</style>
