<template>
  <div class="simplified-reader-wrapper __reader__" :reader-theme="store.theme" rer="reader">
    <Header :messageTag="false" :show-message-hint="false" :simple-preview-mode="true" :show-reading-progress="false" :show-ai-assistent="false" :show-book-lines="false" :show-book-marks="false" :show-return-to-home="false" :showTourGuide="false" :show-search="false" />
    <div class="reader-main">
      <div class="content">
        <Menus ref="menusRef" :showRelatedBooks="false" :show-knowledge-graph="false" />
        <main
          class="content-pages __hidden-scroll__"
          :style="store.styleSetting"
          :id="PAGE_ITEMS_CONTAINER_ID">
          <reflowable-layout v-if="store.pageFlippingMethod === 'y'" :has-book-mark="false" />
          <folio-simulation v-else-if="store.pageFlippingMethod === 'r'" :has-book-mark="false" />
        </main>
      </div>
      <imagePreview></imagePreview>
    </div>
  </div>
</template>

<script setup>
import Header from "./sub/Header.vue";
import useReader from '@/store/modules/reader'
import { onMounted, ref, watch, onBeforeUnmount, getCurrentInstance, provide } from 'vue'
import Menus from './sub/Menus.vue'
import imagePreview from '@/components/galleryImagePreview/index.vue'
import ReflowableLayout from './sub/Pages/ReflowableLayout.vue'
import FolioSimulation from './sub/Pages/FolioSimulation.vue'
import {
  PAGE_ITEMS_CONTAINER_ID,
  generatePaidPageContent,
  hasAccessToReadChapter,
} from '@/utils/reader'
import {getChaptersSimple} from '@/api/book/reader';
import { ElMessage, ElMessageBox } from 'element-plus'

provide('simplifiedReadingMode', true)
const { proxy } = getCurrentInstance();
const store = useReader();
const props = defineProps({
  initChapterId: {
    type: String,
    default: ''
  },
  initCataId: {
    type: String,
    default: ''
  }
})
const menusRef = ref(null)
watch(
  () => store.styleSetting['line-height'],
  (newVal, oldVal) => {
    var element = document.getElementById(PAGE_ITEMS_CONTAINER_ID)
    var pTags = element.querySelectorAll('p')
    // console.log(pTags)
    pTags.forEach(function (pTag) {
      pTag.style.lineHeight = store.styleSetting['line-height']
    })
  },
  { deep: true }
)

onMounted(() => {
  const bookId = proxy.$route.query.k;
  const chapterId = proxy.$route.query.cid;
  const fromType = proxy.$route.query.fromType;

  if (!bookId || !chapterId) {
    ElMessage.error('教材id和章节id不可以为空')
  }
  store.setCommonData({
    bookId
  })
  getChapterList(bookId, chapterId,fromType).then(() => {
    store.setSimplifiedMode(true);
    store.jumpToChapter(chapterId, 1)
  })
  document.title = "章节预览"
})

onBeforeUnmount(() => {
  ElMessageBox.close()
  store.deauthorizeToTheBook()
})

// 加载目录信息
function getChapterList(bookId, chapterId,fromType) {
  let chapterInfoArray = []
  if (!fromType){
    fromType = 1;
  }
  return getChaptersSimple(bookId,fromType).then(res => {
    if (res.code === 200) {
      chapterInfoArray = res.data || []
      chapterInfoArray = chapterInfoArray.filter(chapterItemData => chapterItemData.chapterId === chapterId)
      let pagesCnt = 0
      chapterInfoArray.forEach(chapterInfo => {
        chapterInfo.catalogId = chapterInfo.chapterId
        chapterInfo.title = chapterInfo.chapterName || ''
        let chapterTotalPages = chapterInfo.chapterTotalPages || '1'
        chapterTotalPages = Number.isNaN(Number(chapterTotalPages)) ? 1 : Number(chapterTotalPages)
        chapterInfo.hasAccessToChapter = hasAccessToReadChapter(chapterInfo)
        chapterInfo.totalPages = chapterInfo.hasAccessToChapter ? chapterTotalPages : 1
        chapterInfo.children = chapterInfo.hasAccessToChapter ? chapterInfo.catalogs || [] : []

        if (!chapterInfo.hasAccessToChapter) {
          chapterInfo.chapterContent = generatePaidPageContent()
        }
        pagesCnt += chapterTotalPages
        delete chapterInfo.catalogs
      })
      store.setChaptersData(chapterInfoArray, pagesCnt)
    } else {
      proxy.$modal.msgError(res.msg)
    }
  })
}
</script>
<style lang="scss" scoped>
@import "./sub/readerTheme.scss";
.__reader__ {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: var(--pageBackgroundColor);
  .reader-main {
    width: 100%;
    height: calc(100% - 48px);
  }
}
.content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  .content-pages {
    width: 100%;
    height: 100%;
    min-height: 800px;
    overflow-y: auto;
  }
}
.simplified-reader-wrapper {
  .Bookmark {
    display: none;
  }
}
</style>