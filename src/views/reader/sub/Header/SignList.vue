<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2025-01-17 14:19:53
 * @LastEditTime: 2025-01-20 11:35:52
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Header\SignList.vue
 * @Description 标记列表
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<style lang="scss" scoped>
.BookmarkList {
  width: 350px;
  & > nav {
    font-weight: 400;
    font-size: 16px;
    color: var(--fontColor);
    display: flex;
    justify-content: space-between;
  }
  & > header {
    width: 100%;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: var(--fontColor);
    margin: 18px 0 10px 0;
    & > section {
      cursor: pointer;
      margin: 0 4px;
    }
    & > section:nth-of-type(2),
    & > section:nth-of-type(3),
    & > section:nth-of-type(4) {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      background: #ffc560;
    }

    & > section:nth-of-type(3) {
      background: #8bb4f0;
    }
    & > section:nth-of-type(4) {
      background: #f0828a;
    }
  }
  & > main {
    overflow-y: auto;
    height: calc(100vh - 150px);
    .main-item {
      cursor: pointer;
      min-height: 40px;
      border-left: 3px solid;
      background-color: var(--modelBackgroundColor);
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      .left {
        height: 100%;
        width: 3px;
        border-radius: 3px;
        margin-right: 7px;
      }
      .right {
        flex: 1;
        display: flex;
        justify-content: space-between;
        flex-direction: column;

        .right-content {
          font-size: 12px;
          line-height: 17px;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }
        .right-footer {
          color: #999999;
          display: flex;
          justify-content: space-between;
          & > span {
            font-size: 12px;
            line-height: 17px;
            height: 17px;
          }
          .icon {
            cursor: pointer;
            transition: 0.3s;
            &:hover {
              color: var(--hoverfont);
            }
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="BookmarkList">
    <nav @click="bookExport">
      <span>标记列表</span>
      <el-button link
        size="small">导出&nbsp;<svg-icon
          iconClass="export" /></el-button>
    </nav>
    <header>
      颜色：
      <section
        @click="getLineStyle(null)"
        :style="!lineColor ? 'color:var(--hoverfont)' : ''">
        全部
      </section>
      <section
        @click="getLineStyle('#FFC560')"
        :style="lineColor === '#FFC560' ? 'border: 1px solid #0966B4;' : ''">
      </section>
      <section
        @click="getLineStyle('#8BB4F0')"
        :style="lineColor === '#8BB4F0' ? 'border: 1px solid #0966B4;' : ''">
      </section>
      <section
        @click="getLineStyle('#F0828A')"
        :style="lineColor === '#F0828A' ? 'border: 1px solid #0966B4;' : ''">
      </section>
    </header>
    <main
      class="__hidden-scroll__">
      <div class="main-item"
        v-for="ele in data"
        :key="ele.lineId"
        @click="jumpTo(ele)"
        :style="'border-color:' + ele.color">
        <div class="left">
        </div>
        <div class="right">
          <div
            class="right-content"
            :title="ele.word"
            style="color: #999999">
            {{
              ele.word.length > 40 ? ele.word.slice(0, 40) + "..." : ele.word
            }}
          </div>
          <div
            class="right-footer">
            <span>{{ ele.createTime }}</span>
            <el-icon
              class="icon"
              @click.stop="delBookLine(ele)">
              <Delete />
            </el-icon>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import useReader from '@/store/modules/reader'
import { generateTextIdWithFromId } from '@/views/reader/sub/Pages/ParagraphTool'
import { getBookLine, deleteBookLine, exportBookMark } from '@/api/book/reader'
import { highlightKeyWordSmooth } from '@/utils/reader'
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
import { blobValidate } from '@/utils/dutp'
const store = useReader()
const data = ref([])
watch(
  () => store.bookLineData,
  val => {
    data.value = val
  },
  { deep: true }
)
const lineColor = ref(null)
const getLineStyle = color => {
  lineColor.value = color
  getBookLine({ bookId: store.comprehensiveBookData.bookId, color }).then(res => {
    if (res.code === 200) data.value = res.data
  })
}

//删除划线
const delBookLine = async v => {
  const res = await deleteBookLine({ lineId: v.lineId }).catch(err => {
    ElMessage.error(JSON.stringify(err))
  })
  if (res.code === 200) {
    ElMessage.success('操作成功!')
  }
  getBookLine({ bookId: store.comprehensiveBookData.bookId, color: null }).then(res => {
    if (res.code === 200) {
      store.setBookLineData(res.data)
    }
  })
}

/**跳转页面*/
const jumpTo = v => {
  const signWordIdArr = generateTextIdWithFromId(v.fromWordId, v.endWordId)
  store.jumpToChapter(v.chapterId, v.pageNumber).then(() => {
    highlightKeyWordSmooth(signWordIdArr)
  })
}

// 导出
const bookExport = () => {
  ElMessageBox.confirm('此操作会导出符合条件的划线，您确定要导出吗?', '导出划线', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      let loading = ElLoading.service({
        lock: true,
        text: '正在下载数据，请稍候',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      exportBookMark(store.comprehensiveBookData.bookId)
        .then(async res => {
          let filename = res?.headers['content-disposition']?.split('filename=')[1]
          if (filename) {
            filename = decodeURIComponent(filename)
          } else {
            filename = new Date().getTime() + '.docx'
          }
          let data = res.data
          const isBlob = blobValidate(data)
          if (isBlob) {
            const blob = new Blob([data], { type: 'text/plain;charset=utf-8' })
            saveAs(blob, filename)
          } else {
          }
          loading.close()
        })
        .catch(r => {
          console.error(r)

          loading.close()
        })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: 'Delete canceled'
      })
    })
}
</script>
