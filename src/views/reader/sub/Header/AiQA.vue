<!-- AI问答 -->
<style lang="scss" scoped>
.AiQA {
  .AiQA-free {
    font-size: 12px;
    color: #ffffff;
    padding: 2px 6px;
    background: linear-gradient(to right, #932bff, #4f51ff);
    border-radius: 60px;
    position: absolute;
    right: 60px;
    top: 22px;
  }
  .AiQA-copy {
    position: absolute;
    left: -60px;
    top: 9px;
    font-size: 20px;
    cursor: pointer;
    color: #666;
  }
  .AiQA-edit {
    position: absolute;
    left: -30px;
    top: 9px;
    font-size: 20px;
    cursor: pointer;
    color: #666;
  }
  .AiQA-main {
    width: 100%;
    height: 492px;
    padding: 0 120px;
    box-sizing: border-box;
    text-align: center;
    & > section > p {
      font-size: 14px;
      color: #333333;
      line-height: 18px;
    }
    & > section > p:nth-of-type(3) {
      color: #6766ff;
    }
    .AiQA-main-btn {
      font-size: 20px;
      color: #4e50ff;
    }
  }
  .record {
    width: 100%;
    padding-top: 7px;
    box-sizing: border-box;
    overflow-y: auto;
    height: 510px;
  }
  .record-2,
  .record-1 {
    text-align: right;
    .record-item-1 {
      margin: 5px 0;
      max-width: 650px;
      padding: 8px 16px;
      background: #524fff;
      box-shadow: 0px 0px 16px 0px rgba(230, 237, 255, 0.53);
      border-radius: 8px 0px 8px 8px;
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
      display: inline-block;
      position: relative;
    }
  }
  .record-2 {
    text-align: left;
    .record-item-2 {
      margin: 5px 0;
      max-width: 650px;
      padding: 8px 16px;
      background: #fff;
      box-shadow: 0px 0px 16px 0px rgba(230, 237, 255, 0.53);
      border-radius: 0px 8px 8px 8px;
      font-size: 14px;
      line-height: 20px;
      display: inline-block;
    }
    // .more {
    //   width: 20px;
    //   height: 30px;
    //   display: inline-block;
    //   transition: 0.3s;
    //   overflow: hidden;
    //   white-space: nowrap;
    //   position: absolute;
    //   right: -20px;
    //   bottom: 0;
    //   .more-icon {
    //     width: 8px;
    //     height: 30px;
    //     background-color: rgba(255, 255, 255, 0.6);
    //     border-radius: 2px;
    //     display: inline-block;
    //     margin: 0 5px;
    //     text-align: center;
    //     line-height: 30px;
    //   }
    //   .more-menu {
    //     width: 80px;
    //     height: 30px;
    //     display: inline-block;
    //     background-color: rgba(255, 255, 255, 0.6);
    //     border-radius: 2px;
    //   }
    //   &:hover {
    //     width: 100px;
    //     transform: translateX(80px);
    //   }
    // }
  }
  .AiQA-new-dialogue {
    background: #ffffff;
    border-radius: 5px;
    border: 2px solid #524fff;
    width: 75px;
    height: 28px;
    font-weight: 500;
    font-size: 14px;
    margin: 10px 0;
    color: #777879;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    padding: 0 5px;
  }
  .AiQA-dialogue {
    height: 130px;
    margin-top: 5px;
    background: #ffffff;
    border-radius: 8px;
    border: 2px solid rgba(147, 43, 255, 1);
    overflow: hidden;
    // border-image: linear-gradient(243deg, rgba(147, 43, 255, 1), rgba(79, 81, 255, 1)) 2 2;
    display: flex;
    justify-content: space-between;
    .textarea {
      flex: 1;
      margin-left: 20px;
      .ipt {
        width: 100%;
        resize: none;
        height: calc(100% - 25px);
        border: 0;
        outline: none;
      }
      .textCount {
        font-size: 12px;
        color: #666666;
      }
    }
    .sendOut-btn {
      width: 36px;
      margin: 0 10px;
      position: relative;
      & > img {
        width: 36px;
        position: absolute;
        bottom: 10px;
      }
    }
  }
  .explain {
    font-size: 10px;
    color: #999999;
    line-height: 14px;
    text-align: center;
  }
}
</style>

<template>
  <div class="AiQA">
    <span
      class="AiQA-free">限时免费</span>
    <div class="AiQA-main"
      v-if="!record.length">
      <h2>Hi～我是你的AI助手！</h2>
      <p>
        我能跟你一起讨论关于这本书的内容，读每一章遇到问题时都可以问我哦。
      </p>
      <p>你可以这样问：</p>
      <p>教材中有关概念和知识点</p>
      <p>学生在学习过程中经常遇到的问题</p>
      <p>最新上架的教材相关问题</p>
      <p>创建一些需要思考或讨论的问题</p>
      <p>分析学生的学习进度和偏好预置问题</p>
      <!-- <el-button type="primary" link size="large">
        <span class="AiQA-main-btn __flex-center__"
          >换一批<el-icon><RefreshLeft /></el-icon
        ></span>
      </el-button> -->
    </div>

    <main v-else
      class="record">
      <section
        v-for="(ele, i) in record"
        :key="i">
        <div class="record-1"
          v-if="ele.role == 1">
          <div
            class="record-item-1">
            <span title="复制"
              @click="copyText(ele.content)"
              class="AiQA-copy">
              <el-icon>
                <DocumentCopy />
              </el-icon></span>
            <span title="编辑"
              @click="editContent(ele.content)"
              class="AiQA-edit">
              <el-icon>
                <EditPen />
              </el-icon></span>
            {{ ele.content }}
          </div>
        </div>
        <div class="record-2"
          v-else>
          <div
            class="record-item-2"
            v-html="ele.content.replace(/,<\/div>/g, '</div>')">
          </div>
          <!-- <div class="more">
          <div class="more-icon">
            <svg-icon iconClass="more" style="font-size: 16px; transform: translateX(-4px)" />
          </div>
          <div class="more-menu">123123123</div>
        </div> -->
        </div>
      </section>
    </main>

    <!-- <div
      class="AiQA-new-dialogue"
      @click="cls">
      <el-icon>
        <Plus />
      </el-icon>新对话
    </div> -->
    <div
      class="AiQA-dialogue">
      <div class="textarea"
        v-loading="loading">
        <textarea class="ipt"
          v-model="iptVal"
          placeholder="点这里，尽管问"
          maxlength="200"></textarea>
        <span
          class="textCount">{{ iptValNum }}/200</span>
      </div>

      <div
        class="sendOut-btn">
        <img
          src="@/assets/images/readerHeader/fasong.png"
          alt=""
          @click="keydownEnter({ keyCode: 13 })"
          style="cursor: pointer" />
      </div>
    </div>
    <p class="explain">
      服务生成的所有内容均由人工智能生成，其生成内容的准确性和完整性无法保证，不代表我们的态度或观点
    </p>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { chartAi, getAiAssistantUseCount } from '@/api/openApi/openApi'

const iptVal = ref('')
const iptValNum = computed(() => iptVal.value.length || 0)
const record = ref([
  {
    role: 1,
    content: '你好！'
  },
  {
    role: 2,
    content: '服务生成的所有内容均由人工智能生成，其生成内容的准确性和完整性无法保证，不代表我们的态度或观点!'
  }
])
const loading = ref(false)
const cls = () => {
  record.value = []
}
const keydownEnter = async e => {
  if (!iptVal.value.trim()) return ElMessage.error('请输入内容')
  loading.value = true
  if (e.keyCode === 13) {
    const resNum = await getAiAssistantUseCount()
    if (resNum.aiExperimentCount > 0) {
      const res = await chartAi({ ability: 26, question: iptVal.value, userType: 0 })
      console.log(JSON.parse(res.body.content))

      const { book, explain, extends1, extends2, extends3 } = JSON.parse(res.body.content)
      const res27 = await chartAi({ ability: 27, question: iptVal.value })
      const result = JSON.parse(res27.body.content).result

      record.value.push(
        {
          role: 1,
          content: iptVal.value
        },
        {
          role: 2,
          content: `<div>
          <div class="explain">${explain}</div>
          ${extends1.length > 0 ? extends1.map(i => `<div class="extends1">${i}</div>`).join('') : ''}
           ${extends2.length > 0 ? extends2.map(i => `<div class="extends2">${i}</div>`).join('') : ''}
           ${extends3.length > 0 ? extends3.map(i => `<div class="extends1">${i}</div>`).join('') : ''}
          <div style="padding:10px 0;"><strong>推荐书籍列表</strong></div>
          ${book.map(i => `<div><span>书名：${i.name}</span><span>作者：${i.author}</span></div>`).join('')}
          
          </div>`
        },
        {
          role: 2,
          content: `<div>
          ${result.map(i => `<div class="result">${i}</div>`).join('')}
        
          </div>`
        }
      )
      const timer = setTimeout(() => {
        iptVal.value = ''
        clearTimeout(timer)
      })
    } else {
      loading.value = false
      ElMessage.error('您的免费次数已用完')
    }
    console.log(resNum)
  }

  loading.value = false
  e.stopPropagation && e.stopPropagation()
}
// 复制
const copyText = async content => {
  console.log(content)
  try {
    await navigator.clipboard.writeText(content)
  } catch (err) {
    console.log('复制失败')
  }
}
// 编辑
const editContent = content => {
  console.log(content)
  iptVal.value = content
}
onMounted(() => {})
</script>
