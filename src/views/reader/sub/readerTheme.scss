//默认白天主题
.__reader__ {
  --modelBackgroundColor: #fff; //模块背景
  --pageBackgroundColor: #f5f5f5; //阅读器压面背景
  --fontColor: #333333; //字体颜色
  --hoverBackgroundColor: #f3f7fa; //鼠标滑过背景
  --hoverfont: #75c0ff; //鼠标滑过文字
  --defaultBtnColor: #2ce04a; //默认按钮颜色
  --primaryBtnColor: #caed4b; //primary按钮颜色
  --noteContentBackground: #f5f9fc; //笔记内容
  --noteBoxShadow: 0px 0px 4px 0px rgba(193, 193, 193, 0.5); //笔记内容的模块阴影
  --borderColor: #fff;
  --selectMenuTabs: #eaeaea;
  --boxShadow: 0px 0px 20px 0px rgba(182, 182, 182, 0.37);
  --speedOfProgressColor: #dedede;
  --pageTurningIconColor: #666;
  --pageTurningIconBackground: rgba(255, 255, 255, 0.38);

  .el-menu {
    .el-sub-menu__title,
    .el-menu-item {
      height: 44px !important;
    }
  }

  blockquote {
    border-left: 3px solid #0966b4;
    padding: 0.5em 1em;
    margin:10px 0;
    background-color: rgba(0, 0, 0, 0.03);
    p {
      margin-top: 0;
    }
  }
}

//黑天主题
[reader-theme='dark'] {
  --modelBackgroundColor: #333; //模块背景
  --pageBackgroundColor: #232323; //阅读器页面背景
  --fontColor: #f7f7f7; //字体颜色
  --hoverBackgroundColor: #2b3c4a; //鼠标滑过背景
  --hoverfont: #75c0ff; //鼠标滑过文字
  --defaultBtnColor: #2ce04a; //默认按钮颜色
  --primaryBtnColor: #caed4b; //primary按钮颜色
  --noteContentBackground: #333333; //笔记内容
  --noteBoxShadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.37); //笔记内容的模块阴影
  --borderColor: #fff;
  --selectMenuTabs: #484848;
  --boxShadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.37);
  --speedOfProgressColor: #818181;
  --pageTurningIconColor: #fff;
  --pageTurningIconBackground: rgba(255, 255, 255, 0.2);
  // .__Pages-item__ {
  //   table,
  //   table tr th,
  //   table tr td {
  //     border: 1px solid var(--fontColor); /* 黑色边框，1像素宽 */
  //   }
  //   table {
  //     border-collapse: collapse; /* 合并边框，避免边框重复 */
  //     text-align: center;
  //     width: 100%;
  //   }
  // }
  .el-input-number__decrease,
  .el-switch.is-checked .el-switch__core,
  .el-input-number__increase,
  .el-input-number__decrease.is-disabled,
  .el-input__wrapper,
  .el-dropdown__popper .el-dropdown-menu,
  .el-popper.is-light,
  .el-popper__arrow,
  .el-dialog {
    background-color: var(--modelBackgroundColor);
    &::before{
      background-color: transparent !important;
    }
  }

  //书籍下拉框鼠标滑过得状态
  .el-dropdown-menu__item:not(.is-disabled):focus {
    background-color: var(--hoverBackgroundColor);
    color: var(--hoverfont);
  }

  .el-dropdown-menu__item {
    color: var(--fontColor);
  }
  //默认按钮
  .el-button {
    background-color: var(--modelBackgroundColor);
    border-color: #0966b4;
    color: var(--fontColor);
  }

  //主要按钮
  .el-button--primary {
    background-color: #0966b4;
  }
  .el-switch.is-checked .el-switch__core {
    border-color: var(--borderColor);
  }
  .el-button.is-link {
    border-color: rgba(0, 0, 0, 0);
  }
  .el-button--primary.is-link {
    background-color: rgba(0, 0, 0, 0);
    color: #0966b4;
  }

  .el-menu {
    background-color: rgba(0, 0, 0, 0);
    .el-menu-item {
      height: 44px !important;
      color: var(--fontColor);
      &:hover {
        background-color: var(--hoverBackgroundColor) !important;
        color: var(--hoverfont);
      }
    }

    .el-sub-menu__title {
      height: 44px !important;
      color: var(--fontColor);
      &:hover {
        background-color: var(--hoverBackgroundColor) !important;
        color: var(--hoverfont);
      }
    }

    .is-active {
      color: var(--hoverfont);
    }
  }
  .el-select__wrapper {
    background-color: #232323;
    border-color: #666;
  }
}
