<!-- 模块说明 -->
<style lang="scss">
.Pages-item {
  .Pages-item-main {
    .first-chapter & {
      height: 100% !important;
    }
  }
}
</style>
<style lang="less" scoped>
.Pages-item {
  height: 100%;
  width: 100%;
  //box-shadow: var(--boxShadow);
  //margin: 0 5px;

  .Page-item-header {
    width: 100%;
    height: 80px;
    position: relative;
    .Bookmark {
      position: absolute;
      top: 0;
      right: 20px;
    }
  }
  table {
    width: 100%;
    border-collapse: collapse;
    padding: 20px 0;
    word-break: break-all;
  }
  ::v-deep(td) {
    padding: 5px;
  }
  .Pages-item-main {
    height: calc(100% - 160px);
    font-family: "SimSun";
    // min-height: 1200px;
    overflow: hidden;
    display: flex;
    flex: 1;
    justify-content: space-between;
    padding: 0 3.18cm;
    font-size: 1.125rem;

    .Pages-item-main-content {
      height: 100%;
      flex: 1;
      overflow-y: auto;
      div > p {
        word-wrap: break-word;
      }
      // ::v-deep p span {
      //   font-size: var(--font-size, 14px) !important;
      // }
    }
  }
  .Page-item-footer {
    width: 100%;
    height: 80px;
    position: relative;
    .page-number {
      position: absolute;
      bottom: 10px;
      transform: translateX(-50%);
      left: 50%;
    }
  }
}
</style>

<template>
  <div
    class="Pages-item __Pages-item__"
    :style="`background-color:${store.themeBackground};${minHeight ? 'min-height: 1200px;' : ''}`"
    ref="pageItemRefs"
  >
    <div
      class="Page-item-header"
      :style="templateStyle.show ? templateStyle.header : ''"
    >
      <div class="Bookmark">
        <!-- 书签 -->
        <!-- !proxy.$route.query.cid后加，用于区分简易预览和常规打开，当编辑器打开预览时，不显示Bookmark -->
        <Bookmark
          v-if="hasBookmark"
          :pageIndexInBook="pageIndexInBook"
          :dom="pageItemRefs"
          :chapterId="chapterId"
          :pageIndexInChapter="pageIndexInChapter"
        />
      </div>
    </div>
    <div class="Pages-item-main" :style="templateStyle.main">
      <div
        class="Pages-item-main-content __hidden-scroll__ __pageItemContent__"
      >
        <div v-for="(ele, i) in content" :key="ele.attrs.id">
          <Paragraph
            :style="{ '--font-size': confFontSize }"
            :paragraphData="ele"
            :pageId="pageId"
          />
        </div>
      </div>
    </div>
    <div
      class="Page-item-footer"
      :style="templateStyle.show ? templateStyle.footer : ''"
    >
      <div class="page-number" :style="templateStyle.pageNumber">
        {{ pageIndexInBook }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, ref, computed } from "vue";
import useReader from "@/store/modules/reader";
import Paragraph from "./Paragraph.vue";
import Bookmark from "./Bookmark.vue";
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();

const store = useReader();
const pageItemRefs = ref(null);
const confFontSize = ref();
// 监听样式设置变化,解决文字大小不变的问题
watch(
  () => store.styleSetting["font-size"],
  (newVal, oldVal) => {
    console.log(newVal);
    confFontSize.value = newVal;
  },
  { deep: true }
);
const props = defineProps({
  content: {
    type: Array,
    default: () => [],
  },
  // index: Number,
  pageId: String,
  minHeight: {
    type: Boolean,
    default: true,
  },
  pageIndexInBook: "",
  chapterId: "",
  pageIndexInChapter: "",
  hasBookmark: {
    type: Boolean,
    default: true,
  },
});
const templateStyle = computed(() => {
  const obj = {};
  let d = store.templateStyle;
  if (!d) return obj;
  if (d.headerUrl) {
    obj.header = {
      backgroundImage: `url(${d.headerUrl})`,
      backgroundRepeat: "no-repeat",
      backgroundSize: "100% 100%",
    };
  }
  if (d.footerUrl) {
    obj.footer = {
      backgroundImage: `url(${d.footerUrl})`,
      backgroundRepeat: "no-repeat",
      backgroundSize: "100% 100%",
    };
  }
  if (d.contentUrl) {
    obj.main = {
      backgroundImage: `url(${d.contentUrl})`,
      backgroundRepeat: "no-repeat",
      backgroundSize: "100% 100%",
      minHeight: props.minHeight ? "1200px" : "",
    };
  }
  if (d.pagesAlign === "center") {
    obj.pageNumber = {
      left: "50%",
      transform: "translateX(-50%)",
      color: d.pagesFontColor,
    };
  } else if (d.pagesAlign === "left") {
    obj.pageNumber = { left: "40px", color: d.pagesFontColor };
  } else if (d.pagesAlign === "right") {
    obj.pageNumber = { right: "40px", color: d.pagesFontColor };
  }
  if (d.orderTemplateBgUrl) {
    const ID = "__orderTemplateBgUrl__";
    const s = document.getElementById(ID);
    if (s) s.remove();
    const STYLE = document.createElement("style");
    STYLE.id = ID;
    STYLE.innerText = `
      .__pageItemContent__ .el-collapse{
        border:0;
      }
      .__orderTemplateBgUrl__{
        background-image: url(${d.orderTemplateBgUrl})!important;
        background-repeat: no-repeat; background-size: 100% 50px ;
        }
        .__orderTemplateBgUrl__ .name,
        .__orderTemplateBgUrl__ .__name__{
          color:${d.orderTemplateColor} !important;
        }
        .video-parent .el-collapse-item__header{
          background-image: url(${d.orderTemplateBgUrl})!important;
          background-repeat: no-repeat;
          background-size: 100% 48px;
          background-color: rgba(0,0,0,0);
          border: 0;
        }
        .video-parent .el-collapse-item__header .name{
          color:${d.orderTemplateColor}
        }
        `;
    document.querySelector("body").appendChild(STYLE);
  }
  if (props.content[0]) {
    if (props.content[0].type == "paperWrapping") {
      obj.show = false;
    } else {
      obj.show = true;
    }
  }
  return obj;
});
</script>
