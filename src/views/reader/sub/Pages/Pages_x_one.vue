<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-26 10:54:55
 * @LastEditTime: 2025-02-20 19:22:41
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Pages\Pages_x_one.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 上下滑动 -->
<!--
<style lang="scss" scoped>
.Pages {
  width: 1000px;
  height: calc(100% - 12px);
  .Pages-icon-left,
  .Pages-icon-right {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--pageTurningIconBackground);
    box-shadow: var(--boxShadow);
    color: var(--pageTurningIconColor);
    font-size: 30px;
    cursor: pointer;
    position: fixed;
    top: 50vh;
    transform: translateY(-50%);
  }
  .Pages-icon-left {
    left: 10vw;
  }
  .Pages-icon-right {
    right: 10vw;
  }
  .Pages-item {
    width: 100%;
    min-height: 800px;
  }
}
</style>
-->
<template>
<!--
  <div class="Pages">
    <div class="Pages-icon-left __flex-center__" @click="pageTurning('last')">
      <el-icon><ArrowLeftBold /></el-icon>
    </div>
    <div class="Pages-item __Pages-item__">
      <Pages_r_item :content="pageData.content" :pageId="pageData.attrs.id" :index="store.comprehensiveBookData.currentPageIndex + 1" />
    </div>
    <div class="Pages-icon-right __flex-center__" @click="pageTurning('next')">
      <el-icon><ArrowRightBold /></el-icon>
    </div>
  </div>
-->
</template>

<script setup>
/*
import { ArrowLeftBold, ArrowRightBold } from '@element-plus/icons-vue'
import { defineProps, ref } from 'vue'
import useReader from '@/store/modules/reader'
import Pages_r_item from './Pages_r_item.vue'

const store = useReader()
const props = defineProps({
  pageData: {
    type: Object,
    defaule: () => ({ content: [] }),
    required: true,
  },
})

const pageTurning = (type) => {
  if (type === 'last') {
    store.lastPage()
  } else if (type === 'next') {
    store.nextPage()
  }
}
*/
</script>
