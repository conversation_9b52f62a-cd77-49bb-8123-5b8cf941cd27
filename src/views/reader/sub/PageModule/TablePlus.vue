<!-- 表格 -->
<style lang="scss" scoped>
.TablePlus {
  margin: 15px 0;

  .header {
    text-align: center;
    font-size: 14px;
  }

  .tableMain {
    width: 100%;
    th {
      padding: 5px 0;
    }
    td {
      padding: 5px 0;
      word-break: break-all;
    }
  }
}
</style>
<style lang="scss">
.__tables_default {
  border: 1px solid #000;
  border-collapse: collapse;
  /* 合并边框，避免边框重复 */
  text-align: center;
  width: 100%;
  font-size: 14px;

  th {
    border: 1px solid #000;
    background-color: #f1f3f5;
    font-weight: bold;
  }

  td {
    border: 1px solid #000;
    background-color: #fff;
  }
}

.__tables_3,
.__tables_2,
.__tables_1 {
  border: 1px solid #e1e2e5;
  border-collapse: collapse;
  /* 合并边框，避免边框重复 */
  text-align: center;
  width: 100%;
  font-size: 14px;
  word-break: break-all;
  th {
    border: 1px solid #e1e2e5;
    font-weight: bold;
  }

  td {
    border: 1px solid #e1e2e5;
    background-color: #fff;
  }
}

.__tables_4 {
  border: 1px solid #eaebed;
  border-collapse: collapse;
  /* 合并边框，避免边框重复 */
  text-align: center;
  width: 100%;
  font-size: 14px;

  th {
    border: 1px solid #eaebed;
    font-weight: bold;
  }

  td {
    border: 1px solid #eaebed;
    background-color: #fff;
  }
}
</style>
<template>
  <div class="TablePlus" :id="id">
    <div
      class="header"
      v-if="isShowTitle && !isTitlePosition"
      :style="{ textAlign: hanlin }"
    >
      <text style="margin-right: 10px">{{ number }}</text>
      <text>{{ name }}</text>
    </div>
    <div class="tableMain">
      <Paragraph :paragraphData="content[0]" :class="tableClassName" />
    </div>
    <div
      class="header"
      v-if="isShowTitle && isTitlePosition"
      :style="{ textAlign: hanlin }"
    >
      <text style="margin-right: 10px">{{ number }}</text>
      <text>{{ name }}</text>
    </div>
  </div>
</template>

<script setup>
import { defineProps, computed } from "vue";
import Paragraph from "../Pages/Paragraph.vue";
const props = defineProps({
  name: String,
  id: String,
  isShowTitle: Boolean,
  templateId: String,
  content: Array,
  isTitlePosition: Boolean,
  number: String,
  hanlin: String,
});
const tableClassName = computed(() => {
  if (!props.templateId) {
    return "__tables_default";
  } else {
    return `__tables_${props.templateId}`;
  }
});
</script>
