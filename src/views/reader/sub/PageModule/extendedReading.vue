<template>
  <div :id="id" ref="containerRef" class="umo-node-view">
    <div class="umo-node-view-extended-reading">
      <div
        class="extended-reading-template-bg __orderTemplateBgUrl__"
        style="min-height: 54px"
      >
        <!-- :style="`background: url(${template?.orderTemplateBgUrl}) no-repeat center;height:54px;background-size:100% 100%;`" -->
        <div class="extended-left">
          <div class="extended-icon">
            <img :src="daskRead" alt="" />
          </div>
          <div class="extended-title">拓展阅读</div>
          <el-tooltip effect="dark" :content="name" placement="top">
            <div class="extended-name">{{ name }}</div>
          </el-tooltip>
        </div>
        <div class="extended-right" @click="toggle">
          {{ toggleVisible ? "收起" : "展开" }}
        </div>
      </div>

      <div v-if="toggleVisible" class="extended-reading-template-content">
        <slot />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import daskRead from "@/assets/resources/dark/read.svg";
const props = defineProps({
  id: String,
  name: String,
});
// const { pageTemplateId } = useTemplate();
// const template = pageTemplateId.value;
const toggleVisible = ref(false);
const toggle = () => {
  toggleVisible.value = !toggleVisible.value;
};
</script>

<style lang="scss" scoped>
.umo-node-view {
  background-color: transparent;
  margin: 20px 0;
}
.umo-node-view-extended-reading {
  position: relative;
  line-height: 30px;
  width: 100%;
  .icon {
    position: absolute;
    right: 10px;
    top: 14px;
    z-index: 99;
    font-size: 24px;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: #fff;
    padding: 5px;
    width: 18px;
    height: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .extended-reading-template-bg {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    .extended-left {
      display: flex;
      align-items: center;
      .extended-icon {
        width: 24px;
        height: 24px;
        img {
          width: 100%;
          height: 100%;
        }
      }

      .extended-title {
        padding: 0 10px;
        font-size: 16px;
        font-weight: 400;
      }

      .extended-name {
        color: #333;
        margin-left: 20px;
        width: 450px;
        white-space: nowrap; /* 确保文本在一行内显示 */
        overflow: hidden; /* 超出容器部分的文本隐藏 */
        text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
      }
    }

    .extended-right {
      color: #666;
      font-size: 16px;
      cursor: pointer;
    }
  }
  .extended-reading-template-content {
    background-color: #f6f6f6;
    border: 1px solid #ebebeb;
    padding: 20px 16px;
    margin-top: 10px;
    width: 100%;
    min-height: 100px;
    box-sizing: border-box;
    font-size: 18px;
    color: #333;
    word-break: break-all;
    white-space: pre-wrap;
    overflow: hidden;
    line-height: 1.6;
  }
}
</style>
