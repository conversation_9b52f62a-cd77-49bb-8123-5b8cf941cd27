<!-- 模块说明 -->
<style lang="less" scoped>
.line-numbers {
  overflow-x: auto;
  pre {
    background-color: transparent !important;
  }
}

.code-bg {
  position: relative;
  .language-top {
    position: absolute;
    right: 20px;
    top: 10px;
    z-index: 999;
    display: flex;
    .title {
      color: #fff;
      margin-right: 20px;
    }
  }
}
</style>

<template>
  <!--  -->
  <div
    class="code-bg"
    :style="{
      background: theme == 'dark' ? '#2d2d2d !important' : '#f5f2f0 !important',
    }"
  >
    <div class="language-top">
      <div
        class="title"
        :style="{
          color: theme == 'dark' ? '#fff' : '#666',
        }"
      >
        代码块语言：{{ language }}
      </div>
      <div class="btn">
        <el-button class="code-btn" @click="handleClick"> 运行</el-button>
      </div>
    </div>

    <prism
      :language="language"
      class="line-numbers"
      :theme="theme"
      :style="{
        background:
          theme == 'dark' ? '#2d2d2d !important' : '#f5f2f0 !important',
      }"
    >
      {{ code }}
    </prism>
  </div>
  <el-dialog v-model="dialogVisible" title="代码运行结果" width="50%">
    <div v-if="prismLanguage == 'html'">
      <iframe
        ref="myIframe"
        :srcdoc="resultContent"
        style="width: 100%; height: 700px; border: none"
      ></iframe>
    </div>

    <div v-else v-loading="loading">
      {{ resultContent }}
    </div>
  </el-dialog>
</template>

<script setup>
import QuestionHint from "@/views/reader/sub/PageModule/question-hint/questionHint";
import { defineProps } from "vue";
import Prism from "vue-prism-component";
import "prismjs";
import "prismjs/components/prism-sql";
import "prismjs/components/prism-bash";
import "prismjs/components/prism-css";
import "prismjs/components/prism-ini";
import "prismjs/components/prism-kotlin";
import "prismjs/components/prism-markup";
import "prismjs/components/prism-r";
import "prismjs/components/prism-basic";
import "prismjs/components/prism-vbnet";
import "prismjs/components/prism-c";
import "prismjs/components/prism-opencl";
import "prismjs/components/prism-diff";
import "prismjs/components/prism-java";
import "prismjs/components/prism-less";
import "prismjs/components/prism-objectivec";
import "prismjs/components/prism-wasm";
import "prismjs/components/prism-cpp";
import "prismjs/components/prism-go";
import "prismjs/components/prism-javascript";
import "prismjs/components/prism-js-templates";
import "prismjs/components/prism-jsx";
import "prismjs/components/prism-lua";
import "prismjs/components/prism-perl";
import "prismjs/components/prism-python";
import "prismjs/components/prism-rust";
import "prismjs/components/prism-swift";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-csharp";
import "prismjs/components/prism-graphql";
import "prismjs/components/prism-json";
import "prismjs/components/prism-makefile";
import "prismjs/components/prism-scss";
import "prismjs/components/prism-typescript";
import "prismjs/components/prism-tsx";
import "prismjs/components/prism-yaml";
import "prismjs/components/prism-regex";
import { chartAi } from "@/api/openApi/openApi";
const props = defineProps({
  margin: Object,
  theme: String,
  code: String,
  language: String,
  prismLanguage: String,
});
const dialogVisible = ref(false);
const resultContent = ref("");
const loading = ref(false);
const myIframe = ref(false);
const handleClick = () => {
  console.log(props.language);
  if (props.code) {
    if (props.language != "html") {
      chartAi({
        ability: 25,
        question: props.code,
        developmentLanguage: props.language,
      }).then((res) => {
        loading.value = false;
        console.log(res.body.content.result);
        resultContent.value = res.body.content;
      });
    } else {
      resultContent.value = props.code;
      console.log(resultContent.value);
    }

    dialogVisible.value = true;
  }
};
</script>
