<!-- 模块说明 -->
<style lang="scss" scoped>
.ImageIcon {
  display: inline-flex;
  position: relative;
  top: 5px;
  margin-right: 10px;
  .el-image {
    display: inline-flex;
    /* overflow: hidden; */
    position: relative;
  }
}

.imageContent {
  display: flex;
  justify-content: center;
  align-items: center;
}
.imageText {
  padding: 20px 0;
  text-align: center;
}
</style>

<template>
  <div :id="id" class="ImageIcon" @click="openVisible = true">
    <el-image
      :src="src"
      :style="{ width: width + 'px', height: height + 'px' }"
    />
  </div>

  <el-dialog v-model="openVisible" title="图标预览" width="30%" center>
    <div class="imageContent">
      <img :src="src" fit="cover" style="max-width: 100%" />
    </div>

    <div class="imageText" :style="{ color: linkAddress ? '#409EFF' : '' }">
      <a :href="linkAddress" target="_blank"> {{ imageTitle }}</a>
    </div>
  </el-dialog>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  src: String,
  id: String,
  previewtype: String,
  width: Number,
  name: String,
  height: Number,
  linkAddress: String,
  imageTitle: String,
});

const openVisible = ref(false);
</script>
