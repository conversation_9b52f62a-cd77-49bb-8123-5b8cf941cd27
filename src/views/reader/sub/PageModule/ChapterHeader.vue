<template>
  <div
    class="headerbg"
    :style="`height:${templateStyle.chapterHeaderHeight / 2.5}px;background:url(${templateStyle.bgImg}) no-repeat;background-size: auto 100%;}`"
  >
    <div class="header-title">
      <!-- {{ title }} -->
      <slot></slot>
    </div>
  </div>
</template>
<script setup>
import { defineProps } from "vue";
import useReader from "@/store/modules/reader";

const store = useReader();
const props = defineProps({
  title: String,
  // bgImg: String,
  height: String,
});

const templateStyle = computed(() => {
  const obj = {};
  let tmpStyle = store.templateStyle;
  obj.bgImg = tmpStyle?.chapterHeaderUrl;
  obj.chapterHeaderHeight = tmpStyle?.chapterHeaderHeight;
  return obj;
});
</script>
<style lang="less" scoped>
.headerbg {
  width: 100%;
  margin: 10px 0;
  display: flex;
  align-items: center;
  .header-title {
    padding: 20px;
    outline: none;
    width: 100%;
  }
}
</style>
