<style lang="scss" scoped>
.Questions {
  padding: 26px 28px;
  font-size: 14px;
  min-height: 100px;
  border: 1px solid #e5e6e7;
  // border-radius: 5px;
  background-color: #fff;
  margin: 10px 0;
  .title {
    font-size: 16px;
    font-weight: bold;
  }
  .desc-question {
    background: rgba(255, 245, 219, 1);
    border: 1px solid rgba(255, 226, 189, 1);
    height: 42px;
    color: rgba(255, 156, 69, 1);
    line-height: 42px;
    padding: 0 20px;
  }
}

.questionsBg {
  width: 100%;
  height: 50px;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  .left {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    .title {
      width: 180px;
      font-size: 16px;
      text-align: left;
      margin-left: 10px;
    }
    .name {
      flex: 1;
      font-size: 16px;
      padding-left: 20px;
      color: #2979e9;
      height: 50px;
      overflow: hidden;
    }
  }
  .right {
    cursor: pointer;
    width: 50px;
  }
}
</style>

<template>
  <div class="Questions">
    <div class="title" v-if="questionsData.questionTypeShow == '1'">
      {{ questionsData.questionText }}
    </div>
    <div class="desc-question" v-if="questionsData.questionText === '排序题'">
      提示：请对以下排序题进行操作，将选项拖拽至正确的位置。
    </div>
    <!-- :questionTypeShow="questionsData.questionTypeShow" -->
    <component
      :footer="ableToResubmit"
      v-if="currentComponents"
      :is="currentComponents"
      :OPTION="OPTION"
      :data="questionsData"
    ></component>
  </div>
</template>

<script setup>
import { ref, reactive, defineProps, computed, onMounted } from "vue";
import Topic_1 from "./QuestionsItem/Topic_1.vue";
import Topic_2 from "./QuestionsItem/Topic_2.vue";
import Topic_3 from "./QuestionsItem/Topic_3.vue";
import Topic_4 from "./QuestionsItem/Topic_4.vue";
import Topic_5 from "./QuestionsItem/Topic_5.vue";
import Topic_6 from "./QuestionsItem/Topic_6.vue";
import Topic_8 from "./QuestionsItem/Topic_8.vue";

import router from "@/router";
import useReader from "@/store/modules/reader";
const store = useReader();
const ableToResubmit = ref(true);
const proxy = getCurrentInstance();
const props = defineProps({
  questionsList: Array,
});
const COMPONENTSLIST = [
  Topic_1,
  Topic_2,
  Topic_3,
  Topic_4,
  Topic_5,
  Topic_6,
  Topic_1,
  Topic_8,
];
const currentComponents = ref(null);
const TYPE = [
  "单选题",
  "多选题",
  "填空题",
  "排序题",
  "连线题",
  "简答题",
  "判断题",
  "编程题",
];
const OPTION = [
  "A",
  "B",
  "C",
  "D",
  "E",
  "F",
  "G",
  "H",
  "I",
  "G",
  "K",
  "L",
  "M",
  "N",
  "O",
  "P",
  "Q",
  "R",
  "S",
  "T",
  "U",
  "V",
  "W",
  "X",
  "Y",
  "Z",
];
const questionsData = computed(() => {
  // console.log(props.questionsList);
  const obj = {
    questionText: "", //小题类型的翻译
    questionType: 1, //小题类型1单选 2多选 3填空 4排序 5连线 6简答 7判断 8编程-填空 9编程-简答
    questionContent: "", //题干
    analysis: "", //解析
    options: [], //子选项
    rightAnswer: "", //正确答案
    params: {}, //提交需要的参数
    sectionReferTo: [],
    language: "", // 编程题语言
  };
  const val = props.questionsList[0];
  console.log(val.userQuestion.codeContent);
  if (val) {
    obj.disorder = val.userQuestion.disorder;
    obj.questionText = TYPE[val.userQuestion.questionType - 1];
    obj.questionType = val.userQuestion.questionType;
    obj.questionContent = val.userQuestion.questionContent;
    obj.analysis = val.userQuestion.analysis;
    obj.options = val.userQuestion.options;
    obj.rightAnswer = val.userQuestion.rightAnswer;
    obj.params = {
      bookQuestionId: val.bookQuestionId,
      userQuestionId: val.userQuestionId,
      bookId: val.bookId,
      chapterId: val.chapterId,
    };
    obj.sectionReferTo = val.userQuestion.sectionReferTo ?? [];
    obj.language =
      Object.prototype.toString.call(val.userQuestion.codeConten) ===
      "[object Object]"
        ? val.userQuestion.codeContent?.language
        : JSON.parse(val.userQuestion.codeContent).language;

    obj.questionTypeShow = val.questionTypeShow;
  }
  currentComponents.value = COMPONENTSLIST[val.userQuestion.questionType - 1];

  // console.log(obj);
  return obj;
});
const templateStyle = computed(() => {
  return store.templateStyle;
});

onMounted(() => {
  const simplified = inject("simplifiedReadingMode");
  if (simplified) {
    ableToResubmit.value = false;
  }
});

// 前往编程题
const goToRunCode = () => {
  window.open(
    "/runCode?bookId=" +
      questionsData.value.params.bookId +
      "&chapterId=" +
      questionsData.value.params.chapterId +
      "&bookQuestionId=" +
      questionsData.value.params.bookQuestionId +
      "&userQuestionId=" +
      questionsData.value.params.userQuestionId,
    "_blank"
  );
};
</script>
