<!-- 模块说明 -->
<style lang="less" scoped>
.links {
  display: inline-block;
  .title {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #0966b4;
  }
}
</style>

<template>
  <span class="links">
    <u class="title" @click="openLink">
      <span v-if="plateType === 'titleIcon' && type !== 'websiteLink'">{{ title }}</span>
      <span :href="href" v-else>{{ title }}</span>
      <el-icon><Link /></el-icon>
    </u>
  </span>
</template>

<script setup>
import { nextTick } from 'vue'
import { Link } from '@element-plus/icons-vue'
import useReader from '@/store/modules/reader'
import usePreview from '@/store/modules/preview.js'
const previewStore = usePreview()
const props = defineProps({
  plateType: String,
  type: String,
  title: String,
  href: String,
  attrs: Object,
})
const bookReadingStore = useReader()
const openLink = () => {
  switch (props.type) {
    case 'chaptersInThisBook':
      bookReadingStore.jumpToChapter(props.href)
      break

    case 'websiteLink':
      window.open(props.href, '_blank')
      break

    case 'crossReferencing':
      // 需要验证
      bookReadingStore.jumpToChapter(props.href, props.attrs.pageNumber)
      const dom = document.getElementById(props.attrs.id)
      if (dom) {
        nextTick(() => {
          dom.scrollIntoView({ behavior: 'smooth' })
        })
      }
      break

    case 'resourceLibrary':
      previewStore.setData({ url: props.href, title: props.attrs.name })
      break

    default:
      break
  }
}
</script>
