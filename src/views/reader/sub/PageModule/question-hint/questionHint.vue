<style lang="less">
.question-hint {
  .sectionReferTo {
    list-style: none;
    margin: 0;
    padding: 0;
    li {
      .el-breadcrumb__item .el-breadcrumb__inner {
        cursor: pointer;
        &:hover {
          color: #428bd7;
        }
      }
      .el-breadcrumb {
        line-height: 1.5;
      }
      padding: 10px 0 0 10px;
    }
  }
}
</style>
<template>
  <el-tabs
    v-model="activeName"
    class="question-hint"
    v-if="analysis || sectionReferToData?.length > 0">
    <el-tab-pane label="查看解析"
      name="ana"
      v-if="analysis">
      <div
        class="analysisItem"
        v-html="analysis">
      </div>
    </el-tab-pane>
    <el-tab-pane label="查看知识点"
      name="referTo"
      v-if="sectionReferToData?.length > 0">
      <ul
        class="sectionReferTo">
        <li
          v-for="(sectionItem) in sectionReferToData">
          <el-breadcrumb
            separator="/"
            @click="clickToGoToReferredSection(sectionItem)">
            <el-breadcrumb-item
              v-for="(chapterItem) in sectionItem">{{ chapterItem.name }}</el-breadcrumb-item>
          </el-breadcrumb>
        </li>
      </ul>
    </el-tab-pane>
  </el-tabs>
</template>
<script setup>
import { defineProps } from 'vue'
import useReader from '@/store/modules/reader'
import { HEADING_ATTR_ID, highlightKeyWordSmooth } from '@/utils/reader'
import { PAGE_TURNING_ERROR_MAPPER } from '@/utils/reader'
import { ElMessage } from 'element-plus'

const store = useReader()
const props = defineProps({
  sectionReferToData: {
    type: Object,
    default: () => {
      return []
    }
  },
  analysis: {
    type: String,
    default: ''
  }
})
const activeName = ref('ana')

function clickToGoToReferredSection(clickedSectionItem) {
  const headingItem = clickedSectionItem[clickedSectionItem.length - 1]
  // 如果章节id不匹配
  if (
    !store.comprehensiveChapterAndCatalogData.chaptersData.some(item => {
      return item.chapterId == headingItem.chapterId
    })
  ) {
    return ElMessage.error('暂无知识点')
  }
  // domid存在
  if (headingItem && headingItem.domId) {
    store
      .jumpToPageBasedOnNodeId(headingItem.domId, headingItem.chapterId)
      .then(() => {
        // 参考知识点保存了目录的完整路径，有层级概念，所以具体是h几看位置就可以
        const level = clickedSectionItem.length - 1
        const targetHeadingDom = document.querySelector(`h${level}[${HEADING_ATTR_ID}="${headingItem.domId}"]`)
        if (targetHeadingDom) {
          highlightKeyWordSmooth(targetHeadingDom)
        }
      })
      .catch(err => {
        if (err.code === PAGE_TURNING_ERROR_MAPPER.CHAPTER_DATA_NOT_EXIST.code) {
          ElMessage.error(PAGE_TURNING_ERROR_MAPPER.CHAPTER_DATA_NOT_EXIST.msg)
        }
        return err
      })
  } else {
    // domid不存在，直接跳转章节
    store.jumpToChapter(headingItem.chapterId)
  }
}
</script>