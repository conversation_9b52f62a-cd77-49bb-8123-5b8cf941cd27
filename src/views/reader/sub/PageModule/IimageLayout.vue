<!-- 模块说明 -->
<style lang="scss" scoped></style>

<template>
  <div
    :style="{
      display: 'flex',
      justifyContent: nodeAlign,
      maxWidth: '100%',
    }"
  >
    <div
      :style="{
        textAlign: 'center',
        margin: '20px 0',
      }"
      :id="id"
    >
      <el-image
        :src="src"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        :preview-src-list="[src]"
        @click="handleClick"
        style="cursor: pointer"
        :initial-index="4"
        fit="cover"
        :style="{
          width: width + 'px',
          height: height + 'px',
          transform:
            flipX || flipY
              ? `rotateX(${flipX ? '180' : '0'}deg) rotateY(${flipY ? '180' : '0'}deg)`
              : 'none',
        }"
      />
      <div style="line-height: 20px">
        <a :href="linkAddress" target="_blank">
          <span style="padding-right: 10px" v-if="isShowNo == 1">{{
            number
          }}</span
          ><span v-if="isShowImageTitle == '1'">{{ imageTitle }}</span>
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'
import { getBookResource, getChapters } from '@/api/book/reader'
import usePreview from '@/store/modules/preview.js'
import useReader from '@/store/modules/reader.js'
const previewStore = usePreview()
const store = useReader()

const props = defineProps({
  height: [Number, String],
  src: String,
  nodeAlign: String,
  name: String,
  isShowImageTitle: String,
  number: String,
  id: String,
  width: [Number, String],
  linkAddress: String,
  isShowNo: String,
  imageTitle: String,
  flipX: Boolean,
  flipY: Boolean,
});
async function handleClick() {
  const params = {
    bookId: store.comprehensiveBookData.bookId,
    resourceType: 0,
    chapterId: undefined,
    fileType: '1'
  }
  const res = await getBookResource(params)
  if (res.code === 200) {
    const result = res.data
    let imageResources = []
    result.forEach(item => {
      imageResources = imageResources.concat(item.bookResourcesList)
    })
    previewStore.setPreviewImages(imageResources)
    nextTick(() => {
      // previewStore.setCurrentImage(props.src)
    })
  }
}
</script>
