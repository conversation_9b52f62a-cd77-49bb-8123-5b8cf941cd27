<!-- 模块说明 -->
<style lang="scss" scoped>
.image-title {
  text-align: center;
  font-size: 14px;
  color: #666666;
  margin-top: 10px;
}
</style>

<template>
  <div :id="id"
    :style="{ textAlign: nodeAlign, padding: '20px 0' }">
    <el-image :src="src"
      :zoom-rate="1.2"
      :max-scale="7"
      :min-scale="0.2"
      :preview-src-list="[src]"
      :initial-index="4"
      fit="cover" />

    <div
      v-if="isShowImageTitle === '1'"
      class="image-title"
      @click="toLine">
      <a :href="linkAddress"
        target="_blank">{{ imageTitle }}</a>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'
import usePreview from '@/store/modules/preview.js'
import useReader from '@/store/modules/reader.js'
const previewStore = usePreview()
const store = useReader()

const props = defineProps({
  src: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: '100%'
  },
  width: {
    type: String,
    default: '100%'
  },
  name: {
    type: String,
    default: ''
  },
  nodeAlign: {
    type: String,
    default: 'center'
  },
  isShowImageTitle: {
    type: String,
    default: '0'
  },
  isShowNo: {
    type: String,
    default: '0'
  },
  linkAddress: {
    type: String,
    default: ''
  },
  imageTitle: {
    type: String,
    default: ''
  }
})

const toLine = () => {
  if (props.linkAddress) {
    window.open(props.linkAddress)
  }
}

async function handleClick() {
  const params = {
    bookId: store.comprehensiveBookData.bookId,
    resourceType: 0,
    chapterId: undefined,
    fileType: '1'
  }
  const res = await getBookResource(params)
  if (res.code === 200) {
    const result = res.data
    let imageResources = []
    result.forEach(item => {
      imageResources = imageResources.concat(item.bookResourcesList)
    })
    previewStore.setPreviewImages(imageResources)
    nextTick(() => {
      previewStore.setCurrentImage(props.src)
    })
  }
}
</script>
