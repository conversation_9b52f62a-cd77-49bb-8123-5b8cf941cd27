<!-- 连线 -->
<style lang="scss" scoped>
.questionType {
  .stem {
    margin: 10px 0;
  }
  .answer {
    position: relative;
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    .left,
    .right {
      width: 240px;
    }
    .centers {
      flex: 1;
      height: 100%;
    }
    cursor: pointer;
    .leftItem,
    .rightItem {
      border: 1px solid #999;
      border-radius: 8px;
      line-height: 25px;
      box-sizing: border-box;
      margin: 10px 0;
      position: relative;

      transition: 0.3s;
      height: 89px;
      ::v-deep(div) {
        display: flex;
        align-items: center;
        justify-content: center;
        p {
          img {
            max-width: 100px;
            max-height: 50px;
          }
        }
      }

      .content {
        padding: 0 20px;
        overflow: hidden;
        height: 80px;
        line-height: 25px;
      }
      .anchorPoint {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .rightItemHover:hover {
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
      cursor: pointer;
    }
    .leftItem {
      .anchorPoint {
        right: -12px;
        background-color: #fff;
        cursor: pointer;
      }
    }
    .rightItem {
      .anchorPoint {
        left: -8px;
        width: 15px;
        height: 15px;
        background-color: #666;
        border-radius: 50%;
      }
    }
    .maskLayer {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      cursor: not-allowed;
    }
  }
  .analysis {
    .analysisItem {
      display: flex;
      align-items: center;
      margin: 10px 0;
      .label {
        margin: 0 16px;
      }
    }
  }
  .line {
    margin: 10px 0;
    border: 1px solid #e5e6e7;
  }
  .footer {
    text-align: center;
  }
}
</style>

<template>
  <section class="questionType">
    <div class="stem" v-html="data.questionContent"></div>
    <div class="answer" ref="stemBoxRefs">
      <div class="left">
        <div
          class="leftItem"
          v-for="ele in originalData.left"
          :key="ele.optionId"
          ref="leftItemRefs"
        >
          <div v-html="ele.optionContent" class="content"></div>
          <el-icon class="anchorPoint" size="25" @click="startDrawingLines(ele)"
            ><CirclePlus
          /></el-icon>
        </div>
      </div>

      <div class="centers" ref="canvasRefs">&nbsp;</div>

      <div class="right">
        <div
          :class="`rightItem ${drawingLines.isDrawing ? 'rightItemHover' : ''}`"
          v-for="ele in originalData.right"
          :key="ele.optionId"
          ref="rightItemRefs"
          @click="clickRightItem(ele)"
          @mouseenter="mouseenterRightItem(ele)"
        >
          <div class="content" v-html="ele.optionContent"></div>
          <div class="anchorPoint"></div>
        </div>
      </div>
      <div class="maskLayer" v-show="status.status"></div>
    </div>

    <div class="analysis" v-show="status.status">
      <div class="analysisItem">
        <div>
          我的答案：{{ removeDuplicates(status.myAnswerText) || "未作答" }}
        </div>
        <div style="padding-left: 16px; box-sizing: border-box"></div>
        &nbsp;
        <el-icon color="#70cd79" v-if="status.isCorrect"
          ><CircleCheckFilled
        /></el-icon>
        <el-icon color="#f57878" v-else><CircleCloseFilled /></el-icon>
      </div>
      <div class="analysisItem">正确答案：{{ status.rightKeyText }}</div>
      <question-hint
        v-if="status.status"
        :analysis="data.analysis"
        :sectionReferToData="data.sectionReferTo"
      ></question-hint>
    </div>
    <hr class="line" />
    <div class="footer" v-if="footer">
      <el-button
        @click="redo"
        :disabled="!status.status"
        size="large"
        style="width: 100px; height: 36px"
        >重做</el-button
      >
      <el-button
        type="primary"
        @click="submit"
        size="large"
        :disabled="status.status"
        color="#0966b4"
        style="width: 100px; height: 36px"
        >提交</el-button
      >
    </div>
  </section>
</template>

<script setup>
import {
  ref,
  reactive,
  defineProps,
  onMounted,
  nextTick,
  onBeforeUnmount,
  defineEmits,
} from "vue";
import {
  CircleCheckFilled,
  CircleCloseFilled,
  CirclePlus,
} from "@element-plus/icons-vue";
import { addQuestionAnswer } from "@/api/book/reader";
import { uuid } from "@/utils/index";
import useReader from "@/store/modules/reader";
import QuestionHint from "@/views/reader/sub/PageModule/question-hint/questionHint";

const store = useReader();
const { proxy } = getCurrentInstance();
const emit = defineEmits(["submitTool"]);
const { data } = defineProps({
  data: Object,
  footer: {
    type: Boolean,
    default: true,
  },
});
const canvasRefs = ref(null);
const ctx = ref(null);
const stemBoxRefs = ref(null);
const canvasDom = ref(null);
const leftItemRefs = ref([]);
const rightItemRefs = ref([]);
const status = reactive({
  status: false,
  rightKey: [], //正确答案
  rightKeyText: "", //正确答案文字描述
  myAnswer: [], //我的答案
  myAnswerText: "", //我的答案文字描述
  isCorrect: false,
});
const drawingLines = reactive({
  isDrawing: false, //当前是否正在绘制
  y1: 0,
  line: [], //{ startY: 0, endY: 0 }
});
const originalData = reactive({
  left: [],
  right: [],
});

const redo = () => {
  status.isCorrect = false;
  status.rightKeyText = "";
  status.myAnswerText = "";
  status.myAnswer = [];
  status.status = false;
  status.isCorrect = false;
  drawingLines.line = [];
  ctx.value.clearRect(0, 0, canvasDom.value.width, canvasDom.value.height);
};

function removeDuplicates(input) {
  // 按 "；" 分割字符串为数组
  const items = input.split("；");

  // 使用 Set 去重
  const uniqueItems = [...new Set(items)];

  // 将去重后的数组重新用 "；" 连接为字符串
  return uniqueItems.join("；");
}

const submit = async () => {
  if (!status.myAnswer.length) return proxy.$message.error("请连线后提交");
  const sort_rightKey = status.rightKey.sort((a, b) => a.source - b.source);
  const sort_myAnswer = status.myAnswer.sort((a, b) => a.source - b.source);
  if (
    JSON.stringify(sort_rightKey) ===
    JSON.stringify(
      sort_myAnswer.map((sort) => {
        return {
          ...sort,
          key: undefined,
        };
      })
    )
  ) {
    status.isCorrect = true;
  }
  let rightKeyText = "";
  sort_rightKey.forEach((ele) => {
    rightKeyText += `左${ele.source + 1}----右${ele.target + 1} ；`;
  });
  status.rightKeyText = rightKeyText;

  let myAnswerText = "";
  sort_myAnswer.forEach((ele) => {
    myAnswerText += `左${ele.source + 1}----右${ele.target + 1} ；`;
  });
  status.myAnswerText = myAnswerText;
  const params = {
    ...data.params,
    answerContent: JSON.stringify(status.myAnswer),
    score: status.isCorrect ? 100 : 0,
  };

  const res = await addQuestionAnswer(params);
  if (res.code !== 200) return proxy.$message.error("网络错误情稍后重试");
  status.status = true;
};

/**
 * 开始划线
 */
const startDrawingLines = (val) => {
  drawingLines.y1 = val.y;
  drawingLines.isDrawing = true;
  status.myAnswer.push({ source: val.index, target: null });
  drawingLines.line.push({ startY: val.y, endY: 0 });
};

/**
 * 选中右侧
 */
const clickRightItem = (val) => {
  const key = uuid();
  const lastItem = status.myAnswer.pop();
  status.myAnswer.push({ ...lastItem, target: val.index, key });
  const lineItem = drawingLines.line.pop();
  drawingLines.line.push({ ...lineItem, endY: val.y, key });
  drawingLines.isDrawing = false;
};

/**
 * 判断当前鼠标的所属位置在不在canvas的线上
 */
const containsPoint = (x, y) => {
  let arr = [];
  drawingLines.line.forEach((ele) => {
    let x1 = 0;
    let y1 = ele.startY;
    let x2 = canvasDom.value.width;
    let y2 = ele.endY;
    const dx = x2 - x1;
    const dy = y2 - y1;
    const length = Math.sqrt(dx * dx + dy * dy); // 线段长度
    const u = ((x - x1) * dx + (y - y1) * dy) / (length * length); // 点到线的投影比例
    const ix = x1 + u * dx; // 投影点的x坐标
    const iy = y1 + u * dy; // 投影点的y坐标
    const ix_diff = ix - x; // 投影点与点击点的x差值
    const iy_diff = iy - y; // 投影点与点击点的y差值
    // 检查距离是否小于某个阈值（例如5px）
    if (Math.sqrt(ix_diff * ix_diff + iy_diff * iy_diff) < 5) {
      arr.push({ ...ele });
    }
  });
  return arr;
};

/**
 * canvas的鼠标滑过事件
 */
const mousemoveLine = (event) => {
  if (drawingLines.isDrawing) {
    // 清除之前的线段，并绘制跟随鼠标的线段
    ctx.value.clearRect(0, 0, canvasDom.value.width, canvasDom.value.height);
    drawLine(0, drawingLines.y1, event.offsetX, event.offsetY);
  } else {
    const rect = canvasDom.value.getBoundingClientRect(); // 获取Canvas的绝对位置和尺寸
    const x = event.clientX - rect.left; // 点击的x坐标相对于Canvas的x坐标
    const y = event.clientY - rect.top; // 点击的y坐标相对于Canvas的y坐标
    const [line] = containsPoint(x, y);
    if (line) {
      ctx.value.beginPath();
      ctx.value.moveTo(0, line.startY);
      ctx.value.lineTo(canvasDom.value.width, line.endY);
      ctx.value.strokeStyle = "red"; // 根据isHovering改变颜色
      ctx.value.stroke();
      canvasDom.value.style.cursor = "pointer";
    } else {
      ctx.value.clearRect(0, 0, canvasDom.value.width, canvasDom.value.height);
      drawLine();
      canvasDom.value.style.cursor = "default";
    }
  }
};
const mouseleaveLine = () => {
  if (!drawingLines.isDrawing) {
    ctx.value.clearRect(0, 0, canvasDom.value.width, canvasDom.value.height);
    drawLine();
    canvasDom.value.style.cursor = "default";
  }
};

/**
 * canvas线的鼠标点击事件
 *
 */

const clickLine = (e) => {
  if (drawingLines.isDrawing) return;
  const rect = canvasDom.value.getBoundingClientRect(); // 获取Canvas的绝对位置和尺寸
  const x = e.clientX - rect.left; // 点击的x坐标相对于Canvas的x坐标
  const y = e.clientY - rect.top; // 点击的y坐标相对于Canvas的y坐标
  const [line] = containsPoint(x, y);
  if (line) {
    drawingLines.line = drawingLines.line.filter((ele) => ele.key !== line.key);
    status.myAnswer = status.myAnswer.filter((ele) => ele.key !== line.key);
    ctx.value.clearRect(0, 0, canvasDom.value.width, canvasDom.value.height);
    drawLine();
  }
};

const mouseenterRightItem = (val) => {
  if (drawingLines.isDrawing) {
    ctx.value.clearRect(0, 0, canvasDom.value.width, canvasDom.value.height);
    drawLine(0, drawingLines.y1, canvasDom.value.width, val.y);
  }
};

function drawLine(x1, y1, x2, y2) {
  ctx.value.beginPath();
  drawingLines.line.forEach((ele) => {
    if (ele.endY) {
      ctx.value.moveTo(0, ele.startY);
      ctx.value.lineTo(canvasDom.value.width, ele.endY);
    }
  });
  if (y1) {
    ctx.value.moveTo(x1, y1);
    ctx.value.lineTo(x2, y2);
  }
  ctx.value.strokeStyle = "#000000";
  ctx.value.stroke();
}

const init = () => {
  originalData.left = data.options.filter((ele) => ele.optionPosition === 1);
  originalData.right = data.options.filter((ele) => ele.optionPosition === 2);
  status.rightKey = JSON.parse(data.rightAnswer);
  nextTick(() => {
    originalData.left = originalData.left.map((ele, i) => {
      return {
        ...ele,
        y:
          leftItemRefs.value[i].offsetTop +
          leftItemRefs.value[i]?.offsetHeight / 2,
        index: i,
      };
    });
    originalData.right = originalData.right.map((ele, i) => {
      return {
        ...ele,
        y:
          rightItemRefs.value[i].offsetTop +
          rightItemRefs.value[i].offsetHeight / 2,
        index: i,
      };
    });
    canvasDom.value = document.createElement("canvas");
    ctx.value = canvasDom.value.getContext("2d");
    canvasDom.value.width = canvasRefs.value.offsetWidth;
    canvasDom.value.height = stemBoxRefs.value.offsetHeight;
    canvasRefs.value.innerHTML = "";
    canvasRefs.value.appendChild(canvasDom.value);
    canvasDom.value.addEventListener("mousemove", mousemoveLine);
    canvasDom.value.addEventListener("mouseleave", mouseleaveLine);
    canvasDom.value.addEventListener("click", clickLine);
    if (data.defaultValue) {
      status.myAnswer = JSON.parse(data.defaultValue);
      drawingLines.line = status.myAnswer.map((ele) => {
        return {
          startY: originalData.left[ele.source - 0].y,
          endY: originalData.right[ele.target - 0].y,
        };
      });
      onAnalysis();
      drawLine();
    }
  });
};

const validation = () => !!status.myAnswer.length;
const getData = () => {
  const sort_rightKey = status.rightKey.sort((a, b) => a.source - b.source);
  const sort_myAnswer = status.myAnswer.sort((a, b) => a.source - b.source);
  const new_sort_myAnswer = sort_myAnswer.map(({ key, ...rest }) => rest);

  if (JSON.stringify(sort_rightKey) === JSON.stringify(new_sort_myAnswer)) {
    status.isCorrect = true;
  }
  let rightKeyText = "";
  sort_rightKey.forEach((ele) => {
    rightKeyText += `左${ele.source + 1}----右${ele.target + 1} ；`;
  });
  status.rightKeyText = rightKeyText;

  let myAnswerText = "";
  sort_myAnswer.forEach((ele) => {
    myAnswerText += `左${ele.source + 1}----右${ele.target + 1} ；`;
  });
  status.myAnswerText = myAnswerText;
  return {
    dtbBookQuestionAnswer: {
      answerContent: JSON.stringify(status.myAnswer),
      questionId: data.questionId,
      score: status.isCorrect ? 100 : 0,
    },
    score: status.isCorrect ? data.questionScore : 0, //分值
  };
};
const onAnalysis = () => {
  getData();
  status.status = true;
};
onMounted(() => {
  init();
  emit("submitTool", {
    validation,
    getData,
    onAnalysis,
  });
});

onBeforeUnmount(() => {
  canvasDom.value.removeEventListener("mousemove", mousemoveLine);
  canvasDom.value.removeEventListener("mouseleave", mouseleaveLine);
  canvasDom.value.removeEventListener("click", clickLine);
});
</script>
