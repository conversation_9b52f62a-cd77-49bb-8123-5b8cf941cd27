<!-- 作业 -->

<style lang="less" scoped>
.video-parent {
  background-image: url('@/assets/images/readerResourcesIcon/modelBackground.png');
  margin: 10px 0;
  .el-collapse-item__header {
  }
  .el-collapse-item__content {
    padding: 0;
  }
  .collapse-main {
    display: flex;
    align-items: center;
    padding: 0 20px;
    font-family: 'SimSun';
    .icon {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      margin-right: 10px;
      & > img {
        width: 100%;
        height: 100%;
      }
    }
    .title {
      width: 70px;
      font-size: 16px;
      text-align: left;
    }
    .name {
      flex: 1;
      font-size: 16px;
      padding-left: 10px;
    }
  }
}
.PapersContent {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #f1f1f1;

  .PapersContentItem {
    .title {
      font-weight: bold;
      font-size: 16px;
    }
    .content {
      width: 100%;
      padding: 0 20px;
      box-sizing: border-box;
    }
  }
  .footer {
    margin-top: 20px;
    text-align: right;
  }
}
</style>

<template>
  <el-collapse
    v-model="activeAfterClassExercise"
    @change="collapseChange">
    <el-collapse-item
      class="video-parent __orderTemplateBgUrl__"
      name="1">
      <template #title>
        <div
          class="collapse-main"
          ref="collapseHeaderRefs">
          <div class="icon">
            <img
              :src="templateStyle.theme == 'light' ? lightIcon : darkIcon" />
          </div>
          <div class="title"
            :style="{
              color: templateStyle.theme == 'light' ? '#fff' : '#333',
            }">
            作业
          </div>
          <div class="name">
            {{ paperInfo.paperTitle }}
          </div>
        </div>
      </template>
      <main
        class="PapersContent"
        :key="keys"
        v-loading="loading">
        <div
          class="PapersContentItem"
          v-for="(ele, i) in papersData"
          :key="ele.collectionId">
          <!--  -->
          <div class="title">
            <span
              style="padding-right: 10px">{{ NUM[i] }}</span>{{
              TYPE[ele.questionType - 1] === "排序题"
                ? TYPE[ele.questionType - 1] +
                  "提示：请对以下排序题进行操作，将选项拖拽至正确的位置。"
                : TYPE[ele.questionType - 1]
            }}
          </div>
          <div
            class="content">
            <component
              v-for="item in ele.questionList"
              :key="item.collectionId"
              :is="COMPONENTSLIST[ele.questionType - 1]"
              :OPTION="OPTION"
              :TYPE="TYPE"
              :data="item.questionsData"
              :footer="false"
              @submitTool="(obj) => submits.push(obj)"
              :questionTypeShow="ele.questionTypeShow">
            </component>
          </div>
        </div>
        <div class="footer"
          v-if="ableToResubmit">
          <el-button
            :disabled="!submitStatus"
            @click="reset">重置</el-button>
          <el-button
            type="primary"
            @click="submitThePaper">提交</el-button>
        </div>
      </main>
    </el-collapse-item>
  </el-collapse>
</template>

<script setup>
import { ref, defineProps, nextTick, onMounted } from 'vue'
import lightIcon from '@/assets/resources/light/papers.svg'
import darkIcon from '@/assets/resources/dark/papers.svg'
import { getTestPaperQuestions, addPapersAnswer, getPaperAnswer } from '@/api/book/reader'
import Topic_1 from './Topic_1.vue'
import Topic_2 from './Topic_2.vue'
import Topic_3 from './Topic_3.vue'
import Topic_4 from './Topic_4.vue'
import Topic_5 from './Topic_5.vue'
import Topic_6 from './Topic_6.vue'
import useReader from '@/store/modules/reader'
const ableToResubmit = ref(true)
const activeAfterClassExercise = ref('')
const store = useReader()
const { proxy } = getCurrentInstance()
const COMPONENTSLIST = [Topic_1, Topic_2, Topic_3, Topic_4, Topic_5, Topic_6, Topic_1]
const NUM = ['一', '二', '三', '四', '五', '六', '七']
const TYPE = ['单选题', '多选题', '填空题', '排序题', '连线题', '简答题', '判断题']
const OPTION = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'G',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z'
]
const props = defineProps({
  paperInfo: Object,
  id: String
})
const keys = ref(1)
const collapseHeaderRefs = ref(null)
const submitStatus = ref(false)
// const isShow = ref(false);
const papersData = ref([])
const submits = ref([]) //小题组件所有的提交事件
const loading = ref(false)
const collapseChange = v => {
  if (v.length) {
    previewResource()
  } else {
    isShow.value = false
  }
}

const templateStyle = computed(() => {
  const obj = {}
  let tmpStyle = store.templateStyle
  obj.theme = tmpStyle?.theme
  return obj
})

const reset = () => {
  papersData.value = papersData.value.map(ele => ({
    ...ele,
    questionList: ele.questionList.map(item => ({
      ...item,
      questionsData: { ...item.questionsData, defaultValue: undefined }
    }))
  }))
  keys.value++
  submits.value = []
  nextTick(() => {
    collapseHeaderRefs.value.scrollIntoView({ behavior: 'smooth' })
    submitStatus.value = false
  })
}

/**
 * 监测字符串是否为富文本
 */
function isRichText(str) {
  const div = document.createElement('div')
  div.innerHTML = str
  const _s = div.children.length > 0 || div.childNodes.length > 1
  div.remove()
  return _s
}

/**
 * 向题干开头和结尾添加小题序号和分值
 */
const addserialNumber = (num, str, branch) => {
  let _t = ''
  if (isRichText(str)) {
    const div = document.createElement('div')
    div.innerHTML = str
    div.children[0].insertAdjacentHTML('afterbegin', `${num}、`)
    // div.children[0].insertAdjacentHTML('beforeend', `(${branch}分)`)
    _t = div.innerHTML
    div.remove()
  } else {
    _t = `${num}、${str}`
    // _t = `${num}、${str}(${branch}分)`
  }
  return _t
}

const previewResource = async () => {
  loading.value = true

  let lastSaveAnswer = []
  const saveData = await getPaperAnswer({
    chapterId: store.chapterId,
    bookId: store.comprehensiveBookData.bookId,
    paperId: props.paperInfo.paperId
  })
  if (saveData.data) {
    lastSaveAnswer = saveData.data.dtbBookTestPaperDetailList
    submitStatus.value = true
  }
  const res = await getTestPaperQuestions(props.paperInfo.paperId)
  if (res.code === 200) {
    papersData.value = res.data.map(ele => {
      return {
        ...ele,

        questionList: ele.questionList.map((item, index) => {
          const val = item.questionContent
          const lastSaveAnswerItem = lastSaveAnswer.find(_e => _e.questionId === item.questionId)
          return {
            ...item,
            questionsData: {
              questionText: TYPE[val.questionType - 1],
              questionType: val.questionType,
              questionContent: addserialNumber(index + 1, val.questionContent, item.questionScore),
              analysis: val.analysis,
              options: val.options,
              rightAnswer: val.rightAnswer,
              questionScore: item.questionScore, //分值
              questionId: item.questionId,
              defaultValue: lastSaveAnswerItem?.answerContent
            }
          }
        })
      }
    })
    loading.value = false
  }
}

const submitThePaper = () => {
  const params = {
    chapterId: store.chapterId,
    bookId: store.comprehensiveBookData.bookId,
    paperId: props.paperInfo.paperId,
    domId: props.id,
    dtbBookQuestionAnswerList: []
  }
  const subjectStatus = [] //记录所有题目的状态,是否有没答的题目
  submits.value.forEach(ele => {
    subjectStatus.push(ele.validation())
  })

  if (!subjectStatus.includes(false)) {
    submits.value.forEach(ele => {
      const element = ele.getData()
      console.log('params~~~~~~~~~~', element)
      params.dtbBookQuestionAnswerList.push(element.dtbBookQuestionAnswer)
    })

    // 校验填空提示正确答案 是否还有html代码

    addPapersAnswer(params).then(res => {
      if (res.code === 200) {
        submits.value.forEach(ele => {
          ele.onAnalysis()
        })
        submitStatus.value = true
      }
    })
  } else {
    proxy.$message.error('请将所有题目作答完成后提交')
  }
}
onMounted(() => {
  const simplified = inject('simplifiedReadingMode')
  if (simplified) {
    ableToResubmit.value = false
  }
})
</script>
