<style lang="scss" scoped>
.questionType {
  .stem {
    margin-top: 10px;
    display: flex;
  }
  .answer {
    display: flex;
    align-items: center;
    span {
      margin-right: 20px;
    }
  }
  .analysis {
    .analysisBg {
      padding: 10px 23px;
      background: linear-gradient(180deg, #f7f7f7 0%, #f7f7f7 100%);
    }
    .analysisItem {
      display: flex;
      align-items: center;

      font-size: 14px;
      .label {
        margin: 0 16px;
      }
    }
  }
  .line {
    margin: 10px 0;
    border: 1px solid #e5e6e7;
  }
  .footer {
    text-align: center;
  }
}
</style>

<template>
  <section class="questionType">
    <div class="stem" v-html="data.questionContent"></div>
    <el-radio-group v-model="status.answer" :disabled="status.status">
      <div>
        <div v-for="(ele, i) in data.options" :key="i">
          <el-radio :value="OPTION[i]">
            <section class="answer">
              <span>{{ OPTION[i] }}</span>
              <div v-html="ele.optionContent"></div>
            </section>
          </el-radio>
        </div>
      </div>
    </el-radio-group>
    <div class="analysis" v-show="status.status">
      <div class="analysisBg">
        <div class="analysisItem">正确答案：{{ status.rightKey }}</div>
        <div class="analysisItem">
          我的答案：{{ status.answer }}
          &nbsp;
          <el-icon color="#70cd79" v-if="status.rightKey === status.answer"
            ><CircleCheckFilled
          /></el-icon>
          <el-icon color="#f57878" v-else><CircleCloseFilled /></el-icon>
        </div>
      </div>

      <question-hint
        v-if="status.status"
        :analysis="data.analysis"
        :sectionReferToData="data.sectionReferTo"
      ></question-hint>
    </div>
    <hr class="line" />
    <div class="footer" v-if="footer">
      <el-button @click="redo" size="large" style="width: 100px; height: 36px"
        >重做</el-button
      >
      <el-button
        type="primary"
        @click="submit"
        :disabled="status.status"
        size="large"
        color="#0966b4"
        style="width: 100px; height: 36px"
        >提交</el-button
      >
    </div>
  </section>
</template>

<script setup>
import QuestionHint from "@/views/reader/sub/PageModule/question-hint/questionHint";
import { reactive, defineProps, defineEmits, onMounted } from "vue";
import { CircleCheckFilled, CircleCloseFilled } from "@element-plus/icons-vue";
import { addQuestionAnswer } from "@/api/book/reader";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["submitTool"]);
const { OPTION, data } = defineProps({
  OPTION: Array,
  data: Object,
  footer: {
    type: Boolean,
    default: true,
  },
  questionTypeShow: {
    type: String,
    default: "1",
  },
});
const status = reactive({
  answer: "",
  status: false,
  rightKey: "", //正确答案合集
});
const redo = () => {
  status.answer = "";
  status.status = false;
};

const submit = async () => {
  if (!status.answer) return proxy.$message.error("请选择答案后提交");
  data.options.forEach((ele, i) => {
    if (ele.rightFlag) {
      status.rightKey = OPTION[i];
    }
  });
  const params = {
    ...data.params,
    answerContent: status.answer,
    score: status.rightKey === status.answer ? 100 : 0,
  };
  const res = await addQuestionAnswer(params);
  if (res.code !== 200) return proxy.$message.error("网络错误情稍后重试");
  status.status = true;
};

const validation = () => !!status.answer;
const getData = () => {
  data.options.forEach((ele, i) => {
    if (ele.rightFlag) {
      status.rightKey = OPTION[i];
    }
  });
  return {
    dtbBookQuestionAnswer: {
      answerContent: status.answer,
      questionId: data.questionId,
      score: status.rightKey === status.answer ? 100 : 0,
    },
    score: status.rightKey === status.answer ? data.questionScore : 0, //分值
  };
};
const onAnalysis = () => {
  getData();
  status.status = true;
};

onMounted(() => {
  emit("submitTool", {
    validation,
    getData,
    onAnalysis,
  });
  if (data.defaultValue) {
    status.answer = data.defaultValue;
    onAnalysis();
  }
});
</script>
