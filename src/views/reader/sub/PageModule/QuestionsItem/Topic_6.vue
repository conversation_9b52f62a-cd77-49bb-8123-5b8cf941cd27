<!-- 6简答 -->
<style lang="scss" scoped>
.questionType {
  .stem {
    margin: 10px 0;
  }
  .answer {
    margin-bottom: 15px;
  }
  .analysis {
    .analysisItem {
      display: flex;
      align-items: center;
      margin: 10px 0;
      .label {
        margin: 0 16px;
      }
    }
  }
  .footer {
    text-align: right;
  }
}
</style>

<template>
  <section class="questionType">
    <div class="stem" v-html="data.questionContent"></div>
    <div class="answer">
      <el-input
        type="textarea"
        v-model="status.answer"
        :disabled="status.status"
        :autosize="{ minRows: 8 }"
      />
    </div>
    <div class="analysis" v-show="status.status">
      <div>正确答案：</div>
      <div class="analysisItem">
        <div class="label"></div>
        <div v-html="data.rightAnswer"></div>
      </div>
      <question-hint
        v-if="status.status"
        :analysis="data.analysis"
        :sectionReferToData="data.sectionReferTo"
      ></question-hint>
    </div>
    <div class="footer" v-if="footer">
      <el-button
        @click="redo"
        :disabled="!status.status"
        style="width: 100px; height: 36px"
        >重做</el-button
      >
      <el-button
        type="primary"
        @click="submit"
        :disabled="status.status"
        color="#0966b4"
        style="width: 100px; height: 36px"
        >提交</el-button
      >
    </div>
  </section>
</template>

<script setup>
import { reactive, defineProps, defineEmits, onMounted } from "vue";
import { addQuestionAnswer } from "@/api/book/reader";
import QuestionHint from "@/views/reader/sub/PageModule/question-hint/questionHint";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["submitTool"]);
const { data } = defineProps({
  data: Object,
  footer: {
    type: Boolean,
    default: true,
  },
});
const status = reactive({
  answer: "",
  status: false,
  rightKey: "", //正确答案合集
});

const redo = () => {
  status.answer = "";
  status.status = false;
  status.rightKey = "";
};

const submit = async () => {
  if (!status.answer) return proxy.$message.error("请填写答案后提交");
  const params = { ...data.params, answerContent: status.answer, score: 0 };
  const res = await addQuestionAnswer(params);
  if (res.code !== 200) return proxy.$message.error("网络错误情稍后重试");
  status.status = true;
};

const validation = () => !!status.answer;
const getData = () => ({
  dtbBookQuestionAnswer: {
    answerContent: status.answer,
    questionId: data.questionId,
    score: 0,
  },
  score: 0, //分值
});
const onAnalysis = () => {
  getData();
  status.status = true;
};
onMounted(() => {
  emit("submitTool", {
    validation,
    getData,
    onAnalysis,
  });
  if (data.defaultValue) {
    status.answer = data.defaultValue;
    onAnalysis();
  }
});
</script>
