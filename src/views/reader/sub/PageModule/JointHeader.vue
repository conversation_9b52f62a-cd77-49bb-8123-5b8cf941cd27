<template>
  <div
    class="headerbg"
    :style="`height:${templateStyle.jointHeight / 2.5}px; background:url(${templateStyle.bgImg}) no-repeat; background-size:auto 100%;}`"
  >
    <div class="header-title">
      <!-- {{ title }} -->
      <slot></slot>
    </div>
  </div>
</template>
<script setup>
import { defineProps } from "vue";
import useReader from "@/store/modules/reader";

const store = useReader();
const props = defineProps({
  title: String,
  // bgImg: String,
  height: String,
  // color: String,
});

const templateStyle = computed(() => {
  const obj = {};
  let tmpStyle = store.templateStyle;
  obj.bgImg = tmpStyle?.jointHeaderUrl;
  obj.color = tmpStyle?.jointFontColor;
  obj.jointHeight = tmpStyle?.jointHeight;
  return obj;
});
</script>
<style lang="less" scoped>
.headerbg {
  width: 100%;
  display: flex;
  align-items: center;
  margin: 10px 0;
  .header-title {
    outline: none;
    ::v-deep(p) {
      margin: 0 !important;
    }
  }
}
</style>
