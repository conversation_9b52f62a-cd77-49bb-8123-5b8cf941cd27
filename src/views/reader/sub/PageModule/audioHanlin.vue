<template>
  <div class="audioHanlin-bg" @click="handleClick">
    <i class="audioHanlin-icon"></i>
    <div id="__audio__" style="display: none">
      <audio
        ref="audioHanlinRef"
        class="audio-play"
        :src="src"
        preload="auto"
        controls
        crossorigin="anonymous"
      ></audio>
    </div>
  </div>
</template>
<script setup>
import { ref } from "vue";
const props = defineProps({
  src: String,
  checkTabs: String,
});

const audioHanlinRef = ref(null);

const handleClick = () => {
  audioHanlinRef.value.play();
};
</script>

<style lang="less" scoped>
.audioHanlin-bg {
  display: inline-block;
  padding: 0 5px;
  cursor: pointer;

  .audioHanlin-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url("@/assets/images/hanlinAudio.png");
    background-repeat: no-repeat;
    background-size: contain;
  }
}
</style>
