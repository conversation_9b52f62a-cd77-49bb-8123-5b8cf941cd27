<style lang="less" scoped>
.File {
  width: 100%;
  height: 50px;
  background-image: url(@/assets/images/readerResourcesIcon/modelBackground.png);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  box-sizing: border-box;
  font-size: 16px;
  margin-bottom: 20px;
}
.collapse-main {
  display: flex;
  align-items: center;

  .icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    margin-right: 10px;
    & > img {
      width: 100%;
      height: 100%;
    }
  }
  .title {
    width: 70px;
    font-size: 16px;
    text-align: left;
  }
  .name {
    flex: 1;
    width: 500px;
    white-space: nowrap; /* 禁止文本换行 */
    overflow: hidden; /* 隐藏超出容器的内容 */
    text-overflow: ellipsis;
    padding-left: 20px;
  }
}

.__name__ {
  cursor: pointer;
}
</style>

<template>
  <div
    class="File __orderTemplateBgUrl__"
    v-if="getFileIcon(url, true) !== 'ppt'"
  >
    <div class="collapse-main">
      <div class="icon">
        <el-icon
          ><DocumentChecked
            size="17px"
            :color="templateStyle.theme == 'light' ? '#fff' : '#666'"
        /></el-icon>
      </div>
      <div
        class="title"
        :style="{
          color: templateStyle.theme == 'light' ? '#fff' : '#666',
        }"
      >
        文件
      </div>
      <div class="name">
        <el-tooltip :content="name">{{ name }}</el-tooltip>
      </div>
    </div>
    <div class="__name__" link @click="preview">打开</div>
  </div>
  <el-collapse v-model="activeNames" v-else>
    <el-collapse-item class="video-parent" name="1">
      <template #title>
        <div class="collapse-title">
          <img
            :src="getFileIcon(url)"
            alt=""
            style="width: 17px; height: 17px"
          />
          <span class="name">{{ name }}</span>
        </div>
      </template>
      <div>
        <iframe
          :src="getPreviewFileUrl(url)"
          frameborder="0"
          width="770px"
          height="500px"
          style="transform: translateY(-47px)"
        ></iframe></div
    ></el-collapse-item>
  </el-collapse>
</template>

<script setup>
import { DocumentChecked } from "@element-plus/icons-vue";
import { defineProps } from "vue";
import { getFileIcon, getPreviewFileUrl } from "@/utils/index.js";
import usePreview from "@/store/modules/preview.js";
import useReader from "@/store/modules/reader";
const previewStore = usePreview();
const props = defineProps({
  type: String,
  url: String,
  name: String,
  nodeAlign: String,
  size: Number,
});
const activeNames = ref(["1"]);
const store = useReader();
const templateStyle = computed(() => {
  const obj = {};
  let tmpStyle = store.templateStyle;
  obj.theme = tmpStyle?.theme;
  return obj;
});

const preview = () => {
  previewStore.setData({ url: props.url, title: props.name });
};
</script>
