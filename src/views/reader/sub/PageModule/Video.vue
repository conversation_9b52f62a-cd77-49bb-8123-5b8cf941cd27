<style lang="less" scoped>
.video {
  width: 100%;
  height: 210px;
  .video-play {
    width: 100%;
    height: 100%;
    background-color: #000;
  }
}
.video-name {
  padding: 5px 0;
  text-align: center;
  font-size: 16px;
  color: #333;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 超出容器部分的文本隐藏 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
}
</style>
<style lang="less" scoped>
// .el-collapse-item__header {
//   background-image: url(@/assets/images/readerResourcesIcon/modelBackground.png);
// }
// .el-collapse-item__content {
//   padding: 0;
// }
.collapse-main {
  display: flex;
  width: 100%;
  align-items: center;
  padding: 0 20px;
  justify-content: space-between;
  .left {
    display: flex;
    align-items: center;
    .icon {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      margin-right: 10px;
      & > img {
        width: 100%;
        height: 100%;
      }
    }
    .title {
      width: 70px;
      font-size: 16px;
      text-align: left;
    }
    .name {
      flex: 1;
      font-size: 16px;
      padding-left: 10px;
      width: 500px;
      white-space: nowrap; /* 确保文本在一行内显示 */
      overflow: hidden; /* 超出容器部分的文本隐藏 */
      text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
    }
  }
  .right {
    color: #666;
    cursor: pointer;
    font-size: 14px;
  }
}

.videoCss {
  width: 100%;
  background: #fff;
  .video-play {
    width: 100%;
  }
}
</style>

<template>
  <div :id="id">
    <div
      class="collapse-main"
      @click="handleShow"
      :style="`background-image:url('${templateStyle.orderTemplateBgUrl || modelBackground}');background-size:100% 100%;width:100%; height:55px;margin: 10px 0;`"
    >
      <!--  -->
      <div class="left">
        <div class="icon">
          <img :src="templateStyle.theme == 'light' ? lightIcon : darkIcon" />
        </div>
        <div
          class="title"
          :style="{
            color: templateStyle.theme == 'light' ? '#fff' : '#333',
          }"
        >
          视频
        </div>
        <div class="name">
          <el-tooltip :content="videoTitle">{{ videoTitle }}</el-tooltip>
        </div>
      </div>
      <div class="right">{{ show ? "收起" : "展开" }}</div>
    </div>
    <div v-show="show" class="videoCss" :theFirstFrame="theFirstFrame">
      <video
        class="video-play"
        :src="src"
        preload="metadata"
        controls
        oncontextmenu="return false"
        crossorigin="anonymous"
        ref="videoRef"
        controlslist="nodownload"
        @canplay="loadeddata"
        @loadeddata="onVideoLoaded"
      >
        <source :src="src" type="video/mp4" />
        您的浏览器不支持视频播放。
      </video>
    </div>
  </div>
</template>

<script setup>
import { defineProps, ref } from "vue";
import html2canvas from "html2canvas";
import modelBackground from "@/assets/images/readerResourcesIcon/modelBackground.png";
import lightIcon from "@/assets/resources/light/video.svg";
import darkIcon from "@/assets/resources/dark/video.svg";
import useReader from "@/store/modules/reader";
const props = defineProps({
  height: String,
  src: String,
  checkTabs: String,
  nodeAlign: String,
  videoTitle: String,
  id: String,
});
let videoRef = ref(null);
let show = ref(false);
let theFirstFrame = ref(null);
const store = useReader();
const loading = ref(false);
const videoLoaded = ref(false);
const templateStyle = computed(() => {
  const obj = {};
  let tmpStyle = store.templateStyle;
  obj.theme = tmpStyle?.theme;
  obj.orderTemplateBgUrl = tmpStyle?.orderTemplateBgUrl;
  return obj;
});

const loadeddata = (e) => {
  if (props.checkTabs === "export") {
    setTimeout(async () => {
      const canvas = await html2canvas(videoRef.value);
      theFirstFrame.value = canvas.toDataURL("image/png");
    }, 100);
  }
};

const handleShow = () => {
  loading.value = true;
  requestAnimationFrame(() => {
    show.value = !show.value;
    if (show.value) {
      loading.value = false;
      videoRef.value.play();
    } else {
      videoRef.value.pause();
    }
  });
};

const onVideoLoaded = (e) => {
  videoLoaded.value = true;
};
</script>
