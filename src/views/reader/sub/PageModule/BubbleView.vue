<style lang="less" scoped>
.slotCss {
  max-width: 500px;
  font-size: 14px;
  line-height: 25px;
}

.umo-popup__arrow {
  bottom: -3px;
}

.bubble-main {
  margin-right: 0px;
}

.bubble-url {
  text-align: right;
}
.bubbleIcon {
  display: inline-block;
  min-width: 16px;
  min-height: 16px;
  background: url("@/assets/images/bubble-icon.svg") no-repeat;
  background-size: 100% 100%;
}

.bubbleTitleCss {
  font-weight: bold;
  color: #0966b4;
}
</style>

<template>
  <span class="bubble-main">
    <el-tooltip placement="top-start">
      <span
        class="bubbleTitleCss"
        :style="`border-bottom:${bubbleType == '2' ? 'none' : '2px solid #0966b4;'};display:inline-block;`"
      >
        {{ bubbleType == "2" ? null : bubbleTitle.trim() }}
        <i class="bubbleIcon" v-if="bubbleType == '2'"></i>
      </span>

      <template #content>
        <div class="slotCss">
          <div v-html="bubbleContent"></div>
          <div class="bubble-url" v-if="bubbleUrl">
            <a :href="bubbleUrl" target="_blank">
              <el-icon><ChatRound /></el-icon>
            </a>
          </div>
        </div>
      </template>
    </el-tooltip>
  </span>
</template>

<script setup>
const { id, bubbleTitle, bubbleContent, bubbleUrl } = defineProps({
  id: {
    type: String,
    default: "",
  },
  bubbleTitle: {
    type: String,
    default: "",
  },
  bubbleType: {
    type: String,
    default: "1",
  },
  bubbleContent: {
    type: String,
    default: "",
  },
  bubbleUrl: {
    type: String,
    default: "",
  },
});
</script>
