<!-- 选中阅读器文字出现的公工具栏 -->
<style lang="scss" scoped>
.Selection {
  // background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  .Selection-nav {
    height: 28px;
    display: flex;
    margin-bottom: 5px;
    .Selection-nav-item {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background-color: #484949;
      color: #fff;
      margin: 0 3px;
      cursor: pointer;
      position: relative;
      &:hover {
        color: var(--hoverfont);
      }
    }
  }
  .Selection-nav-item2 {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 68px;
    height: 28px;
    border-radius: 28px;
    background-color: #484949;
    padding: 3px;
    box-sizing: border-box;
    & > section {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      cursor: pointer;
    }
    & > section:nth-of-type(1) {
      background-color: #ffc560;
    }
    & > section:nth-of-type(2) {
      background-color: #8bb4f0;
    }
    & > section:nth-of-type(3) {
      background-color: #f0828a;
    }
  }
  .Selection-content {
    width: 526px;
    height: 54px;
    background-image: url("@/assets/images/selectionTableBar/background.png");
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: space-around;
    align-items: center;
    .Selection-content-item {
      text-align: center;
      font-size: 12px;
      color: #fff;
      padding-bottom: 7px;
      box-sizing: border-box;
      position: relative;
      cursor: pointer;
      &:hover {
        color: var(--hoverfont);
      }
    }
  }
}
</style>

<template>
  <div
    class="Selection"
    :style="`left:${x - 263}px;top:${y - 54 - 33}px`"
    @click.stop="() => {}"
  >
    <div class="Selection-nav">
      <div
        class="Selection-nav-item __flex-center__"
        :style="'color:' + drawALineStyle.color"
        v-show="drawALineStatus"
        @click="setLineStyle('background')"
      >
        <svg-icon iconClass="A" style="font-size: 16px" />
      </div>
      <div
        class="Selection-nav-item __flex-center__"
        v-show="drawALineStatus"
        @click="setLineStyle('underline')"
      >
        <svg-icon iconClass="A1" style="font-size: 16px" />
      </div>
      <div
        class="Selection-nav-item __flex-center__"
        v-show="drawALineStatus"
        @click="setLineStyle('underline double')"
      >
        <svg-icon iconClass="A_2" style="font-size: 16px" />
      </div>
      <div
        class="Selection-nav-item __flex-center__"
        v-show="drawALineStatus"
        @click="setLineStyle('underline wavy')"
      >
        <svg-icon iconClass="A_3" style="font-size: 16px" />
      </div>
      <div class="Selection-nav-item2" v-show="drawALineStatus">
        <section @click="tabLineColor('#FFC560')"></section>
        <section @click="tabLineColor('#8BB4F0')"></section>
        <section @click="tabLineColor('#F0828A')"></section>
      </div>
    </div>
    <div class="Selection-content">
      <div
        class="Selection-content-item"
        v-for="ele in option"
        :key="ele.icon"
        @click="clickItem(ele.icon)"
        :style="
          ele.icon === 'underline' && drawALineStatus
            ? 'color: var(--hoverfont);'
            : ''
        "
      >
        <svg-icon :iconClass="ele.icon" style="font-size: 16px" />
        <div>{{ ele.title }}</div>
        <div
          class="AIicon"
          style="position: absolute; right: -8px; top: -5px"
          v-if="ele.ai"
        >
          <svg-icon iconClass="AIicon" style="font-size: 10px" />
        </div>
        <div
          class="AIicon"
          style="position: absolute; right: 4px; top: -5px"
          v-if="ele.icon === 'spirit'"
        >
          <svg-icon iconClass="AIicon" style="font-size: 10px" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, defineProps } from "vue";
import { saveBookLine, getBookLine, deleteBookLine } from "@/api/book/reader";
import useReader from "@/store/modules/reader";
import { getToken } from "@/utils/auth";
import {
  deriveParagraphIdFromKeywordComplexityId,
  deriveKeyWordIndexFromComplexityId,
} from "@/views/reader/sub/Pages/ParagraphTool";
import { findClosestPageNode, CHAPTER_PAGE_INDEX } from "@/utils/reader";

const store = useReader();
const STYLE_ID = "__abcdefghijklmnopqrstuvwxyz__";
const props = defineProps({
  x: {
    type: Number,
    default: 0,
  },
  y: {
    type: Number,
    default: 0,
  },
  elementList: {
    type: Array,
    default: () => [],
  },
  close: Function,
  isStatus: Boolean,
});
const drawALineStatus = ref(false);
// text-decoration underline --单实线 | underline double --双实线 | underline wavy --波浪线 | background --背景色
// text-decoration-color 下划线颜色
const drawALineStyle = reactive({
  color: "#FFC560",
  lineStyle: "underline",
});
const lineDefaultValue = ref({});
const idList = ref("");
const clickItem = (type) => {
  if (type !== "underline") {
    props.close(type);
  } else {
    addBookLine("own");
    createStyle();
  }
};
const tabLineColor = (v) => {
  drawALineStyle.color = v;
  addBookLine();
};
const setLineStyle = (v) => {
  drawALineStyle.lineStyle = v;
  addBookLine();
};

/**获取选中的文字*/
const getSelectedText = () => {
  console.log(props.elementList);
  let text = "";
  props.elementList
    .sort((a, b) => a.id.split("_")[1] - b.id.split("_")[1])
    .forEach((element) => {
      text += element.innerText;
    });
  return text;
};

/**
 * 保存划线
 */
const addBookLine = async (source) => {
  if (!getToken()) return;
  if (drawALineStatus.value && source) {
    await deleteBookLine({ lineId: lineDefaultValue.value.lineId });
  } else {
    const fromWordId = props.elementList[0].id;
    const endWordId = props.elementList[props.elementList.length - 1].id;

    const overlappingLineIds = findOverlapping(fromWordId, endWordId);
    if (overlappingLineIds.length > 0) {
      // 如果发现有重叠的划线，先讲重叠的划线删除
      // 以防万一有多个重叠的划线依次删除
      const removeLinePromise = [];
      overlappingLineIds.forEach((lineId) => {
        removeLinePromise.push(deleteBookLine({ lineId }));
      });
      Promise.all(removeLinePromise);
    }

    const parentPageNode = findClosestPageNode(
      document.querySelector("#" + fromWordId)
    );
    const pageIndexInChapter = parentPageNode.getAttribute(CHAPTER_PAGE_INDEX);
    const data = {
      bookId: store.comprehensiveBookData.bookId,
      chapterId: store.chapterId,
      pageNumber: pageIndexInChapter,
      word: getSelectedText(),
      color: drawALineStyle.color,
      lineStyle: drawALineStyle.lineStyle,
      fromWordId,
      endWordId,
    };
    await saveBookLine(data);
  }
  const res = await getBookLine({
    bookId: store.comprehensiveBookData.bookId,
    color: null,
  });
  if (res.code === 200) {
    store.setBookLineData(res.data);
  }
  isDefauleValue();
};

function findOverlapping(newFromWordId, newEndWordId) {
  const overlappedLineId = [];
  const paraId = deriveParagraphIdFromKeywordComplexityId(newFromWordId);
  const newFromWordIndex = deriveKeyWordIndexFromComplexityId(newFromWordId);
  const newEndWordIndex = deriveKeyWordIndexFromComplexityId(newEndWordId);
  store.bookLineData.forEach((booklineData) => {
    // 兼容性写法，保证划线处处于同一段落
    if (
      booklineData.fromWordId.indexOf(paraId) < 0 &&
      booklineData.endWordId.indexOf(paraId) < 0
    ) {
      return;
    }
    if (
      booklineData.fromWordId === newFromWordId &&
      booklineData.endWordId === newEndWordId
    ) {
      // 另外一处逻辑会处理该情况
      return;
    }
    const frompWordIndex = deriveKeyWordIndexFromComplexityId(
      booklineData.fromWordId
    );
    const endWordIndex = deriveKeyWordIndexFromComplexityId(
      booklineData.endWordId
    );
    // if (booklineData.fromWordId < newFromWordId && booklineData.endWordId > newEndWordId) {
    //   overlappedLineId.push(booklineData.lineId)
    // } else
    if (frompWordIndex >= newFromWordIndex && endWordIndex <= newEndWordIndex) {
      overlappedLineId.push(booklineData.lineId);
    } else if (
      frompWordIndex <= newEndWordIndex &&
      endWordIndex > newEndWordIndex
    ) {
      overlappedLineId.push(booklineData.lineId);
    } else if (
      frompWordIndex < newFromWordIndex &&
      endWordIndex >= newFromWordIndex
    ) {
      overlappedLineId.push(booklineData.lineId);
    }
  });
  return overlappedLineId;
}

/**
 * 根据开始和节数标签的id生成样式
 */
const underlineIdName = () => {
  const selection = window.getSelection();
  const { endContainer, startContainer } = selection.getRangeAt(0);
  const startIdName = startContainer.parentNode.id?.split("_");
  const endIdName = endContainer.parentNode.id?.split("_");
  let _i = startIdName[1];
  let _e = endIdName[1];
  let _id = "";
  for (let i = _i; i <= _e; i++) {
    _id += `#${startIdName[0]}_${i},`;
  }
  return _id + ".asdff";
};

const createStyle = () => {
  drawALineStatus.value = !drawALineStatus.value;
  removeStyle();
  const styles = document.createElement("style");
  styles.id = STYLE_ID;
  styles.innerHTML = `${idList.value}{background-color:#0078d7;color:#fff}`;
  const bodys = document.querySelector("body");
  bodys.appendChild(styles);
};

const removeStyle = () => {
  const styles = document.getElementById(STYLE_ID);
  if (styles) styles.remove();
};

/**判断选中是否为默认值*/
const isDefauleValue = () => {
  let startId = props.elementList[0].id;
  let endId = props.elementList[props.elementList.length - 1].id;
  const v = store.bookLineData.find(
    (ele) => ele.fromWordId === startId && ele.endWordId === endId
  );
  if (v) {
    drawALineStatus.value = true;
    drawALineStyle.color = v.color;
    drawALineStyle.lineStyle = v.lineStyle;
    lineDefaultValue.value = v;
  } else {
    lineDefaultValue.value = {};
  }
};

onMounted(() => {
  idList.value = underlineIdName();
  isDefauleValue();
});

onBeforeUnmount(() => {
  removeStyle();
});
const option = [
  {
    title: "复制",
    icon: "fuzhi",
  },
  {
    title: "划线",
    icon: "underline",
  },
  {
    title: "笔记",
    icon: "note",
  },
  {
    title: "助记卡",
    icon: "flashcard",
    ai: true,
  },
  {
    title: "百科",
    icon: "baike",
  },
  {
    title: "词典",
    icon: "shuben",
  },
  {
    title: "朗读",
    icon: "langdu",
    // ai: true,
  },
  {
    title: "纠错",
    icon: "jiucuo",
  },
  {
    title: "阅读精灵",
    icon: "spirit",
  },
  {
    title: "翻译",
    icon: "translate",
    ai: true,
  },
];
</script>
