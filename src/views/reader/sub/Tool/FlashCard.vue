<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-12 10:34:47
 * @LastEditTime: 2025-02-18 14:43:09
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Tool\FlashCard.vue
 * @Description: 助记卡列表
-->
<template>
  <div class="flash-card-container">
    <div class="flash-card-header">
      <h3>助记卡</h3>
      <span class="card-count">{{ filteredCardList.length }}张</span>
    </div>
    
    <!-- 筛选tab栏 -->
    <div class="filter-tabs" v-if="cardList.length > 0">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="flash-card-tabs">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="未学习" name="new"></el-tab-pane>
        <el-tab-pane label="需复习" name="review"></el-tab-pane>
      </el-tabs>
    </div>

          <div class="card-list" v-if="filteredCardList.length > 0">
        <div 
          v-for="(card, index) in filteredCardList" 
          :key="card.cardId"
          class="card-item"
          @click="reviewCard(index)"
        >
        <div class="card-content">
          <div class="source-text">{{ card.sourceText }}</div>
          <div class="card-info">
            <span class="card-type">{{ getCardTypeText(card.cardType) }}</span>
            <span class="memory-status" :class="getStatusClass(card.memoryStatus)">
              {{ getStatusText(card.memoryStatus) }}
            </span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="empty-state" v-else>
      <div class="empty-icon">📚</div>
      <div class="empty-text">
        {{ cardList.length === 0 ? '暂无助记卡' : '暂无符合条件的助记卡' }}
      </div>
      <div class="empty-hint">
        {{ cardList.length === 0 ? '选中文字后可创建助记卡' : '试试切换其他筛选条件' }}
      </div>
    </div>

    <!-- 复习弹窗 -->
    <ReviewFlashCard
      v-model:visible="showReviewDialog"
      :cards="filteredCardList"
      :initial-index="currentCardIndex"
      @updated="onCardUpdated"
      @deleted="onCardDeleted"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import useReader from "@/store/modules/reader";
import { listCard } from '@/api/book/card';
import ReviewFlashCard from './ReviewFlashCard.vue';

const store = useReader();
const cardList = ref([]);
const activeTab = ref('all'); // 当前选中的tab
const showReviewDialog = ref(false);
const currentCardIndex = ref(0);

// 根据activeTab筛选卡片
const filteredCardList = computed(() => {
  if (activeTab.value === 'all') {
    return cardList.value;
  }
  
  const statusMap = {
    'new': 0,      // 未学习
    'review': 1    // 需复习
  };
  
  const targetStatus = statusMap[activeTab.value];
  return cardList.value.filter(card => card.memoryStatus === targetStatus);
});

onMounted(() => {
  if (store.comprehensiveBookData.bookId) {
    getCardList();
  }
});

// 监听书籍变化，重新加载助记卡
watch(
  () => store.comprehensiveBookData.bookId,
  (newBookId) => {
    if (newBookId) {
      getCardList();
      activeTab.value = 'all'; // 重置筛选条件
    } else {
      cardList.value = [];
    }
  }
);

const getCardList = () => {
  listCard({
    bookId: store.comprehensiveBookData.bookId,
    pageNum: 1,
    pageSize: 100 // 加载更多卡片
  }).then(res => {
    if (res.code === 200) {
      cardList.value = res.rows || [];
    }
  });
};

const handleTabChange = (tabName) => {
  activeTab.value = tabName;
};

const getCardTypeText = (type) => {
  const typeMap = {
    1: '简答题',
    2: '选择题', 
    3: '主题卡片'
  };
  return typeMap[type] || '未知';
};

const getStatusText = (status) => {
  const statusMap = {
    0: '未学习',
    1: '需复习',
    2: '已掌握'
  };
  return statusMap[status] || '未知';
};

const getStatusClass = (status) => {
  const classMap = {
    0: 'status-new',
    1: 'status-review',
    2: 'status-mastered'
  };
  return classMap[status] || '';
};

const reviewCard = (index) => {
  currentCardIndex.value = index;
  showReviewDialog.value = true;
};

const onCardUpdated = () => {
  getCardList(); // 刷新列表
};

const onCardDeleted = () => {
  getCardList(); // 刷新列表
};

// 暴露方法给父组件调用
defineExpose({
  refreshList: getCardList
});
</script>

<style lang="scss" scoped>
.flash-card-container {
  padding: 20px;
  color: var(--fontColor);
  height: 100%;
  overflow-y: auto;
  min-width: 350px;
}

.flash-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }
  
  .card-count {
    color: var(--textSecondaryColor);
    font-size: 12px;
  }
}

.filter-tabs {
  margin-bottom: 20px;
  
  :deep(.flash-card-tabs) {
    .el-tabs__header {
      margin: 0 0 15px 0;
    }
    
    .el-tabs__nav-wrap::after {
      display: none;
    }
    
    .el-tabs__nav-scroll {
      padding: 0;
    }
    
    .el-tabs__nav {
      width: 100%;
      display: flex;
    }
    
    .el-tabs__item {
      flex: 1;
      text-align: center;
      font-size: 14px;
      color: var(--fontColor);
      padding: 0 16px;
      height: 36px;
      line-height: 36px;
      
      &.is-active {
        color: #18D7BF;
        font-weight: 500;
      }
      
      &:hover {
        color: #18D7BF;
      }
    }
    
    .el-tabs__active-bar {
      background-color: #18D7BF;
    }
  }
}

.card-list {
  .card-item {
    background: var(--cardBackgroundColor);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid var(--borderColor);
    
    &:hover {
      background: var(--hoverBackgroundColor);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
  }
  
  .card-content {
    .source-text {
      font-size: 14px;
      line-height: 1.4;
      margin-bottom: 8px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .card-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      
      .card-type {
        color: var(--textSecondaryColor);
      }
      
      .memory-status {
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 11px;
        
        &.status-new {
          background: #e3f2fd;
          color: #1976d2;
        }
        
        &.status-review {
          background: #fff3e0;
          color: #f57c00;
        }
        
        &.status-mastered {
          background: #e8f5e8;
          color: #388e3c;
        }
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .empty-text {
    font-size: 16px;
    color: var(--textSecondaryColor);
    margin-bottom: 8px;
  }
  
  .empty-hint {
    font-size: 12px;
    color: var(--textTertiaryColor);
  }
}
</style> 