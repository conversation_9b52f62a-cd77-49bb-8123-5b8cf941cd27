<template>
  <el-dialog
    v-model="dialogVisible"
    title="思维导图"
    width="90%"
    :fullscreen="isFullscreen"
    :before-close="handleClose"
    append-to-body
    destroy-on-close
    class="mind-map-dialog"
  >
    <template #header="{ titleId, titleClass }">
      <div class="dialog-header">
        <span :id="titleId" :class="titleClass">思维导图</span>
        <div class="header-actions">
          <el-button 
            size="small" 
            @click="toggleFullscreen"
            text
          >
            <el-icon>
              <Minus v-if="isFullscreen" />
              <FullScreen v-else />
            </el-icon>
            {{ isFullscreen ? '退出全屏' : '全屏' }}
          </el-button>
        </div>
      </div>
    </template>

    <div class="mind-map-dialog-content">
      <MindMapContent @edit="handleEdit" ref="mindMapRef" />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSave">保存并关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Minus, FullScreen } from '@element-plus/icons-vue';
import MindMapContent from './MindMapContent.vue';

const emit = defineEmits(['edit', 'close']);

const dialogVisible = ref(false);
const isFullscreen = ref(false);
const mindMapRef = ref(null);

// 打开弹窗
const open = () => {
  dialogVisible.value = true;
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  emit('close');
};

// 切换全屏
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  // 延迟一下让思维导图重新适应容器大小
  nextTick(() => {
    if (mindMapRef.value) {
      setTimeout(() => {
        mindMapRef.value.fit && mindMapRef.value.fit();
      }, 300);
    }
  });
};

// 保存数据
const handleSave = () => {
  if (mindMapRef.value) {
    mindMapRef.value.saveData();
  }
  handleClose();
};

// 处理编辑事件
const handleEdit = (type, data) => {
  emit('edit', type, data);
};

// 加载数据
const loadData = (data) => {
  if (mindMapRef.value) {
    mindMapRef.value.loadData(data);
  }
};

// 监听弹窗显示状态，确保思维导图正确初始化
watch(dialogVisible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      // 给一些时间让DOM完全渲染
      setTimeout(() => {
        if (mindMapRef.value && mindMapRef.value.fit) {
          mindMapRef.value.fit();
        }
      }, 500);
    });
  }
});

defineExpose({
  open,
  close: handleClose,
  loadData
});
</script>

<style lang="scss">
.mind-map-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;
    
    .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      
      .header-actions {
        display: flex;
        align-items: center;
      }
    }
  }
  
  .el-dialog__body {
    padding: 0 !important;
    height: 70vh !important;
    min-height: 500px !important;
    
    .mind-map-dialog-content {
      height: 100% !important;
      width: 100% !important;
      min-height: 500px !important;
    }
  }
  
  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #e4e7ed;
    text-align: right;
  }
  
  // 全屏时的样式调整
  &.is-fullscreen {
    .el-dialog__body {
      height: calc(100vh - 120px) !important;
      min-height: calc(100vh - 120px) !important;
    }
  }
}
</style> 