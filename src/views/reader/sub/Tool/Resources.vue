<!-- 资源 -->
<style lang="scss" scoped>
.Resources {
  width: 345px;
  height: 100%;
  overflow: hidden;

  & > header {
    margin: 20px 0 0;
  }

  & > main {
    overflow-y: auto;
    height: calc(100% - 70px);

    .chapter-name {
      width: 345px;
      margin-top: 20px;
      font-size: 16px;
      color: #999;
      font-weight: bold;
      margin-bottom: 15px;
    }

    .main-item {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 10px 10px;

      .resources-card {
        width: 100%;
        height: 100px;
        box-shadow: 0px 0px 4px 0px rgba(200, 200, 200, 0.5);
        border: 1px solid rgba(200, 200, 200, 0.5);
        border-radius: 3px;
        transition: all 0.5s;
        transform: translateZ(0);
        overflow: hidden;
        .header {
          display: flex;
          align-items: center;
          padding: 10px 4px;
          box-sizing: border-box;

          & > img {
            width: 30px;
            height: 30px;
          }

          .title {
            flex: 1;
            font-size: 12px;
            color: #333333;
            margin-left: 5px;
          }
        }

        .footer {
          display: flex;
          align-items: center;
          justify-content: space-around;

          .footer-btn {
            font-size: 12px;
            color: #0966b4;
            cursor: pointer;
          }
        }

        .jumpTo {
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 24px;
          width: 100%;
          height: 24px;
          overflow: hidden;

          position: absolute;
          right: 0;
          bottom: 0;
          text-align: center;
          border-radius: 0px 0px 3px 3px;
          cursor: pointer;
          display: none;

          align-items: center;
          .jumpTo-icon {
            width: 10px;
            height: 10px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 5px;
            background-image: url('@/assets/images/jumpTo.svg');
            background-size: 100% 100%;
            background-repeat: no-repeat;
          }
          .jumpTo-view {
            width: 10px;
            height: 10px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 5px;
            background-image: url('@/assets/images/previewIcon.svg');
            background-size: 100% 100%;
            background-repeat: no-repeat;
          }
          .jumpTo-text {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
          }
          .green {
            background: rgba(0, 178, 134, 1);
          }
          .blue {
            background: rgba(9, 102, 180, 1);
          }
          &:hover {
            color: #0966b4;
          }
        }
      }
      .resources-card:hover {
        transform: translate3d(0, -2px, 0);
        box-shadow: 0px 4px 8px 0px #d7e5f8;
      }
      .resources-card:hover .jumpTo {
        display: flex;
      }

      .resources-card-3,
      .resources-card-1 {
        position: relative;

        .maskLayer {
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
          padding: 6px;
          box-sizing: border-box;
          color: #fff;
          font-size: 12px;
          cursor: pointer;

          .icon {
            position: absolute;
            left: 50%;
            top: 30%;
            font-size: 42px;
            transform: translate(-50%);
            transition: 0.3s;

            &:hover {
              color: var(--hoverfont);
            }
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="Resources">
    <header>
      <el-select
        v-model="filterCatalogResourceByChapter"
        style="width: 143px"
        :popper-class="store.theme === 'dark' ? '__darkSelect__' : ''"
        @change="change"
        :clearable="true">
        <el-option
          v-for="(item, index) in chaptersOptionsComputed"
          :key="index"
          :label="item.label"
          :value="item.value" />
      </el-select>
    </header>
    <main
      class="__hidden-scroll__">
      <div
        v-for="(item, index) in bookResourceList"
        :key="index">
        <div
          class="chapter-name">
          {{ item.chapterName }}
        </div>
        <div
          class="main-item">
          <template
            v-for="(ele, i) in item.bookResourcesList"
            :key="i">
            <div
              v-if="ele.fileType === 1"
              class="resources-card resources-card-1"
              @click="preview(ele)">
              <img
                style="width: 100%; height: 100%"
                :src="ele.fileUrl"
                alt="" />
              <div
                class="maskLayer __ellipsis__">
                {{ ele.fileName }}
              </div>
              <div
                class="jumpTo">
                <div
                  class="jumpTo-text green"
                  @click.stop="jumpTo(ele)">
                  <i
                    class="jumpTo-icon"></i>跳转
                </div>
                <div
                  class="jumpTo-text blue"
                  @click.stop="preview(ele)">
                  <i
                    class="jumpTo-view"></i>预览
                </div>
              </div>
            </div>
            <div
              v-else-if="ele.fileType === 2"
              class="resources-card resources-card-3"
              @click="preview(ele)">
              <audio
                style="width: 170px; height: 100%"
                :src="ele.fileUrl"></audio>
              <div
                class="maskLayer __ellipsis__">
                {{ ele.fileName }}
                <div
                  class="icon">
                  <el-icon>
                    <VideoPlay />
                  </el-icon>
                </div>
              </div>
              <div
                class="jumpTo">
                <div
                  class="jumpTo-text green"
                  @click.stop="jumpTo(ele)">
                  <i
                    class="jumpTo-icon"></i>跳转
                </div>
                <div
                  class="jumpTo-text blue"
                  @click.stop="preview(ele)">
                  <i
                    class="jumpTo-view"></i>预览
                </div>
              </div>
            </div>
            <div
              v-else-if="ele.fileType === 3"
              class="resources-card resources-card-3"
              @click="preview(ele)">
              <video
                style="width: 170px; height: 100%"
                :src="ele.fileUrl"></video>
              <div
                class="maskLayer __ellipsis__">
                {{ ele.fileName }}
                <div
                  class="icon">
                  <el-icon>
                    <VideoPlay />
                  </el-icon>
                </div>
              </div>
              <div
                class="jumpTo">
                <div
                  class="jumpTo-text green"
                  @click.stop="jumpTo(ele)">
                  <i
                    class="jumpTo-icon"></i>跳转
                </div>
                <div
                  class="jumpTo-text blue"
                  @click.stop="preview(ele)">
                  <i
                    class="jumpTo-view"></i>预览
                </div>
              </div>
            </div>
            <!-- <div v-else class="resources-card">
              <div class="header">
                <img :src="(ele.fileUrl)" alt="" />
                <div class="title __ellipsis__" :title="ele.fileName">{{ ele.fileName }}</div>
              </div>
              <div class="footer">
                <div class="footer-btn"></div>
                <div class="footer-btn"></div>
                <div class="footer-btn footer-btn-1" @click="preview(ele)">
                  <u>预览</u>
                </div>
              </div>
              <div class="jumpTo"  @click.stop="jumpTo(ele)">跳转</div>
            </div> -->
          </template>
        </div>
        <br />
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getBookResource, getChapters } from '@/api/book/reader'
// import { getFileIcon } from '@/utils/index.js'
import useReader from '@/store/modules/reader'
import usePreview from '@/store/modules/preview.js'
import { ElMessage } from 'element-plus'

const previewStore = usePreview()
const store = useReader()
const bookResourceList = ref([])
const chaptersOptions = ref([])
const filterCatalogResourceByChapter = ref('all')

//fileType 文件类型1=图片，2=音频，3=视频，4=虚拟仿真，5=AR/VR，6=3D模型，7习题，8课件
function preview(v) {
  // if (!v.fileUrl) {
  //   return ElMessage.error("该资源地址无效");
  // }
  if (v.fileType === 1) {
    previewStore.setCurrentImage(v.fileUrl)
  } else {
    console.log(v.fileUrl.split(/[\r\n]/g).join(''))
    previewStore.setData({
      url: v.fileUrl.split(/[\r\n]/g).join(''),
      title: v.fileName
    })
  }
}

// hook
const chaptersOptionsComputed = computed(() => {
  const chapterResourceList = [
    {
      label: '全部章节',
      value: 'all'
    }
  ]
  return chapterResourceList.concat(
    store.comprehensiveChapterAndCatalogData.chaptersData.map(catalogItem => {
      return {
        label: catalogItem.chapterName,
        value: catalogItem.chapterId
      }
    })
  )
})

/**
 * 跳转
 */
const jumpTo = async bookResourceItem => {
  store.jumpToChapter(bookResourceItem.chapterId, bookResourceItem.pageNumber)
}
/**
 * 获取资源列表
 */
const getResourcesList = async () => {
  const params = {
    bookId: store.comprehensiveBookData.bookId,
    resourceType: 0,
    chapterId: filterCatalogResourceByChapter.value === 'all' ? undefined : filterCatalogResourceByChapter.value
  }
  const res = await getBookResource(params)
  if (res.code === 200) {
    bookResourceList.value = res.data
    console.log(res.data)
    const tmpConvertedImageList = []
    bookResourceList.value.forEach(chapterResourceItem => {
      chapterResourceItem.bookResourcesList.forEach(bookResourceItem => {
        bookResourceItem.fileType === 1 &&
          tmpConvertedImageList.push({
            ...bookResourceItem
          })
      })
    })
    previewStore.setPreviewImages(tmpConvertedImageList)
  }
}

const change = () => {
  if (!filterCatalogResourceByChapter.value) filterCatalogResourceByChapter.value = 'all'
  getResourcesList()
}

//获取章节列表
const getChaptersList = async () => {
  const res = await getChapters(store.comprehensiveBookData.bookId)
  const option = [
    {
      label: '全部章节',
      value: 'all'
    }
  ]
  if (res.code === 200) {
    res.data.forEach(ele => {
      option.push({ label: ele.chapterName, value: ele.chapterId })
    })
  }
  chaptersOptions.value = option
}
onMounted(() => {
  getResourcesList()
  getChaptersList()
})
</script>
