<!-- 作业 -->
<style lang="scss" scoped>
.Task {
  width: 305px;

  .header {
    margin-bottom: 20px;
  }

  .title {
    font-weight: 400;
    font-size: 14px;
    color: #999;
    line-height: 14px;
    text-align: justify;
    margin-bottom: 14px;
  }

  .content {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 20px;
    .content-card {
      width: 100%;
      height: 78px;
      background: var(--pageBackgroundColor);
      box-shadow: var(--noteBoxShadow);
      border-radius: 3px;
      padding: 4px;
      box-sizing: border-box;
      margin-bottom: 5px;
      float: left;

      & > nav {
        display: flex;
        align-items: center;
        height: 48px;

        & > img {
          width: 29px;
          height: 29px;
        }

        .name {
          flex: 1;
          font-size: 12px;
          color: #999;
        }
      }

      & > footer {
        position: relative;

        .footer-btn {
          position: absolute;
          right: 0;
          width: 52px;
          height: 24px;
          border-radius: 9px;
          border: 1px solid #0966b4;
          font-weight: 400;
          font-size: 12px;
          color: #0966b4;
          cursor: pointer;
          transition: 0.3s;

          &:hover {
            background-color: #0966b4;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="Task">
    <header class="header">
      <el-select
        v-model="chapter"
        style="width: 143px"
        :popper-class="store.theme === 'dark' ? '__darkSelect__' : ''"
        @change="change"
        :clearable="true"
      >
        <el-option
          v-for="item in chaptersOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        /> </el-select
      >&nbsp;
      <el-select
        v-model="state"
        style="width: 105px"
        :popper-class="store.theme === 'dark' ? '__darkSelect__' : ''"
        @change="change"
        :clearable="true"
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </header>

    <main>
      <div v-for="ele in taskList" :key="ele.chapterId">
        <section class="title __ellipsis__">
          <el-tooltip :content="ele.chapterName">
            {{ ele.chapterName }}
          </el-tooltip>
        </section>
        <div class="content">
          <section
            class="content-card"
            v-for="(item, i) in ele.dtbBookTestPaperList"
            :key="i"
          >
            <nav>
              <img src="@/assets/images/sidebar/taskTitle.png" alt="" />
              <div class="name __ellipsis__">{{ item.paperTitle }}</div>
            </nav>
            <footer>
              <div class="footer-btn __flex-center__" @click="goTaskPage(item)">
                {{ item.state === "1" ? "打开" : "去做题" }}
              </div>
            </footer>
          </section>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import useReader from "@/store/modules/reader";
import { getPaperList, getChapters } from "@/api/book/reader";
const store = useReader();
const chapter = ref("全部章节");
const state = ref("3");
const chaptersOptions = ref([]);
const taskList = ref([]);
const statusOptions = [
  {
    label: "全部",
    value: "3",
  },
  {
    label: "已完成",
    value: "1",
  },
  {
    label: "未完成",
    value: "0",
  },
];
const goTaskPage = async (assignmentItem) => {
  store.jumpToChapter(assignmentItem.chapterId, assignmentItem.pageNumber);
};

const change = () => {
  if (!chapter.value) chapter.value = "全部章节";
  if (!state.value) state.value = "3";
  getTaskList();
};
//获取作业列表
const getTaskList = async () => {
  const params = {
    bookId: store.comprehensiveBookData.bookId,
    paperType: 2,
    chapterId: chapter.value === "全部章节" ? undefined : chapter.value,
    state: state.value === "3" ? undefined : state.value,
  };
  const res = await getPaperList(params);
  if (res.code === 200) {
    taskList.value = res.data;
  }
};

//获取章节列表
const getChaptersList = async () => {
  const res = await getChapters(store.comprehensiveBookData.bookId);
  const option = [
    {
      label: "全部章节",
      value: "全部章节",
    },
  ];
  if (res.code === 200) {
    res.data.forEach((ele) => {
      option.push({ label: ele.chapterName, value: ele.chapterId });
    });
  }
  chaptersOptions.value = option;
};
onMounted(() => {
  getTaskList();
  getChaptersList();
});
</script>
