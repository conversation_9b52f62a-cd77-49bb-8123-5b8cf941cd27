<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-12 10:34:47
 * @LastEditTime: 2025-02-18 14:43:09
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Tool\ReviewFlashCard.vue
 * @Description: 助记卡复习界面
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="复习助记卡"
    width="800px"
    height="370px"
    :before-close="handleClose"
    append-to-body
    center
  >
    <!-- 右上角切换模式选择器 -->
    <template #header>
      <div class="dialog-header">
        <span>复习助记卡</span>
        <div class="header-controls">
          <span class="card-counter" v-if="totalCards > 1">{{ currentIndex + 1 }}/{{ totalCards }}</span>
          <el-select 
            v-model="switchMode" 
            size="small" 
            style="width: 120px; margin-left: 12px;"
            @change="onSwitchModeChange"
          >
            <el-option label="顺序切换" value="sequential" />
            <el-option label="随机切换" value="random" />
          </el-select>
        </div>
      </div>
    </template>

    <div class="review-card-container" v-if="currentCard">
      <!-- 使用flex布局：左箭头 - 内容区域 - 右箭头 -->
      <div class="review-content-wrapper">
        <!-- 左箭头区域 -->
        <div class="arrow-area left-arrow-area">
          <div 
            v-if="totalCards > 1" 
            class="nav-arrow" 
            @click="previousCard" 
            :class="{ disabled: !canGoPrevious }"
          >
            <img src="@/assets/icons/svg/up-arrow.svg" alt="上一个" class="arrow-icon" />
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="card-content">
          <!-- 简答题 -->
          <template v-if="currentCard.cardType === 1">
            <div class="question-section">
              <div class="question-text">{{ currentCard.question }}</div>
            </div>
            
            <div class="answer-section">
              <div class="answer-text" v-show="showAnswer">{{ currentCard.answer }}</div>
              <div class="answer-placeholder" v-show="!showAnswer">
                <div class="placeholder-icon">
                  <img src="@/assets/icons/svg/FlashCardEmpty.svg" alt="未显示答案" />
                </div>
                <div class="placeholder-text">请先尝试回忆一下</div>
              </div>
            </div>
          </template>

          <!-- 选择题 -->
          <template v-if="currentCard.cardType === 2">
            <div class="question-section">
              <div class="section-title">问题</div>
              <div class="question-text">{{ currentCard.question }}</div>
            </div>
            
            <div class="options-section">
              <div class="section-title">选项</div>
              <div class="options-list">
                <div 
                  v-for="option in parsedOptions" 
                  :key="option.key"
                  class="option-item"
                  :class="{ 
                    'selected': userAnswer === option.key,
                    'correct': showAnswer && currentCard.answer === option.key,
                    'wrong': showAnswer && userAnswer === option.key && userAnswer !== currentCard.answer
                  }"
                  @click="selectAnswer(option.key)"
                >
                  <span class="option-label">{{ option.key }}.</span>
                  <span class="option-text">{{ option.value }}</span>
                </div>
              </div>
              
              <div v-if="showAnswer" class="result-text">
                <span v-if="userAnswer === currentCard.answer" class="correct-result">
                  ✓ 回答正确！
                </span>
                <span v-else class="wrong-result">
                  ✗ 回答错误，正确答案是 {{ currentCard.answer }}
                </span>
              </div>
            </div>
          </template>

          <!-- 主题卡片 -->
          <template v-if="currentCard.cardType === 3">
            <div class="question-section">
              <div class="section-title">主题总结</div>
            </div>
            
            <div class="answer-section">
              <div class="answer-text" v-show="showAnswer">{{ currentCard.answer }}</div>
              <div class="answer-placeholder" v-show="!showAnswer">
                <div class="placeholder-icon">
                  <img src="@/assets/icons/svg/FlashCardEmpty.svg" alt="未显示内容" />
                </div>
                <div class="placeholder-text">请先尝试回忆一下</div>
              </div>
            </div>
          </template>

          <!-- 记忆状态控制 -->
          <div class="memory-controls" v-if="showAnswer">

            <div class="memory-buttons">
              <el-button 
                type="danger" 
                size="small" 
                @click="updateMemoryStatus(1)"
                :class="{ 'active': currentCard.memoryStatus === 0 || currentCard.memoryStatus === 1 }"
              >
                忘记了
              </el-button>
              <el-button 
                type="warning" 
                size="small" 
                @click="updateMemoryStatus(1)"
                :class="{ 'active': currentCard.memoryStatus === 1 }"
              >
                模糊了
              </el-button>
              <el-button 
                type="success" 
                size="small" 
                @click="updateMemoryStatus(2)"
                :class="{ 'active': currentCard.memoryStatus === 2 }"
              >
                记住了
              </el-button>
              
              <!-- 新增的方形按钮 -->
              <el-button 
                class="square-button complete-button"
                @click="completeStudy"
              >
                <div class="button-content">
                  <div class="button-title">完成学习</div>
                  <div class="button-subtitle">设为完成状态，不再复习</div>
                </div>
              </el-button>
              <el-button 
                class="square-button forget-button"
                @click="resetStudy"
              >
                <div class="button-content">
                  <div class="button-title">彻底忘记</div>
                  <div class="button-subtitle">重置学习进度，重新开始复习</div>
                </div>
              </el-button>
            </div>
          </div>
        </div>

        <!-- 右箭头区域 -->
        <div class="arrow-area right-arrow-area">
          <div 
            v-if="totalCards > 1" 
            class="nav-arrow" 
            @click="nextCard" 
            :class="{ disabled: !canGoNext }"
          >
            <img src="@/assets/icons/svg/down-arrow.svg" alt="下一个" class="arrow-icon" />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <!-- 简答题的答案控制 -->
        <template v-if="currentCard?.cardType === 1">
          <el-button 
            type="primary" 
            size="large"
            @click="showAnswer = !showAnswer"
          >
            {{ showAnswer ? '隐藏答案' : '显示答案' }}
          </el-button>
        </template>
        
        <!-- 选择题的答案控制 -->
        <template v-if="currentCard?.cardType === 2">
          <el-button 
            v-if="!showAnswer && userAnswer"
            type="primary" 
            size="large"
            @click="checkAnswer"
          >
            查看答案
          </el-button>
          <el-button 
            v-if="showAnswer"
            type="primary" 
            size="large"
            @click="showAnswer = false; userAnswer = ''"
          >
            重新答题
          </el-button>
          <el-button 
            v-if="!userAnswer"
            type="info" 
            size="large"
            disabled
          >
            请先选择答案
          </el-button>
        </template>
        
        <!-- 主题卡片的答案控制 -->
        <template v-if="currentCard?.cardType === 3">
          <el-button 
            type="primary" 
            size="large"
            @click="showAnswer = !showAnswer"
          >
            {{ showAnswer ? '隐藏内容' : '显示内容' }}
          </el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { updateCard, delCard } from '@/api/book/card';
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  // 兼容原有的单个卡片传参
  card: {
    type: Object,
    default: null
  },
  // 新增的卡片数组传参
  cards: {
    type: Array, 
    default: () => []
  },
  initialIndex: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['update:visible', 'updated', 'deleted']);

const dialogVisible = ref(false);
const showAnswer = ref(false);
const userAnswer = ref('');
const currentIndex = ref(0);
const switchMode = ref('sequential'); // 'sequential' | 'random'
const randomSequence = ref([]); // 随机序列

// 计算属性
const totalCards = computed(() => {
  // 优先使用cards数组，如果没有则使用单个card创建数组
  if (props.cards && props.cards.length > 0) {
    return props.cards.length;
  } else if (props.card) {
    return 1;
  }
  return 0;
});

const currentCard = computed(() => {
  // 如果传入了cards数组
  if (props.cards && props.cards.length > 0) {
    if (switchMode.value === 'random') {
      const randomIndex = randomSequence.value[currentIndex.value];
      return props.cards[randomIndex];
    }
    return props.cards[currentIndex.value];
  }
  // 兼容原有的单个card传参
  else if (props.card) {
    return props.card;
  }
  return null;
});

const canGoPrevious = computed(() => {
  // 只有多张卡片时才能切换
  return totalCards.value > 1 && currentIndex.value > 0;
});

const canGoNext = computed(() => {
  // 只有多张卡片时才能切换
  return totalCards.value > 1 && currentIndex.value < totalCards.value - 1;
});

const parsedOptions = computed(() => {
  if (currentCard.value?.cardType === 2 && currentCard.value?.options) {
    try {
      // 解析JSON字符串，返回包含key和value的数组
      const options = JSON.parse(currentCard.value.options);
      return Array.isArray(options) ? options : [];
    } catch (error) {
      console.error('解析选项失败:', error);
      return [];
    }
  }
  return [];
});

// 生成随机序列
const generateRandomSequence = () => {
  const sequence = Array.from({ length: totalCards.value }, (_, i) => i);
  
  // Fisher-Yates 洗牌算法
  for (let i = sequence.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [sequence[i], sequence[j]] = [sequence[j], sequence[i]];
  }
  
  return sequence;
};

// 初始化随机序列
const initializeRandomSequence = () => {
  if (totalCards.value > 1) { // 只有多张卡片时才需要随机序列
    randomSequence.value = generateRandomSequence();
  }
};

// 监听器
watch(() => props.visible, (val) => {
  dialogVisible.value = val;
  if (val) {
    currentIndex.value = props.initialIndex || 0;
    initializeRandomSequence();
    resetReview();
  }
});

watch(dialogVisible, (val) => {
  emit('update:visible', val);
});

// 监听cards数组变化
watch(() => props.cards, () => {
  if (props.cards && props.cards.length > 0) {
    initializeRandomSequence();
  }
}, { deep: true });

// 监听单个card变化
watch(() => props.card, () => {
  if (props.card) {
    resetReview();
  }
}, { deep: true });

// 切换卡片时重置状态
watch(currentIndex, () => {
  resetReview();
});

// 方法
const resetReview = () => {
  showAnswer.value = false;
  userAnswer.value = '';
};

const previousCard = () => {
  if (canGoPrevious.value) {
    currentIndex.value--;
  }
};

const nextCard = () => {
  if (canGoNext.value) {
    currentIndex.value++;
  }
};

const onSwitchModeChange = () => {
  // 切换模式时重新初始化
  if (switchMode.value === 'random') {
    initializeRandomSequence();
  }
  resetReview();
};

const selectAnswer = (answer) => {
  if (!showAnswer.value) {
    userAnswer.value = answer;
  }
};

const checkAnswer = () => {
  showAnswer.value = true;
};

const updateMemoryStatus = async (status) => {
  try {
    const updateData = {
      cardId: currentCard.value.cardId,
      memoryStatus: status,
      reviewCount: currentCard.value.reviewCount + 1,
      lastReviewTime: new Date().toISOString(),
      // 根据记忆状态设置下次复习时间
      nextReviewTime: calculateNextReviewTime(status)
    };

    const res = await updateCard(updateData);
    if (res.code === 200) {
      ElMessage.success('记忆状态已更新');
      emit('updated');
      
      // 多张卡片时自动切换到下一张，单张卡片时直接关闭
      if (totalCards.value > 1) {
        if (canGoNext.value) {
          nextCard();
        } else {
          handleClose();
        }
      } else {
        handleClose();
      }
    } else {
      ElMessage.error(res.msg || '更新失败');
    }
  } catch (error) {
    ElMessage.error('更新失败');
  }
};

const calculateNextReviewTime = (status) => {
  const now = new Date();
  switch (status) {
    case 0: // 需要学习
      now.setHours(now.getHours() + 1); // 1小时后复习
      break;
    case 1: // 需要复习
      now.setDate(now.getDate() + 1); // 1天后复习
      break;
    case 2: // 已掌握
      now.setDate(now.getDate() + 7); // 1周后复习
      break;
  }
  return now.toISOString();
};

const deleteCard = async () => {
  try {
    await ElMessageBox.confirm('确定要删除这张助记卡吗？', '提示', {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
    });

    const res = await delCard(currentCard.value.cardId);
    if (res.code === 200) {
      ElMessage.success('删除成功');
      emit('deleted');
      handleClose();
    } else {
      ElMessage.error(res.msg || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

// 完成学习 - 删除卡片
const completeStudy = async () => {
  try {
    await ElMessageBox.confirm('确认已完成学习此卡片？完成后将删除该卡片。', '完成学习', {
      confirmButtonText: '确认完成',
      cancelButtonText: '取消',
      type: 'success',
    });

    const res = await delCard(currentCard.value.cardId);
    if (res.code === 200) {
      ElMessage.success('学习完成，卡片已删除');
      emit('deleted');
      
      // 多张卡片时切换到下一张，单张卡片时关闭
      if (totalCards.value > 1) {
        if (canGoNext.value) {
          nextCard();
        } else {
          handleClose();
        }
      } else {
        handleClose();
      }
    } else {
      ElMessage.error(res.msg || '操作失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败');
    }
  }
};

// 彻底忘记 - 重置为0状态
const resetStudy = async () => {
  try {
    await ElMessageBox.confirm('确认彻底忘记此卡片？将重置学习计划从头开始。', '彻底忘记', {
      confirmButtonText: '确认重置',
      cancelButtonText: '取消',
      type: 'warning',
    });

    const updateData = {
      cardId: currentCard.value.cardId,
      memoryStatus: 0,
      reviewCount: 0, // 重置复习次数
      lastReviewTime: new Date().toISOString(),
      nextReviewTime: calculateNextReviewTime(0)
    };

    const res = await updateCard(updateData);
    if (res.code === 200) {
      ElMessage.success('已重置学习计划');
      emit('updated');
      
      // 多张卡片时切换到下一张，单张卡片时关闭
      if (totalCards.value > 1) {
        if (canGoNext.value) {
          nextCard();
        } else {
          handleClose();
        }
      } else {
        handleClose();
      }
    } else {
      ElMessage.error(res.msg || '重置失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置失败');
    }
  }
};

const handleClose = () => {
  dialogVisible.value = false;
};
</script>

<style lang="scss" scoped>
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  
  .header-controls {
    display: flex;
    align-items: center;
    
    .card-counter {
      font-size: 14px;
      color: var(--fontColor);
      opacity: 0.8;
    }
  }
}

.review-card-container {
  position: relative;
  
  .review-content-wrapper {
    display: flex;
    align-items: stretch;
    min-height: 350px;
    gap: 16px;
  }

  .arrow-area {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    flex-shrink: 0;
    
    .nav-arrow {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover:not(.disabled) {
        .arrow-icon {
          transform: scale(1.2);
        }
      }
      
      &.disabled {
        cursor: not-allowed;
      }
      
      .arrow-icon {
        width: 32px;
        height: 32px;
        object-fit: contain;
        transition: all 0.3s;
      }
      
      &.disabled .arrow-icon {
        opacity: 0.3;
        filter: grayscale(1);
      }
    }
  }

  .card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0 16px;
  }

  .section-title {
    font-weight: 600;
    color: var(--fontColor);
    margin-bottom: 10px;
    font-size: 14px;
  }

  .source-section {
    margin-bottom: 24px;
    
    .source-text {
      background: var(--cardBackgroundColor);
      padding: 16px;
      border-radius: 8px;
      border: 1px solid var(--borderColor);
      line-height: 1.6;
      color: var(--fontColor);
    }
  }

  .question-section {
    margin-bottom: 20px;
    
    .question-text {
      color: var(--fontColor);
      font-size: 16px;
      line-height: 1.5;
    }
  }

  .answer-section {
    margin-bottom: 24px;
    
    .answer-text {
      color: var(--fontColor);
      line-height: 1.6;
      padding: 12px;
      background: #f0f9ff;
      border-radius: 6px;
      border: 1px solid #bfdbfe;
    }
    
    .answer-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      background: #fafbfc;
      border-radius: 8px;
      border: 1px dashed #d0d7de;
      
      .placeholder-icon {
        margin-bottom: 16px;
        
        img {
          width: 240px;
          height: 90px;
          opacity: 0.8;
        }
      }
      
      .placeholder-text {
        font-size: 14px;
        color: #656d76;
        text-align: center;
        font-weight: 500;
      }
    }
  }

  .options-section {
    margin-bottom: 24px;
    
    .options-list {
      .option-item {
        display: flex;
        align-items: center;
        padding: 12px;
        margin-bottom: 8px;
        background: var(--cardBackgroundColor);
        border: 1px solid var(--borderColor);
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          background: var(--hoverBackgroundColor);
        }
        
        &.selected {
          border-color: #409eff;
          background: #ecf5ff;
        }
        
        &.correct {
          border-color: #67c23a;
          background: #f0f9ff;
          color: #67c23a;
        }
        
        &.wrong {
          border-color: #f56c6c;
          background: #fef0f0;
          color: #f56c6c;
        }
        
        .option-label {
          width: 24px;
          font-weight: 600;
        }
        
        .option-text {
          flex: 1;
        }
      }
    }
    
    .answer-controls {
      margin-top: 16px;
      
      .result-text {
        margin-top: 10px;
        font-weight: 600;
        
        .correct-result {
          color: #67c23a;
        }
        
        .wrong-result {
          color: #f56c6c;
        }
      }
    }
  }



  .memory-controls {
    .memory-buttons {
      display: flex;
      gap: 10px;
      align-items: center;
      
      .el-button {
        &.active {
          background: var(--el-button-bg-color);
          border-color: var(--el-button-border-color);
        }
      }
      
      .square-button {
        width: 154px;
        height: 54px;
        border-radius: 8px;
        font-weight: 500;
        padding: 8px 12px;
        
        .button-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          line-height: 1.2;
          
          .button-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
          }
          
          .button-subtitle {
            font-size: 10px;
            font-weight: 400;
            opacity: 0.8;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 130px;
          }
        }
        
        &.complete-button {
          background: #EBFFE0;
          border: 1px solid #52C41A;
          color: #52C41A;
          
          &:hover {
            background: #d9f7be;
            border-color: #52C41A;
            color: #52C41A;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(82, 196, 26, 0.15);
          }
        }
        
        &.forget-button {
          background: #fff2f0;
          border: 1px solid #FF4D4F;
          color: #FF4D4F;
          
          &:hover {
            background: #ffebe8;
            border-color: #FF4D4F;
            color: #FF4D4F;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 77, 79, 0.15);
          }
        }
      }
    }
  }
}
</style> 