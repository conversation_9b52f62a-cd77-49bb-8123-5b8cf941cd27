<!-- 谈论 -->
<style lang="scss" scoped>
.FloatNote {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 2;
  .note-main {
    width: 346px;
    max-height: 523px;
    border-radius: 8px;
    background-color: var(--pageBackgroundColor);
    box-shadow: var(--noteBoxShadow);
    position: absolute;
    left: 50%;
    padding: 12px 10px;
    box-sizing: border-box;
    overflow-y: auto;
    & > nav {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      color: #666666;
    }
    .textCard {
      padding: 15px;
      box-sizing: border-box;
      border-radius: 8px;
      background-color: var(--modelBackgroundColor);
      margin: 10px 0;
      color: var(--fontColor);
      font-size: 14px;
    }
    .enclosure {
      background-color: var(--modelBackgroundColor);
      border-radius: 8px;
      padding: 10px;
      box-sizing: border-box;
      overflow: hidden;
      .enclosure-card {
        width: 148px;
        height: 60px;
        border-radius: 5px;
        overflow: hidden;
        float: left;
        margin: 3px 2px;
        .image {
          width: 100%;
          height: 100%;
          position: relative;
          .maskLayer {
            background: linear-gradient(
              to bottom,
              rgba(0, 0, 0, 0.5),
              rgba(0, 0, 0, 0)
            );
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            color: #fff;
            font-size: 10px;
            padding: 5px;
            cursor: pointer;
          }
          & > img {
            width: 100%;
            height: 100%;
          }
        }
        .audio {
          width: 100%;
          height: 100%;
          font-size: 10px;
          padding: 10px;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          position: relative;
          background-color: var(--pageBackgroundColor);
          & > img {
            width: 27px;
            height: 27px;
            margin-right: 5px;
          }
          .name {
            font-size: 10px;
            color: var(--fontColor);
            width: 100px;
            margin: 5px 0;
          }
          .size {
            color: #999999;
          }
          .play {
            right: 6px;
            bottom: 6px;
            position: absolute;
            cursor: pointer;
            & > img {
              width: 15px;
              height: 15px;
            }
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="FloatNote" v-if="isShow" @click="onBlurhandler">
    <div
      class="note-main __hidden-scroll__"
      v-on:click.stop
      :style="{ top: `${posY - 50}px`, transform: transform }"
      ref="noteMainRef"
    >
      <nav>
        <span>{{ noteContent.createTime }}</span>
        <span>我的笔记</span>
      </nav>
      <div class="textCard">{{ noteContent.nodeContent }}</div>
      <div class="enclosure" v-if="noteContent.attachments?.length">
        <div class="enclosure-card" v-for="ele in noteContent.attachments">
          <div class="image" v-if="ele.attachmentType === 1">
            <img :src="ele.attachmentUrl" alt="" />
            <div class="maskLayer __ellipsis__" @click="preview(ele)">
              {{ ele.attachmentName }}
            </div>
          </div>

          <div class="audio" v-if="ele.attachmentType === 2">
            <img src="@/assets/images/sidebar/note-voice.png" alt="" />
            <section>
              <div class="name __ellipsis__">{{ ele.attachmentName }}</div>
              <div class="size">{{ ele.attachmentSize }}KB</div>
              <div class="play">
                <img
                  src="@/assets/images/sidebar/play-voice.png"
                  alt=""
                  @click="preview(ele)"
                />
              </div>
            </section>
          </div>
        </div>
      </div>

      <!-- 这个img标签用来判断note-main容器是否加载完成 -->
      <img
        src="@/assets/images/readerHeader/bookmark.png"
        alt=""
        v-show="false"
        @load="onload"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, computed } from "vue";
import useReader from "@/store/modules/reader";
import { getSectionIdName } from "@/utils/index.js";
import usePreview from "@/store/modules/preview.js";
const previewStore = usePreview();
const store = useReader();
const isShow = ref(false);
const posY = ref(0);
const posX = ref(0);
const noteMainRef = ref(null);
const noteContent = ref({});
let floatNoteTimer = undefined;
function onBlurhandler() {
  clearTimeout(floatNoteTimer);
  floatNoteTimer = undefined;
  floatNoteTimer2 = undefined;
  isShow.value = false;
}
const transform = computed(() => {
  if (store.flex === "one") {
    return "translateX(-200%)";
  } else {
    const _x = window.innerWidth + 80;
    if (posX.value < _x / 2) {
      return "translateX(-10%)";
    } else {
      return "translateX(-94%)";
    }
  }
});

const createNoteStyle = () => {
  const NOTESTYLEID = "__NOTE_STYLE_ID__";
  let styles = document.getElementById(NOTESTYLEID);
  if (styles) styles.remove();
  styles = document.createElement("style");
  styles.id = NOTESTYLEID;
  const noteIdNameList = [];
  store.noteList.forEach((ele) => {
    noteIdNameList.push(getSectionIdName(ele.fromWordId, ele.endWordId));
  });
  styles.innerHTML = `${noteIdNameList.toString()}{ background-color: rgb(22 92 151 / .6); border-bottom:2px dashed #666;cursor: pointer; }`;
  const Body = document.querySelector("body");
  Body.appendChild(styles);
};

const clickNote = (e, id_ls) => {
  posY.value = e.clientY;
  posX.value = e.clientX;
  isShow.value = true;
  noteContent.value = store.noteList.find(
    (ele) =>
      "#" + ele.fromWordId === id_ls[0] && "#" + ele.endWordId === id_ls.pop()
  );
};
const preview = (v) => {
  previewStore.setData({ url: v.attachmentUrl, title: v.attachmentName });
};
const onload = () => {
  //元素底部距离游览器顶部的距离
  const _bh = noteMainRef.value.clientHeight + posY.value - 50;
  //浏览器可视区高度
  const _wh = window.innerHeight;
  if (_bh > _wh) {
    const _c = _bh - _wh;
    posY.value = posY.value - _c - 10; //这个10 是偏移量
  }
};

let floatNoteTimer2 = null;
const registrationClickEvent = () => {
  const noteIdNameList = [];
  store.noteList.forEach((ele) => {
    noteIdNameList.push(getSectionIdName(ele.fromWordId, ele.endWordId));
  });
  noteIdNameList.forEach((ele) => {
    ele.split(",").forEach((item) => {
      const element = document.querySelector(item);
      if (element) {
        // element.addEventListener('mouseover', (e) => {
        //   clearTimeout(floatNoteTimer)
        //   floatNoteTimer = setTimeout(() => {
        //     clickNote(e, ele.split(','))
        //   }, 400)
        // })
        element.addEventListener("mouseenter", (e) => {
          if (floatNoteTimer2) {
            return;
          }
          floatNoteTimer2 = setTimeout(() => {
            clickNote(e, ele.split(","));
          }, 1000);
          // console.log('floatNoteTimer2 init:', floatNoteTimer2)
        });
        element.addEventListener("mouseleave", (e) => {
          // console.log('mouseleave:', floatNoteTimer2)
          clearTimeout(floatNoteTimer2);
          floatNoteTimer2 = undefined;
        });
      }
    });
  });
};

watch(
  () => [
    store.noteList,
    store.comprehensiveBookData.currentPageIndex,
    store.pageFlippingMethod,
    store.flex,
  ],
  async () => {
    await nextTick();
    setTimeout(() => {
      createNoteStyle();
      registrationClickEvent();
    }, 100);
  },
  { deep: true }
);
</script>
