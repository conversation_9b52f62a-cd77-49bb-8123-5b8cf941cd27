<template>
  <div class="mind-map-wrapper">
    <!-- 思维导图弹窗 -->
    <MindMapDialog 
      ref="mindMapDialogRef" 
      @edit="handleEdit" 
      @close="handleClose"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import MindMapDialog from './MindMapDialog.vue';

const emit = defineEmits(['edit']);
const mindMapDialogRef = ref(null);

// 打开思维导图
const openMindMap = () => {
  if (mindMapDialogRef.value) {
    mindMapDialogRef.value.open();
  }
};

// 处理编辑事件
const handleEdit = (type, data) => {
  emit('edit', type, data);
};

// 处理关闭事件
const handleClose = () => {
  console.log('思维导图弹窗已关闭');
};

// 加载数据方法，供父组件调用
const loadData = (data) => {
  if (mindMapDialogRef.value) {
    mindMapDialogRef.value.loadData(data);
  }
};

defineExpose({
  loadData,
  openMindMap
});
</script>

<style lang="scss" scoped>
.mind-map-wrapper {
  display: none; // 隐藏包装器，因为内容在弹窗中
}
</style> 