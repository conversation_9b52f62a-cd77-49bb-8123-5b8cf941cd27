<template>
  <div class="mind-map-editor" :class="{ 'is-dark': isDark, 'zen-mode': isZenMode }">
    <!-- 工具栏 -->
    <div class="toolbar" v-if="!isZenMode">
      <div class="toolbar-left">
        <!-- 主要功能按钮 -->
        <el-button-group>
          <el-button size="small" @click="handleInsertSibling">
            <el-icon><Plus /></el-icon>
            同级节点
          </el-button>
          <el-button size="small" @click="handleInsertChild">
            <el-icon><Operation /></el-icon>
            子节点
          </el-button>
          <el-button size="small" @click="handleDeleteNode">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </el-button-group>
        
        <el-divider direction="vertical" />
        
        <!-- 节点内容功能 -->
        <el-button-group>
          <el-button size="small" @click="handleAddImage">
            <el-icon><Picture /></el-icon>
            图片
          </el-button>
          <el-button size="small" @click="handleAddLink">
            <el-icon><Link /></el-icon>
            链接
          </el-button>
          <el-button size="small" @click="handleAddNote">
            <el-icon><Document /></el-icon>
            备注
          </el-button>
        </el-button-group>
        
        <el-divider direction="vertical" />
        
        <!-- 高级功能：概要、关联线、外框 -->
        <el-button-group>
          <el-button size="small" @click="handleAddGeneralization">
            <el-icon><Collection /></el-icon>
            概要
          </el-button>
          <el-button size="small" @click="handleAddAssociativeLine">
            <el-icon><Connection /></el-icon>
            关联线
          </el-button>
          <el-button size="small" @click="handleAddOuterFrame">
            <el-icon><Grid /></el-icon>
            外框
          </el-button>
        </el-button-group>
        
        <el-divider direction="vertical" />
        
        <!-- 视图控制 -->
        <el-button-group>
          <el-button size="small" @click="handleZoomIn">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
          <el-button size="small" @click="handleZoomOut">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button size="small" @click="handleFit">
            <el-icon><FullScreen /></el-icon>
            适应
          </el-button>
        </el-button-group>
        
        <el-divider direction="vertical" />
        
        
        <el-dropdown @command="handleLayoutChange">
          <el-button size="small">
            布局 <el-icon><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="logicalStructure">逻辑结构图</el-dropdown-item>
              <el-dropdown-item command="mindMap">思维导图</el-dropdown-item>
              <el-dropdown-item command="catalogOrganization">目录组织图</el-dropdown-item>
              <el-dropdown-item command="organizationStructure">组织结构图</el-dropdown-item>
              <el-dropdown-item command="timeline">时间轴</el-dropdown-item>
              <el-dropdown-item command="fishbone">鱼骨图</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      
      <div class="toolbar-right">
        <!-- 导入导出 -->
        <el-button-group>
          <el-button size="small" @click="handleNewFile">
            <el-icon><DocumentAdd /></el-icon>
            新建
          </el-button>
          <el-button size="small" @click="handleImport">
            <el-icon><Upload /></el-icon>
            导入
          </el-button>
          <el-button size="small" @click="handleSaveAs">
            <el-icon><DocumentCopy /></el-icon>
            另存为
          </el-button>
          <el-dropdown @command="handleExport">
            <el-button size="small">
              导出 <el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="png">PNG图片</el-dropdown-item>
                <el-dropdown-item command="svg">SVG图片</el-dropdown-item>
                <el-dropdown-item command="pdf">PDF文档</el-dropdown-item>
                <el-dropdown-item command="json">JSON数据</el-dropdown-item>
                <el-dropdown-item command="xmind">XMind文件</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-button-group>
        
        <el-divider direction="vertical" />
        
        <!-- 其他功能 -->
        <el-button size="small" @click="toggleZenMode">
          <el-icon><View /></el-icon>
          {{ isZenMode ? '退出禅模式' : '禅模式' }}
        </el-button>
        
  
      </div>
    </div>
    
    <!-- 思维导图主容器 -->
    <div class="mind-map-main">
      <div 
        class="mind-map-container" 
        ref="mindMapContainer"
        @contextmenu.prevent="handleContextMenu"
      ></div>
      
      <!-- 统计信息 -->
      <div class="count-info" v-if="!isZenMode && mindMap">
        节点: {{ nodeCount }} | 字数: {{ wordCount }}
      </div>
      
      <!-- 小地图 -->
      <div class="mini-map-container" v-show="showMiniMap && mindMap"></div>
    </div>
    
    <!-- 右键菜单 -->
    <div 
      class="context-menu" 
      v-show="contextMenu.show"
      :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
      @click="hideContextMenu"
    >
      <div class="menu-item" @click="handleInsertSibling">
        <el-icon><Plus /></el-icon>
        插入同级节点
      </div>
      <div class="menu-item" @click="handleInsertChild">
        <el-icon><Operation /></el-icon>
        插入子节点
      </div>
      <div class="menu-item" @click="handleDeleteNode" v-if="!isRootNode">
        <el-icon><Delete /></el-icon>
        删除节点
      </div>
      <div class="menu-divider"></div>
      <div class="menu-item" @click="handleAddImage">
        <el-icon><Picture /></el-icon>
        添加图片
      </div>
      <div class="menu-item" @click="handleAddIcon">
        <el-icon><Star /></el-icon>
        添加图标
      </div>
      <div class="menu-item" @click="handleAddLink">
        <el-icon><Link /></el-icon>
        添加链接
      </div>
      <div class="menu-item" @click="handleAddNote">
        <el-icon><Document /></el-icon>
        添加备注
      </div>
      <div class="menu-divider"></div>
      <div class="menu-item" @click="handleAddGeneralization">
        <el-icon><Collection /></el-icon>
        添加概要
      </div>
      <div class="menu-item" @click="handleAddAssociativeLine">
        <el-icon><Connection /></el-icon>
        添加关联线
      </div>
      <div class="menu-item" @click="handleAddOuterFrame">
        <el-icon><Grid /></el-icon>
        添加外框
      </div>
      <div class="menu-divider"></div>
      <div class="menu-item" @click="handleCopyNode">
        <el-icon><DocumentCopy /></el-icon>
        复制节点
      </div>
      <div class="menu-item" @click="handlePasteNode" v-if="canPaste">
        <el-icon><Document /></el-icon>
        粘贴节点
      </div>
      <div class="menu-divider"></div>
      <div class="menu-item" @click="handleEditNode">
        <el-icon><Edit /></el-icon>
        编辑文本
      </div>
      <div class="menu-item" @click="handleNodeStyle">
        <el-icon><Setting /></el-icon>
        设置样式
      </div>
      <div class="menu-divider" v-if="hasNodeFeatures"></div>
      <div class="menu-item" @click="handleRemoveImage" v-if="hasImage">
        <el-icon><Picture /></el-icon>
        移除图片
      </div>
      <div class="menu-item" @click="handleRemoveIcon" v-if="hasIcon">
        <el-icon><Star /></el-icon>
        移除图标
      </div>
      <div class="menu-item" @click="handleRemoveLink" v-if="hasLink">
        <el-icon><Link /></el-icon>
        移除链接
      </div>
      <div class="menu-item" @click="handleRemoveNote" v-if="hasNote">
        <el-icon><Document /></el-icon>
        移除备注
      </div>
      <div class="menu-item" @click="handleRemoveGeneralization" v-if="hasGeneralization">
        <el-icon><Collection /></el-icon>
        移除概要
      </div>
      <div class="menu-item" @click="handleRemoveOuterFrame" v-if="hasOuterFrame">
        <el-icon><Grid /></el-icon>
        移除外框
      </div>
    </div>
    
    <!-- 图片上传输入框 -->
    <input 
      ref="imageInput" 
      type="file" 
      style="display: none" 
      accept="image/*"
      @change="onImageChange"
    />
    
    <!-- 链接设置对话框 -->
    <el-dialog v-model="linkDialog.visible" title="添加超链接" width="500px">
      <el-form :model="linkDialog" label-width="80px">
        <el-form-item label="链接文本">
          <el-input v-model="linkDialog.text" placeholder="请输入链接显示文本" />
        </el-form-item>
        <el-form-item label="链接地址">
          <el-input v-model="linkDialog.url" placeholder="请输入链接地址" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="linkDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddLink">确定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 备注设置对话框 -->
    <el-dialog v-model="noteDialog.visible" title="添加备注" width="500px">
      <el-form :model="noteDialog" label-width="80px">
        <el-form-item label="备注内容">
          <el-input 
            v-model="noteDialog.content" 
            type="textarea" 
            :rows="4"
            placeholder="请输入备注内容" 
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="noteDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddNote">确定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 图标选择对话框 -->
    <el-dialog v-model="iconDialog.visible" title="选择图标" width="600px">
      <div class="icon-grid">
        <div 
          v-for="icon in iconList" 
          :key="icon.name"
          class="icon-item"
          :class="{ 'selected': iconDialog.selected === icon.name }"
          @click="selectIcon(icon.name)"
        >
          <el-icon :size="24">
            <component :is="icon.component" />
          </el-icon>
          <span class="icon-name">{{ icon.label }}</span>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="iconDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddIcon">确定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 概要设置对话框 -->
    <el-dialog v-model="generalizationDialog.visible" title="添加概要" width="500px">
      <el-form :model="generalizationDialog" label-width="80px">
        <el-form-item label="概要内容">
          <el-input 
            v-model="generalizationDialog.text" 
            placeholder="请输入概要内容" 
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="generalizationDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddGeneralization">确定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 外框设置对话框 -->
    <el-dialog v-model="outerFrameDialog.visible" title="添加外框" width="500px">
      <el-form :model="outerFrameDialog" label-width="80px">
        <el-form-item label="外框文字">
          <el-input 
            v-model="outerFrameDialog.text" 
            placeholder="请输入外框文字（可选）" 
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="outerFrameDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddOuterFrame">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, computed } from 'vue';
import MindMap from 'simple-mind-map';
import { getMindmapByBookId, addMindmap, updateMindmap } from '@/api/edu/mindmap';
import useReaderStore from "@/store/modules/reader";

// 导入所有插件 - 基于官方完整配置，新增 OuterFrame
import MiniMap from 'simple-mind-map/src/plugins/MiniMap.js';
import Watermark from 'simple-mind-map/src/plugins/Watermark.js';
import KeyboardNavigation from 'simple-mind-map/src/plugins/KeyboardNavigation.js';
import ExportPDF from 'simple-mind-map/src/plugins/ExportPDF.js';
import ExportXMind from 'simple-mind-map/src/plugins/ExportXMind.js';
import Export from 'simple-mind-map/src/plugins/Export.js';
import Drag from 'simple-mind-map/src/plugins/Drag.js';
import Select from 'simple-mind-map/src/plugins/Select.js';
// import RichText from 'simple-mind-map/src/plugins/RichText.js'; // 暂时禁用
import AssociativeLine from 'simple-mind-map/src/plugins/AssociativeLine.js';
import TouchEvent from 'simple-mind-map/src/plugins/TouchEvent.js';
import NodeImgAdjust from 'simple-mind-map/src/plugins/NodeImgAdjust.js';
import SearchPlugin from 'simple-mind-map/src/plugins/Search.js';
import Painter from 'simple-mind-map/src/plugins/Painter.js';
import Scrollbar from 'simple-mind-map/src/plugins/Scrollbar.js';
import Formula from 'simple-mind-map/src/plugins/Formula.js';
import OuterFrame from 'simple-mind-map/src/plugins/OuterFrame.js';

// 导入解析器
import xmind from 'simple-mind-map/src/parse/xmind.js';
import markdown from 'simple-mind-map/src/parse/markdown.js';

import { 
  Plus, 
  Operation, 
  Delete, 
  ZoomIn, 
  ZoomOut, 
  FullScreen,
  ArrowDown,
  Upload,
  View,
  Coordinate,
  Picture,
  Star,
  Link,
  Document,
  DocumentCopy,
  DocumentAdd, // 新增：新建文件图标
  Edit,
  Setting,
  Warning,
  InfoFilled,
  Clock,
  User,
  Location,
  Phone,
  Folder,
  Search,
  Refresh,
  Timer,
  Check,
  Close,
  Connection,  // 新增：关联线图标
  Grid,        // 新增：外框图标
  Collection   // 新增：概要图标
} from '@element-plus/icons-vue';
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus';

// 注册所有插件 - 完整版本（暂时禁用 RichText，新增 OuterFrame）
MindMap.usePlugin(MiniMap)
  .usePlugin(Watermark)
  .usePlugin(KeyboardNavigation)
  .usePlugin(ExportPDF)
  .usePlugin(ExportXMind)
  .usePlugin(Export)
  .usePlugin(Drag)
  .usePlugin(Select)
  // .usePlugin(RichText) // 暂时禁用，避免初始化错误
  .usePlugin(AssociativeLine)
  .usePlugin(TouchEvent)
  .usePlugin(NodeImgAdjust)
  .usePlugin(SearchPlugin)
  .usePlugin(Painter)
  .usePlugin(Scrollbar)
  .usePlugin(Formula)
  .usePlugin(OuterFrame);

const emit = defineEmits(['edit']);

// 响应式数据
const mindMapContainer = ref(null);
const imageInput = ref(null);
let mindMap = null;
let fileHandle = null; // 用于存储文件句柄
let mindMapData = reactive({ id: null }); // 用于存储从后端获取的思维导图数据

const readerStore = useReaderStore();
const bookId = computed(() => readerStore.comprehensiveBookData.bookId);
// const userId = computed(() => readerStore.userInfo.userId); - 移除

const isDark = ref(false);
const isZenMode = ref(false);
const showMiniMap = ref(false);
const nodeCount = ref(0);
const wordCount = ref(0);

// 关联线功能（简化版本）

// 右键菜单
const contextMenu = reactive({
  show: false,
  x: 0,
  y: 0
});

// 链接对话框
const linkDialog = reactive({
  visible: false,
  text: '',
  url: ''
});

// 备注对话框
const noteDialog = reactive({
  visible: false,
  content: ''
});

// 图标对话框
const iconDialog = reactive({
  visible: false,
  selected: ''
});

// 新增：概要对话框
const generalizationDialog = reactive({
  visible: false,
  text: ''
});

// 新增：外框对话框
const outerFrameDialog = reactive({
  visible: false,
  text: ''
});

// 图标列表 - 使用基础安全的 Element Plus 图标
const iconList = [
  { name: 'star', component: Star, label: '重要' },
  { name: 'check', component: Check, label: '完成' },
  { name: 'warning', component: Warning, label: '警告' },
  { name: 'info', component: InfoFilled, label: '信息' },
  { name: 'clock', component: Clock, label: '时间' },
  { name: 'timer', component: Timer, label: '计时' },
  { name: 'user', component: User, label: '用户' },
  { name: 'location', component: Location, label: '位置' },
  { name: 'phone', component: Phone, label: '电话' },
  { name: 'folder', component: Folder, label: '文件夹' },
  { name: 'search', component: Search, label: '搜索' },
  { name: 'refresh', component: Refresh, label: '刷新' },
  { name: 'link', component: Link, label: '链接' },
  { name: 'edit', component: Edit, label: '编辑' },
  { name: 'close', component: Close, label: '关闭' }
];

// 默认数据 - 修正为正确的官方格式
const defaultData = {
  layout: 'logicalStructure',
  theme: 'default',
  root: {
    data: {
      text: '中心主题',
      expand: true,
      isActive: false
    },
    children: [
      {
        data: {
          text: '分支主题1',
          expand: true,
          isActive: false
        },
        children: []
      },
      {
        data: {
          text: '分支主题2', 
          expand: true,
          isActive: false
        },
        children: []
      }
    ]
  }
};

// 计算属性 - 右键菜单状态判断
const currentActiveNode = computed(() => {
  if (!mindMap?.renderer?.activeNodeList) return null;
  return mindMap.renderer.activeNodeList[0] || null;
});

const isRootNode = computed(() => {
  return currentActiveNode.value?.isRoot || false;
});

const hasImage = computed(() => {
  const node = currentActiveNode.value;
  return node?.getData('image') ? true : false;
});

const hasIcon = computed(() => {
  const node = currentActiveNode.value;
  const icons = node?.getData('icon');
  return icons && icons.length > 0;
});

const hasLink = computed(() => {
  const node = currentActiveNode.value;
  const hyperlink = node?.getData('hyperlink');
  return hyperlink && hyperlink.url;
});

const hasNote = computed(() => {
  const node = currentActiveNode.value;
  const note = node?.getData('note');
  return note && note.trim();
});

// 新增：概要状态判断
const hasGeneralization = computed(() => {
  const activeNodes = mindMap?.renderer?.activeNodeList || [];
  if (activeNodes.length === 0) return false;

  // 如果激活的就是概要节点，则可以移除
  if (activeNodes.some(node => node.isGeneralization)) {
    return true;
  }

  // 检查激活的节点是否有概要
  return activeNodes.some(node => {
    return node.getData('generalization');
  });
});

// 新增：外框状态判断
const hasOuterFrame = computed(() => {
  const node = currentActiveNode.value;
  if (!node) return false;
  const outerFrame = node.getData('outerFrame');
  return outerFrame && outerFrame.text;
});

const hasNodeFeatures = computed(() => {
  return hasImage.value || hasIcon.value || hasLink.value || hasNote.value || hasGeneralization.value || hasOuterFrame.value;
});

const canPaste = computed(() => {
  // 这里可以检查剪贴板或者内部复制状态
  return true; // 简化处理，实际项目中可以添加更精确的判断
});

onMounted(async () => {
  await nextTick();
  await initMindMap();
});

onUnmounted(() => {
  if (mindMap) {
    mindMap.destroy();
  }
});

// 初始化思维导图 - 完整配置
const initMindMap = async () => {
  if (!mindMapContainer.value) return;
  
  const loading = ElLoading.service({
    target: mindMapContainer.value,
    text: '思维导图加载中...'
  });
  
  let initialData = null;
  let successMessage = '思维导图已加载';
  
  // Step 1: Attempt to load data from the cloud
  if (bookId.value) { // 只检查 bookId
    try {
      loading.text = '正在加载云端数据...';
      const response = await getMindmapByBookId(bookId.value);
      if (response.data && response.data.mindMapData) {
        initialData = JSON.parse(response.data.mindMapData);
        mindMapData.id = response.data.mindMapId;
        successMessage = '云端数据加载成功';
      } else {
        ElMessage.info('暂无云端数据，将为您创建新的思维导图');
      }
    } catch (error) {
      console.error('加载云端数据失败:', error);
      ElMessage.error('加载云端数据失败，将使用本地默认模板');
    }
  } else {
    ElMessage.warning('缺少书籍ID，无法加载云端数据');
  }
  
  // Step 2: If no cloud data, use the default template
  if (!initialData) {
    initialData = JSON.parse(JSON.stringify(defaultData));
  }
  
  // Step 3: Initialize the mind map with the data
  try {
    mindMap = new MindMap({
      el: mindMapContainer.value,
      data: initialData.root, // Use the root from the loaded/default data
      layout: initialData.layout || defaultData.layout,
      theme: initialData.theme || defaultData.theme,
      readonly: false,
      fit: true, // 开启自适应，这很重要！
      enableFreeDrag: true,
      watermark: '',
      enableNodeRichText: false, // 暂时禁用富文本，避免初始化错误
      enableKeyboard: true,
      enableMouseWheelZoom: true,
      mousewheelAction: 'zoom',
      mousewheelMoveStep: 100,
      maxTag: 5,
      maxGeneralization: 5,
      exportPaddingX: 10,
      exportPaddingY: 10,
      imgTextMargin: 5,
      textContentMargin: 2,
      selectTranslateStep: 3,
      selectTranslateLimit: 20,
      initRootNodePosition: ['center', 'center'],
      outerFramePaddingX: 20,
      outerFramePaddingY: 20,
      defaultOuterFrameText: '',
      miniMapConfig: {
        width: 200,
        height: 150,
        viewBoxStyle: {
          fill: 'rgba(102, 126, 234, 0.3)',
          stroke: 'rgb(102, 126, 234)',
          strokeWidth: 1
        },
        nodeStyle: {
          fill: '#67c23a',
          stroke: 'transparent'
        }
      }
    });
    
    // 监听事件
    setupEventListeners();
    
    // 首次加载时更新一次统计，并添加延时确保渲染完成
    setTimeout(() => {
      updateStats();
    }, 200);
    
    ElMessage.success(successMessage);
    
  } catch (error) {
    console.error('思维导图初始化失败:', error);
    ElMessage.error('思维导图初始化失败: ' + error.message);
  } finally {
    loading.close();
  }
};

// 设置事件监听
const setupEventListeners = () => {
  if (!mindMap) return;
  
  // 完整的事件监听 - 参考官方版本
  const events = [
    'node_active',
    'data_change',
    'view_data_change',
    'back_forward',
    'node_contextmenu',
    'node_click',
    'draw_click',
    'expand_btn_click',
    'svg_mousedown',
    'mouseup',
    'mode_change',
    'node_tree_render_end',
    'rich_text_selection_change',
    'transforming-dom-to-images',
    'generalization_node_contextmenu',
    'painter_start',
    'painter_end',
    'scrollbar_change',
    'scale',
    'translate',
    'node_attachmentClick',
    'node_attachmentContextmenu',
    'demonstrate_jump',
    'exit_demonstrate',
    'node_note_dblclick',
    'node_mousedown',
    // 关联线相关事件
    'associative_line_click',
    'associative_line_change',
    // 外框相关事件
    'outer_frame_change',
    'outer_frame_delete',
    'outer_frame_active'
  ];
  
  events.forEach(event => {
    mindMap.on(event, (...args) => {
      // 统计更新
      if (['data_change', 'node_tree_render_end', 'back_forward'].includes(event)) {
        // 增加延时确保数据已更新
        setTimeout(() => {
          updateStats();
        }, 100);
      }
      
      // 数据变化通知
      if (event === 'data_change') {
        emit('edit', 'mindMap', { action: 'dataChange', data: args[0] });
      }
      
      // 关联线和外框操作后确保更新
      if (event === 'associative_line_change' || event === 'outer_frame_change' || event === 'outer_frame_delete') {
        // 外框插件会自动处理渲染，我们只需要更新统计
        setTimeout(() => {
          updateStats();
        }, 100);
      }
      
      // 调试日志
      console.log(`思维导图事件: ${event}`, args);
    });
  });
};

// 更新统计信息
const updateStats = () => {
  if (!mindMap) return;
  
  try {
    // 统计节点数
    const walkNodes = (root) => {
      if (!root || !root.data) return { count: 0, words: 0 };
      
      let count = 1;
      let words = root.data.text ? root.data.text.length : 0;
      
      if (root.children && Array.isArray(root.children)) {
        root.children.forEach(child => {
          const result = walkNodes(child);
          count += result.count;
          words += result.words;
        });
      }
      
      return { count, words };
    };
    
    const rootNode = mindMap.getData();
    if (!rootNode) {
      console.warn('MindMap data is invalid for stats.', rootNode);
      nodeCount.value = 0;
      wordCount.value = 0;
      return;
    }
    
    const stats = walkNodes(rootNode);
    nodeCount.value = stats.count;
    wordCount.value = stats.words;
  } catch (error) {
    console.error('统计信息更新失败:', error);
    // 设置默认值，避免界面异常
    nodeCount.value = 0;
    wordCount.value = 0;
  }
};

// 工具栏功能
const handleInsertSibling = () => {
  if (!mindMap) return;
  mindMap.execCommand('INSERT_NODE');
  updateStats();
};

const handleInsertChild = () => {
  if (!mindMap) return;
  mindMap.execCommand('INSERT_CHILD_NODE');
  updateStats();
};

const handleDeleteNode = () => {
  if (!mindMap) return;
  mindMap.execCommand('REMOVE_NODE');
  updateStats();
};

const handleZoomIn = () => {
  if (!mindMap) return;
  mindMap.view.enlarge();
};

const handleZoomOut = () => {
  if (!mindMap) return;
  mindMap.view.narrow();
};

const handleFit = () => {
  if (!mindMap) return;
  mindMap.view.fit();
};

// 主题切换
const handleThemeChange = (theme) => {
  if (!mindMap) return;
  mindMap.setTheme(theme);
  ElMessage.success(`已切换到${theme}主题`);
};

// 布局切换
const handleLayoutChange = (layout) => {
  if (!mindMap) return;
  mindMap.setLayout(layout);
  ElMessage.success(`已切换到${layout}布局`);
};

// 新增：新建文件
const handleNewFile = () => {
  ElMessageBox.confirm('您确定要新建一个文件吗？当前未保存的更改将会丢失。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    if (mindMap.setFullData) {
      mindMap.setFullData(JSON.parse(JSON.stringify(defaultData)));
    } else {
      mindMap.setData(JSON.parse(JSON.stringify(defaultData.root)));
    }
    fileHandle = null; // 重置文件句柄
    ElMessage.success('已新建思维导图');
    updateStats();
  }).catch(() => {
    // 用户取消
  });
};

// 新增：另存为
const handleSaveAs = async () => {
  if (!mindMap) return;

  const loading = ElLoading.service({
    text: `正在保存文件...`
  });

  try {
    const data = mindMap.getData(true); // 获取完整数据
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });

    // 使用 File System Access API
    const handle = await window.showSaveFilePicker({
      suggestedName: '思维导图.smm',
      types: [
        {
          description: 'Simple Mind Map File',
          accept: { 'application/json': ['.smm'] },
        },
      ],
    });
    const writable = await handle.createWritable();
    await writable.write(blob);
    await writable.close();
    
    fileHandle = handle; // 保存文件句柄以备后用

    loading.close();
    ElMessage.success(`文件保存成功`);
  } catch (error) {
    loading.close();
    if (error.name !== 'AbortError') {
      console.error('保存文件失败:', error);
      ElMessage.error(`保存失败: ${error.message}`);
    }
  }
};


// 禅模式切换
const toggleZenMode = () => {
  isZenMode.value = !isZenMode.value;
  ElMessage.info(isZenMode.value ? '已进入禅模式' : '已退出禅模式');
};

// 小地图切换
const toggleMiniMap = () => {
  showMiniMap.value = !showMiniMap.value;
  
  if (!mindMap) return;
  
  if (showMiniMap.value) {
    // 显示小地图
    const miniMapContainer = document.querySelector('.mini-map-container');
    if (miniMapContainer && mindMap.miniMap) {
      mindMap.miniMap.show();
      ElMessage.success('小地图已开启');
    }
  } else {
    // 隐藏小地图
    if (mindMap.miniMap) {
      mindMap.miniMap.hide();
      ElMessage.info('小地图已关闭');
    }
  }
};

// 导出功能
const handleExport = (format) => {
  if (!mindMap) return;
  
  const loading = ElLoading.service({
    text: `正在导出${format.toUpperCase()}格式...`
  });
  
  try {
    switch (format) {
      case 'png':
        mindMap.export('png', true, '思维导图');
        break;
      case 'svg':
        mindMap.export('svg', true, '思维导图');
        break;
      case 'pdf':
        mindMap.export('pdf', true, '思维导图');
        break;
      case 'json':
        const data = mindMap.getData(true); // 导出完整数据
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = '思维导图.json';
        a.click();
        URL.revokeObjectURL(url);
        break;
      case 'xmind':
        mindMap.export('xmind', true, '思维导图');
        break;
    }
    loading.close();
    ElMessage.success(`${format.toUpperCase()}导出成功`);
  } catch (error) {
    loading.close();
    ElMessage.error(`导出失败: ${error.message}`);
  }
};

// 导入功能
const handleImport = async () => {
  try {
    const [handle] = await window.showOpenFilePicker({
      types: [
        {
          description: 'Mind Map Files',
          accept: {
            'application/json': ['.smm', '.json'],
            'application/vnd.xmind.workbook': ['.xmind'],
            'text/markdown': ['.md'],
          },
        },
      ],
      excludeAcceptAllOption: true,
      multiple: false,
    });
    
    fileHandle = handle;
    const file = await fileHandle.getFile();
    
    if (!file || !mindMap) return;

    const loading = ElLoading.service({ text: '正在导入文件...' });

    try {
      if (/\.(smm|json)$/.test(file.name)) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const data = JSON.parse(e.target.result);
            // 智能判断是全量数据还是节点数据
            if (data && data.root && data.layout) {
              mindMap.setFullData(data);
            } else {
              mindMap.setData(data);
            }
            ElMessage.success('JSON/SMM 文件导入成功');
          } catch (error) {
            ElMessage.error('文件格式错误或内容损坏');
          } finally {
            loading.close();
            updateStats();
          }
        };
        reader.readAsText(file);
      } else if (/\.xmind$/.test(file.name)) {
        xmind.parseXmindFile(file).then(data => {
          mindMap.setData(data);
          ElMessage.success('XMind 文件导入成功');
        }).catch(err => {
          console.error(err);
          ElMessage.error('XMind 文件解析失败');
        }).finally(() => {
          loading.close();
          updateStats();
        });
      } else if (/\.md$/.test(file.name)) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const data = markdown.transform(e.target.result);
            mindMap.setData({
              layout: 'logicalStructure',
              theme: 'default',
              root: data
            });
            ElMessage.success('Markdown 文件导入成功');
          } catch (error) {
            console.error(error);
            ElMessage.error('Markdown 文件解析失败');
          } finally {
            loading.close();
            updateStats();
          }
        };
        reader.readAsText(file);
      } else {
        ElMessage.warning('不支持的文件格式');
        loading.close();
      }
    } catch(err) {
      console.error("导入错误", err);
      ElMessage.error('导入失败: ' + err.message);
      loading.close();
    }
    
  } catch (error) {
    if (error.name !== 'AbortError') {
      console.error('打开文件失败:', error);
      ElMessage.error(`打开文件失败: ${error.message}`);
    }
  }
};

// 右键菜单
const handleContextMenu = (e) => {
  contextMenu.x = e.clientX;
  contextMenu.y = e.clientY;
  contextMenu.show = true;
  
  document.addEventListener('click', hideContextMenu);
};

const hideContextMenu = () => {
  contextMenu.show = false;
  document.removeEventListener('click', hideContextMenu);
};

// 其他节点操作
const handleCopyNode = () => {
  if (!mindMap) return;
  mindMap.execCommand('COPY_NODE');
  ElMessage.success('已复制');
};

const handlePasteNode = () => {
  if (!mindMap) return;
  mindMap.execCommand('PASTE_NODE');
  ElMessage.success('已粘贴');
};

const handleEditNode = () => {
  if (!mindMap) return;
  // 开始编辑当前激活节点
  const activeNodes = mindMap.renderer.activeNodeList;
  if (activeNodes.length > 0) {
    activeNodes[0].enterEditMode();
  }
  hideContextMenu();
};

const handleNodeStyle = () => {
  // 这里可以打开样式设置面板
  ElMessage.info('样式设置功能开发中...');
  hideContextMenu();
};

// 新增的功能函数

// 添加图片
const handleAddImage = () => {
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (!activeNodes || activeNodes.length === 0) {
    ElMessage.warning('请先选择一个节点');
    return;
  }
  imageInput.value?.click();
  hideContextMenu();
};

// 图片文件选择处理
const onImageChange = async (event) => {
  const file = event.target.files[0];
  if (!file || !mindMap) return;
  
  const loading = ElLoading.service({
    text: '正在上传图片...'
  });
  
  try {
    // 转换为 base64
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageUrl = e.target.result;
      const activeNodes = mindMap.renderer.activeNodeList;
      if (activeNodes && activeNodes.length > 0) {
        // 给当前节点添加图片 - 使用正确的API格式
        mindMap.execCommand('SET_NODE_IMAGE', activeNodes[0], {
          url: imageUrl,
          title: file.name,
          width: 100,
          height: 100
        });
        ElMessage.success('图片添加成功');
      }
      loading.close();
    };
    reader.readAsDataURL(file);
    
    // 清空文件输入框
    event.target.value = '';
  } catch (error) {
    loading.close();
    ElMessage.error('图片上传失败: ' + error.message);
  }
};

// 添加图标
const handleAddIcon = () => {
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (!activeNodes || activeNodes.length === 0) {
    ElMessage.warning('请先选择一个节点');
    return;
  }
  iconDialog.visible = true;
  iconDialog.selected = '';
  hideContextMenu();
};

// 选择图标
const selectIcon = (iconName) => {
  iconDialog.selected = iconName;
};

// 确认添加图标
const confirmAddIcon = () => {
  if (!iconDialog.selected) {
    ElMessage.warning('请选择一个图标');
    return;
  }
  
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (activeNodes && activeNodes.length > 0) {
    // 给节点添加图标 - 使用 SET_NODE_ICON 命令
    mindMap.execCommand('SET_NODE_ICON', activeNodes[0], [iconDialog.selected]);
    ElMessage.success('图标添加成功');
  }
  
  iconDialog.visible = false;
};

// 添加超链接
const handleAddLink = () => {
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (!activeNodes || activeNodes.length === 0) {
    ElMessage.warning('请先选择一个节点');
    return;
  }
  
  // 获取当前节点的文本作为默认链接文本
  const currentText = activeNodes[0].getData('text') || '';
  linkDialog.text = currentText;
  linkDialog.url = '';
  linkDialog.visible = true;
  hideContextMenu();
};

// 确认添加链接
const confirmAddLink = () => {
  if (!linkDialog.url.trim()) {
    ElMessage.warning('请输入链接地址');
    return;
  }
  
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (activeNodes && activeNodes.length > 0) {
    // 设置节点超链接
    mindMap.execCommand('SET_NODE_HYPERLINK', activeNodes[0], linkDialog.url, linkDialog.text || linkDialog.url);
    ElMessage.success('超链接添加成功');
  }
  
  linkDialog.visible = false;
};

// 添加备注
const handleAddNote = () => {
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (!activeNodes || activeNodes.length === 0) {
    ElMessage.warning('请先选择一个节点');
    return;
  }
  
  noteDialog.content = '';
  noteDialog.visible = true;
  hideContextMenu();
};

// 确认添加备注
const confirmAddNote = () => {
  if (!noteDialog.content.trim()) {
    ElMessage.warning('请输入备注内容');
    return;
  }
  
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (activeNodes && activeNodes.length > 0) {
    // 设置节点备注
    mindMap.execCommand('SET_NODE_NOTE', activeNodes[0], noteDialog.content);
    ElMessage.success('备注添加成功');
  }
  
  noteDialog.visible = false;
};

// 移除功能
const handleRemoveImage = () => {
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (!activeNodes || activeNodes.length === 0) {
    ElMessage.warning('请先选择一个节点');
    return;
  }
  
  mindMap.execCommand('SET_NODE_IMAGE', activeNodes[0], { url: null });
  ElMessage.success('图片已移除');
  hideContextMenu();
};

const handleRemoveIcon = () => {
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (!activeNodes || activeNodes.length === 0) {
    ElMessage.warning('请先选择一个节点');
    return;
  }
  
  mindMap.execCommand('SET_NODE_ICON', activeNodes[0], []);
  ElMessage.success('图标已移除');
  hideContextMenu();
};

const handleRemoveLink = () => {
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (!activeNodes || activeNodes.length === 0) {
    ElMessage.warning('请先选择一个节点');
    return;
  }
  
  mindMap.execCommand('SET_NODE_HYPERLINK', activeNodes[0], '', '');
  ElMessage.success('超链接已移除');
  hideContextMenu();
};

const handleRemoveNote = () => {
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (!activeNodes || activeNodes.length === 0) {
    ElMessage.warning('请先选择一个节点');
    return;
  }
  
  mindMap.execCommand('SET_NODE_NOTE', activeNodes[0], '');
  ElMessage.success('备注已移除');
  hideContextMenu();
};

// 新增功能：概要管理
const handleAddGeneralization = () => {
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (!activeNodes || activeNodes.length === 0) {
    ElMessage.warning('请先选择一个节点');
    return;
  }
  
  generalizationDialog.text = '';
  generalizationDialog.visible = true;
  hideContextMenu();
};

const confirmAddGeneralization = () => {
  if (!generalizationDialog.text.trim()) {
    ElMessage.warning('请输入概要内容');
    return;
  }

  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (!activeNodes || activeNodes.length === 0) {
    ElMessage.warning('请先选择一个或多个节点');
    return;
  }

  try {
    mindMap.execCommand('ADD_GENERALIZATION', {
      text: generalizationDialog.text.trim()
    });
    ElMessage.success('概要添加成功');
    generalizationDialog.visible = false;
  } catch (error) {
    console.error('添加概要失败:', error);
    ElMessage.error('添加概要失败: ' + (error.message || ''));
  }
};

const handleRemoveGeneralization = () => {
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (!activeNodes || activeNodes.length === 0) {
    ElMessage.warning('请先选择一个节点');
    return;
  }

  try {
    mindMap.execCommand('REMOVE_GENERALIZATION');
    ElMessage.success('概要已移除');
  } catch (error) {
    console.error('移除概要失败:', error);
    ElMessage.error('移除概要失败: ' + (error.message || ''));
  }
  hideContextMenu();
};

// 新增功能：关联线管理  
const handleAddAssociativeLine = () => {
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (!activeNodes || activeNodes.length === 0) {
    ElMessage.warning('请先选择一个节点作为关联线起点');
    return;
  }
  
  try {
    // 按照官方方式处理关联线
    if (mindMap.associativeLine) {
      // 直接调用官方API - 这会启动关联线绘制模式
      mindMap.associativeLine.createLineFromActiveNode();
      
      ElMessage({
        message: '关联线模式已开启，请点击目标节点完成连接',
        type: 'success',
        duration: 3000
      });
      
      // 多重监听关联线创建完成事件，确保及时渲染
             const forceRefresh = () => {
         if (!mindMap) return;
         
         try {
           // 立即强制渲染
           mindMap.render();
           // 触发重新布局（使用正确的方法）
           if (typeof mindMap.reRender === 'function') {
             mindMap.reRender();
           }
           // 更新统计
           updateStats();
           // 触发数据变化事件
           const data = mindMap.getData();
           if (data) {
             mindMap.emit('data_change', data);
           }
         } catch (error) {
           console.error('关联线刷新失败:', error);
         }
       };
      
      // 监听多个相关事件，确保及时刷新
      const associativeLineEvents = [
        'associative_line_click',
        'associative_line_change', 
        'associative_line_create',
        'draw_click'
      ];
      
      const cleanupListeners = () => {
        associativeLineEvents.forEach(event => {
          mindMap.off(event, onAssociativeLineEvent);
        });
      };
      
      const onAssociativeLineEvent = (...args) => {
        // 立即刷新
        forceRefresh();
        
        // 延迟再次刷新，确保渲染完整
        setTimeout(() => {
          forceRefresh();
        }, 50);
        
        // 再次延迟刷新，处理可能的异步操作
        setTimeout(() => {
          forceRefresh();
        }, 200);
        
        // 清理监听器
        setTimeout(cleanupListeners, 1000);
      };
      
      // 注册所有事件监听
      associativeLineEvents.forEach(event => {
        mindMap.on(event, onAssociativeLineEvent);
      });
      
    } else {
      ElMessage.error('关联线插件未正确加载');
    }
  } catch (error) {
    console.error('关联线错误:', error);
    ElMessage.error('关联线功能启动失败: ' + error.message);
  }
  hideContextMenu();
};

// 新增功能：外框管理
const handleAddOuterFrame = () => {
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (!activeNodes || activeNodes.length === 0) {
    ElMessage.warning('请先选择一个或多个节点');
    return;
  }
  
  // 过滤掉根节点和概要节点
  const validNodes = activeNodes.filter(node => !node.isRoot && !node.isGeneralization);
  if (validNodes.length === 0) {
    ElMessage.warning('无法为根节点或概要节点添加外框');
    hideContextMenu();
    return;
  }
  
  if (validNodes.length === 1) {
    ElMessage.info('提示：外框功能通常用于多个同级节点，单节点也可以添加');
  }
  
  outerFrameDialog.text = '';
  outerFrameDialog.visible = true;
  hideContextMenu();
};

const confirmAddOuterFrame = () => {
  try {
    const activeNodes = mindMap?.renderer?.activeNodeList;
    if (!activeNodes || activeNodes.length === 0) {
      ElMessage.warning('请先选择节点');
      return;
    }
    
    // 强制刷新函数
    const forceRefreshOuterFrame = () => {
      if (!mindMap) return;
      
      try {
        // 立即强制渲染
        mindMap.render();
        // 触发重新布局（使用正确的方法）
        if (typeof mindMap.reRender === 'function') {
          mindMap.reRender();
        }
        // 更新统计
        updateStats();
        // 触发数据变化事件
        const data = mindMap.getData();
        if (data) {
          mindMap.emit('data_change', data);
        }
        // 特别触发外框相关事件
        mindMap.emit('outer_frame_change');
      } catch (error) {
        console.error('外框刷新失败:', error);
      }
    };
    
    // 使用官方外框插件的正确方法
    if (mindMap.outerFrame && mindMap.outerFrame.addOuterFrame) {
      // 构建外框配置对象
      const outerFrameConfig = {
        text: outerFrameDialog.text.trim(),
        stroke: '#409eff',
        strokeWidth: 2,
        fill: 'rgba(64, 158, 255, 0.05)',
        strokeDasharray: ''
      };
      
      // 调用官方方法 - 这样会正确计算位置
      mindMap.outerFrame.addOuterFrame(activeNodes, outerFrameConfig);
      ElMessage.success('外框添加成功');
      
      // 立即强制刷新
      forceRefreshOuterFrame();
      
      // 延迟再次刷新，确保外框完全渲染
      setTimeout(() => {
        forceRefreshOuterFrame();
      }, 50);
      
      setTimeout(() => {
        forceRefreshOuterFrame();
      }, 200);
      
    } else if (mindMap.execCommand) {
      // 备用方法：使用官方命令
      const outerFrameConfig = {
        text: outerFrameDialog.text.trim(),
        stroke: '#409eff',
        strokeWidth: 2,
        fill: 'rgba(64, 158, 255, 0.05)'
      };
      
      try {
        mindMap.execCommand('ADD_OUTER_FRAME', activeNodes, outerFrameConfig);
        ElMessage.success('外框添加成功');
      } catch (cmdError) {
        console.warn('ADD_OUTER_FRAME 命令失败，尝试其他方式:', cmdError);
        
        // 如果命令失败，直接操作节点但使用正确的数据结构
        const groupId = Date.now() + Math.random();
        activeNodes.forEach(node => {
          if (!node.isRoot && !node.isGeneralization) {
            // 使用 SET_NODE_DATA 命令而不是直接操作
            mindMap.execCommand('SET_NODE_DATA', node, {
              outerFrame: {
                ...outerFrameConfig,
                groupId: groupId
              }
            });
          }
        });
        ElMessage.success('外框添加成功');
      }
      
      // 立即强制刷新
      forceRefreshOuterFrame();
      
      // 延迟再次刷新，确保外框完全渲染
      setTimeout(() => {
        forceRefreshOuterFrame();
      }, 50);
      
      setTimeout(() => {
        forceRefreshOuterFrame();
      }, 200);
      
    } else {
      ElMessage.error('外框插件未正确加载');
      outerFrameDialog.visible = false;
      return;
    }
    
    outerFrameDialog.visible = false;
    
  } catch (error) {
    console.error('外框错误:', error);
    ElMessage.error('外框添加失败: ' + error.message);
  }
};

const handleRemoveOuterFrame = () => {
  const activeNodes = mindMap?.renderer?.activeNodeList;
  if (!activeNodes || activeNodes.length === 0) {
    ElMessage.warning('请先选择一个节点');
    return;
  }
  
  try {
    // 强制刷新函数
    const forceRefreshAfterRemove = () => {
      if (!mindMap) return;
      
      try {
        // 立即强制渲染
        mindMap.render();
        // 触发重新布局（使用正确的方法）
        if (typeof mindMap.reRender === 'function') {
          mindMap.reRender();
        }
        // 更新统计
        updateStats();
        // 触发数据变化事件
        const data = mindMap.getData();
        if (data) {
          mindMap.emit('data_change', data);
        }
        // 特别触发外框删除事件
        mindMap.emit('outer_frame_delete');
      } catch (error) {
        console.error('外框移除刷新失败:', error);
      }
    };
    
    // 使用官方外框插件的移除方法
    if (mindMap.outerFrame && mindMap.outerFrame.removeActiveOuterFrame) {
      // 首先尝试设置激活的外框，然后移除
      // 官方插件需要先激活外框才能移除
      let removedAny = false;
      
      activeNodes.forEach(node => {
        const outerFrame = node.getData('outerFrame');
        if (outerFrame) {
          // 使用 SET_NODE_DATA 命令移除
          mindMap.execCommand('SET_NODE_DATA', node, {
            outerFrame: null
          });
          removedAny = true;
        }
      });
      
      if (removedAny) {
        ElMessage.success('外框已移除');
        
        // 立即强制刷新
        forceRefreshAfterRemove();
        
        // 延迟再次刷新，确保删除效果完全渲染
        setTimeout(() => {
          forceRefreshAfterRemove();
        }, 50);
        
        setTimeout(() => {
          forceRefreshAfterRemove();
        }, 200);
      } else {
        ElMessage.info('所选节点没有外框');
      }
      
    } else {
      // 备用方法：直接使用 SET_NODE_DATA 命令
      let removedCount = 0;
      activeNodes.forEach(node => {
        const outerFrame = node.getData('outerFrame');
        if (outerFrame) {
          // 使用官方命令而不是直接操作
          mindMap.execCommand('SET_NODE_DATA', node, {
            outerFrame: null
          });
          removedCount++;
        }
      });
      
      if (removedCount > 0) {
        ElMessage.success(`已移除 ${removedCount} 个外框`);
        
        // 立即强制刷新
        forceRefreshAfterRemove();
        
        // 延迟再次刷新，确保删除效果完全渲染
        setTimeout(() => {
          forceRefreshAfterRemove();
        }, 50);
        
        setTimeout(() => {
          forceRefreshAfterRemove();
        }, 200);
      } else {
        ElMessage.info('所选节点没有外框');
      }
    }
    
  } catch (error) {
    console.error('外框移除错误:', error);
    ElMessage.error('外框移除失败: ' + error.message);
  }
  hideContextMenu();
};

// 暴露方法
const saveData = async () => {
  if (!mindMap) return null;

  const dataToSave = mindMap.getData(true);
  const data = {
    bookId: bookId.value,
    // userId: userId.value, // 移除
    mindMapData: JSON.stringify(dataToSave)
  };

  const loading = ElLoading.service({ text: '正在保存数据...' });

  try {
    if (mindMapData.id) {
      // 更新
      data.mindMapId = mindMapData.id;
      await updateMindmap(data);
      ElMessage.success('思维导图更新成功');
    } else {
      // 新增
      const response = await addMindmap(data);
      if (response.data && response.data.mindMapId) {
        mindMapData.id = response.data.mindMapId;
      }
      ElMessage.success('思维导图保存成功');
    }
    emit('edit', 'mindMap', { action: 'save', data: dataToSave });
    return dataToSave;
  } catch (error) {
    console.error('保存思维导图失败:', error);
    ElMessage.error('保存失败，请稍后重试');
    return null;
  } finally {
    loading.close();
  }
};

const loadData = (data) => {
  if (!mindMap || !data) return;
  mindMap.setData(data);
};

const fit = () => {
  if (!mindMap) return;
  mindMap.view.fit();
};

defineExpose({
  saveData,
  loadData,
  fit,
  mindMap
});
</script>

<style lang="scss" scoped>
.mind-map-editor {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;

  &.is-dark {
    background-color: #2b2b2b;
    color: #fff;
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    z-index: 100;

    .is-dark & {
      background-color: #333;
      border-bottom: 1px solid #555;
    }

    .toolbar-left,
    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    :deep(.el-button-group) {
      margin-right: 10px;
    }

    :deep(.el-divider--vertical) {
      margin: 0 10px;
      height: 20px;
    }

    :deep(.el-button) {
      margin: 0;
      border-radius: 6px;
      font-size: 12px;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
      }
    }

    :deep(.el-dropdown-link) {
      cursor: pointer;
      font-size: 12px;
    }
  }

  .mind-map-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;

    .mind-map-container {
      height: 100%;
      width: 100%;
      background: #fff;
      position: relative;
      min-height: 500px; // 确保最小高度
      
      .is-dark & {
        background: #2b2b2b;
      }

      // 思维导图容器样式重置 - 这个很重要！
      :deep(*) {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      // 思维导图节点样式优化
      :deep(.smm-node) {
        cursor: pointer;
        
        &:hover {
          filter: brightness(1.1);
        }
      }
      
      // 连线样式
      :deep(.smm-line) {
        transition: all 0.3s ease;
      }
      
      // 选中状态
      :deep(.smm-node.active) {
        box-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
      }
    }

    .count-info {
      position: absolute;
      bottom: 15px;
      left: 15px;
      background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
      color: #fff;
      padding: 8px 12px;
      border-radius: 20px;
      font-size: 12px;
      backdrop-filter: blur(10px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 10;
      
      .is-dark & {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
        border: 1px solid rgba(255, 255, 255, 0.1);
      }
    }

    .mini-map-container {
      position: absolute;
      top: 15px;
      right: 15px;
      width: 200px;
      height: 150px;
      background-color: #fff;
      border: 1px solid #e5e5e5;
      border-radius: 8px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      z-index: 9;
      overflow: hidden;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .is-dark & {
        background-color: rgba(51, 51, 51, 0.9);
        border-color: #555;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
      }
    }
  }

  .context-menu {
    position: fixed;
    background-color: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    backdrop-filter: blur(20px);
    z-index: 1000;
    padding: 6px 0;
    min-width: 140px;
    animation: contextMenuFadeIn 0.2s ease-out;

    .is-dark & {
      background-color: rgba(51, 51, 51, 0.95);
      border-color: #555;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .menu-item {
      padding: 10px 16px;
      cursor: pointer;
      font-size: 13px;
      color: #333;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        background: linear-gradient(90deg, #f0f9ff, #e0f2fe);
        color: #1976d2;
        padding-left: 20px;
      }

      .is-dark & {
        color: #fff;

        &:hover {
          background: linear-gradient(90deg, rgba(64, 158, 255, 0.1), rgba(64, 158, 255, 0.05));
          color: #66b1ff;
        }
      }
    }

    .menu-divider {
      height: 1px;
      background: linear-gradient(90deg, transparent, #e5e5e5, transparent);
      margin: 6px 12px;

      .is-dark & {
        background: linear-gradient(90deg, transparent, #555, transparent);
      }
    }
  }

  // 禅模式样式
  &.zen-mode {
    .toolbar {
      display: none;
    }
    
    .count-info {
      display: none;
    }
  }
}

// 动画效果
@keyframes contextMenuFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .mind-map-editor {
    .toolbar {
      padding: 8px 10px;
      flex-direction: column;
      gap: 8px;
      
      .toolbar-left,
      .toolbar-right {
        justify-content: center;
        flex-wrap: wrap;
      }
    }
    
    .mini-map-container {
      width: 150px;
      height: 120px;
      top: 10px;
      right: 10px;
    }
    
    .count-info {
      bottom: 10px;
      left: 10px;
      font-size: 11px;
    }
  }
}

// 滚动条样式
:deep(.customScrollbar) {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: linear-gradient(45deg, rgba(64, 158, 255, 0.3), rgba(64, 158, 255, 0.6));
    cursor: pointer;
    
    &:hover {
      background: linear-gradient(45deg, rgba(64, 158, 255, 0.5), rgba(64, 158, 255, 0.8));
    }
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

// Element Plus 组件样式覆盖
:deep(.el-button--small) {
  padding: 6px 12px;
  font-size: 12px;
  height: auto;
}

:deep(.el-dropdown-menu) {
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e5e5e5;
  padding: 6px 0;
}

:deep(.el-dropdown-menu__item) {
  padding: 8px 16px;
  font-size: 13px;
  
  &:hover {
    background: linear-gradient(90deg, #f0f9ff, #e0f2fe);
    color: #1976d2;
  }
}

// 图标选择网格样式
.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;

  .icon-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 8px;
    border: 2px solid transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);

    &:hover {
      background: linear-gradient(135deg, #e3f2fd, #bbdefb);
      border-color: #42a5f5;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(66, 165, 245, 0.3);
    }

    &.selected {
      background: linear-gradient(135deg, #1976d2, #42a5f5);
      border-color: #1976d2;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(25, 118, 210, 0.4);

      .icon-name {
        color: white;
      }
    }

    .icon-name {
      margin-top: 6px;
      font-size: 12px;
      color: #666;
      text-align: center;
      transition: color 0.3s ease;
    }

    .is-dark & {
      background: linear-gradient(135deg, #424242, #616161);
      color: #fff;

      &:hover {
        background: linear-gradient(135deg, #1976d2, #42a5f5);
        border-color: #42a5f5;
      }

      .icon-name {
        color: #ccc;
      }
    }
  }
}

// 对话框样式优化
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);

  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    border-bottom: none;

    .el-dialog__title {
      color: white;
      font-weight: 600;
      font-size: 16px;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 18px;

        &:hover {
          color: #f0f0f0;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
    background: #fafafa;

    .is-dark & {
      background: #2b2b2b;
      color: #fff;
    }
  }

  .el-dialog__footer {
    padding: 16px 24px;
    background: #fafafa;
    border-top: 1px solid #f0f0f0;

    .is-dark & {
      background: #2b2b2b;
      border-top-color: #444;
    }
  }
}

// 表单样式优化
:deep(.el-form-item) {
  margin-bottom: 20px;

  .el-form-item__label {
    font-weight: 600;
    color: #333;
    font-size: 14px;

    .is-dark & {
      color: #fff;
    }
  }

  .el-input__wrapper {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.is-focus {
      box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
    }
  }

  .el-textarea__inner {
    border-radius: 8px;
    resize: vertical;
    min-height: 100px;
    line-height: 1.6;
  }
}

// 按钮样式优化
:deep(.el-button) {
  border-radius: 6px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
  }

  &.el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }
  }
}

// 工具栏按钮分组样式优化
.toolbar {
  :deep(.el-button-group) {
    .el-button {
      position: relative;
      
      &:first-child {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
      }
      
      &:last-child {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
      }
      
      &:hover {
        z-index: 1;
        border-color: #409eff;
        color: #409eff;
      }
      
      &:active {
        background: linear-gradient(135deg, #409eff, #66b1ff);
        color: white;
        border-color: #409eff;
      }
    }
  }
  

}



// 右键菜单项图标
.context-menu {
  .menu-item {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    
    .el-icon {
      font-size: 14px;
      color: #666;
      transition: color 0.2s ease;
    }
    
    &:hover {
      .el-icon {
        color: #1976d2;
      }
    }
    
    .is-dark & {
      .el-icon {
        color: #ccc;
      }
      
      &:hover {
        .el-icon {
          color: #66b1ff;
        }
      }
    }
  }
}

// 加载动画
.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
}

// 节点功能状态指示器
.node-features {
  position: absolute;
  top: -8px;
  right: -8px;
  display: flex;
  gap: 2px;
  
  .feature-indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #409eff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    
    &.has-image {
      background: #67c23a;
    }
    
    &.has-link {
      background: #e6a23c;
    }
    
    &.has-note {
      background: #f56c6c;
    }
    
    &.has-icon {
      background: #909399;
    }
  }
}
</style> 