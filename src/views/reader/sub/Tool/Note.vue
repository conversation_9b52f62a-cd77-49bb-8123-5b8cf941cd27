<!-- 笔记 -->
<style lang="less" scoped>
.Note {
  width: 310px;
  height: 100%;
  overflow: hidden;
  position: relative;

  .header {
    margin-top: 20px;
  }

  .footer {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    background-color: #000;
    bottom: 0;
    .el-button {
      width: 100%;
      height: 100%;
      border-radius: 0;
    }
  }

  .content {
    height: calc(100% - 104px);
    overflow-y: auto;
    padding-bottom: 50px;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
    .content-nodelist {
      margin-top: 12px;
      padding: 0 5px 5px;
      color: #999;
      cursor: pointer;
      > p {
        font-size: 16px;
        margin-top: 0;
        margin-bottom: 5px;
        color: #333;
      }
      .content-nodelist-item {
        margin-top: 10px;
        // border: 1px solid #000;
        border-radius: 5px;
        padding: 8px;
        -moz-box-shadow: 0px 0px 4px 0px rgba(193, 193, 193, 0.5);
        -webkit-box-shadow: 0px 0px 4px 0px rgba(193, 193, 193, 0.5);
        box-shadow: 0px 0px 4px 0px rgba(193, 193, 193, 0.5);
        &::first-child {
          margin-top: 0;
        }
        header {
          margin-bottom: 5px;
          font-size: 14px;
          display: flex;

          align-items: center;
          > img {
            cursor: pointer;
            float: right;
            margin-top: 4px;
          }
          text-indent: 1.5em;
          background: url('@/assets/images/sidebar/noteTitle.png') no-repeat left / contain;
        }
        .quoted-text {
          background-color: rgb(245, 249, 252);
          display: -webkit-box;
          -webkit-line-clamp: 2; /* 限制行数 */
          -webkit-box-orient: vertical;
          overflow: hidden;
          padding: 4px;
          max-height: 45px;
        }
        .quoted-text-down {
          border-bottom: 1px solid #e4dede;
          margin-bottom: 5px;
        }
        .quoted-text,
        .node-content {
          font-size: 12px;
          line-height: 20px;
          margin-bottom: 5px;
          border-radius: 6px;
          .node-upload-list {
            margin: 5px 0;
          }
          .node-content-font {
            // width: 266px;
            background-color: rgb(245, 249, 252);
            padding: 4px;
            max-height: 45px;
            display: -webkit-box;
            -webkit-line-clamp: 2; /* 限制行数 */
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }
        .content-footer {
          // margin-top: 12px;
          display: flex;
          flex-direction: row;
          font-size: 12px;
          justify-content: center;
          align-items: center;
        }
        .content-voice {
          flex: 1;
        }
        .content-voice-item {
          display: flex;
          flex-direction: column;
        }
      }
    }
  }
}
</style>
<style lang="less">
.el-popper {
  max-width: 500px;
}
</style>

<template>
  <div class="Note">
    <nav class="Note-tabBar">
      <!-- 我的笔记 -->
      <el-button
        type="primary" round
        color="#0966b4">我的笔记</el-button>
      <!-- <el-button round>班级笔记</el-button>
      <el-button round>教师笔记</el-button> -->
    </nav>
    <header class="header">
      <el-select
        v-model="selectedChapter"
        style="width: 143px"
        :clearable="true"
        :popper-class="store.theme === 'dark' ? '__darkSelect__' : ''"
        @change="chapterChange"
        placeholder="全部章节">
        <el-option
          v-for="item in optionsComputed"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </el-select>&nbsp;
      <el-select
        v-model="sortVal"
        style="width: 105px"
        :popper-class="store.theme === 'dark' ? '__darkSelect__' : ''"
        @change="sortChange">
        <el-option
          v-for="item in sortOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </el-select>
    </header>

    <div class="content">
      <el-empty
        :image-size="100"
        v-if="noteListByChapter.length <= 0" />
      <div
        v-for="ele in noteListByChapter"
        :key="ele.noteId"
        class="content-nodelist">
        <p
          v-if="ele.noteList.length > 0">
          {{ ele.chapterName }}
        </p>
        <section
          v-for="(eleItem, index) in ele.noteList"
          :key="index"
          class="content-nodelist-item"
          @click="goToChapter(eleItem)">
          <!-- <header class="__ellipsis__">
            {{ ele.chapterName }}
          </header> -->

          <!-- <img
            width="16"
            src="@/assets/images/sidebar/navTo.png"
            title="去往笔记所在位置"
          /> -->

          <div style="
              border-radius: 10px;
              padding: 4px;
            ">
            <!-- 选中内容 -->
            <div
              class="quoted-text">
              <el-tooltip
                :content="eleItem.bookContent">
                {{ eleItem.bookContent }}
              </el-tooltip>
            </div>
            <div
              class="quoted-text-down">
            </div>
            <!-- 笔记内容 -->
            <div
              class="node-content">
              <div
                class="node-content-font">
                <el-tooltip
                  :content="eleItem.nodeContent"
                  :style="{ width: '200px' }">
                  {{
                    eleItem.nodeContent?.length > 40
                      ? eleItem.nodeContent.slice(0, 40) + "..."
                      : eleItem.nodeContent
                  }}
                </el-tooltip>
              </div>
              <div
                class="node-upload-list"
                v-if="eleItem.attachments.length > 0">
                <note-upload-list
                  :attachmentList="eleItem.attachments"
                  :support-remove="false"
                  :note-item="eleItem"
                  @remove="toRemoveAttachmentItem"></note-upload-list>
              </div>
            </div>
          </div>

          <div
            class="content-footer">
            <span
              @click.stop>{{ eleItem.createTime }}</span>
            <el-button link
              :icon="Edit"
              title="修改笔记"
              @click.stop="editNote(eleItem)" />
            <span
              style="flex: 1"
              :page-number="eleItem.pageNumber"></span>
            <el-popconfirm
              icon-color="#626AEF"
              title="确定移除该笔记吗？"
              @confirm="confirmRemoveNote(eleItem)">
              <template
                #reference>
                <el-button
                  title="移除笔记"
                  link
                  :icon="Delete"
                  @click.stop />
              </template>
            </el-popconfirm>
          </div>
        </section>
      </div>
    </div>
    <div class="footer">
      <el-button title="导出"
        :icon="Edit" round
        @click="exportNoteList"
        v-loading="exportInProcess">导出</el-button>
    </div>
  </div>
</template>

<script setup>
import NoteUploadList from './noteUploadList'
import { Delete, Edit } from '@element-plus/icons-vue'
import { ref, onMounted, defineProps, toRaw } from 'vue'
import { getBookNote, deleteBookNote } from '@/api/book/reader'
import { ElMessage } from 'element-plus'
import { getChapters, updateBookNote, exportNotes } from '@/api/book/reader'
import useReader from '@/store/modules/reader'
import usePreview from '@/store/modules/preview.js'
import { getToken } from '@/utils/auth'
import { highlightKeyWordSmooth } from '@/utils/reader'
// import ExportImg from "@/assets/icons/svg/export.svg";
import { generateTextIdWithFromId } from '@/views/reader/sub/Pages/ParagraphTool'
import { blobValidate } from '@/utils/dutp'

// const props = defineProps({ editNote: Function })
const emits = defineEmits(['edit'])

const previewStore = usePreview()
const store = useReader()
const exportInProcess = ref(false)
const selectedChapter = ref('')
const chapterList = ref([])
const sortVal = ref(1)
const noteListByChapter = computed(() => {
  const noteDataListByChapter = []
  store.comprehensiveChapterAndCatalogData.chaptersData.reduce((chapterArray, chapterDataItem) => {
    let chapterCatalogDataObj = chapterArray.find(chapterItem => chapterItem.chapterId === chapterDataItem.chapterId)
    if (!chapterCatalogDataObj) {
      chapterCatalogDataObj = {
        chapterId: chapterDataItem.chapterId,
        chapterName: chapterDataItem.chapterName,
        noteList: []
      }
      chapterArray.push(chapterCatalogDataObj)
    }
    const noteList = store.noteList.filter(noteItem => noteItem.chapterId === chapterCatalogDataObj.chapterId)
    noteList.sort((a, b) => {
      return a.pageNumber - b.pageNumber
    })
    chapterCatalogDataObj.noteList = toRaw(noteList)
    return chapterArray
  }, noteDataListByChapter)
  // const result = noteDataListByChapter.filter(chapterCatalogData => {
  //   if (chapterCatalogData.noteList.length <= 0) {
  //     return false
  //   }
  //   return true
  //   // return selectedChapter ? chapterCatalogData.chapterId === selectedChapter : true
  // })

  noteDataListByChapter.forEach(chapterNodeDataItem => {
    chapterNodeDataItem.noteList.sort((a, b) => {
      return a.createTime > b.createTime
    })
    chapterNodeDataItem.noteList.forEach(nodeDataItem => {
      nodeDataItem.attachments.sort((a, b) => {
        return a.createTime > b.createTime
      })
    })
  })

  return noteDataListByChapter.filter(noteData => {
    return noteData.noteList.length > 0
  })
  // return noteList.value.map((item) => {
  //   return {
  //     ...item,
  //     attachments: item.attachments.map((ele) => {
  //       return {
  //         ...ele,
  //         attachmentUrl: ele.attachmentUrl.replace('http://', 'https://'),
  //       }
  //     }),
  //   }
  // })
})
const optionsComputed = computed(() => {
  const catalogList = store.comprehensiveChapterAndCatalogData.chaptersData.map(chapterItem => {
    return {
      ...chapterItem,
      value: chapterItem.chapterId,
      label: chapterItem.chapterName
    }
  })
  catalogList.unshift({
    value: '',
    label: '全部章节'
  })
  return catalogList
})
function exportNoteList() {
  exportInProcess.value = true
  exportNotes({
    bookId: store.comprehensiveBookData.bookId,
    chapterId: selectedChapter.value
  })
    .then(res => {
      let filename = res?.headers['content-disposition']?.split('filename=')[1]
      if (filename) {
        filename = decodeURIComponent(filename)
      } else {
        filename = new Date().getTime() + '.zip'
      }
      let data = res.data
      const isBlob = blobValidate(data)
      if (isBlob) {
        const blob = new Blob([data], { type: 'text/plain;charset=utf-8' })
        saveAs(blob, filename)
      } else {
      }
    })
    .finally(() => {
      exportInProcess.value = false
    })
}
const sortOptions = [
  {
    label: '章节排序',
    value: 1
  },
  {
    label: '时间排序',
    value: 2
  }
]
const getChapterList = async () => {
  const res = await getChapters(store.comprehensiveBookData.bookId)
  if (res.code === 200) {
    chapterList.value = [{ chapterId: 0, chapterName: '全部章节' }, ...res.data]
  }
}
const chapterChange = v => {
  if (!v) selectedChapter.value = v
  getNoteList()
}
const sortChange = () => {
  getNoteList()
}

const getNoteList = async () => {
  if (!getToken()) return
  const data = {
    bookId: store.comprehensiveBookData.bookId,
    sort: sortVal.value,
    chapterId: selectedChapter.value,
    noteType: 1
  }
  if (!selectedChapter.value) delete data.chapterId
  const res = await getBookNote(data)
  if (res.code === 200) {
    store.setNoteList(res.data)
    // noteList.value = res.data
  } else {
    // noteList.value = []
  }
}

const preview = v => {
  previewStore.setData({ url: v.attachmentUrl, title: v.attachmentName })
}
const editNote = noteItem => {
  if (!getToken()) return
  emits('edit', noteItem)
}

function toRemoveAttachmentItem(noteDataItem, item) {
  const filterItems = noteDataItem.attachments.filter(noteItem => noteItem.attachmentId !== item.attachmentId)
  updateBookNote({
    noteId: noteDataItem.noteId,
    attachments: filterItems.map(attachment => {
      return {
        ...attachment,
        createTime: undefined
      }
    })
  }).then(resp => {
    if (resp.code === 200) {
      ElMessage.success('删除附件完毕')
      getNoteList()
    } else {
      ElMessage.error('删除失败:' + resp.msg)
    }
  })
}
function goToChapter(noteItem) {
  // const targetTextNodeId = generateTextId({
  //   paraId: searchItem.textId,
  //   startIndex: keywordIndex,
  //   keywordLength: searcherText.value.length
  // })
  const keyWordIdArr = generateTextIdWithFromId(noteItem.fromWordId, noteItem.endWordId)
  store.jumpToChapter(noteItem.chapterId, noteItem.pageNumber).then(() => {
    highlightKeyWordSmooth(keyWordIdArr)
  })
}
async function confirmRemoveNote(noteItem) {
  if (!getToken()) return
  const res = await deleteBookNote(noteItem.noteId)
  if (res.code === 200) {
    ElMessage.success('操作成功!')
    getNoteList()
    // const res2 = await getBookNote({ bookId: store.comprehensiveBookData.bookId, sort: 1, noteType: 1 })
    // if (res2.code === 200) {
    //   store.setNoteList(res2.data)
    // }
  } else {
    ElMessage.error('删除失败,请稍后重试!')
  }
}
// watch(
//   () => store.noteList,
//   () => {
//     getNoteList()
//   },
//   { deep: true }
// )
onMounted(() => {
  getNoteList()
  getChapterList()
})
</script>
