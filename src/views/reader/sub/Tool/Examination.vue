<!-- 考试 -->
<style lang="scss" scoped>
.Task {
  width: 305px;
  .header {
    margin-bottom: 20px;
  }
  .title {
    font-weight: 400;
    font-size: 14px;
    color: #999;
    line-height: 14px;
    text-align: justify;
    margin-bottom: 14px;
  }
  .content {
    .list {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      width: 100%;
    }
    .content-card {
      width: 140px;
      height: 78px;
      background: var(--pageBackgroundColor);
      box-shadow: var(--noteBoxShadow);
      border-radius: 3px;
      padding: 4px;
      box-sizing: border-box;
      margin-bottom: 5px;
      position: relative;
      .jumpTo {
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        line-height: 24px;
        width: 58px;
        height: 24px;
        background: rgba(0, 0, 0, 0.5);
        position: absolute;
        right: 0;
        top: 0;
        text-align: center;
        border-radius: 0px 3px 0px 5px;
        cursor: pointer;
        display: none;

        &:hover {
          color: #0966b4;
        }
      }
      float: left;
      & > nav {
        display: flex;
        align-items: center;
        height: 58px;

        & > img {
          width: 29px;
          height: 29px;
        }
        .name {
          flex: 1;
          font-size: 12px;
          color: #999;
        }
      }
      & > footer {
        position: relative;
        .footer-btn {
          position: absolute;
          right: 0;
          bottom: -15px;
          border-radius: 9px;
          border: 1px solid #0966b4;
          font-weight: 400;
          font-size: 12px;
          color: #0966b4;
          cursor: pointer;
          transition: 0.3s;
          &:hover {
            background-color: #0966b4;
            color: #fff;
          }
        }
      }
    }
  }
  .content-card:hover .jumpTo {
    display: block;
  }
}
</style>

<template>
  <div class="Task">
    <header class="header">
      <el-select
        v-model="chapter"
        style="width: 143px"
        :popper-class="store.theme === 'dark' ? '__darkSelect__' : ''"
        @change="change"
        :clearable="true">
        <el-option
          v-for="item in chaptersOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </el-select>
    </header>
    <main class="content">
      <div
        v-for="ele in taskList"
        :key="ele.chapterId">
        <section
          class="title __ellipsis__">
          <el-tooltip
            :content="ele.chapterName">
            {{ ele.chapterName }}
          </el-tooltip>
        </section>

        <div class="list">
          <section
            class="content-card"
            v-for="(item, i) in ele.dtbBookTestPaperList"
            :key="i">
            <nav>
              <img
                src="@/assets/images/sidebar/taskTitle.png"
                alt="" />
              <div
                class="name __ellipsis__">
                {{ item.paperTitle }}
              </div>
            </nav>
            <footer>
              <div
                class="footer-btn __flex-center__"
                @click="openDialog(item)"
                style="padding: 5px 10px">
                {{ item.state === "1" ? "打开" : "去考试" }}
              </div>
            </footer>
            <div
              class="jumpTo"
              @click.stop="jumpTo(item)">
              跳转</div>
          </section>
        </div>
      </div>
    </main>
  </div>
  <Papers_1 :uuid="uid"
    :paperInfo="paperInfo"
    :chapterId="chapterId" />
</template>

<script setup>
import { ref, onMounted } from 'vue'
import useReader from '@/store/modules/reader'
import { getPaperList, getChapters } from '@/api/book/reader'
import Papers_1 from '../PageModule/QuestionsItem/Papers_1.vue'
import { uuid } from '@/utils/index'
const store = useReader()
const chapter = ref('全部章节')
const uid = ref('1')
const paperInfo = ref({})
const chapterId = ref('')
const chaptersOptions = ref([])

const taskList = ref([])

const change = () => {
  if (!chapter.value) chapter.value = '全部章节'
  getTaskList()
}
// * 跳转

const jumpTo = async v => {
  console.log(v)
  await store.jumpToChapter(v.chapterId, v.pageNumber)
}
//打开作业弹窗
const openDialog = val => {
  console.log(val)
  uid.value = uuid()
  paperInfo.value = {
    paperTitle: val.paperTitle,
    paperId: val.paperId,
    bookPaperId: val.bookPaperId,
    chapterId: val.chapterId,
    state: val.state
  }
  chapterId.value = val.chapterId
}

//获取作业列表
const getTaskList = async () => {
  const params = {
    bookId: store.comprehensiveBookData.bookId,
    paperType: 1,
    chapterId: chapter.value === '全部章节' ? undefined : chapter.value
  }
  const res = await getPaperList(params)
  if (res.code === 200) {
    taskList.value = res.data
  }
}

//获取章节列表
const getChaptersList = async () => {
  const res = await getChapters(store.comprehensiveBookData.bookId)
  const option = [
    {
      label: '全部章节',
      value: '全部章节'
    }
  ]
  if (res.code === 200) {
    res.data.forEach(ele => {
      option.push({ label: ele.chapterName, value: ele.chapterId })
    })
  }
  chaptersOptions.value = option
}
onMounted(() => {
  getTaskList()
  getChaptersList()
})
</script>
