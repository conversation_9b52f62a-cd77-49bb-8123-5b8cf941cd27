<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-12 10:34:47
 * @LastEditTime: 2025-02-18 14:43:09
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Tool\CreateFlashCard.vue
 * @Description: 创建助记卡对话框
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="AI助记卡"
    width="1000px"
    min-width="500px"
    :before-close="handleClose"
    center
  >
    <div class="create-card-form">
      <div class="source-text-section">
        <div class="source-text">{{ getSelectedText() }}</div>
      </div>

      <div class="card-type-section">
        <label class="form-label">卡片类型：</label>
        <div class="card-type-buttons">
          <div 
            class="card-type-button" 
            :class="{ 'active': formData.cardType === 1 }"
            @click="formData.cardType = 1"
          >
            <div class="blue-decorator"></div>
            <div class="button-content">
              <span class="button-text">简答题</span>
              <div class="button-description">适合知识点与语言词条等</div>
            </div>
          </div>
          <div 
            class="card-type-button" 
            :class="{ 'active': formData.cardType === 2 }"
            @click="formData.cardType = 2"
          >
            <div class="blue-decorator"></div>
            <div class="button-content">
              <span class="button-text">选择题</span>
              <div class="button-description">适合知识应用与训练场景等</div>
            </div>
          </div>
          <div 
            class="card-type-button" 
            :class="{ 'active': formData.cardType === 3 }"
            @click="formData.cardType = 3"
          >
            <div class="blue-decorator"></div>
            <div class="button-content">
              <span class="button-text">主题卡片</span>
              <div class="button-description">适合概念介绍与名称解释等</div>
            </div>
          </div>
        </div>
      </div>

    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>

        <el-button 
          type="primary" 
          @click="generateCard" 
          :loading="aiGenerating"
          :disabled="!getSelectedText().trim()"
          style="margin-right: 10px;"
        >
          {{ aiGenerating ? 'AI生成中...' : '生成助记卡' }}
        </el-button>
    
        <div class="ai-tip" v-if="!getSelectedText().trim()">请先选择文本内容</div>
      </span>
    </template>
  </el-dialog>

  <!-- 预览弹窗 -->
  <el-dialog
    v-model="previewVisible"
    title="预览助记卡"
    width="800px"
    min-width="600px"
    :before-close="handlePreviewClose"
    center
  >
    <div class="preview-content" v-if="aiGeneratedData">
      <div class="preview-section">
        <div class="preview-text source-preview">{{ getSelectedText() }}</div>
      </div>


      <div class="preview-section" v-if="aiGeneratedData.question">
        <label class="preview-label">问题：</label>
        <div class="preview-text">{{ aiGeneratedData.question }}</div>
      </div>

      <div class="preview-section" v-if="aiGeneratedData.answer">
        <label class="preview-label">答案：</label>
        <div class="preview-text">{{ aiGeneratedData.answer }}</div>
      </div>

      <div class="preview-section" v-if="formData.cardType === 2 && aiGeneratedData.options">
        <label class="preview-label">选项：</label>
        <div class="options-preview">
          <div 
            v-for="option in getParsedOptions(aiGeneratedData.options)" 
            :key="option.key" 
            class="option-item"
          >
            {{ option.key }}. {{ option.value }}
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handlePreviewClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmAndSave"
          :loading="saving"
        >
          {{ saving ? '保存中...' : '加入' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { MagicStick } from '@element-plus/icons-vue';
import useReader from "@/store/modules/reader";
import { generateFlashCard, addCard } from '@/api/book/card'; // 导入AI生成和保存接口

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  selectedText: {
    type: String,
    default: ''
  },
  chapterId: {
    type: String,
    default: ''
  },
  pageNumber: {
    type: Number,
    default: 1
  },
  bookId: {
    type: String,
    default: ''
  },
  elementList: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:visible', 'success']);

const store = useReader();
const dialogVisible = ref(false);
const previewVisible = ref(false); // 预览弹窗显示状态
const aiGenerating = ref(false); // AI生成loading状态
const saving = ref(false); // 保存loading状态
const aiGeneratedData = ref(null); // 存储AI生成的数据

const formData = reactive({
  cardType: 1 // 1简答题 2选择题 3主题卡片
});

watch(() => props.visible, (val) => {
  dialogVisible.value = val;
  if (val) {
    resetForm();
  }
});

watch(dialogVisible, (val) => {
  emit('update:visible', val);
});

const resetForm = () => {
  formData.cardType = 1;
  aiGeneratedData.value = null;
};

const handleClose = () => {
  dialogVisible.value = false;
};

const handlePreviewClose = () => {
  previewVisible.value = false;
};

// AI生成助记卡（只生成不保存）
const generateCard = async () => {
  const sourceText = getSelectedText();
  if (!sourceText.trim()) {
    ElMessage.warning('请先选择文本内容');
    return;
  }

  aiGenerating.value = true;
  try {
    // 调用AI生成接口
    const generateRes = await generateFlashCard({
      sourceText: sourceText,
      cardType: formData.cardType
    });

    if (generateRes.code !== 200) {
      ElMessage.error(generateRes.msg || 'AI生成失败');
      return;
    }

    // 存储生成的数据并显示预览弹窗
    aiGeneratedData.value = generateRes.data;
    previewVisible.value = true;

  } catch (error) {
    console.error('AI生成失败:', error);
    ElMessage.error('AI生成失败，请稍后重试');
  } finally {
    aiGenerating.value = false;
  }
};

// 确认并保存助记卡
const confirmAndSave = async () => {
  if (!aiGeneratedData.value) {
    ElMessage.error('没有可保存的数据');
    return;
  }

  saving.value = true;
  try {
    // 构建保存数据
    const cardData = {
      bookId: props.bookId,
      chapterId: props.chapterId,
      pageNumber: props.pageNumber,
      sourceText: getSelectedText(),
      cardType: formData.cardType,
      question: aiGeneratedData.value.question || null,
      answer: aiGeneratedData.value.answer || null,
      options: formData.cardType === 2 ? aiGeneratedData.value.options : null // 选择题才有options
    };

    const saveRes = await addCard(cardData);
    
    if (saveRes.code === 200) {
      ElMessage.success('助记卡保存成功！');
      emit('success'); // 通知父组件刷新列表
      handlePreviewClose();
      handleClose();
    } else {
      ElMessage.error(saveRes.msg || '保存失败');
    }

  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败，请稍后重试');
  } finally {
    saving.value = false;
  }
};

// 从elementList提取选中文本，参考AddNote的实现
const getSelectedText = () => {
  if (!props.elementList || props.elementList.length === 0) {
    return props.selectedText || ''; // 兜底使用selectedText
  }
  
  let text = '';
  props.elementList
    .sort((a, b) => a.id.split('_')[1] - b.id.split('_')[1])
    .forEach(element => {
      text += element.innerText;
    });
  return text;
};

// 解析JSON字符串为数组，并添加key属性
const getParsedOptions = (optionsJson) => {
  if (!optionsJson) {
    return [];
  }
  try {
    // API返回的是数组格式的JSON字符串，如：[{value:"", key:"A"}, ...]
    const options = JSON.parse(optionsJson);
    return Array.isArray(options) ? options : [];
  } catch (e) {
    console.error('解析选项失败:', e);
    return [];
  }
};
</script>

<style lang="scss" scoped>
.create-card-form {
  .source-text-section {
    margin-bottom: 20px;
    
    .source-text {
      background: var(--cardBackgroundColor);
      padding: 12px;
      border-radius: 6px;
      border: 1px solid var(--borderColor);
      color: var(--fontColor);
      line-height: 1.6;
      max-height: 120px;
      overflow-y: auto;
    }
  }

  .card-type-section {
    margin-bottom: 20px;
    
    .card-type-buttons {
      display: flex;
      flex-direction: row;
      gap: 16px;
      justify-content: center;
    }
    
    .card-type-button {
      position: relative;
      width: 300px;
      height: 80px;
      background: #F7F9FB;
      border-radius: 12px;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s ease;
      overflow: hidden;
      
      .blue-decorator {
        width: 20px;
        height: 100%;
        background: #18D7BF;
        border-radius: 12px 0 0 12px;
      }
      
      .button-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        padding: 12px 16px;
      }
      
      .button-text {
        color: var(--fontColor);
        font-size: 14px;
        font-weight: 400;
        margin-bottom: 4px;
        transition: all 0.2s ease;
        text-align: left;
      }
      
      .button-description {
        font-size: 12px;
        color: var(--fontColor);
        opacity: 0.7;
        text-align: left;
        line-height: 1.3;
        transition: all 0.2s ease;
      }
      
      &:hover {
        background: #EDF5F4;
        
        .button-text {
          color: #18D7BF;
        }
        
        .button-description {
          opacity: 0.8;
        }
      }
      
      &.active {
        background: #EDF5F4;
        
        .button-text {
          font-weight: 500;
          color: #18D7BF;
        }
        
        .button-description {
          opacity: 0.8;
          color: #18D7BF;
        }
      }
    }
  }

  .ai-generate-section {
    margin-bottom: 20px;
    text-align: center;

    .ai-tip {
      display: block;
      margin-top: 10px;
      color: var(--fontColor);
      font-size: 0.9em;
      opacity: 0.7;
    }
    
    .ai-description {
      margin-top: 10px;
      font-size: 0.85em;
      color: var(--fontColor);
      opacity: 0.6;
      line-height: 1.4;
    }
  }

  .form-item {
    margin-bottom: 20px;
  }

  .form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--fontColor);
  }

  .options-container {
    .option-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      
      .option-label {
        width: 24px;
        font-weight: 500;
        color: var(--fontColor);
      }
    }
  }
}

.preview-content {
  .preview-section {
    margin-bottom: 20px;
    
    .preview-label {
      display: block;
      font-weight: 500;
      color: var(--fontColor);
      margin-bottom: 8px;
    }
    
    .preview-text {
      background: var(--cardBackgroundColor);
      padding: 12px;
      border-radius: 6px;
      border: 1px solid var(--borderColor);
      color: var(--fontColor);
      line-height: 1.6;
      
      &.source-preview {
        max-height: 100px;
        overflow-y: auto;
        opacity: 0.8;
      }
    }
    
    .options-preview {
      background: var(--cardBackgroundColor);
      padding: 12px;
      border-radius: 6px;
      border: 1px solid var(--borderColor);
      
      .option-item {
        padding: 8px 0;
        color: var(--fontColor);
        line-height: 1.5;
        
        &:not(:last-child) {
          border-bottom: 1px solid var(--borderColor);
        }
      }
    }
  }
}
</style>