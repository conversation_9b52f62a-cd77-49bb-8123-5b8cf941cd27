<template>
    <div>
      <!-- 搜索表单 -->
      <el-form :model="searchForm" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item prop="catalogName">
          <el-input
            v-model="searchForm.catalogName"
            placeholder="请输入目录名称"
            clearable
            @keyup.enter="handleSearch"
            @input="syncSearchNames"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
        </el-form-item>
      </el-form>

      <!-- 目录树标题带悬浮菜单 -->
      <div class="folder-header">
        <strong>文件夹</strong>
        <el-popover
          placement="bottom"
          trigger="hover"
          popper-class="custom-folder-popover"
        >
          <template #reference>
            <div class="folder-more">
              <el-icon class="folder-icon"><More /></el-icon>
            </div>
          </template>
          <div class="popover-buttons compact-buttons">
            <el-button link type="primary" @click="handleAddRootCatalog">新增子目录</el-button>
          </div>
        </el-popover>
      </div>

      <el-tree
        style="max-width: 600px"
        :allow-drop="allowDrop"
        :allow-drag="allowDrag"
        :data="processedCatalogs"
        :props="{
          children: 'children',
          label: item => item.catalogName || item.folderName
        }"
        draggable
        default-expand-all
        node-key="catalogId"
        @node-drag-start="handleDragStart"
        @node-drag-enter="handleDragEnter"
        @node-drag-leave="handleDragLeave"
        @node-drag-over="handleDragOver"
        @node-drag-end="handleDragEnd"
        highlight-current
        :expand-on-click-node="false"
        :check-on-click-node="true"
        @node-drop="handleDrop"
      >
        <template #default="{ node, data }">
          <span @click="$emit('click-catalog', data)" class="custom-tree-node">
            <span>{{ node.label }}</span>
            <span>
              <div class="card-actions">
                <el-popover
                  popper-class="custom-folder-popover"
                  placement="bottom"
                  trigger="hover"
                  v-model:visible="data.popoverVisible"
                >
                  <template #reference>
                    <el-icon slot="reference" @click="data.popoverVisible=!data.popoverVisible"><More /></el-icon>
                  </template>
                  <div>
                    <el-button link type="primary" style="margin-left: 10px" @click="handleAddCatalog(data)">新建子目录</el-button>
                    <el-button v-if="!data.defaultType || data.defaultType !== '1'" link type="primary" @click="handleDeleteCatalog(data)">删除</el-button>
                    <el-button link type="primary" @click="handleUpdateCatalog(data)">重命名</el-button>
                    <el-button link type="primary" @click="handleMoveCatalog(data)">移动</el-button>
                  </div>
                </el-popover>
              </div>
            </span>
          </span>
        </template>
      </el-tree>

      <!-- 添加目录对话框 -->
      <el-dialog :title="catalogTitle" v-model="catalogOpen" width="500px" append-to-body>
        <el-form ref="catalogRef" :model="catalogForm" :rules="rules" label-width="80px">
          <el-input label="目录名称：" placeholder="请输入目录名称" v-model="catalogForm.catalogName"></el-input>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="catalogSubmitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 移动目录对话框 -->
      <el-dialog :title="catalogTitle" v-model="catalogMoveOpen">
        <el-form ref="catalogRef" :model="catalogForm" :rules="rules" label-width="80px">
          <el-form-item label="目标位置">
            <el-cascader v-model="catalogForm.moveParentIds"
              :options="processedCatalogsForMove"
              @change="changeCatalogParentId"
              :props="{
                expandTrigger: 'hover',
                value: 'catalogId',
                label: 'catalogName',
                children: 'children',
                checkStrictly: true,
                emitPath: true,
                multiple: false,
                disabled: 'disabled'
              }">
            </el-cascader>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="catalogSubmitForm">确 定</el-button>
            <el-button @click="() => {
              cancel();
              $emit('refresh-catalogs');
            }">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </template>

  <script setup>
  import { ref, computed, nextTick, getCurrentInstance } from 'vue'
  import { More } from '@element-plus/icons-vue'
import { de } from 'element-plus/es/locales.mjs'

  const { proxy } = getCurrentInstance()

  // Props定义
  const props = defineProps({
    catalogs: {
      type: Array,
      required: true
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    allowDrop: {
      type: Function,
      default: null
    },
    allowDrag: {
      type: Function,
      default: null
    }
  })

  // Emits定义
  const emit = defineEmits([
    'search',
    'click-catalog',
    'add-catalog',
    'delete-catalog',
    'update-catalog',
    'move-catalog',
    'node-drop',
    'node-drag-start',
    'node-drag-enter',
    'node-drag-leave',
    'node-drag-over',
    'node-drag-end',
    'refresh-catalogs'
  ])

  // 内部状态
  const searchForm = ref({
    catalogName: null,
    folderName: null
  })

  // 处理数据转换
  const processedCatalogs = computed(() => {
    if (!props.catalogs) return []

    const processCatalog = (item) => {
      const processed = {
        catalogId: item.catalogId || item.folderId,
        catalogName: item.catalogName || item.folderName,
        defaultType: item.defaultType,
        children: item.children ? item.children.map(processCatalog) : []
      }
      return processed
    }

    return props.catalogs.map(processCatalog)
  })

  // 为移动操作处理目录树，禁用根目录和当前目录
  const processedCatalogsForMove = computed(() => {
    if (!props.catalogs) return []

    const currentCatalogId = catalogForm.value.catalogId

    const processCatalog = (item) => {
      const processed = {
        catalogId: item.catalogId || item.folderId,
        catalogName: item.catalogName || item.folderName,
        children: item.children ? item.children.map(processCatalog) : [],
        // 禁用根目录和当前目录
        disabled: item.catalogId === currentCatalogId || item.folderId === currentCatalogId
      }

      // 递归检查子节点，如果当前节点包含目标节点，也禁用
      const containsTarget = (node) => {
        if (node.catalogId === currentCatalogId || node.folderId === currentCatalogId) {
          return true
        }
        if (node.children && node.children.length > 0) {
          return node.children.some(containsTarget)
        }
        return false
      }

      // 如果当前节点的子节点中包含目标节点，也禁用当前节点
      if (processed.children && processed.children.length > 0 && processed.children.some(containsTarget)) {
        processed.disabled = true
      }

      return processed
    }

    return props.catalogs.map(processCatalog)
  })

  // 拖拽相关方法
  const allowDrop = (draggingNode, dropNode, type) => {
    return props.allowDrop ? props.allowDrop(draggingNode, dropNode, type) : true
  }

  const allowDrag = (draggingNode) => {
    return props.allowDrag ? props.allowDrag(draggingNode) : true
  }

  const handleDragStart = (node, ev) => {
    emit('node-drag-start', { node, ev })
  }

  const handleDragEnter = (draggingNode, dropNode, ev) => {
    emit('node-drag-enter', { draggingNode, dropNode, ev })
  }

  const handleDragLeave = (draggingNode, dropNode, ev) => {
    emit('node-drag-leave', { draggingNode, dropNode, ev })
  }

  const handleDragOver = (draggingNode, dropNode, ev) => {
    emit('node-drag-over', { draggingNode, dropNode, ev })
  }

  const handleDragEnd = (draggingNode, dropNode, dropType, ev) => {
    emit('node-drag-end', { draggingNode, dropNode, dropType, ev })
  }
  const handleDrop = (draggingNode, dropNode, dropType, ev) => {
    // 获取完整的父级路径
    const getParentPath = (node) => {

      const path = [];
      let current = node.parent;
      while (current && current.data) {
        if (current.data.catalogId || current.data.folderId) {
          path.unshift(current.data.catalogId || current.data.folderId);
        }
        current = current.parent;
      }
      return path; // 移除最后一个push，因为我们只需要父级路径
    };

    const moveData = {
      catalogId: draggingNode.data.catalogId || draggingNode.data.folderId,
      folderId: draggingNode.data.folderId || draggingNode.data.catalogId,
      catalogName: draggingNode.data.catalogName || draggingNode.data.folderName,
      folderName: draggingNode.data.folderName || draggingNode.data.catalogName,
      parentId: dropNode.data.catalogId || dropNode.data.folderId || 0,
      parentFolderId: dropNode.data.folderId || dropNode.data.catalogId || 0,
      moveParentIds: getParentPath(dropNode)
    }

    // 检查是否试图将节点移动到自己
    if (moveData.catalogId === moveData.parentId) {
      proxy.$modal.msgError("不能把目录移动到自己下面");
      return;
    }

    // 设置表单数据并打开移动弹窗
    catalogForm.value = { ...moveData };
    // 确保moveParentIds包含目标位置的ID
    catalogForm.value.moveParentIds.push(moveData.parentId);
    catalogTitle.value = "移动目录";
    catalogMoveOpen.value = true;

    // 阻止默认的拖拽行为
    ev.preventDefault();
    ev.stopPropagation();
  }

  // 新增状态变量
  const catalogOpen = ref(false);
  const catalogMoveOpen = ref(false);
  const catalogTitle = ref("");
  const catalogForm = ref({});

  // 取消按钮
  function cancel() {
    catalogOpen.value = false;
    catalogMoveOpen.value = false;
    catalogForm.value = {};
    // 刷新目录列表，恢复原始状态
    emit('refresh-catalogs');
  }

  // 处理目录移动
  function changeCatalogParentId() {

    console.log(catalogForm.value.moveParentIds)
    catalogForm.value.parentId = catalogForm.value.moveParentIds[catalogForm.value.moveParentIds.length-1];
  }

  // 添加规则验证
  const rules = {
    folderName: [
      { required: true, message: "目录名称不能为空", trigger: "blur" },
      { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" }
    ],
    catalogName: [
      { required: true, message: "目录名称不能为空", trigger: "blur" },
      { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" }
    ]
  }

  // 添加处理新增目录的方法
  const handleAddCatalog = (data) => {
    catalogForm.value = {
      parentId: data.catalogId || data.folderId || 0,
      catalogName: '',
      folderName: ''
    }
    catalogTitle.value = "新增目录"
    catalogOpen.value = true
  }

  // 添加处理根目录新增的方法
  const handleAddRootCatalog = () => {
    catalogForm.value = {
      parentId: 0, // 确保 parentId 为 0
      catalogName: '',
      folderName: ''
    }
    catalogTitle.value = "新增根目录"
    catalogOpen.value = true
  }

  // 添加处理重命名的方法
  const handleUpdateCatalog = (data) => {
    catalogForm.value = {
      catalogId: data.catalogId || data.folderId,
      folderId: data.folderId || data.catalogId,
      catalogName: data.catalogName || data.folderName,
      folderName: data.folderName || data.catalogName
    }
    catalogTitle.value = "重命名目录"
    catalogOpen.value = true
  }

  // 添加处理移动的方法
  const handleMoveCatalog = (data) => {
    catalogForm.value = {
      catalogId: data.catalogId || data.folderId,
      folderId: data.folderId || data.catalogId,
      catalogName: data.catalogName || data.folderName,
      folderName: data.folderName || data.catalogName,
      moveParentIds: [],
      parentId: null
    }
    catalogTitle.value = "移动目录"
    catalogMoveOpen.value = true
  }

  // 修改处理删除的方法
  const handleDeleteCatalog = (data) => {
    proxy.$modal.confirm('是否确认删除该目录？').then(() => {
      emit('delete-catalog', {
        catalogId: data.catalogId || data.folderId,
        folderId: data.folderId || data.catalogId
      })
    }).catch(() => {})
  }

  // 修改模板部分的点击事件
  const template = `
    <span @click="$emit('click-catalog', data)" class="custom-tree-node">
      <span>{{ node.label }}</span>
      <span>
        <div class="card-actions">
          <el-popover
            :width="60"
            placement="bottom"
            trigger="hover"
            v-model:visible="data.popoverVisible"
          >
            <template #reference>
              <el-icon slot="reference" @click="data.popoverVisible=!data.popoverVisible"><More /></el-icon>
            </template>
            <div class="popover-buttons">
              <el-button link type="primary" @click="handleAddCatalog(data)">新建子目录</el-button>
              <el-button link type="primary" @click="handleDeleteCatalog(data)">删除</el-button>
              <el-button link type="primary" style="margin-left: 11px" @click="handleUpdateCatalog(data)">重命名</el-button>
              <el-button link type="primary" @click="handleMoveCatalog(data)">移动</el-button>
            </div>
          </el-popover>
        </div>
      </span>
    </span>
  `

  // 修改提交表单方法
  const catalogSubmitForm = () => {
    if (!proxy.$refs.catalogRef) return

    proxy.$refs.catalogRef.validate(valid => {
      if (valid) {
        const formData = { ...catalogForm.value }

        // 确保名称字段同步
        if (formData.catalogName) {
          formData.folderName = formData.catalogName
        }

        // 处理移动操作
        if (catalogMoveOpen.value) {
          if (formData.catalogId === formData.parentId) {
            proxy.$modal.msgError("不能把目录移动到自己下面")
            return
          }
          emit('move-catalog', formData)
        }
        // 处理新增或更新操作
        else {
          if (formData.catalogId != null) {
            emit('update-catalog', formData)
          } else {
            emit('add-catalog', formData)
          }
        }

        // 关闭弹窗
        catalogOpen.value = false
        catalogMoveOpen.value = false
        // 重置表单
        catalogForm.value = {}
      }
    })
  }

  // 添加同步搜索名称的方法
  const syncSearchNames = (value) => {
    searchForm.value.folderName = value
  }

  // 修改搜索处理方法
  const handleSearch = () => {
    emit('search', {
      ...searchForm.value,
      folderName: searchForm.value.catalogName
    })
  }
  </script>

  <style lang="scss" scoped>
  .custom-tree-node {
    display: flex;
    justify-content: space-between;
    height: 48px;
    line-height: 48px;
    margin: 10px 0;
    border-radius: 2px;
    width: 100%;
  }



  .popover-buttons {
    display: flex;
  flex-direction: column;
  align-items: flex-start !important;
  padding: 4px !important;
  }



  .folder-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    height: 32px;
  }

  .folder-more {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 24px;
    height: 24px;
    border-radius: 4px;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  .folder-icon {
    font-size: 18px;
    color: #909399;
  }
  </style>

  <style >
  /* 这里不使用 scoped，确保样式可以应用到 popover */
  .custom-folder-popover {
    max-width: 120px !important;
    min-width: 120px !important;
  }

  /* 添加禁用节点的样式 */
  .el-cascader-node.is-disabled {
    color: #c0c4cc;
    cursor: not-allowed;
  }
  </style>
