<template>
    <div class="question-content">
        <!-- 题目标题 -->
        <div class="question-header" v-if="question.title">
            <span class="question-type-tag">{{ getQuestionTypeLabel(question.questionType) }}</span>
            <span class="question-title">{{ question.title }}</span>
        </div>

        <!-- 在题干上方显示备注 -->
        <div v-if="question.questionRemark" class="content-item remark-section">
            <div class="label">备注：</div>
            <div class="content">{{ safeDecode(question.questionRemark) }}</div>
        </div>

        <!-- 题干 -->
        <div class="content-item">
            <div class="label">题干：</div>
            <div class="content" v-html="formatBlankQuestion(question.questionContent, question.questionType)"></div>
        </div>

        <!-- 选项区域移到外面，始终显示 -->
        <!-- 其他题型的选项区域 -->
        <div class="options-area"
            v-if="shouldShowOptions(question.questionType) && question.options && question.options.length">
            <!-- 单选题 -->
            <template v-if="question.questionType === 1">
                <div v-for="(option, index) in question.options"
                    :key="option.id || index" 
                    class="option-item"
                    :class="{ 
                        'correct-option': showAnswer && option.rightFlag === 1,
                        'selected-option': userSelectedOptions.includes(option.id || index)
                    }">
                    <div class="option-content">
                        <el-radio 
                            v-model="userSelectedRadio" 
                            :label="option.id || index"
                            @change="handleSingleSelect(option.id || index)"
                            :disabled="showAnswer"
                        >
                            <div class="option-text">
                                <span class="option-label">
                                    {{`${String.fromCharCode(65 + index)}. ` }}
                                </span>
                                <span v-html="option.optionContent"></span>
                            </div>
                        </el-radio>
                        <el-icon v-if="showAnswer && option.rightFlag === 1" class="correct-icon">
                            <Check />
                        </el-icon>
                    </div>
                </div>
            </template>

            <!-- 多选题 -->
            <template v-else-if="question.questionType === 2">
                <div v-for="(option, index) in question.options"
                    :key="option.id || index" 
                    class="option-item"
                    :class="{ 
                        'correct-option': showAnswer && option.rightFlag === 1,
                        'selected-option': userSelectedOptions.includes(option.id || index)
                    }">
                    <div class="option-content">
                        <el-checkbox 
                            :model-value="userSelectedOptions.includes(option.id || index)"
                            @change="(val) => handleMultipleSelect(option.id || index, val)"
                            :disabled="showAnswer"
                        >
                            <div class="option-text">
                                <span class="option-label">
                                    {{`${String.fromCharCode(65 + index)}. ` }}
                                </span>
                                <span v-html="option.optionContent"></span>
                            </div>
                        </el-checkbox>
                        <el-icon v-if="showAnswer && option.rightFlag === 1" class="correct-icon">
                            <Check />
                        </el-icon>
                    </div>
                </div>
            </template>

            <!-- 判断题 -->
            <template v-else-if="question.questionType === 7">
                <div v-for="(option, index) in question.options"
                    :key="option.id || index" 
                    class="option-item"
                    :class="{ 
                        'correct-option': showAnswer && option.rightFlag === 1,
                        'selected-option': userSelectedOptions.includes(option.id || index)
                    }">
                    <div class="option-content">
                        <el-radio 
                            v-model="userSelectedRadio" 
                            :label="option.id || index"
                            @change="handleSingleSelect(option.id || index)"
                            :disabled="showAnswer"
                        >
                            <div class="option-text">
                                <span class="option-label">
                                    {{`${String.fromCharCode(65 + index)}. ` }}
                                </span>
                                <span v-html="option.optionContent"></span>
                            </div>
                        </el-radio>
                        <el-icon v-if="showAnswer && option.rightFlag === 1" class="correct-icon">
                            <Check />
                        </el-icon>
                    </div>
                </div>
            </template>

            <!-- 排序题 -->
            <template v-else-if="question.questionType === 4">
                <div class="sort-container">
                    <draggable 
                        v-model="userSortedOptions" 
                        item-key="id"
                        :disabled="showAnswer"
                        @end="handleSortChange"
                        class="sort-list"
                    >
                        <template #item="{element, index}">
                            <div class="sort-item">
                                <span class="sort-handle">☰</span>
                                <span class="sort-number">{{ index + 1 }}.</span>
                                <span v-html="element.optionContent"></span>
                            </div>
                        </template>
                    </draggable>
                </div>
            </template>
        </div>

        <!-- 填空题交互 -->
        <div v-if="question.questionType === 3" class="blank-answers">
            <div v-for="(blank, index) in extractBlanks(question.questionContent)" :key="index" class="blank-input-item">
                <span>空格 {{ index + 1 }}：</span>
                <el-input 
                    v-model="userBlankAnswers[index]" 
                    placeholder="请输入答案"
                    :disabled="showAnswer"
                />
            </div>
        </div>
        
        <!-- 连线题专用组件 -->
        <div v-if="question.questionType === 5" class="matching-question">
            <MatchingQuestion
                :options="question.options"
                :rightAnswer="question.rightAnswer"
                :showAnswer="showAnswer"
                :userMatches="userMatchingAnswers"
                @update:userMatches="handleMatchChange"
                :disabled="showAnswer"
            />
        </div>

        <!-- 问答题/简答题 -->
        <div v-if="question.questionType === 6" class="qa-container">
            <el-input
                v-model="userQaAnswer"
                type="textarea"
                :rows="4"
                placeholder="请在此输入您的答案..."
                :disabled="showAnswer"
            />
        </div>

        <!-- 修改答案和解析区域，添加 v-show -->
        <template v-if="showAnswer">
            <!-- 编程题相关模板 -->
            <template v-if="question.questionType === 8">
                <div class="content-item">
                    <div class="label">代码：</div>
                    <div class="language-tag">语言：{{ codeContent.language || 'javascript' }}</div>
                    <code-editor
                        v-model="codeContent.code"
                        :language="codeContent.language || 'javascript'"
                        :readOnly="true"
                    />
                </div>
            </template>

            <!-- 排序题答案区域 -->
            <div class="content-item" v-if="question.questionType === 4">
                <div class="label">正确顺序：</div>
                <div class="content">
                    <div v-for="(option, index) in question.options" :key="option.id || index" class="sort-answer-item">
                        <span class="sort-number">{{ index + 1 }}.</span>
                        <span v-html="option.optionContent"></span>
                    </div>
                </div>
            </div>

            <!-- 参考答案区域 -->
            <div class="content-item" v-if="question.rightAnswer && (question.questionType === 6 || question.questionType === 8)">
                <div class="label">参考答案：</div>
                <div class="content" v-html="question.rightAnswer"></div>
            </div>

            <!-- 解析区域 -->
            <div class="content-item" v-if="question.analysis">
                <div class="label">解析：</div>
                <div class="content" v-html="question.analysis"></div>
            </div>
        </template>

        <!-- 提交按钮区域 -->
        <div class="question-actions">
            <el-button 
                type="primary" 
                @click="submitAnswer" 
                v-if="!showAnswer"
            >提交答案</el-button>
            <el-button 
                type="success" 
                disabled 
                v-else
            >已提交</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, watchEffect, computed } from 'vue'
import MatchingQuestion from '@/views/openClass/components/resources/MatchingQuestion'
import { Check } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import draggable from 'vuedraggable'


// 添加组件名称定义
defineOptions({
  name: 'QuestionPreview'
})

// 添加 safeDecode 函数
function safeDecode(content) {
  if (!content) return ''
  try {
    // 检查内容是否已经被编码
    const isEncoded = (str) => {
      try {
        // 尝试解码，如果解码后的结果与原字符串不同，说明是编码过的
        const decoded = decodeURIComponent(str)
        return decoded !== str
      } catch (e) {
        // 如果解码失败，说明可能包含特殊字符，此时返回 false
        return false
      }
    }

    // 如果内容已经被编码，则进行解码
    if (isEncoded(content)) {
      return decodeURIComponent(content)
    }
    
    // 如果内容未被编码或解码失败，直接返回原内容
    return content
  } catch (e) {
    console.error('解码内容失败:', e)
    return content
  }
}

const props = defineProps({
    question: {
        type: Object,
        required: true
    },
    showAnswerButton: {
        type: Boolean,
        default: true
    }
})

// 定义事件
const emit = defineEmits(['answer-submitted'])

// 修改 watch 的实现
const codeContent = ref({})

// 使用 watchEffect 来处理 codeContent
watchEffect(() => {
    if (props.question.codeContent) {
        try {
            codeContent.value = JSON.parse(props.question.codeContent)
        } catch (e) {
            console.error('解析代码内容失败:', e)
            codeContent.value = { code: '', language: 'javascript' }
        }
    }
})

// 添加控制答案显示的状态
const showAnswer = ref(false)

// 添加用户答案相关的响应式变量
const userSelectedRadio = ref('') // 单选/判断题选中值
const userSelectedOptions = ref([]) // 多选题选中值
const userBlankAnswers = ref({})
const userSortedOptions = ref([]) // 排序题选项顺序
const userMatchingAnswers = ref([]) // 新增：用于存储连线题答案
const userQaAnswer = ref('') // 问答题答案

// 题型标签获取函数
const getQuestionTypeLabel = (questionType) => {
    const typeMap = {
        1: '单选题',
        2: '多选题',
        3: '填空题',
        4: '排序题',
        5: '连线题',
        6: '简答题',
        7: '判断题',
        8: '编程题'
    }
    return typeMap[questionType] || '未知题型'
}

// 单选题选择处理
const handleSingleSelect = (optionId) => {
    userSelectedOptions.value = [optionId]
}

// 多选题选择处理
const handleMultipleSelect = (optionId, checked) => {
    if (checked) {
        if (!userSelectedOptions.value.includes(optionId)) {
            userSelectedOptions.value.push(optionId)
        }
    } else {
        userSelectedOptions.value = userSelectedOptions.value.filter(id => id !== optionId)
    }
}

// 排序题变化处理
const handleSortChange = () => {
    console.log('排序变化:', userSortedOptions.value)
    // 可以在这里添加其他逻辑
}

// 连线题匹配变化处理
const handleMatchChange = (matches) => {
    userMatchingAnswers.value = matches
}

// 从题干中提取填空题空格
const extractBlanks = (content) => {
    if (!content) return []
    const matches = content.match(/###(.*?)###/g) || []
    return matches.map(match => match.replace(/###(.*?)###/, '$1'))
}

// 初始化用户排序选项
watchEffect(() => {
    if (props.question.questionType === 4 && props.question.options) {
        userSortedOptions.value = [...props.question.options]
    }
})

// 提交答案
const submitAnswer = () => {
    // 检查是否已经作答
    let hasAnswer = false
    
    switch (Number(props.question.questionType)) {
        case 1: // 单选题
        case 7: // 判断题
            hasAnswer = userSelectedOptions.value.length > 0
            break
        case 2: // 多选题
            hasAnswer = userSelectedOptions.value.length > 0
            break
        case 3: // 填空题
            hasAnswer = userBlankAnswers.value.some(answer => answer && answer.trim() !== '')
            break
        case 4: // 排序题
            hasAnswer = userSortedOptions.value.length > 0
            break
        case 5: // 连线题
            hasAnswer = userMatchingAnswers.value.length > 0
            break
        case 6: // 简答题
            hasAnswer = userQaAnswer.value && userQaAnswer.value.trim() !== ''
            break
    }
    
    if (!hasAnswer && props.question.questionType !== 8) { // 编程题可能不需要用户作答
        ElMessage.warning('请先完成答题再提交')
        return
    }
    
    // 显示答案和解析
    showAnswer.value = true
    
    // 发送答案提交事件
    emit('answer-submitted', {
        questionId: props.question.id,
        questionType: props.question.questionType,
        answer: getUserAnswer()
    })
}

// 获取用户答案
const getUserAnswer = () => {
    switch (Number(props.question.questionType)) {
        case 1: // 单选题
        case 7: // 判断题
            return userSelectedOptions.value[0]
        case 2: // 多选题
            return userSelectedOptions.value
        case 3: // 填空题
            return userBlankAnswers.value
        case 4: // 排序题
            return userSortedOptions.value.map(option => option.id || option.optionContent)
        case 5: // 连线题
            return userMatchingAnswers.value
        case 6: // 简答题
            return userQaAnswer.value
        default:
            return null
    }
}

// 切换答案显示状态的方法 (保留原有功能)
const toggleAnswer = () => {
    showAnswer.value = !showAnswer.value
}

// 添加一个响应式变量来存储格式化后的题干内容
const formattedContent = ref('')

// 监听showAnswer的变化，重新格式化填空题内容
watch(showAnswer, () => {
    if (props.question.questionType === 3) {
        // 强制重新渲染填空题内容
        formattedContent.value = formatBlankQuestion(props.question.questionContent, props.question.questionType)
    }
})

// 修改判断是否显示选项的方法
const shouldShowOptions = (type) => {
  // 增加多选题(2)和排序题(4)
  return [1, 2, 4, 7].includes(type)
}

// 处理填空题格式
const formatBlankQuestion = (content, questionType) => {
    if (questionType !== 3) return content;
    
    if (showAnswer.value) {
        // 提交后，将占位符替换为正确答案，并标记显示
        return content.replace(/###(.*?)###/g, (match, answer) => {
            return `<span class="blank-line"><span class="blank-answer" style="display: block;">${answer}</span></span>`;
        });
    } else {
        // 未提交时，将占位符替换为可交互的输入区域
        let blankIndex = 0;
        return content.replace(/###(.*?)###/g, (match, answer) => {
            const index = blankIndex++;
            // 使用一个特殊标记来标识填空位置，便于后续处理
            return `<span class="blank-line blank-input-placeholder" data-index="${index}"></span>`;
        });
    }
}

// 添加一个计算属性来获取乱序的选项
const shuffledOptions = computed(() => {
    if (!props.question.options || props.question.questionType !== 4) {
        return [];
    }
    
    // 复制选项数组，避免修改原数组
    const options = [...props.question.options];
    
    // Fisher-Yates 洗牌算法打乱数组
    for (let i = options.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [options[i], options[j]] = [options[j], options[i]];
    }
    
    return options;
});

// 当题目变化时重新生成乱序选项
watch(() => props.question, () => {
    // 题目变化时，重新计算 shuffledOptions
    // 由于 shuffledOptions 是计算属性，会自动重新计算

    // 重置用户答案
    userSelectedRadio.value = ''
    userSelectedOptions.value = []
    userBlankAnswers.value = {}
    userQaAnswer.value = ''
    
    // 初始化填空题答案数组
    if (props.question.questionType === 3) {
        const blanks = extractBlanks(props.question.questionContent)
        userBlankAnswers.value = Array(blanks.length).fill('')
    }
    
    // 初始化排序题选项
    if (props.question.questionType === 4 && props.question.options) {
        userSortedOptions.value = [...shuffledOptions.value]
    }
    
    // 重置答案显示状态
    showAnswer.value = false
}, { deep: true });
</script>

<style lang="scss" scoped>
.question-content {
    max-width: 100%;
    overflow-x: hidden;
    position: relative;  // 保持相对定位，作为绝对定位的参考
    padding-bottom: 50px;
    
    .question-header {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .question-type-tag {
        background-color: #409eff;
        color: white;
        padding: 4px 10px;
        border-radius: 4px;
        font-size: 13px;
        margin-right: 12px;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
    }

    .question-title {
        font-weight: 600;
        font-size: 17px;
        color: #303133;
        line-height: 1.4;
    }
    
    .content-item {
        margin-bottom: 15px;
        
        &.remark-section {
            color: #909399; // 备注文字颜色
            font-size: 14px; // 备注文字大小
            .content {
                word-break: break-all; // 备注内容过长时换行
            }
        }

        .label {
            font-weight: bold;
            margin-bottom: 8px;
            color: #606266;
        }
        
        .content {
            color: #303133;
            line-height: 1.6;
            word-break: break-word;
            overflow-wrap: break-word;
        }
    }

    .options-area {
        margin: 10px 0;
        
        .option-item {
            margin-bottom: 8px;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #ebeef5;
            transition: all 0.2s;
            
            &.correct-option {
                background-color: #f0f9eb;
                border-color: #67c23a;
            }
            
            &.selected-option:not(.correct-option) {
                background-color: #ecf5ff;
                border-color: #409eff;
            }
            
            &:hover {
                background-color: #f5f7fa;
                border-color: #dcdfe6;
            }
        }
    }

    .option-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 1;
        
        .option-label {
            margin-right: 8px;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            padding-left: 5px;
        }
        
        .correct-icon {
            margin-left: 8px;
            color: #67c23a;
        }
    }

    .sort-container {
        margin: 15px 0;
        
        .sort-list {
            min-height: 200px;
        }
        
        .sort-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin-bottom: 8px;
            background-color: #f5f7fa;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            cursor: pointer;
            
            .sort-handle {
                margin-right: 10px;
                cursor: move;
                color: #909399;
            }
            
            .sort-number {
                margin-right: 10px;
                font-weight: bold;
            }
        }
    }

    .blank-answers {
        margin: 15px 0;
        
        .blank-input-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            
            span {
                min-width: 80px;
                margin-right: 10px;
            }
            
            .el-input {
                width: 300px;
            }
        }
    }

    .qa-container {
        margin: 15px 0;
        
        .el-textarea {
            width: 100%;
        }
    }

    .question-actions {
        margin-top: 25px;
        display: flex;
        justify-content: center;
    }

    .toggle-answer {
        margin: 15px 0;
        text-align: right;
    }

    // 修改固定定位为绝对定位
    .toggle-answer-fixed {
        position: absolute;  // 改为绝对定位
        bottom: 20px;
        right: 20px;
        background-color: white;
        padding: 8px 15px;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        z-index: 100;
    }
}

.option-text {
    display: inline-flex;
    align-items: center;
    flex-wrap: nowrap;
    width: 100%;
}

:deep(.el-radio), :deep(.el-checkbox) {
    width: 100%;
    display: flex;
    align-items: center;
}

:deep(.el-radio__label), :deep(.el-checkbox__label) {
    white-space: normal;
    display: inline-flex;
    align-items: center;
    padding-left: 5px;
}

:deep(.blank-line) {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    min-width: 100px;
    margin: 0 5px;
    
    .blank-answer {
        color: #409EFF;
        font-size: 14px;
        margin-bottom: 2px;
    }
    
    &::after {
        content: '';
        width: 100%;
        height: 1px;
        background-color: #000;
        display: block;
    }
}

// 为填空题输入占位符添加样式
:deep(.blank-input-placeholder) {
    position: relative;
    display: inline-block;
    min-width: 100px;
    height: 24px;
    border-bottom: 1px solid #000;
    margin: 0 5px;
    vertical-align: middle;
}

.sort-answer-item {
  margin-bottom: 8px;
  padding: 5px 10px;
  background-color: #f0f9eb;
  border-radius: 4px;
  display: flex;
  align-items: center;
  
  .sort-number {
    font-weight: bold;
    margin-right: 10px;
    min-width: 20px;
  }
}

.language-tag {
  margin-bottom: 8px;
  color: #000000;
  font-size: 14px;
  font-style: italic;
}

// 为富文本中的代码块添加黑框样式
:deep(pre) {
  background-color: #1e1e1e;
  color: #d4d4d4;
  border-radius: 4px;
  padding: 16px;
  margin: 12px 0;
  overflow-x: auto;
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  border: 1px solid #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

:deep(.el-radio__inner), :deep(.el-checkbox__inner) {
    vertical-align: middle;
}

:deep(.el-radio__input), :deep(.el-checkbox__input) {
    align-self: center;
}
</style>