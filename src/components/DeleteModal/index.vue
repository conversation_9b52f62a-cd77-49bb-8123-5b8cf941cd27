<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="500"
    :before-close="handleClose"
  >
    <el-input
      v-model="password"
      style="width: 240px"
      type="password"
      placeholder="请输入登录密码"
      show-password
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { checkMatches } from "@/api/system/user";

const props = defineProps({
  modelValue: Boolean,
  title: {
    type: String,
    default: "确认要删除吗？",
  },
});

const { proxy } = getCurrentInstance();
const dialogVisible = ref(false);
const emit = defineEmits(["confirm", "update:modelValue"]);
const password = ref("");

watch(
  () => props.modelValue,
  (val) => {
    if(val){
      password.value = "";
    }
    dialogVisible.value = val;
  }
);
// 关闭
function handleClose() {
  emit("update:modelValue", false);
}

// 确认
async function handleConfirm() {
  try {
    let res = await checkMatches({
      password: password.value,
    });
    if (res.data == true) {
      emit("confirm", (re) => {
        if (re) {
          handleClose();
        }
      });
    } else {
      proxy.$message.error("密码错误");
    }
  } catch (error) {
    console.error(error);
  }
}
</script>

<style scoped lang="scss"></style>
