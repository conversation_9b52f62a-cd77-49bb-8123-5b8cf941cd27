<template>
  <div class="question-content">
      <!-- 题目标题 -->
      <div class="question-header" v-if="question.title">
          <span class="question-type-tag">{{ getQuestionTypeLabel(question.questionType) }}</span>
          <span class="question-title">{{ question.title }}</span>
      </div>

      <!-- 在题干上方显示备注 -->
      <div v-if="question.questionRemark" class="content-item remark-section">
          <div class="label">备注：</div>
          <div class="content">{{ safeDecode(question.questionRemark) }}</div>
      </div>

      <!-- 题干 -->
      <div class="content-item">
          <div class="label">题干：</div>
          <div class="content" v-html="formatBlankQuestion(question.questionContent, question.questionType)"></div>
      </div>

      <!-- 选项区域 -->
      <div class="options-area"
          v-if="shouldShowOptions(question.questionType) && question.options && question.options.length">
          <!-- 单选题 -->
          <template v-if="question.questionType === 1">
              <el-radio-group v-model="userAnswer" @change="emitAnswer">
                  <div v-for="(option, index) in question.options" :key="option.id || index" class="option-item">
                      <el-radio :label="option.id || index">
                          <div class="option-text">
                              <span class="option-label">{{ `${String.fromCharCode(65 + index)}. ` }}</span>
                              <span v-html="option.optionContent"></span>
                          </div>
                      </el-radio>
                  </div>
              </el-radio-group>
          </template>

          <!-- 多选题 -->
          <template v-else-if="question.questionType === 2">
              <el-checkbox-group v-model="userAnswer" @change="emitAnswer">
                  <div v-for="(option, index) in question.options" :key="option.id || index" class="option-item">
                      <el-checkbox :label="option.id || index">
                          <div class="option-text">
                              <span class="option-label">{{ `${String.fromCharCode(65 + index)}. ` }}</span>
                              <span v-html="option.optionContent"></span>
                          </div>
                      </el-checkbox>
                  </div>
              </el-checkbox-group>
          </template>

          <!-- 判断题 -->
          <template v-else-if="question.questionType === 7">
            <el-radio-group v-model="userAnswer" @change="emitAnswer">
                <div v-for="(option, index) in question.options" :key="option.id || index" class="option-item">
                    <el-radio :label="option.id || index">
                        <div class="option-text">
                            <span class="option-label">{{ `${String.fromCharCode(65 + index)}. ` }}</span>
                            <span v-html="option.optionContent"></span>
                        </div>
                    </el-radio>
                </div>
            </el-radio-group>
          </template>

          <!-- 排序题 -->
          <template v-else-if="question.questionType === 4">
              <div class="sort-container">
                  <draggable v-model="userAnswer" item-key="id" @end="emitAnswer" class="sort-list">
                      <template #item="{element, index}">
                          <div class="sort-item">
                              <span class="sort-handle">☰</span>
                              <span class="sort-number">{{ index + 1 }}.</span>
                              <span v-html="element.optionContent"></span>
                          </div>
                      </template>
                  </draggable>
              </div>
          </template>
      </div>

      <!-- 填空题 -->
      <div v-if="question.questionType === 3" class="blank-answers">
          <div v-for="(blank, index) in extractBlanks(question.questionContent)" :key="index" class="blank-input-item">
              <span>空格 {{ index + 1 }}：</span>
              <el-input v-model="userAnswer[index]" placeholder="请输入答案" @change="emitAnswer" />
          </div>
      </div>
      
      <!-- 连线题 -->
      <div v-if="question.questionType === 5" class="matching-question">
          <MatchingQuestion
              :options="question.options"
              :userMatches="userAnswer"
              @update:userMatches="handleMatchChange"
          />
      </div>

      <!-- 问答题 -->
      <div v-if="question.questionType === 6" class="qa-container">
          <el-input
              v-model="userAnswer"
              type="textarea"
              :rows="4"
              placeholder="请在此输入您的答案..."
              @change="emitAnswer"
          />
      </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import draggable from 'vuedraggable';
import MatchingQuestion from '@/views/openClass/components/resources/MatchingQuestion'
import { questionTypeOptions } from '@/utils/optionUtil.js';

defineOptions({
  name: 'QuestionAnswer'
});

const props = defineProps({
  question: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['answer-change']);

const userAnswer = ref(null);

const questionTypeMap = questionTypeOptions.reduce((acc, item) => {
    acc[item.value] = item.label;
    return acc;
}, {});


function initializeAnswer() {
    switch (props.question.questionType) {
        case 1: // 单选
        case 7: // 判断
            userAnswer.value = null;
            break;
        case 2: // 多选
        case 5: // 连线
            userAnswer.value = [];
            break;
        case 3: // 填空
            userAnswer.value = Array(extractBlanks(props.question.questionContent).length).fill('');
            break;
        case 4: // 排序
            userAnswer.value = [...props.question.options];
            break;
        case 6: // 问答
            userAnswer.value = '';
            break;
        default:
            userAnswer.value = null;
    }
}


const emitAnswer = () => {
  emit('answer-change', {
    questionId: props.question.questionId,
    answer: userAnswer.value
  });
};

const handleMatchChange = (matches) => {
    userAnswer.value = matches;
    emitAnswer();
}

function getQuestionTypeLabel(type) {
    return questionTypeMap[type] || '未知';
}

function safeDecode(content) {
  if (!content) return '';
  try {
    return decodeURIComponent(content);
  } catch (e) {
    return content;
  }
}

function formatBlankQuestion(content, type) {
    if (type !== 3 || !content) {
        return content;
    }
    let blankIndex = 0;
    return content.replace(/\{BLANK\}/g, () => {
        blankIndex++;
        return `<u>&nbsp;&nbsp;&nbsp;(${blankIndex})&nbsp;&nbsp;&nbsp;</u>`;
    });
}

function extractBlanks(content) {
    if (!content) return [];
    return content.match(/\{BLANK\}/g) || [];
}

function shouldShowOptions(type) {
    return [1, 2, 4, 7].includes(type);
}

onMounted(() => {
    initializeAnswer();
});

watch(() => props.question, () => {
    initializeAnswer();
}, { deep: true });
</script>

<style lang="scss" scoped>
.question-content {
    padding: 16px;
    background-color: #f9f9f9;
    border-radius: 4px;
}
.content-item {
    margin-bottom: 15px;
    display: flex;
    .label {
        font-weight: bold;
        margin-right: 8px;
        white-space: nowrap;
    }
    .content {
        flex-grow: 1;
    }
}
.options-area {
    margin-top: 10px;
}
.option-item {
    margin-bottom: 10px;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.3s;
}
.option-text {
    display: inline-flex;
    align-items: center;
    .option-label {
        margin-right: 5px;
        font-weight: bold;
    }
}
.sort-container, .qa-container, .blank-answers {
    margin-top: 15px;
}
.sort-item {
    padding: 8px 12px;
    margin-bottom: 5px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: grab;
    display: flex;
    align-items: center;
}
.sort-handle {
    margin-right: 10px;
    cursor: grab;
}
.blank-input-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}
</style> 