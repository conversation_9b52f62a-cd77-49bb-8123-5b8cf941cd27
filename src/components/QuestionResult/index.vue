<template>
    <div class="question-result-content">
        <!-- 题目标题 -->
        <div class="question-header" v-if="question.title">
            <span class="question-type-tag">{{ getQuestionTypeLabel(question.questionType) }}</span>
            <span class="question-title">{{ question.title }}</span>
        </div>

        <!-- 在题干上方显示备注 -->
        <div v-if="question.questionRemark" class="content-item remark-section">
            <div class="label">备注：</div>
            <div class="content">{{ safeDecode(question.questionRemark) }}</div>
        </div>
        
        <!-- 题干 -->
        <div class="content-item">
            <div class="label">题干：</div>
            <div class="content" v-html="formatBlankQuestion(question.questionContent, question.questionType)"></div>
        </div>

        <!-- 选项区域 -->
        <div class="options-area"
            v-if="shouldShowOptions(question.questionType) && question.options && question.options.length">
            <!-- 单选题/判断题 -->
            <template v-if="question.questionType === 1 || question.questionType === 7">
                <div v-for="(option, index) in question.options" :key="option.id || index" class="option-item"
                    :class="getChoiceOptionClass(option, option.id || index)">
                    <div class="option-content">
                        <el-radio :model-value="userSelectedRadio" :label="option.id || index" disabled>
                            <div class="option-text">
                                <span class="option-label">{{ `${String.fromCharCode(65 + index)}. ` }}</span>
                                <span v-html="option.optionContent"></span>
                            </div>
                        </el-radio>
                        <span class="option-feedback-icon">
                          <el-icon v-if="option.rightFlag === 1"><Check /></el-icon>
                          <el-icon v-else-if="userSelectedRadio === (option.id || index)"><Close /></el-icon>
                        </span>
                    </div>
                </div>
            </template>

            <!-- 多选题 -->
            <template v-else-if="question.questionType === 2">
                <div v-for="(option, index) in question.options" :key="option.id || index" class="option-item"
                    :class="getChoiceOptionClass(option, option.id || index)">
                    <div class="option-content">
                        <el-checkbox :model-value="userSelectedOptions.includes(option.id || index)" disabled>
                            <div class="option-text">
                                <span class="option-label">{{ `${String.fromCharCode(65 + index)}. ` }}</span>
                                <span v-html="option.optionContent"></span>
                            </div>
                        </el-checkbox>
                         <span class="option-feedback-icon">
                          <el-icon v-if="option.rightFlag === 1"><Check /></el-icon>
                          <el-icon v-else-if="userSelectedOptions.includes(option.id || index)"><Close /></el-icon>
                        </span>
                    </div>
                </div>
            </template>
        </div>

        <!-- 填空题 -->
        <div v-if="question.questionType === 3" class="blank-answers">
            <div v-for="(blank, index) in (question.rightAnswer || '').split(';')" :key="index" class="blank-input-item">
                <span>空格 {{ index + 1 }}：</span>
                <el-input :model-value="userBlankAnswers[index]" disabled placeholder="未作答" />
                <span class="correct-answer-text">正确答案: {{ blank }}</span>
            </div>
        </div>

        <!-- 排序题 -->
        <div v-if="question.questionType === 4" class="sorting-question-result">
            <div class="content-item">
                <div class="label">你的回答：</div>
                <div class="content">
                    <div v-if="!userSortingAnswer || !userSortingAnswer.length">未作答</div>
                    <div v-else>
                        <div v-for="(item, index) in formattedUserSortingAnswer" :key="index"
                             class="sorting-answer-item"
                             :class="{'correct-text': item.isCorrect, 'wrong-text': !item.isCorrect}">
                            <span>{{ index + 1 }}. {{ item.text }}</span>
                            <el-icon v-if="item.isCorrect"><Check /></el-icon>
                            <el-icon v-else><Close /></el-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 连线题 -->
        <div v-if="question.questionType === 5" class="matching-question-result">
             <div class="content-item">
                <div class="label">你的回答：</div>
                <div class="content">
                    <div v-if="!userMatchingAnswer.length">未作答</div>
                    <div v-for="(item, index) in formattedUserMatchingAnswer" :key="index"
                         class="matching-answer-item"
                         :class="{'correct-text': item.isCorrect, 'wrong-text': !item.isCorrect}">
                        <span>{{ item.text }}</span>
                        <el-icon v-if="item.isCorrect"><Check /></el-icon>
                        <el-icon v-else><Close /></el-icon>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 问答题 -->
        <div v-if="question.questionType === 6" class="qa-container">
            <div class="content-item">
                <div class="label">你的回答：</div>
                <div class="content">
                    <el-input :model-value="userQaAnswer" type="textarea" :rows="4" disabled placeholder="未作答" />
                </div>
            </div>
        </div>

        <!-- 答案和解析 -->
        <div class="answer-section">
            <div class="content-item" v-if="question.rightAnswer">
                <div class="label">参考答案：</div>
                <!-- 排序题特殊展示 -->
                <div class="content" v-if="question.questionType === 4">
                     <div v-for="(text, index) in formattedRightSortingAnswer" :key="index" class="sorting-answer-item">
                        <span>{{ index + 1 }}. {{ text }}</span>
                    </div>
                </div>
                <!-- 连线题特殊展示 -->
                <div class="content" v-if="question.questionType === 5">
                     <div v-for="(text, index) in formattedRightMatchingAnswer" :key="index" class="matching-answer-item">
                        <span>{{ text }}</span>
                    </div>
                </div>
                 <!-- 其他题型 -->
                <div class="content" v-else-if="question.questionType !== 3" v-html="question.rightAnswer"></div>
            </div>

            <!-- 解析区域 -->
            <div class="content-item" v-if="question.analysis">
                <div class="label">解析：</div>
                <div class="content" v-html="question.analysis"></div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watchEffect, computed } from 'vue';
import { questionTypeOptions } from '@/utils/optionUtil.js';
import { Check, Close } from '@element-plus/icons-vue';

defineOptions({
  name: 'QuestionResult'
});

const props = defineProps({
  question: {
    type: Object,
    required: true
  },
  userAnswer: {
    type: [String, Number, Array, Object],
    default: null
  }
});

const userSelectedRadio = ref(null);
const userSelectedOptions = ref([]);
const userBlankAnswers = ref([]);
const userQaAnswer = ref('');
const userMatchingAnswer = ref([]);
const userSortingAnswer = ref([]);

watchEffect(() => {
    const answer = props.userAnswer ? props.userAnswer.answer : null;
    switch (props.question.questionType) {
        case 1: // 单选
        case 7: // 判断
            userSelectedRadio.value = answer;
            break;
        case 2: // 多选
            userSelectedOptions.value = Array.isArray(answer) ? answer : [];
            break;
        case 3: // 填空
            userBlankAnswers.value = Array.isArray(answer) ? answer : [];
            break;
        case 4: // 排序
            userSortingAnswer.value = Array.isArray(answer) ? answer : [];
            break;
        case 5: // 连线
            userMatchingAnswer.value = Array.isArray(answer) ? answer : [];
            break;
        case 6: // 问答
            userQaAnswer.value = answer || '';
            break;
    }
});

const questionTypeMap = questionTypeOptions.reduce((acc, item) => {
    acc[item.value] = item.label;
    return acc;
}, {});

const getChoiceOptionClass = (option, optionId) => {
    const isCorrect = option.rightFlag === 1;
    let isSelected = false;

    if (Array.isArray(userSelectedOptions.value)) { // 多选
        isSelected = userSelectedOptions.value.includes(optionId);
    } else { // 单选、判断
        isSelected = userSelectedRadio.value === optionId;
    }

    return {
        'correct-option': isCorrect,
        'wrong-option': !isCorrect && isSelected,
    };
};

function getQuestionTypeLabel(type) {
    return questionTypeMap[type] || '未知';
}

function safeDecode(content) {
  if (!content) return '';
  try { return decodeURIComponent(content); } catch (e) { return content; }
}

function formatBlankQuestion(content, type) {
    if (type !== 3 || !content) return content;
    let blankIndex = 0;
    return content.replace(/\{BLANK\}/g, () => {
        blankIndex++;
        return `<u>&nbsp;&nbsp;&nbsp;(${blankIndex})&nbsp;&nbsp;&nbsp;</u>`;
    });
}

function shouldShowOptions(type) {
    return [1, 2, 7].includes(type);
}

function stripHtml(html) {
  if (!html) return '';
  let doc = new DOMParser().parseFromString(html, 'text/html');
  return doc.body.textContent || "";
}

const formattedUserMatchingAnswer = computed(() => {
    if (props.question.questionType !== 5) return [];
    try {
        const leftOptions = props.question.options.filter(o => o.optionPosition === 1).sort((a,b) => (a.sort || 0) - (b.sort || 0));
        const rightOptions = props.question.options.filter(o => o.optionPosition === 2).sort((a,b) => (a.sort || 0) - (b.sort || 0));
        const rightAnswerLinks = JSON.parse(props.question.rightAnswer || '[]');

        return userMatchingAnswer.value.map(link => {
            const sourceText = leftOptions[link.source]?.optionContent || '未知选项';
            const targetText = rightOptions[link.target]?.optionContent || '未知选项';
            const isCorrect = rightAnswerLinks.some(correctLink => correctLink.source === link.source && correctLink.target === link.target);
            return {
                text: `${stripHtml(sourceText)} → ${stripHtml(targetText)}`,
                isCorrect: isCorrect
            };
        });
    } catch (e) {
        return [];
    }
});

const formattedRightMatchingAnswer = computed(() => {
    if (props.question.questionType !== 5) return [];
     try {
        const leftOptions = props.question.options.filter(o => o.optionPosition === 1).sort((a,b) => (a.sort || 0) - (b.sort || 0));
        const rightOptions = props.question.options.filter(o => o.optionPosition === 2).sort((a,b) => (a.sort || 0) - (b.sort || 0));

        const rightAnswerLinks = JSON.parse(props.question.rightAnswer || '[]');

        return rightAnswerLinks.map(link => {
            const sourceText = leftOptions[link.source]?.optionContent || '未知选项';
            const targetText = rightOptions[link.target]?.optionContent || '未知选项';
            return `${stripHtml(sourceText)} → ${stripHtml(targetText)}`;
        });
    } catch (e) {
        console.error("Failed to parse matching answer", e);
        return ["答案解析失败"];
    }
});

const formattedUserSortingAnswer = computed(() => {
    if (props.question.questionType !== 4 || !userSortingAnswer.value) return [];
    try {
        const optionsMap = new Map(props.question.options.map(opt => [opt.id, opt.optionContent]));
        const rightAnswerOrder = JSON.parse(props.question.rightAnswer || '[]');

        return userSortingAnswer.value.map((optionId, index) => {
            const text = stripHtml(optionsMap.get(optionId) || '未知选项');
            const isCorrect = rightAnswerOrder[index] === optionId;
            return { text, isCorrect };
        });
    } catch (e) {
        console.error("Error formatting user sorting answer:", e);
        return [];
    }
});

const formattedRightSortingAnswer = computed(() => {
    if (props.question.questionType !== 4) return [];
    try {
        const optionsMap = new Map(props.question.options.map(opt => [opt.id, opt.optionContent]));
        const rightAnswerOrder = JSON.parse(props.question.rightAnswer || '[]');
        return rightAnswerOrder.map(optionId => stripHtml(optionsMap.get(optionId) || '未知选项'));
    } catch (e) {
        console.error("Error formatting right sorting answer:", e);
        return [];
    }
});

</script>

<style lang="scss" scoped>
.question-result-content {
    max-width: 100%;
    overflow-x: hidden;
    position: relative;
    padding-bottom: 50px;
    
    .question-header {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .question-type-tag {
        background-color: #409eff;
        color: white;
        padding: 4px 10px;
        border-radius: 4px;
        font-size: 13px;
        margin-right: 12px;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
    }

    .question-title {
        font-weight: 600;
        font-size: 17px;
        color: #303133;
        line-height: 1.4;
    }
    
    .content-item {
        margin-bottom: 15px;
        
        &.remark-section {
            color: #909399;
            font-size: 14px;
            .content {
                word-break: break-all;
            }
        }

        .label {
            font-weight: bold;
            margin-bottom: 8px;
            color: #606266;
        }
        
        .content {
            color: #303133;
            line-height: 1.6;
            word-break: break-word;
            overflow-wrap: break-word;
        }
    }
}

.options-area {
    margin: 10px 0;
}
.option-item {
    margin-bottom: 8px;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    transition: all 0.3s;

    &.correct-option {
        background-color: #f0f9eb;
        border-color: #67c23a;
        .option-feedback-icon {
          color: #67c23a;
        }
    }
    &.wrong-option {
        background-color: #fef0f0;
        border-color: #f56c6c;
        .option-feedback-icon {
          color: #f56c6c;
        }
    }
}
.option-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.option-feedback-icon {
  font-size: 18px;
}
.blank-answers, .qa-container {
    margin-top: 15px;
}
.blank-input-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .el-input {
      margin: 0 10px;
    }
}
.correct-answer-text{
  color: #67c23a;
  font-weight: bold;
}
.answer-section {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
}
.matching-answer-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;

    .el-icon {
        font-size: 16px;
    }
}
.correct-text {
    color: #67c23a;
}
.wrong-text {
    color: #f56c6c;
}

.option-text {
    display: inline-flex;
    align-items: center;
    flex-wrap: nowrap;
    width: 100%;
}

:deep(.el-radio), :deep(.el-checkbox) {
    width: 100%;
    display: flex;
    align-items: center;
}

:deep(.el-radio__label), :deep(.el-checkbox__label) {
    white-space: normal;
    display: inline-flex;
    align-items: center;
    padding-left: 5px;
}

:deep(.el-radio__inner), :deep(.el-checkbox__inner) {
    vertical-align: middle;
}

:deep(.el-radio__input), :deep(.el-checkbox__input) {
    align-self: center;
}
</style> 