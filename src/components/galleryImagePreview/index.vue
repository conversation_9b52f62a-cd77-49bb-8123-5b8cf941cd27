<template>
  <!-- <viewer
    class="viewer"
    ref="viewerRef"
    :images="images"
    :options="viewerOptions"
    @inited="inited"
    @close="onViewerClose"
    >
    <template #default="scope">
      <img v-for="src in scope.images" :src="src" :key="src">
    </template>
  </viewer> -->
</template>
<script setup>
import { watch } from 'vue'

import VueViewer, { api as viewerApi, component as Viewer } from "v-viewer"
import usePreview from '@/store/modules/preview.js'

VueViewer.setDefaults({
  zIndex: 2021,
})

const previewStore = usePreview()
let images = ref([])
const $viewer = ref(null)
// const viewerOptions = reactive({
//   initialViewIndex: 1,
//   hidden() {
//     previewStore.setCurrentImage('')
//     emits('close')
//   }
// })
// const emits = defineEmits(['close'])

// function inited (viewer) {
//   $viewer.value = viewer
// };

watch(
  () => previewStore.currentOnShowImageUrl,
  (nValue) => {
    if (!nValue) {
      return
    }
    let defaultImageIndex = images.value.findIndex(imgItem => imgItem.fileUrl === nValue)
    if (defaultImageIndex < 0) {
      defaultImageIndex = 1
    }
    // viewerOptions.initialViewIndex = defaultImageIndex 
    viewerApi({
      options: {
        toolbar: true,
        url: 'fileUrl',
        initialViewIndex: defaultImageIndex
      },
      images: images.value,
    })
    // setTimeout(() => {
    //   $viewer.value.show()
    // }, 0)
  }
)

watch(
  () => previewStore.allResourceLimages,
  (nValue) => {
    if (!nValue) {
      nValue = []
    }
    images.value = nValue
    // .map(imageResourceItem => {
    //   return imageResourceItem.fileUrl
    // })
  }
)
</script>
<style>
</style>
