<template>
  <div class="head-nav-con" :class="[{ 'blue-bg': showBackground }, 'theme-dark']">
    <div class="head-nav">
      <div class="head-nav-con-left">
        <img
          class="logo"
          referrerpolicy="referrer"
          :src="siteInfo?.logoUrl"
          alt=""
          v-if="!isLoginPage"
        />
        <img
          class="logo"
          referrerpolicy="referrer"
          :src="siteInfo?.blueLogoUrl"
          alt=""
          v-else
        />
      </div>
      <div class="head-nav-con-right">
        <div class="nav-right-menu">
          <template v-for="(item, index) in menus" :key="index">
            <div
              :class="[
                { 'menu-item': !isLoginPage },
                { 'menu-item-ln': isLoginPage },
                { active: index === activeMenuIndex },
              ]"
              :style="{ visibility: item.visibleByRole ? 'visible' : 'hidden' }"
              @click="handleSelect(item, index)"
            >
              <el-badge :is-dot="true" :hidden="index !== 6">
                <span class="label"> {{ item.menuName }} </span>
              </el-badge>
            </div>
          </template>
          <el-input
            style="margin: 0px 50px;"
            class="floating-input"
            placeholder="请输入搜索关键词"
            v-model="searchKey"
            >
            <template #suffix>
              <img class="floating-img" @click="selOpenCourse(searchKey)" :src="openCourseListSelect"/>
            </template>
          </el-input>
        </div>
        <div class="nav-right-name">
          <template v-if="getToken()">
            <div class="avatar-container">
              <el-dropdown @command="handleCommand" trigger="click">
                <div style="display: flex; align-items: center">
                  <img
                    class="avg"
                    :src="userStore.avatar ? userStore.avatar : profile"
                    alt="Logo"
                    referrerpolicy="referrer"
                  />
                  <span class="name">{{ userName }}</span>
                  <el-icon
                    :style="{ color: '#000' }"
                  >
                    <caret-bottom />
                  </el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="routerLink"
                      >个人中心</el-dropdown-item
                    >
                    <el-dropdown-item divided command="logout">
                      <span>退出登录</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
          <template v-else>
            <span
              class="span_login"
              :class="{ 'ln-sty': isLoginPage }"
              @click="toLogin"
            >
              <span>登录/注册</span>
            </span>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="headNavComp">
import useUserStore from "@/store/modules/user.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { onMounted, onUpdated, ref, watch } from "vue";
import openCourseListSelect from "@/assets/images/openCourse/open_course_list_select.png";
import { getToken } from "@/utils/auth";
import { useRouter } from "vue-router";
import profile from "@/assets/images/profile.png";
import { getSiteInfoById } from "@/api/basic/home.js";
import { SiteId } from "@/utils/constant";
const router = useRouter();
const userStore = useUserStore();
const menus = ref([
  {
    menuName: "公开课",
    menuRoute: "/open-course-list",
    routeType: 1,
    showFlg: '1',
    sort: 1,
    visibleByRole: true,
  },
  {
    menuName: "公开课",  //教师端
    menuRoute: "/openCourse",
    routeType: 1,
    showFlg: '2',
    sort: 1,
    visibleByRole: true,
  },
    {
    menuName: "互动课堂",
    menuRoute: "/smart-course-list",
    routeType: 1,
    showFlg: '2',
    sort: 2,
    visibleByRole: true,
  },
  {
    menuName: "教师空间",
    menuRoute: "/teacher-space/teacher-resource",
    routeType: 1,
    showFlg: '2',
    sort: 3,
    visibleByRole: true,
  },
  {
    menuName: "测试活动",
    menuRoute: "/test-activity",
    routeType: 1,
    showFlg: '2',
    sort: 4,
    visibleByRole: true,
  },
  {
    menuName: "课程设计",
    menuRoute: "/courseware-design/mycourseware_design",
    routeType: 1,
    showFlg: '2',
    sort: 5,
    visibleByRole: true,
  },
]);
const siteInfo = ref(null);
let token = computed(() => getToken());
const userName = ref("");
const userInfo = ref({});
const searchKey = ref(null);
const { proxy } = getCurrentInstance();
const activeMenuIndex = ref(-1);
const props = defineProps({
  showBackground: {
    type: Boolean,
    default: false,
  },
  isLoginPage: {
    type: Boolean,
    default: false,
  },
  showInteractiveClass: {
    type: Boolean,
    default: true
  }
});
const themeClass = computed(() => {
  return `theme-${props.theme}`;
});
const isShowItem = (userInfo) => {
  menus.value.forEach((item, index) => {
    item.visibleByRole = item.showFlg === userInfo.userType;

    // if (item.menuName === "互动课堂") {
    //   item.visibleByRole =  visibleByRole && props.showInteractiveClass;
    // }
  });
  console.log('menus', menus.value);
};
const handleSelect = (item, index) => {
  activeMenuIndex.value = index;
  // 内部链接
  if (item.routeType == 1) {
    if (item.menuName === "教学平台") {
      window.open(item.menuRoute, "_blank");
    }
    if (item.menuRoute || item.menuRoute === 0) {
      proxy.$router.push({ path: item.menuRoute });
    } else {
      proxy.$router.push({ path: "/user" });
    }
    // 外部链接
  } else {
    let url = item.menuRoute;
    if (url.indexOf("http") == -1) {
      url = "http://" + item.menuRoute;
    }
    window.open(url, "_blank");
  }
};
const toLogin = () => {
  proxy.$emit("toLogin");
};

const routerLink = () => {
  if (getToken()) {
    router.push({ path: `/basic-information` });
  }
};

function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      userStore.logOut().then(() => {
        location.href = "/index";
      });
    })
    .catch(() => {});
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

const emits = defineEmits(["setLayout"]);

function setLayout() {
  emits("setLayout");
}

function checkTokenNotExpired() {
  try {
    userStore
      .getInfo()
      .then((response) => {
        if (response.code !== 200) {
          ElMessage.error(response.msg);
          setTimeout(() => {
            userStore.logOut().then(() => {
              location.href = "/index";
            });
          }, 500);
        }
      })
      .catch(() => {
        // return false;
      });
  } catch (e) {
    // return false;
  }
}

function checkLoginTimeOut() {
  if (token.value) {
    checkTokenNotExpired();
  }
}
function selOpenCourse (searchKey) {
    emits("search", searchKey); // 触发自定义事件
}
onMounted(async () => {
  if (getToken()) {
      userName.value = useUserStore().nickName;
      const response = await userStore.getInfo();
      userInfo.value = response.user;
      isShowItem(response.user);
      console.log('userInfo.value', userInfo.value);
    }
  checkLoginTimeOut();
  await getSiteInfoById(SiteId).then((res) => {
    if (res.code === 200) {
    }
    siteInfo.value = res.data;
  });
});
</script>

<style lang="scss" scoped>
@import "@/assets/styles/index";

.head-nav-con {
  width: 100%;
  height: 68px;
  padding-top: 10px;
  padding-bottom: 10px;
  @extend .base-flex;
  justify-content: center;
  align-items: center;

  .head-nav {
    width: 1400px;
    height: 48px;
    padding: 0 10px;
    @extend .base-flex;
    justify-content: space-between;
    align-items: center;

    .head-nav-con-left {
      .logo {
        width: 175px;
        height: 54px;
      }
    }

    .head-nav-con-right {
      @extend .base-flex;
      justify-content: space-between;
      align-items: center;

      .nav-right-menu {
        height: 48px;
        @extend .base-flex;
        justify-content: space-between;
        align-items: center;

        .menu-item {
          min-width: 120px;
          height: 30px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #ffffff;
          text-align: justify;
          font-style: normal;
          cursor: pointer;
          @extend .base-flex;
          justify-content: center;
          align-items: center;
        }

        .menu-item:hover {
          opacity: 0.7;
        }

        .menu-item-ln {
          height: 30px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #333333;
          text-align: justify;
          font-style: normal;
          padding: 0 12px;
          cursor: pointer;
          @extend .base-flex;
          justify-content: center;
          align-items: center;
        }

        .menu-item-ln:hover {
          opacity: 0.7;
        }
        .active {
          position: relative;
          color: #00DDFF;
          &::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 40%;
            width: 20%;
            height: 2px;
            background: linear-gradient(135deg, #00DDFF 0%, #BDF6FF 31%, #00DDFF 71%, #BDF6FF 100%);
          }
        }
        .label {
          font-weight: normal;
          font-size: 18px;
          color: #333333;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }

      .nav-right-name {
        font-weight: normal;
        font-size: 18px;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
        @extend .base-flex;
        justify-content: space-between;
        align-items: center;

        .avg {
          width: 25px;
          height: 25px;
          border-radius: 50%;
          margin-right: 8px;
        }

        .name {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #ffffff;
          font-style: normal;
        }
      }
    }
  }
}

.blue-bg {
  background: #0966b4;
}

.span_login {
  height: 17px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
  font-style: normal;
  line-height: 17px;
  cursor: pointer;
  @extend .base-flex;
  justify-content: center;
  align-items: center;
}

.span_login:hover {
  opacity: 0.7;
}

.ln-sty {
  color: #0966b4;
}
:deep(.el-input__wrapper) {
    width: 238px;
    height: 40px;
    background: rgba(255,255,255,0.28);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #FFFFFF;
}
:deep(.el-input__suffix) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
.floating-img {
  cursor: pointer;
}

.theme-dark {
  .menu-item,
  .menu-item .label,
  .menu-item-ln,
  .menu-item-ln .label,
  .nav-right-name .name,
  .span_login,
  .span_login span {
    color: #000000 !important;
  }

  .el-icon {
    color: #000000 !important;
  }

  .floating-img {
    filter: brightness(0) !important;
  }

  .active {
    position: relative;
    color: #00A4C6 !important;
    &::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 40%;
      width: 20%;
      height: 2px;
      background: linear-gradient(135deg, #00A4C6 0%, #AEEAF3 31%, #00A4C6 71%, #AEEAF3 100%) !important;
    }
  }

  :deep(.el-input__inner::placeholder) {
    font-weight: normal;
    font-size: 14px;
    color: #999999;
    line-height: 14px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
   :deep(.el-input__wrapper) {
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #333333;
  }
}
:deep(.floating-input .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px #333333 !important;
  border: 1px solid #33333300 !important;
}
</style>
