<template>
  <div class="layout-open-course">
    <div class="page-nav-container">
      <div class="nav-con">
        <headNavComp @toLogin="toLogin" @search="handleSearch" :showBackground="false" />
      </div>
      <div class="banner-con">
        <banner />
      </div>
    </div>
    <div class="page-content-con">
      <!-- <app-main /> -->
      <router-view :searchKey ="searchKey"/>
          <!-- 底部   -->
      <div class="foot-con">
        <footComp />
      </div>
    </div>

    <!-- 联系客服 -->
    <customerServiceBar @sendMessage="receiveMessage" />
  </div>
</template>

<script setup name="LayoutOpenCourse">
import footComp from './components/footComp/index.vue';
import headNavComp from './components/headNavComp/index.vue';
import banner from './components/Banner/index.vue';
import customerServiceBar from "@/views/home/<USER>/customerServiceBar/index.vue";
import { useRouter } from 'vue-router';

const router = useRouter();
const searchKey = ref(null);
const key = ref(0);
function toLogin() {
  router.push({ path: '/login' });
}
function handleSearch(value) {
  searchKey.value = value;
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/index.scss';
.layout-open-course {
  width: 99%;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;
  .page-nav-container {
    width: 100%;
    .nav-con {
      width: 100%;
      position: absolute;
      top: 11px;
      left: 0px;
      z-index: 2;
    }
    .banner-con {
      width: 100%;
      height: 442px;
      position: absolute;
      top: 0px;
      left: 0px;
      z-index: 1;
    }
  }
  .page-content-con {
    width: 100%;
    height: auto; // 自适应内容
    position: absolute;
    top: 442px; // 设置为 banner 的高度，确保内容显示在 banner 之下
    align-self: stretch;
    @extend .base-flex-column;
    justify-content: flex-start;
    // align-items: center;
  }
  .foot-con {
    width: 100%;
  }
}
</style>
