<template>
  <div class="banner-sty-open">
      <div class="img-carousel-item-open">
        <img class="img-sty-open" src="@/assets/images/openCourse/open_course_bg.png"/>
      </div>
      <el-button class="floating-btn" @click="handleMyCourseClick">我的课程</el-button>
  </div>
</template>

<script setup name="Banner">
const show = ref(false);
import { getBanners } from '@/api/basic/home.js';
// import { BANNER_KEY } from '@/utils/constant.js';
import { useRouter } from 'vue-router';
const router = useRouter();
const banners = ref([]);
const refresh = ref(true);
function doGetBanners() {
  getBanners({ bannerPosition: 1, device: 'PC' })
    .then((res) => {
      if (res.code == 200) {
        banners.value = res.data;
        refresh.value = !refresh.value;
        setTimeout(() => {
          refresh.value = true;
        }, 100)
        // sessionStorage.setItem(BANNER_KEY, JSON.stringify(banners.value));
      }
    })
    .catch((error) => {});
}
function jumpBanner(item) {
  if (item.linkUrl) {
    window.open(item.linkUrl);
  }
}
onMounted(() => {
  // banners.value = JSON.parse(sessionStorage.getItem(BANNER_KEY));
  // if (!banners.value) {

  // }
});
doGetBanners()
function handleMyCourseClick() {
  // 根据业务需求实现跳转或弹窗逻辑
  console.log('我的课程按钮被点击');
  // 示例：跳转到指定页面
  router.push('/my-open-classes');
}
</script>

<style lang="scss" scoped>
.banner-sty-open {
  width: 100%;
  height: 442px;
  .img-carousel-item-open {
    width: 100%;
    height: 442px;
  }
  .img-sty-open {
    width: 100%;
    height: 100%;
  }
}

.floating-btn {
  cursor: pointer;
  position: absolute;
  z-index: 10; // 提升至更高层级
  top: 66%;
  left: 17%;
  transform: translateX(-50%);
  border-radius: 10px 10px 10px 10px;
  background: #226BFE;
  font-weight: normal;
  font-size: clamp(14px, 2vw, 18px);
  padding: 0.5em 0.8em; // 动态内边距
  width: auto; // 自适应宽度
  height: auto; // 自适应高度
  color: #000000;
  text-align: left;
  font-style: normal;
}
</style>
