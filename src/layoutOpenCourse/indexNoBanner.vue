<template>
  <div class="layout-open-course">
    <div class="page-nav-container">
      <div class="nav-con">
        <headerSection @toLogin="toLogin" @search="handleSearch" :showBackground="false" />
      </div>
    </div>
    <div class="page-content-con">
      <!-- <app-main /> -->
      <router-view :searchKey ="searchKey"/>
          <!-- 底部   -->
      <div class="foot-con">
        <footComp />
      </div>
    </div>
    <!-- 联系客服 -->
    <customerServiceBar @sendMessage="receiveMessage" />
  </div>
</template>

<script setup name="LayoutOpenCourse">
import footComp from './components/footComp/index.vue';
import headerSection from './components/headerSection/index.vue';
import customerServiceBar from "@/views/home/<USER>/customerServiceBar/index.vue";
import { useRouter } from 'vue-router';

const router = useRouter();
const searchKey = ref('');
function toLogin() {
  router.push({ path: '/login' });
}
function handleSearch(value) {
  searchKey.value = value;
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/index.scss';
.layout-open-course {
  width: 100%;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;
  .page-nav-container {
    width: 100%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    position: relative;
    height: 80px; /* 添加具体高度 */
    background-color: #fff; /* 添加背景色以便看到阴影 */
    z-index: 10;
    .nav-con {
      width: 100%;
      position: absolute;
      top: 11px;
      left: 0px;
      z-index: 2;
    }

  }
  .page-content-con {
    width: 100%;
    height: auto; // 自适应内容
    position: absolute;
    top: 80px; // 修改为与nav-container高度相同的值
    @extend .base-flex-column;
    justify-content: flex-start;
    background-color: #f0f2f5;
  }
  .foot-con {
    width: 100%;
  }
}
</style>
