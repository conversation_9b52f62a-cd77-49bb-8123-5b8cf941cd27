/*
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-10 15:19:19
 * @LastEditTime: 2025-01-13 13:35:12
 * @FilePath: \dutp-stu-tea-vue\src\main.js
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
 */
import { createApp } from 'vue'

import Cookies from 'js-cookie'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import locale from 'element-plus/es/locale/lang/zh-cn'
import 'prismjs/themes/prism-tomorrow.css'

import '@/assets/styles/index.scss' // global css
import '@/utils/jquery.min.1.7.js'
import App from './App.vue'
import store from './store'
import router from './router'

// 权限v-hasRole,vhasPermi，复制粘贴板 v-copyText
import directive from './directive' // directive

// 注册指令
import plugins from './plugins' // plugins
import { download } from '@/utils/request'

// svg图标
import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvgIcon'
import elementIcons from '@/components/SvgIcon/svgicon'

import './permission' // 拦截器permission control
import { useDict } from '@/utils/dict' // 字典数据工具
import { parseTime, resetForm, addDateRange, handleTree, selectDictLabel, selectDictLabels } from '@/utils/dutp'

// 分页组件
import Pagination from '@/components/Pagination'
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar'
// 富文本组件
import Editor from "@/components/Editor"
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
// 自定义树选择组件
import TreeSelect from '@/components/TreeSelect'
// 字典标签组件
import DictTag from '@/components/DictTag'
// 卡片组件
import Cards from '@/components/Cards'
import disableDevtool from 'disable-devtool';
import 'viewerjs/dist/viewer.css'
import './assets/fonts.css'

// 验证域名，在该域名下，禁用开发者工具
function checkDomain() {
  return window.location.hostname.endsWith('.dutp.cn');
}
const openDev = sessionStorage.getItem('OPEN_DEV');
console.log('openDev cache', openDev)
if (openDev && openDev == '1') {
  console.log('打开开发者模式成功！！！')
} else {
if (checkDomain()) {
  disableDevtool({
    ondevtoolopen: function() {
        window.location.href = 'https://ebook.dutp.cn';
    }
  })
  }
}
// 创建app实例
const app = createApp(App)


// 全局方法挂载
app.config.globalProperties.useDict = useDict
app.config.globalProperties.download = download
app.config.globalProperties.parseTime = parseTime
app.config.globalProperties.resetForm = resetForm
app.config.globalProperties.handleTree = addDateRange
app.config.globalProperties.handleTree = handleTree
app.config.globalProperties.handleTree = selectDictLabel
app.config.globalProperties.handleTree = selectDictLabels

// 全局组件挂载
app.component('Pagination', Pagination)
app.component('RightToolbar', RightToolbar)
app.component('Editor', Editor)
app.component('FileUpload', FileUpload)
app.component('ImageUpload', ImageUpload)
app.component('ImagePreview', ImagePreview)
app.component('TreeSelect', TreeSelect)
app.component('DictTag', DictTag)
app.component('Cards', Cards)

app.use(router)
app.use(store)
app.use(plugins)
app.use(elementIcons)
app.component('svg-icon', SvgIcon)
directive(app) // init directive

// 使用element-plus 并且设置,中文、全局的大小
app.use(ElementPlus, {
  locale: locale,
  // 支持 large、default、small
  size: Cookies.get('size') || 'default'
})


app.mount('#app')
