{"name": "dutp-stu-tea-vue", "version": "1.0.0", "description": "学生教师端系统", "author": "DUTP", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:stage": "vite build --mode staging", "build:prod": "vite build --mode production", "preview": "vite preview"}, "repository": {"type": "git", "url": " https://codeup.aliyun.com/62f1acd937e2c6c98549efed/dutp/dutp-stu-tea-vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@stomp/stompjs": "^7.1.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "ali-oss": "^6.21.0", "axios": "0.28.1", "buffer": "^6.0.3", "bufferutil": "^4.0.9", "dayjs": "^1.11.13", "disable-devtool": "^0.3.8", "echarts": "5.5.1", "element-plus": "^2.9.11", "file-saver": "2.0.5", "fuse.js": "6.6.2", "html2canvas": "^1.4.1", "js-base64": "^3.7.7", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "jszip": "^3.10.1", "nprogress": "0.2.0", "pinia": "2.1.7", "prismjs": "^1.29.0", "qrcode.vue": "^3.6.0", "simple-mind-map": "^0.14.0-fix.1", "sockjs-client": "^1.6.1", "sortablejs": "^1.15.3", "speak-tts": "~2.0.8", "swiper": "^11.2.6", "utf-8-validate": "^6.0.5", "uuid": "^10.0.0", "v-viewer": "^3.0.21", "viewerjs": "^1.11.7", "vue": "3.4.31", "vue-cropper": "1.1.1", "vue-draggable-plus": "^0.6.0", "vue-prism-component": "^2.0.0", "vue-router": "4.4.0", "vue3-emoji-picker": "^1.1.8", "vue3-slide-verify": "^1.1.5", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "5.0.5", "less": "^4.2.1", "prettier": "^3.4.2", "sass": "1.77.5", "unplugin-auto-import": "0.17.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "^2.0.1"}}